//! 性能指标定义

/// 性能指标
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct PerformanceMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub latency: f64,
    pub throughput: f64,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            memory_usage: 0.0,
            latency: 0.0,
            throughput: 0.0,
        }
    }
}
