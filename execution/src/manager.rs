//! 订单管理器

use sigmax_core::{Order, OrderId, OrderStatus, SigmaXResult};
use std::collections::HashMap;
use tokio::sync::RwLock;

/// 订单管理器
pub struct OrderManager {
    orders: RwLock<HashMap<OrderId, Order>>,
}

impl OrderManager {
    pub fn new() -> Self {
        Self {
            orders: RwLock::new(HashMap::new()),
        }
    }

    /// 添加订单
    pub async fn add_order(&self, order: Order) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        orders.insert(order.id, order);
        Ok(())
    }

    /// 获取订单
    pub async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        let orders = self.orders.read().await;
        Ok(orders.get(&order_id).cloned())
    }

    /// 更新订单状态
    pub async fn update_order_status(&self, order_id: OrderId, status: OrderStatus) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        if let Some(order) = orders.get_mut(&order_id) {
            order.status = status;
            order.updated_at = chrono::Utc::now();
        }
        Ok(())
    }

    /// 获取所有活跃订单
    pub async fn get_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let active_orders = orders.values()
            .filter(|order| matches!(order.status, OrderStatus::Pending | OrderStatus::PartiallyFilled))
            .cloned()
            .collect();
        Ok(active_orders)
    }

    /// 获取所有订单
    pub async fn get_all_orders(&self) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        Ok(orders.values().cloned().collect())
    }

    /// 删除订单
    pub async fn remove_order(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        let mut orders = self.orders.write().await;
        Ok(orders.remove(&order_id))
    }

    /// 获取订单数量
    pub async fn get_order_count(&self) -> usize {
        let orders = self.orders.read().await;
        orders.len()
    }

    /// 取消订单
    pub async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        if let Some(order) = orders.get_mut(&order_id) {
            order.status = OrderStatus::Cancelled;
            order.updated_at = chrono::Utc::now();
            tracing::info!("订单已取消: {}", order_id);
            Ok(())
        } else {
            Err(sigmax_core::SigmaXError::NotFound(format!("订单 {} 未找到", order_id)))
        }
    }
}
