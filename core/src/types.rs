//! 核心类型定义

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 价格类型
pub type Price = Decimal;

/// 数量类型
pub type Quantity = Decimal;

/// 金额类型
pub type Amount = Decimal;

/// 交易对ID
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TradingPair {
    pub base: String,
    pub quote: String,
}

impl TradingPair {
    pub fn new(base: impl Into<String>, quote: impl Into<String>) -> Self {
        Self {
            base: base.into(),
            quote: quote.into(),
        }
    }
    
    pub fn symbol(&self) -> String {
        format!("{}/{}", self.base, self.quote)
    }

    pub fn from_symbol(symbol: &str) -> Result<Self, String> {
        let parts: Vec<&str> = symbol.split('/').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid trading pair symbol: {}", symbol));
        }
        Ok(Self::new(parts[0], parts[1]))
    }
}

/// 订单ID
pub type OrderId = Uuid;

/// 策略ID
pub type StrategyId = Uuid;

/// 交易所ID
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ExchangeId {
    Binance,
    Coinbase,
    Kraken,
    OKX,
    Simulator,
}

impl std::fmt::Display for ExchangeId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ExchangeId::Binance => write!(f, "binance"),
            ExchangeId::Coinbase => write!(f, "coinbase"),
            ExchangeId::Kraken => write!(f, "kraken"),
            ExchangeId::OKX => write!(f, "okx"),
            ExchangeId::Simulator => write!(f, "simulator"),
        }
    }
}

impl From<String> for ExchangeId {
    fn from(s: String) -> Self {
        match s.to_lowercase().as_str() {
            "binance" => ExchangeId::Binance,
            "coinbase" => ExchangeId::Coinbase,
            "kraken" => ExchangeId::Kraken,
            "okx" => ExchangeId::OKX,
            "simulator" => ExchangeId::Simulator,
            _ => ExchangeId::Simulator, // 默认值
        }
    }
}

impl From<&str> for ExchangeId {
    fn from(s: &str) -> Self {
        Self::from(s.to_string())
    }
}

/// 引擎ID
pub type EngineId = Uuid;

// 注意：EngineType、EngineStatus 已移至 enums.rs 模块
// 避免重复定义导致的类型冲突

// 注意：OrderSide、OrderType、OrderStatus 已移至 enums.rs 模块
// 避免重复定义导致的类型冲突

// 注意：TimeFrame 已移至 enums.rs 模块
// 避免重复定义导致的类型冲突

// 注意：EngineConfig、EngineStatistics 已移至 models/engine.rs 模块
// 避免重复定义导致的类型冲突

// 注意：RiskCheckResult、RiskMetrics、AdapterMetrics 已移至 traits.rs 模块
// 避免重复定义导致的类型冲突

// 注意：Candle、Trade、OrderBook 已移至 models/ 模块
// 避免重复定义导致的类型冲突
// 使用 models::Candle, models::Trade, models::OrderBook
