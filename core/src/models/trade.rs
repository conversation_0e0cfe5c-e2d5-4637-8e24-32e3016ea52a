//! 交易记录（成交）模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::str::FromStr;
use validator::{Validate, ValidationError};

use crate::{
    Amount, ExchangeId, OrderId, OrderSide, Price, Quantity, SigmaXResult,
    traits::{DatabaseConnection, DatabaseOperations}
};

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Trade {
    pub id: Uuid,
    pub order_id: OrderId,
    pub exchange_id: ExchangeId,
    #[validate(custom = "validate_trading_pair")]
    pub trading_pair: crate::TradingPair,
    pub side: OrderSide,
    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,
    #[validate(custom = "validate_positive_price")]
    pub price: Price,
    #[validate(custom = "validate_non_negative_amount")]
    pub fee: Amount,
    pub fee_asset: Option<String>,
    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

impl Trade {
    /// 执行完整的交易记录验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 然后执行跨字段验证
        // 验证执行时间不能晚于创建时间
        if self.executed_at > self.created_at {
            return Err(crate::SigmaXError::ValidationError(
                "Executed time cannot be later than created time".to_string()
            ));
        }

        Ok(())
    }

    /// 创建一个新的成交记录.
    pub fn new(
        order_id: OrderId,
        exchange_id: ExchangeId,
        trading_pair: crate::TradingPair,
        side: OrderSide,
        quantity: Quantity,
        price: Price,
        fee: Amount,
        fee_asset: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            order_id,
            exchange_id,
            trading_pair,
            side,
            quantity,
            price,
            fee,
            fee_asset,
            executed_at: now,
            created_at: now,
        }
    }

    /// 计算交易总价值（不含手续费）.
    pub fn total_value(&self) -> Amount {
        self.quantity * self.price
    }

    /// 计算净价值（扣除手续费后）.
    /// 注意：此计算假设手续费资产与计价资产相同。
    /// 在实际应用中可能需要更复杂的转换逻辑。
    pub fn net_value(&self) -> Amount {
        self.total_value() - self.fee
    }
}

impl Default for Trade {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            order_id: Uuid::new_v4(),
            exchange_id: "binance".to_string().into(),
            trading_pair: crate::TradingPair::new("BTC".to_string(), "USDT".to_string()),
            side: OrderSide::Buy,
            quantity: rust_decimal::Decimal::new(1, 3), // 0.001
            price: rust_decimal::Decimal::new(50000, 0), // 50000
            fee: rust_decimal::Decimal::new(5, 1), // 0.5
            fee_asset: Some("USDT".to_string()),
            executed_at: now,
            created_at: now,
        }
    }
}

#[async_trait::async_trait]
impl DatabaseOperations<Trade> for Trade {
    /// 将成交记录保存到数据库.
    async fn save(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        let query = r#"
            INSERT INTO trades (
                id, order_id, exchange_id, trading_pair_base, trading_pair_quote,
                side, quantity, price, fee, fee_asset, executed_at, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        "#;

        let exchange_id_str = self.exchange_id.to_string();
        let side_str = format!("{:?}", self.side);
        let quantity_str = self.quantity.to_string();
        let price_str = self.price.to_string();
        let fee_str = self.fee.to_string();
        let fee_asset_str = self.fee_asset.as_deref().unwrap_or("NULL").to_string();
        let executed_at_str = self.executed_at.to_rfc3339();
        let created_at_str = self.created_at.to_rfc3339();

        let params = vec![
            self.id.to_string(),
            self.order_id.to_string(),
            exchange_id_str,
            self.trading_pair.base.clone(),
            self.trading_pair.quote.clone(),
            side_str,
            quantity_str,
            price_str,
            fee_str,
            fee_asset_str,
            executed_at_str,
            created_at_str,
        ];

        db.execute_query(query, &params).await?;
        Ok(())
    }

    /// 从数据库加载成交记录.
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植了更健壮的错误处理和数据解析逻辑
    async fn load(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<Option<Trade>> {
    let query = r#"
        SELECT id, order_id, exchange_id, trading_pair_base, trading_pair_quote,
               side, quantity, price, fee, fee_asset, executed_at, created_at
        FROM trades WHERE id = $1
    "#;
    let params = vec![id.to_string()];

    if let Some(row) = db.query_one(query, &params).await? {
        // 🔥 移植说明：使用 models_old.rs 中更健壮的解析逻辑
        let trade = Trade {
            id: Uuid::parse_str(row.get("id").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid UUID: {}", e)))?,
            order_id: Uuid::parse_str(row.get("order_id").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid order_id: {}", e)))?,
            exchange_id: ExchangeId::from(row.get("exchange_id").unwrap().as_str().unwrap()),
            trading_pair: crate::TradingPair::new(
                row.get("trading_pair_base").unwrap().as_str().unwrap().to_string(),
                row.get("trading_pair_quote").unwrap().as_str().unwrap().to_string(),
            ),
            side: match row.get("side").unwrap().as_str().unwrap() {
                "buy" => OrderSide::Buy,
                "sell" => OrderSide::Sell,
                _ => return Err(crate::SigmaXError::InvalidParameter("Invalid trade side".to_string())),
            },
            quantity: rust_decimal::Decimal::from_str(row.get("quantity").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid quantity: {}", e)))?,
            price: rust_decimal::Decimal::from_str(row.get("price").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid price: {}", e)))?,
            fee: rust_decimal::Decimal::from_str(row.get("fee").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid fee: {}", e)))?,
            fee_asset: row.get("fee_asset").and_then(|v| v.as_str()).map(|s| s.to_string()),
            executed_at: chrono::DateTime::parse_from_rfc3339(row.get("executed_at").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid executed_at: {}", e)))?
                .with_timezone(&chrono::Utc),
            created_at: chrono::DateTime::parse_from_rfc3339(row.get("created_at").unwrap().as_str().unwrap())
                .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid created_at: {}", e)))?
                .with_timezone(&chrono::Utc),
        };
        Ok(Some(trade))
    } else {
        Ok(None)
    }
}

    /// 更新成交记录（通常不被允许）.
    async fn update(&self, _db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        Err(crate::SigmaXError::InvalidOperation("Trade records are immutable and cannot be updated.".to_string()))
    }

    /// 从数据库删除成交记录.
    async fn delete(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        let query = "DELETE FROM trades WHERE id = $1";
        let params = vec![id.to_string()];
        db.execute_query(query, &params).await?;
        Ok(())
    }
}



// ============================================================================
// 自定义验证函数 (用于validator derive宏)
// ============================================================================

/// 验证交易对
fn validate_trading_pair(trading_pair: &crate::TradingPair) -> Result<(), ValidationError> {
    if trading_pair.base.is_empty() || trading_pair.quote.is_empty() {
        return Err(ValidationError::new("Trading pair base and quote cannot be empty"));
    }
    Ok(())
}

/// 验证数量为正数
fn validate_positive_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("Trade quantity must be positive"));
    }
    Ok(())
}

/// 验证价格为正数
fn validate_positive_price(price: &Price) -> Result<(), ValidationError> {
    if *price <= Price::ZERO {
        return Err(ValidationError::new("Trade price must be positive"));
    }
    Ok(())
}

/// 验证金额非负数
fn validate_non_negative_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount < Amount::ZERO {
        return Err(ValidationError::new("Trade fee cannot be negative"));
    }
    Ok(())
}