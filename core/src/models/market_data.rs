//! 市场数据相关模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::{Price, Quantity, TradingPair};

/// K线数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Candle {
    pub timestamp: DateTime<Utc>,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
}

/// 市场深度数据
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OrderBook {
    pub timestamp: DateTime<Utc>,
    pub bids: Vec<(Price, Quantity)>, // (价格, 数量)
    pub asks: Vec<(Price, Quantity)>, // (价格, 数量)
}

/// 数据提供者类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum DataProviderType {
    Binance,
    Coinbase,
    Kraken,
    Backtest,
}

/// 数据提供者配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DataProviderConfig {
    pub name: String,
    pub provider_type: DataProviderType,
    pub api_endpoint: Option<String>,
    pub websocket_endpoint: Option<String>,
    pub api_key: Option<String>,
    pub api_secret: Option<String>,
    pub rate_limit_per_minute: u32,
    pub timeout_seconds: u64,
    pub retry_attempts: u32,
    pub cache_ttl_seconds: u64,
    pub enable_websocket: bool,
    pub supported_pairs: Vec<TradingPair>,
}

impl Default for DataProviderConfig {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            provider_type: DataProviderType::Backtest, // 默认使用回测模式，避免mock
            api_endpoint: None,
            websocket_endpoint: None,
            api_key: None,
            api_secret: None,
            rate_limit_per_minute: 1200,
            timeout_seconds: 30,
            retry_attempts: 3,
            cache_ttl_seconds: 5,
            enable_websocket: false,
            supported_pairs: vec![],
        }
    }
}

/// 数据质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataQuality {
    pub timestamp: DateTime<Utc>,
    pub provider: String,
    pub trading_pair: TradingPair,
    pub latency_ms: u64,
    pub accuracy_score: f64,
    pub completeness_score: f64,
    pub freshness_score: f64,
    pub overall_score: f64,
}

/// 价格流数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceStream {
    pub trading_pair: TradingPair,
    pub price: Price,
    pub volume: Quantity,
    pub timestamp: DateTime<Utc>,
    pub source: String,
}

/// 聚合价格数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedPrice {
    pub trading_pair: TradingPair,
    pub best_bid: Price,
    pub best_ask: Price,
    pub mid_price: Price,
    pub spread: Price,
    pub volume_weighted_price: Price,
    pub sources: Vec<String>,
    pub timestamp: DateTime<Utc>,
    pub quality: DataQuality,
}