//! SigmaX 数据验证模块
//!
//! 提供声明式验证和自定义验证器
//!
//! 🔥 注意：此模块中的旧验证函数将被弃用，所有验证逻辑将迁移到validator derive宏

pub mod unified;

// 重新导出统一验证接口
pub use unified::{UnifiedValidate, ValidationUtils, ValidatorComposer, CustomValidator};

use validator::ValidationError;
use crate::{TradingPair, OrderType, OrderSide, Price, Quantity, StrategyId};

/// 验证策略ID格式
pub fn validate_strategy_id(strategy_id: &Option<StrategyId>) -> Result<(), ValidationError> {
    if let Some(id) = strategy_id {
        if id.to_string().is_empty() {
            return Err(ValidationError::new("strategy_id_empty"));
        }
    }
    Ok(())
}

/// 验证交易对格式
pub fn validate_trading_pair(trading_pair: &TradingPair) -> Result<(), ValidationError> {
    if trading_pair.base.is_empty() {
        return Err(ValidationError::new("trading_pair_base_empty"));
    }
    
    if trading_pair.quote.is_empty() {
        return Err(ValidationError::new("trading_pair_quote_empty"));
    }
    
    // 验证交易对格式（基础资产和计价资产不能相同）
    if trading_pair.base == trading_pair.quote {
        return Err(ValidationError::new("trading_pair_same_assets"));
    }
    
    Ok(())
}

/// 验证订单类型与价格的匹配性
pub fn validate_order_type_price(order_type: &OrderType, price: &Option<Price>) -> Result<(), ValidationError> {
    match order_type {
        OrderType::Market => {
            if price.is_some() {
                return Err(ValidationError::new("market_order_should_not_have_price"));
            }
        }
        OrderType::Limit => {
            if price.is_none() {
                return Err(ValidationError::new("limit_order_must_have_price"));
            }
            if let Some(p) = price {
                if *p <= Price::ZERO {
                    return Err(ValidationError::new("limit_order_price_must_be_positive"));
                }
            }
        }
        OrderType::StopLoss => {
            if price.is_none() {
                return Err(ValidationError::new("stop_loss_order_must_have_price"));
            }
        }
        OrderType::StopLimit => {
            if price.is_none() {
                return Err(ValidationError::new("stop_limit_order_must_have_price"));
            }
        }
    }
    Ok(())
}

/// 验证数量的有效性
pub fn validate_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("quantity_must_be_positive"));
    }
    
    // 验证精度（最多8位小数）
    let decimal_places = quantity.scale();
    if decimal_places > 8 {
        return Err(ValidationError::new("quantity_too_many_decimal_places"));
    }
    
    Ok(())
}

/// 验证价格的有效性
pub fn validate_price(price: &Option<Price>) -> Result<(), ValidationError> {
    if let Some(p) = price {
        if *p <= Price::ZERO {
            return Err(ValidationError::new("price_must_be_positive"));
        }
        
        // 验证精度（最多8位小数）
        let decimal_places = p.scale();
        if decimal_places > 8 {
            return Err(ValidationError::new("price_too_many_decimal_places"));
        }
    }
    Ok(())
}

/// 验证订单方向
pub fn validate_order_side(side: &OrderSide) -> Result<(), ValidationError> {
    // 目前只是简单验证，未来可以添加更复杂的业务规则
    match side {
        OrderSide::Buy | OrderSide::Sell => Ok(()),
    }
}

/// 验证已成交数量不超过订单总数量
pub fn validate_filled_quantity(quantity: &Quantity, filled_quantity: &Quantity) -> Result<(), ValidationError> {
    if *filled_quantity > *quantity {
        return Err(ValidationError::new("filled_quantity_exceeds_total"));
    }
    Ok(())
}

/// 验证平均价格的合理性
pub fn validate_average_price(
    filled_quantity: &Quantity, 
    average_price: &Option<Price>
) -> Result<(), ValidationError> {
    if *filled_quantity > Quantity::ZERO && average_price.is_none() {
        return Err(ValidationError::new("filled_order_must_have_average_price"));
    }
    
    if *filled_quantity == Quantity::ZERO && average_price.is_some() {
        return Err(ValidationError::new("unfilled_order_should_not_have_average_price"));
    }
    
    Ok(())
}

/// 自定义验证错误消息
pub fn get_validation_message(error_code: &str) -> &'static str {
    match error_code {
        "strategy_id_empty" => "策略ID不能为空",
        "trading_pair_base_empty" => "交易对基础资产不能为空",
        "trading_pair_quote_empty" => "交易对计价资产不能为空",
        "trading_pair_same_assets" => "交易对的基础资产和计价资产不能相同",
        "market_order_should_not_have_price" => "市价单不应该设置价格",
        "limit_order_must_have_price" => "限价单必须设置价格",
        "limit_order_price_must_be_positive" => "限价单价格必须大于0",
        "stop_loss_order_must_have_price" => "止损单必须设置价格",
        "stop_limit_order_must_have_price" => "止损限价单必须设置价格",
        "quantity_must_be_positive" => "数量必须大于0",
        "quantity_too_many_decimal_places" => "数量小数位数不能超过8位",
        "price_must_be_positive" => "价格必须大于0",
        "price_too_many_decimal_places" => "价格小数位数不能超过8位",
        "filled_quantity_exceeds_total" => "已成交数量不能超过订单总数量",
        "filled_order_must_have_average_price" => "已成交订单必须有平均价格",
        "unfilled_order_should_not_have_average_price" => "未成交订单不应该有平均价格",
        _ => "未知验证错误",
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{TradingPair, OrderType, Price, Quantity};

    use std::str::FromStr;

    #[test]
    fn test_validate_trading_pair() {
        // 有效的交易对
        let valid_pair = TradingPair::new("BTC".to_string(), "USDT".to_string());
        assert!(validate_trading_pair(&valid_pair).is_ok());

        // 无效的交易对：基础资产为空
        let invalid_pair = TradingPair::new("".to_string(), "USDT".to_string());
        assert!(validate_trading_pair(&invalid_pair).is_err());

        // 无效的交易对：相同资产
        let same_assets_pair = TradingPair::new("BTC".to_string(), "BTC".to_string());
        assert!(validate_trading_pair(&same_assets_pair).is_err());
    }

    #[test]
    fn test_validate_order_type_price() {
        // 市价单不应该有价格
        assert!(validate_order_type_price(&OrderType::Market, &None).is_ok());
        assert!(validate_order_type_price(&OrderType::Market, &Some(Price::from_str("100.0").unwrap())).is_err());

        // 限价单必须有价格
        assert!(validate_order_type_price(&OrderType::Limit, &Some(Price::from_str("100.0").unwrap())).is_ok());
        assert!(validate_order_type_price(&OrderType::Limit, &None).is_err());
    }

    #[test]
    fn test_validate_quantity() {
        // 有效数量
        let valid_quantity = Quantity::from_str("1.5").unwrap();
        assert!(validate_quantity(&valid_quantity).is_ok());

        // 无效数量：零
        let zero_quantity = Quantity::ZERO;
        assert!(validate_quantity(&zero_quantity).is_err());

        // 无效数量：负数
        let negative_quantity = Quantity::from_str("-1.0").unwrap();
        assert!(validate_quantity(&negative_quantity).is_err());
    }

    #[test]
    fn test_validate_filled_quantity() {
        let total_quantity = Quantity::from_str("10.0").unwrap();
        let valid_filled = Quantity::from_str("5.0").unwrap();
        let invalid_filled = Quantity::from_str("15.0").unwrap();

        assert!(validate_filled_quantity(&total_quantity, &valid_filled).is_ok());
        assert!(validate_filled_quantity(&total_quantity, &invalid_filled).is_err());
    }
}
