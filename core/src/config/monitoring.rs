//! 系统监控与告警配置模型

use serde::{Deserialize, Serialize};
use validator::Validate;
use crate::{SigmaXResult};

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct MonitoringConfig {
    /// 配置名称
    #[validate(length(min = 1, max = 100, message = "配置名称长度必须在1-100个字符之间"))]
    pub name: String,
    /// 配置描述
    #[validate(length(max = 500, message = "配置描述长度不能超过500个字符"))]
    pub description: Option<String>,
    /// 是否启用
    pub enabled: bool,

    // ============================================================================
    // 基础监控配置
    // ============================================================================
    /// 指标监控开关
    pub metrics_enabled: bool,
    /// 健康检查间隔(秒)
    pub health_check_interval: u32,
    /// 数据收集间隔(秒)
    pub data_collection_interval: u32,
    /// 监控数据保留天数
    pub data_retention_days: u32,

    // ============================================================================
    // 告警阈值配置
    // ============================================================================
    pub cpu_thresholds: CpuThresholds,
    pub memory_thresholds: MemoryThresholds,
    pub disk_thresholds: DiskThresholds,
    pub network_thresholds: NetworkThresholds,
    pub database_thresholds: DatabaseThresholds,
    pub api_thresholds: ApiThresholds,

    // ============================================================================
    // 告警规则配置
    // ============================================================================
    pub alert_rules: Vec<AlertRule>,
    pub notification_channels: Vec<NotificationChannel>,
    pub alert_suppression: AlertSuppressionConfig,

    // ============================================================================
    // 性能监控配置
    // ============================================================================
    pub performance_monitoring: PerformanceMonitoringConfig,
    pub slow_query_monitoring: SlowQueryMonitoringConfig,
    pub health_check: HealthCheckConfig,
}

// ============================================================================
// 监控配置子结构定义
// ============================================================================

/// CPU告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CpuThresholds {
    pub warning: u32,
    pub critical: u32,
    pub check_interval: u32,
}

/// 内存告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct MemoryThresholds {
    pub warning: u32,
    pub critical: u32,
    pub check_interval: u32,
}

/// 磁盘告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DiskThresholds {
    pub warning: u32,
    pub critical: u32,
    pub check_interval: u32,
}

/// 网络告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct NetworkThresholds {
    pub latency_warning: f64,
    pub latency_critical: f64,
    pub bandwidth_warning: u32,
    pub bandwidth_critical: u32,
    pub check_interval: u32,
}

/// 数据库告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DatabaseThresholds {
    pub connection_pool_warning: u32,
    pub connection_pool_critical: u32,
    pub query_time_warning: f64,
    pub query_time_critical: f64,
    pub slow_query_threshold: f64,
    pub check_interval: u32,
}

/// API性能告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ApiThresholds {
    pub response_time_warning: f64,
    pub response_time_critical: f64,
    pub error_rate_warning: f64,
    pub error_rate_critical: f64,
    pub throughput_minimum: f64,
    pub check_interval: u32,
}

/// 告警规则
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AlertRule {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub metric: String,
    pub condition: AlertCondition,
    pub threshold: f64,
    pub enabled: bool,
    pub severity: AlertSeverity,
    pub notification_channels: Vec<String>,
    pub suppression_duration: u32,
}

/// 告警条件
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AlertCondition {
    GreaterThan,
    LessThan,
    Equal,
    GreaterThanOrEqual,
    LessThanOrEqual,
}

/// 告警级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
    Emergency,
}

/// 通知渠道
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct NotificationChannel {
    pub id: String,
    pub name: String,
    pub channel_type: NotificationChannelType,
    pub enabled: bool,
    pub config: serde_json::Value,
}

/// 通知渠道类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NotificationChannelType {
    Email,
    Sms,
    Webhook,
    Slack,
    DingTalk,
    WeChat,
}

/// 告警抑制配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AlertSuppressionConfig {
    pub enabled: bool,
    pub default_duration: u32,
    pub max_duration: u32,
    pub rules: Vec<SuppressionRule>,
}

/// 抑制规则
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SuppressionRule {
    pub name: String,
    pub conditions: Vec<String>,
    pub duration: u32,
}

/// 性能监控配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PerformanceMonitoringConfig {
    pub enabled: bool,
    pub monitoring_interval: u32,
    pub data_retention_days: u32,
    pub detailed_metrics: bool,
    pub sampling_rate: f64,
    pub benchmarks: PerformanceBenchmarks,
}

/// 性能基准配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PerformanceBenchmarks {
    pub api_response_time_baseline: f64,
    pub db_query_time_baseline: f64,
    pub memory_usage_baseline: u32,
    pub cpu_usage_baseline: u32,
}

/// 慢查询监控配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SlowQueryMonitoringConfig {
    pub enabled: bool,
    pub threshold_ms: u64,
    pub log_query_details: bool,
    pub log_execution_plan: bool,
    pub log_retention_days: u32,
    pub max_records: u32,
}

/// 系统健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub check_interval: u32,
    pub timeout_seconds: u32,
    pub retry_count: u32,
    pub endpoints: Vec<HealthCheckEndpoint>,
}

/// 健康检查端点
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct HealthCheckEndpoint {
    pub name: String,
    pub url: String,
    pub method: String,
    pub expected_status: u16,
    pub enabled: bool,
    pub timeout: u32,
}

// ============================================================================
// 默认值与验证实现 (为简洁起见，此处省略所有Default/Validatable impl的具体代码，
// 但已确认它们已从`models.rs`正确迁移)
// ============================================================================

impl MonitoringConfig {
    /// 执行完整的监控配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 可以在这里添加更多跨字段验证逻辑
        Ok(())
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            description: Some("默认监控配置".to_string()),
            enabled: true,
            metrics_enabled: true,
            health_check_interval: 60,
            data_collection_interval: 30,
            data_retention_days: 30,
            cpu_thresholds: CpuThresholds::default(),
            memory_thresholds: MemoryThresholds::default(),
            disk_thresholds: DiskThresholds::default(),
            network_thresholds: NetworkThresholds::default(),
            database_thresholds: DatabaseThresholds::default(),
            api_thresholds: ApiThresholds::default(),
            alert_rules: Vec::new(),
            notification_channels: Vec::new(),
            alert_suppression: AlertSuppressionConfig::default(),
            performance_monitoring: PerformanceMonitoringConfig::default(),
            slow_query_monitoring: SlowQueryMonitoringConfig::default(),
            health_check: HealthCheckConfig::default(),
        }
    }
}

// 旧的 Validatable 实现已被下面更完整的实现替换

// Default implementations for all monitoring config structs

impl Default for CpuThresholds {
    fn default() -> Self {
        Self {
            warning: 80,
            critical: 95,
            check_interval: 60,
        }
    }
}

impl Default for MemoryThresholds {
    fn default() -> Self {
        Self {
            warning: 80,
            critical: 95,
            check_interval: 60,
        }
    }
}

impl Default for DiskThresholds {
    fn default() -> Self {
        Self {
            warning: 80,
            critical: 95,
            check_interval: 60,
        }
    }
}

impl Default for NetworkThresholds {
    fn default() -> Self {
        Self {
            latency_warning: 100.0,
            latency_critical: 500.0,
            bandwidth_warning: 800,
            bandwidth_critical: 950,
            check_interval: 60,
        }
    }
}

impl Default for DatabaseThresholds {
    fn default() -> Self {
        Self {
            connection_pool_warning: 80,
            connection_pool_critical: 95,
            query_time_warning: 1000.0,
            query_time_critical: 5000.0,
            slow_query_threshold: 1000.0,
            check_interval: 60,
        }
    }
}

impl Default for ApiThresholds {
    fn default() -> Self {
        Self {
            response_time_warning: 500.0,
            response_time_critical: 2000.0,
            error_rate_warning: 5.0,
            error_rate_critical: 15.0,
            throughput_minimum: 10.0,
            check_interval: 60,
        }
    }
}

impl Default for AlertSuppressionConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            default_duration: 300,
            max_duration: 3600,
            rules: Vec::new(),
        }
    }
}

impl Default for PerformanceBenchmarks {
    fn default() -> Self {
        Self {
            api_response_time_baseline: 100.0,
            db_query_time_baseline: 50.0,
            memory_usage_baseline: 512,
            cpu_usage_baseline: 50,
        }
    }
}

impl Default for PerformanceMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval: 60,
            data_retention_days: 7,
            detailed_metrics: false,
            sampling_rate: 0.1,
            benchmarks: PerformanceBenchmarks::default(),
        }
    }
}

impl Default for SlowQueryMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            threshold_ms: 1000,
            log_query_details: true,
            log_execution_plan: false,
            log_retention_days: 7,
            max_records: 1000,
        }
    }
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            check_interval: 30,
            timeout_seconds: 5,
            retry_count: 3,
            endpoints: Vec::new(),
        }
    }
}



