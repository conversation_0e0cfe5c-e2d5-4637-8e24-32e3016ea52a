//! 系统配置模型
//!
//! 本模块包含了系统级别的配置结构，从 models_old.rs 中移植而来。
//! 主要包括数据库映射的系统配置记录和相关的系统级配置。

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::{SigmaXResult, SigmaXError, traits::Validatable};

// ============================================================================
// 系统配置记录 - 数据库映射
// ============================================================================

/// 系统配置记录（数据库映射）
///
/// 对应数据库中的system_config表的原始记录
/// 
/// # 移植说明
/// 从 models_old.rs 移植，保持与数据库表结构的一致性。
/// 这是系统配置的基础数据结构，用于存储键值对形式的配置。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SystemConfigRecord {
    /// 配置ID
    pub id: i32,
    /// 配置键名（唯一）
    pub key: String,
    /// 配置值（JSON格式）
    pub value: serde_json::Value,
    /// 配置描述
    pub description: Option<String>,
    /// 是否加密存储
    pub is_encrypted: bool,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

// ============================================================================
// 系统配置记录实现
// ============================================================================

impl SystemConfigRecord {
    /// 创建新的系统配置记录
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供便捷的配置记录创建方法
    pub fn new(
        key: impl Into<String>,
        value: serde_json::Value,
        description: Option<String>,
        is_encrypted: bool,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库自动生成
            key: key.into(),
            value,
            description,
            is_encrypted,
            created_at: now,
            updated_at: now,
        }
    }

    /// 创建字符串值配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，简化字符串配置的创建
    pub fn new_string(
        key: impl Into<String>,
        value: impl Into<String>,
        description: Option<String>,
    ) -> Self {
        Self::new(
            key,
            serde_json::Value::String(value.into()),
            description,
            false,
        )
    }

    /// 创建数字值配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，简化数字配置的创建
    pub fn new_number(
        key: impl Into<String>,
        value: f64,
        description: Option<String>,
    ) -> Self {
        Self::new(
            key,
            serde_json::Value::Number(serde_json::Number::from_f64(value).unwrap()),
            description,
            false,
        )
    }

    /// 创建布尔值配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，简化布尔配置的创建
    pub fn new_bool(
        key: impl Into<String>,
        value: bool,
        description: Option<String>,
    ) -> Self {
        Self::new(
            key,
            serde_json::Value::Bool(value),
            description,
            false,
        )
    }

    /// 创建JSON对象配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，支持复杂对象的配置存储
    pub fn new_object<T: Serialize>(
        key: impl Into<String>,
        value: &T,
        description: Option<String>,
    ) -> SigmaXResult<Self> {
        let json_value = serde_json::to_value(value)
            .map_err(|e| SigmaXError::Config(format!("Failed to serialize config value: {}", e)))?;
        
        Ok(Self::new(key, json_value, description, false))
    }

    /// 获取字符串值
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供类型安全的值获取方法
    pub fn get_string(&self) -> Option<String> {
        self.value.as_str().map(|s| s.to_string())
    }

    /// 获取数字值
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供类型安全的值获取方法
    pub fn get_number(&self) -> Option<f64> {
        self.value.as_f64()
    }

    /// 获取布尔值
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供类型安全的值获取方法
    pub fn get_bool(&self) -> Option<bool> {
        self.value.as_bool()
    }

    /// 获取JSON对象
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，支持复杂对象的反序列化
    pub fn get_object<T: for<'de> Deserialize<'de>>(&self) -> SigmaXResult<T> {
        serde_json::from_value(self.value.clone())
            .map_err(|e| SigmaXError::Config(format!("Failed to deserialize config value: {}", e)))
    }

    /// 更新配置值
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供配置值的更新方法
    pub fn update_value(&mut self, value: serde_json::Value) {
        self.value = value;
        self.updated_at = Utc::now();
    }

    /// 更新描述
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供描述的更新方法
    pub fn update_description(&mut self, description: Option<String>) {
        self.description = description;
        self.updated_at = Utc::now();
    }
}

// ============================================================================
// 验证逻辑实现
// ============================================================================

/// 系统配置记录的验证逻辑
///
/// # 移植说明
/// 从 models_old.rs 移植，确保配置记录的有效性
impl Validatable for SystemConfigRecord {
    fn validate(&self) -> SigmaXResult<()> {
        let errors = self.validation_errors();
        if errors.is_empty() {
            Ok(())
        } else {
            Err(SigmaXError::InvalidParameter(format!(
                "SystemConfigRecord validation failed: {}",
                errors.join(", ")
            )))
        }
    }

    fn validation_errors(&self) -> Vec<String> {
        let mut errors = Vec::new();

        // 验证键名
        if self.key.is_empty() {
            errors.push("配置键名不能为空".to_string());
        }

        if self.key.len() > 255 {
            errors.push("配置键名长度不能超过255个字符".to_string());
        }

        // 验证键名格式（只允许字母、数字、下划线、点号）
        if !self.key.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '.') {
            errors.push("配置键名只能包含字母、数字、下划线和点号".to_string());
        }

        // 验证描述长度
        if let Some(ref desc) = self.description {
            if desc.len() > 1000 {
                errors.push("配置描述长度不能超过1000个字符".to_string());
            }
        }

        // 验证时间逻辑
        if self.updated_at < self.created_at {
            errors.push("更新时间不能早于创建时间".to_string());
        }

        errors
    }
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for SystemConfigRecord {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: 0,
            key: "default".to_string(),
            value: serde_json::Value::Null,
            description: None,
            is_encrypted: false,
            created_at: now,
            updated_at: now,
        }
    }
}

impl SystemConfigRecord {
    /// 获取配置值作为指定类型
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供类型安全的配置值获取
    pub fn get_value<T>(&self) -> crate::SigmaXResult<T>
    where
        T: for<'de> serde::Deserialize<'de>,
    {
        serde_json::from_value(self.value.clone())
            .map_err(|e| crate::SigmaXError::Config(format!("Failed to deserialize config value for key '{}': {}", self.key, e)))
    }

    /// 设置配置值
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供类型安全的配置值设置
    pub fn set_value<T>(&mut self, value: T) -> crate::SigmaXResult<()>
    where
        T: serde::Serialize,
    {
        self.value = serde_json::to_value(value)
            .map_err(|e| crate::SigmaXError::Config(format!("Failed to serialize config value for key '{}': {}", self.key, e)))?;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查配置键是否属于指定的命名空间
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供命名空间检查功能
    pub fn is_in_namespace(&self, namespace: &str) -> bool {
        self.key.starts_with(&format!("{}.", namespace))
    }

    /// 获取配置键的命名空间部分
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供命名空间提取功能
    pub fn get_namespace(&self) -> Option<String> {
        self.key.split('.').next().map(|s| s.to_string())
    }

    /// 获取配置键的名称部分（去除命名空间）
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供键名提取功能
    pub fn get_key_name(&self) -> String {
        self.key.split('.').skip(1).collect::<Vec<_>>().join(".")
    }
}

// ============================================================================
// 系统级配置结构 - 从 models_old.rs 移植
// ============================================================================

/// 系统通用配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了系统级别的通用配置参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SystemGeneralConfig {
    /// 系统维护模式
    pub maintenance_mode: bool,
    /// 最大并发策略数
    pub max_concurrent_strategies: u32,
    /// 数据保留天数
    pub data_retention_days: u32,
    /// 备份开关
    pub backup_enabled: bool,
    /// 日志级别
    pub log_level: String,
}

/// 通知配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了系统通知相关的配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct NotificationConfig {
    /// 邮件通知开关
    pub email_enabled: bool,
    /// Webhook通知URL
    pub webhook_url: Option<String>,
    /// Slack Webhook URL
    pub slack_webhook: Option<String>,
    /// Telegram Bot Token
    pub telegram_bot_token: Option<String>,
    /// Telegram Chat ID
    pub telegram_chat_id: Option<String>,
}

/// API配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了API服务相关的配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ApiConfig {
    /// API每分钟请求限制
    pub rate_limit_per_minute: u32,
    /// API最大请求大小(字节)
    pub max_request_size: u64,
    /// API超时时间(秒)
    pub timeout_seconds: u32,
    /// CORS开关
    pub cors_enabled: bool,
}

/// 数据库系统配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了数据库系统级别的配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DatabaseSystemConfig {
    /// 数据库连接池大小
    pub connection_pool_size: u32,
    /// 数据库查询超时时间
    pub query_timeout_seconds: u32,
    /// 自动清理开关
    pub auto_vacuum_enabled: bool,
}

/// 策略系统配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了策略系统级别的配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct StrategySystemConfig {
    /// 默认网格层数
    pub default_grid_levels: u32,
    /// 默认网格间距百分比
    pub default_grid_spacing: f64,
    /// 最大活跃订单数
    pub max_active_orders: u32,
    /// 重平衡间隔(秒)
    pub rebalance_interval: u32,
}

/// 交易所系统配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了交易所系统级别的配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExchangeSystemConfig {
    /// 手续费配置
    pub fees: ExchangeFees,
    /// 限流配置
    pub rate_limits: ExchangeRateLimits,
    /// API端点
    pub api_endpoint: String,
    /// 最小名义价值
    pub min_notional: rust_decimal::Decimal,
    /// WebSocket端点
    pub websocket_endpoint: String,
    /// 支持的订单类型
    pub supported_order_types: Vec<String>,
    /// 是否为模拟模式
    pub simulation_mode: Option<bool>,
}

/// 交易所手续费
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了交易所手续费结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExchangeFees {
    /// Maker手续费
    pub maker: f64,
    /// Taker手续费
    pub taker: f64,
}

/// 交易所限流配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了交易所限流配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExchangeRateLimits {
    /// 每秒订单数限制
    pub orders_per_second: u32,
    /// 每分钟权重限制
    pub weight_per_minute: Option<u32>,
    /// 每分钟请求数限制
    pub requests_per_minute: u32,
}

// ============================================================================
// 配置查询和批量操作工具 - 从 models_old.rs 移植
// ============================================================================

/// 系统配置查询过滤器
///
/// # 移植说明
/// 从 models_old.rs 移植，提供灵活的配置查询过滤功能
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct SystemConfigFilter {
    /// 按键名过滤（支持模糊匹配）
    pub key_pattern: Option<String>,
    /// 按命名空间过滤
    pub namespace: Option<String>,
    /// 按是否加密过滤
    pub is_encrypted: Option<bool>,
    /// 按创建时间范围过滤
    pub created_after: Option<DateTime<Utc>>,
    /// 按创建时间范围过滤
    pub created_before: Option<DateTime<Utc>>,
    /// 按更新时间范围过滤
    pub updated_after: Option<DateTime<Utc>>,
    /// 按更新时间范围过滤
    pub updated_before: Option<DateTime<Utc>>,
}

impl SystemConfigFilter {
    /// 创建新的过滤器
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置键名模式过滤
    pub fn with_key_pattern(mut self, pattern: impl Into<String>) -> Self {
        self.key_pattern = Some(pattern.into());
        self
    }

    /// 设置命名空间过滤
    pub fn with_namespace(mut self, namespace: impl Into<String>) -> Self {
        self.namespace = Some(namespace.into());
        self
    }

    /// 设置加密状态过滤
    pub fn with_encryption(mut self, is_encrypted: bool) -> Self {
        self.is_encrypted = Some(is_encrypted);
        self
    }

    /// 设置创建时间范围过滤
    pub fn with_created_range(mut self, after: Option<DateTime<Utc>>, before: Option<DateTime<Utc>>) -> Self {
        self.created_after = after;
        self.created_before = before;
        self
    }

    /// 设置更新时间范围过滤
    pub fn with_updated_range(mut self, after: Option<DateTime<Utc>>, before: Option<DateTime<Utc>>) -> Self {
        self.updated_after = after;
        self.updated_before = before;
        self
    }
}

/// 系统配置批量操作结果
///
/// # 移植说明
/// 从 models_old.rs 移植，提供批量操作的结果统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfigBatchResult {
    /// 成功处理的配置数量
    pub success_count: usize,
    /// 失败的配置数量
    pub failure_count: usize,
    /// 失败的配置键名和错误信息
    pub failures: Vec<(String, String)>,
    /// 操作总耗时（毫秒）
    pub duration_ms: u64,
}

impl SystemConfigBatchResult {
    /// 创建新的批量操作结果
    pub fn new() -> Self {
        Self {
            success_count: 0,
            failure_count: 0,
            failures: Vec::new(),
            duration_ms: 0,
        }
    }

    /// 添加成功记录
    pub fn add_success(&mut self) {
        self.success_count += 1;
    }

    /// 添加失败记录
    pub fn add_failure(&mut self, key: String, error: String) {
        self.failure_count += 1;
        self.failures.push((key, error));
    }

    /// 设置操作耗时
    pub fn set_duration(&mut self, duration_ms: u64) {
        self.duration_ms = duration_ms;
    }

    /// 检查是否有失败
    pub fn has_failures(&self) -> bool {
        self.failure_count > 0
    }

    /// 获取总处理数量
    pub fn total_count(&self) -> usize {
        self.success_count + self.failure_count
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_count() == 0 {
            0.0
        } else {
            self.success_count as f64 / self.total_count() as f64
        }
    }
}

impl Default for SystemConfigBatchResult {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 默认值实现 - 从 models_old.rs 移植
// ============================================================================

/// 系统通用配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的系统配置默认值
impl Default for SystemGeneralConfig {
    fn default() -> Self {
        Self {
            maintenance_mode: false,
            max_concurrent_strategies: 50,
            data_retention_days: 365,
            backup_enabled: true,
            log_level: "INFO".to_string(),
        }
    }
}

/// 通知配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的通知配置默认值
impl Default for NotificationConfig {
    fn default() -> Self {
        Self {
            email_enabled: true,
            webhook_url: None,
            slack_webhook: None,
            telegram_bot_token: None,
            telegram_chat_id: None,
        }
    }
}

/// API配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的API配置默认值
impl Default for ApiConfig {
    fn default() -> Self {
        Self {
            rate_limit_per_minute: 1000,
            max_request_size: 1048576, // 1MB
            timeout_seconds: 30,
            cors_enabled: true,
        }
    }
}

/// 数据库系统配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的数据库配置默认值
impl Default for DatabaseSystemConfig {
    fn default() -> Self {
        Self {
            connection_pool_size: 20,
            query_timeout_seconds: 30,
            auto_vacuum_enabled: true,
        }
    }
}

/// 策略系统配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的策略配置默认值
impl Default for StrategySystemConfig {
    fn default() -> Self {
        Self {
            default_grid_levels: 10,
            default_grid_spacing: 1.0,
            max_active_orders: 20,
            rebalance_interval: 300, // 5分钟
        }
    }
}

/// 交易所系统配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的交易所配置默认值
impl Default for ExchangeSystemConfig {
    fn default() -> Self {
        Self {
            fees: ExchangeFees::default(),
            rate_limits: ExchangeRateLimits::default(),
            api_endpoint: "https://api.example.com".to_string(),
            min_notional: rust_decimal::Decimal::new(10, 0), // 10 USDT
            websocket_endpoint: "wss://stream.example.com".to_string(),
            supported_order_types: vec![
                "market".to_string(),
                "limit".to_string(),
                "stop_loss".to_string(),
                "stop_limit".to_string(),
            ],
            simulation_mode: Some(true),
        }
    }
}

/// 交易所手续费的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的手续费默认值
impl Default for ExchangeFees {
    fn default() -> Self {
        Self {
            maker: 0.001, // 0.1%
            taker: 0.001, // 0.1%
        }
    }
}

/// 交易所限流配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的限流配置默认值
impl Default for ExchangeRateLimits {
    fn default() -> Self {
        Self {
            orders_per_second: 10,
            weight_per_minute: Some(1200),
            requests_per_minute: 1200,
        }
    }
}

// ============================================================================
// 策略和用户配置结构 - 从 models_old.rs 移植
// ============================================================================

/// 策略模板配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了策略模板的结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct StrategyTemplate {
    /// 策略类型
    pub strategy_type: String,
    /// 策略参数
    pub parameters: serde_json::Value,
    /// 风险管理参数
    pub risk_management: serde_json::Value,
}

impl Default for StrategyTemplate {
    fn default() -> Self {
        Self {
            strategy_type: "grid_trading".to_string(),
            parameters: serde_json::json!({
                "grid_levels": 10,
                "base_order_size": 100,
                "grid_spacing_percent": 1.0
            }),
            risk_management: serde_json::json!({
                "max_position_size": 10000,
                "stop_loss_percent": 5.0
            }),
        }
    }
}

/// 用户角色权限配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了用户角色的权限结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UserRoleConfig {
    /// 权限列表
    pub permissions: Vec<String>,
}

impl Default for UserRoleConfig {
    fn default() -> Self {
        Self {
            permissions: vec![
                "strategies.view".to_string(),
                "orders.view".to_string(),
                "trades.view".to_string(),
            ],
        }
    }
}
