//! 配置管理系统
//!
//! 定义了应用顶层配置结构，并聚合了所有子配置模块，
//! 如数据库、风险、缓存、监控等。

use serde::{Deserialize, Serialize};
use std::path::Path;
use crate::{SigmaXResult, SigmaXError};

// 🔥 从 models_old.rs 移植：声明所有配置子模块
pub mod cache;
pub mod monitoring;
pub mod risk;
pub mod system;    // 🔥 新增：系统配置模块
pub mod trading;   // 🔥 新增：交易配置模块

// 🔥 从 models_old.rs 移植：重新导出子模块中的内容，方便外部使用
pub use cache::*;
pub use monitoring::*;
pub use risk::*;
pub use system::*;    // 🔥 新增：导出系统配置（包括StrategyTemplate、UserRoleConfig）
pub use trading::*;   // 🔥 新增：导出交易配置


/// 应用程序配置
///
/// # 移植说明
/// 从 models_old.rs 移植，增加了交易配置、监控配置、缓存配置等新的配置模块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 交易所配置
    pub exchanges: ExchangeConfigs,
    /// 风险管理配置 (已更新为新的、更全面的结构)
    pub risk: RiskManagementConfig,
    /// 🔥 新增：交易配置（从 models_old.rs 移植）
    pub trading: TradingConfig,
    /// 🔥 新增：监控配置（从 models_old.rs 移植）
    pub monitoring: MonitoringConfig,
    /// 🔥 新增：缓存配置（从 models_old.rs 移植）
    pub cache: CacheConfig,
    /// Web服务配置
    pub web: WebConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 是否启用自动迁移
    pub auto_migrate: bool,
}

/// 交易所配置集合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExchangeConfigs {
    /// Binance配置
    pub binance: Option<ExchangeConfig>,
    /// Coinbase配置
    pub coinbase: Option<ExchangeConfig>,
    /// Kraken配置
    pub kraken: Option<ExchangeConfig>,
    /// 模拟交易所配置
    pub simulator: Option<SimulatorConfig>,
}

/// 交易所配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExchangeConfig {
    /// API密钥
    pub api_key: String,
    /// API密钥密码
    pub api_secret: String,
    /// 是否启用沙盒模式
    pub sandbox: bool,
    /// API请求超时时间（秒）
    pub timeout: u64,
    /// 请求限流配置
    pub rate_limit: RateLimitConfig,
}

/// 模拟交易所配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimulatorConfig {
    /// 初始余额
    pub initial_balance: rust_decimal::Decimal,
    /// 手续费率
    pub fee_rate: rust_decimal::Decimal,
    /// 滑点率
    pub slippage_rate: rust_decimal::Decimal,
}

/// 请求限流配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// 每秒最大请求数
    pub requests_per_second: u32,
    /// 突发请求数
    pub burst_size: u32,
}

// 注意：旧的、简单的 `RiskConfig` 结构体已被移除。

/// Web服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebConfig {
    /// 监听地址
    pub host: String,
    /// 监听端口
    pub port: u16,
    /// JWT密钥
    pub jwt_secret: String,
    /// 会话超时时间（秒）
    pub session_timeout: u64,
    /// 是否启用CORS
    pub enable_cors: bool,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志格式
    pub format: LogFormat,
    /// 日志输出
    pub output: LogOutput,
    /// 文件轮转配置
    pub file_rotation: Option<FileRotationConfig>,
}

/// 日志格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogFormat {
    Text,
    Json,
    Compact,
}

/// 日志输出
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogOutput {
    Console,
    File { path: String },
    Both { path: String },
}

/// 文件轮转配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileRotationConfig {
    pub max_size_mb: u64,
    pub max_files: u32,
    pub rotation_hours: u32,
}

impl AppConfig {
    /// 从配置文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> SigmaXResult<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| SigmaXError::Config(format!("Failed to read config file: {}", e)))?;

        let config: AppConfig = toml::from_str(&content)
            .map_err(|e| SigmaXError::Config(format!("Failed to parse config file: {}", e)))?;

        config.validate()?;
        Ok(config)
    }

    /// 从环境变量加载配置
    pub fn from_env() -> SigmaXResult<Self> {
        let config = AppConfig {
            database: DatabaseConfig {
                url: std::env::var("DATABASE_URL")
                    .map_err(|_| SigmaXError::Config("DATABASE_URL environment variable is required".to_string()))?,
                max_connections: std::env::var("DATABASE_MAX_CONNECTIONS")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .map_err(|e| SigmaXError::Config(format!("Invalid DATABASE_MAX_CONNECTIONS: {}", e)))?,
                connection_timeout: std::env::var("DATABASE_CONNECTION_TIMEOUT")
                    .unwrap_or_else(|_| "30".to_string())
                    .parse()
                    .map_err(|e| SigmaXError::Config(format!("Invalid DATABASE_CONNECTION_TIMEOUT: {}", e)))?,
                auto_migrate: std::env::var("DATABASE_AUTO_MIGRATE")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .map_err(|e| SigmaXError::Config(format!("Invalid DATABASE_AUTO_MIGRATE: {}", e)))?,
            },
            exchanges: ExchangeConfigs {
                binance: None,
                coinbase: None,
                kraken: None,
                simulator: Some(SimulatorConfig {
                    initial_balance: rust_decimal::Decimal::from(10000),
                    fee_rate: rust_decimal::Decimal::new(1, 3), // 0.001 = 0.1%
                    slippage_rate: rust_decimal::Decimal::new(5, 4), // 0.0005 = 0.05%
                }),
            },
            risk: RiskManagementConfig::default(),
            web: WebConfig {
                host: std::env::var("WEB_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
                port: std::env::var("WEB_PORT")
                    .unwrap_or_else(|_| "8080".to_string())
                    .parse()
                    .map_err(|e| SigmaXError::Config(format!("Invalid WEB_PORT: {}", e)))?,
                jwt_secret: std::env::var("JWT_SECRET")
                    .unwrap_or_else(|_| "default-secret-change-in-production".to_string()),
                session_timeout: 3600, // 1 hour
                enable_cors: true,
            },
            logging: LoggingConfig {
                level: std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
                format: LogFormat::Text,
                output: LogOutput::Console,
                file_rotation: None,
            },
            trading : TradingConfig::default(),
            monitoring: MonitoringConfig::default(),
            cache: CacheConfig::default(),
        };

        config.validate()?;
        Ok(config)
    }

    /// 验证配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，增加了对新配置模块的验证
    pub fn validate(&self) -> SigmaXResult<()> {
        // 验证风险管理配置
        self.risk.validate_complete()?;

        // 🔥 新增：验证交易配置（从 models_old.rs 移植）
        self.trading.validate_complete()?;

        // 🔥 新增：验证监控配置（从 models_old.rs 移植）
        self.monitoring.validate_complete()?;

        // 🔥 新增：验证缓存配置（从 models_old.rs 移植）
        self.cache.validate_complete()?;

        // 验证数据库配置
        if self.database.url.is_empty() {
            return Err(SigmaXError::Config("数据库URL不能为空".to_string()));
        }

        if self.database.max_connections == 0 {
            return Err(SigmaXError::Config("数据库最大连接数不能为0".to_string()));
        }

        // 验证Web配置
        if self.web.host.is_empty() {
            return Err(SigmaXError::Config("Web服务监听地址不能为空".to_string()));
        }

        if self.web.port == 0 {
            return Err(SigmaXError::Config("Web服务端口不能为0".to_string()));
        }

        if self.web.jwt_secret.is_empty() {
            return Err(SigmaXError::Config("JWT密钥不能为空".to_string()));
        }

        Ok(())
    }
}

/// AppConfig 的默认实现
///
/// # 移植说明
/// 从 models_old.rs 移植，增加了新配置模块的默认值
impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            exchanges: ExchangeConfigs::default(),
            risk: RiskManagementConfig::default(),
            // 🔥 新增：交易配置默认值（从 models_old.rs 移植）
            trading: TradingConfig::default(),
            // 🔥 新增：监控配置默认值（从 models_old.rs 移植）
            monitoring: MonitoringConfig::default(),
            // 🔥 新增：缓存配置默认值（从 models_old.rs 移植）
            cache: CacheConfig::default(),
            web: WebConfig::default(),
            logging: LoggingConfig::default(),
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "postgresql://localhost:5432/sigmax".to_string(),
            max_connections: 10,
            connection_timeout: 30,
            auto_migrate: true,
        }
    }
}

impl Default for ExchangeConfigs {
    fn default() -> Self {
        Self {
            binance: None,
            coinbase: None,
            kraken: None,
            simulator: Some(SimulatorConfig::default()),
        }
    }
}

impl Default for SimulatorConfig {
    fn default() -> Self {
        Self {
            initial_balance: rust_decimal::Decimal::from(10000),
            fee_rate: rust_decimal::Decimal::new(1, 3), // 0.001 = 0.1%
            slippage_rate: rust_decimal::Decimal::new(5, 4), // 0.0005 = 0.05%
        }
    }
}

impl Default for WebConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            jwt_secret: "default-secret-change-in-production".to_string(),
            session_timeout: 3600,
            enable_cors: true,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Text,
            output: LogOutput::Console,
            file_rotation: None,
        }
    }
}