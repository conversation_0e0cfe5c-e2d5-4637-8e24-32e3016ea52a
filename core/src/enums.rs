//! 核心枚举定义

use serde::{Deserialize, Serialize};

/// 订单方向
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl std::fmt::Display for OrderSide {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OrderSide::Buy => write!(f, "buy"),
            OrderSide::Sell => write!(f, "sell"),
        }
    }
}

/// 订单类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
    StopLoss,
    StopLimit,
}

impl std::fmt::Display for OrderType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OrderType::Market => write!(f, "market"),
            OrderType::Limit => write!(f, "limit"),
            OrderType::StopLoss => write!(f, "stop_loss"),
            OrderType::StopLimit => write!(f, "stop_limit"),
        }
    }
}

/// 订单状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Expired,
}

impl std::str::FromStr for OrderStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Pending" => Ok(OrderStatus::Pending),
            "PartiallyFilled" => Ok(OrderStatus::PartiallyFilled),
            "Filled" => Ok(OrderStatus::Filled),
            "Cancelled" => Ok(OrderStatus::Cancelled),
            "Rejected" => Ok(OrderStatus::Rejected),
            "Expired" => Ok(OrderStatus::Expired),
            _ => Err(format!("Invalid order status: {}", s)),
        }
    }
}

/// 时间周期
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TimeFrame {
    M1,   // 1分钟
    M5,   // 5分钟
    M15,  // 15分钟
    M30,  // 30分钟
    H1,   // 1小时
    H4,   // 4小时
    D1,   // 1天
    W1,   // 1周
    MN1,  // 1月
}

impl TimeFrame {
    pub fn to_seconds(&self) -> u64 {
        match self {
            TimeFrame::M1 => 60,
            TimeFrame::M5 => 300,
            TimeFrame::M15 => 900,
            TimeFrame::M30 => 1800,
            TimeFrame::H1 => 3600,
            TimeFrame::H4 => 14400,
            TimeFrame::D1 => 86400,
            TimeFrame::W1 => 604800,
            TimeFrame::MN1 => 2592000, // 30天近似
        }
    }
}

/// 策略状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum StrategyStatus {
    Created,
    Initializing,
    Running,
    Paused,
    Stopped,
    Error,
}

/// 引擎类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EngineType {
    Backtest,
    Live,
    Paper,
    Simulation,
}

impl std::fmt::Display for EngineType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EngineType::Backtest => write!(f, "backtest"),
            EngineType::Live => write!(f, "live"),
            EngineType::Paper => write!(f, "paper"),
            EngineType::Simulation => write!(f, "simulation"),
        }
    }
}

/// 引擎状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EngineStatus {
    Stopped,
    Starting,
    Running,
    Paused,
    Stopping,
    Error,
}
