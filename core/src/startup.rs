//! 应用程序启动框架
//!
//! 提供完整的应用程序生命周期管理，包括启动、运行和优雅关闭。

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::{RwLock, Notify};
use tokio::signal;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::{SigmaXResult, SigmaXError, ServiceManager, ServiceRegistry, AppConfig, LoggingManager};

/// 应用程序状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AppState {
    /// 初始化中
    Initializing,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 停止中
    Stopping,
    /// 已停止
    Stopped,
    /// 错误状态
    Error,
}

/// 应用程序上下文
pub struct AppContext {
    /// 应用程序名称
    pub app_name: String,
    /// 应用程序版本
    pub app_version: String,
    /// 当前状态
    pub state: Arc<RwLock<AppState>>,
    /// 启动时间
    pub start_time: DateTime<Utc>,
    /// 配置管理器
    pub config: Arc<AppConfig>,
    /// 日志管理器
    pub logging: Arc<LoggingManager>,
    /// 服务管理器
    pub services: Arc<ServiceManager>,
    /// 应用程序数据
    pub data: Arc<RwLock<HashMap<String, serde_json::Value>>>,
    /// 关闭通知
    pub shutdown_notify: Arc<Notify>,
}

impl AppContext {
    /// 创建新的应用程序上下文
    pub fn new(
        app_name: String,
        app_version: String,
        config: AppConfig,
        logging: LoggingManager,
    ) -> Self {
        Self {
            app_name,
            app_version,
            state: Arc::new(RwLock::new(AppState::Initializing)),
            start_time: Utc::now(),
            config: Arc::new(config),
            logging: Arc::new(logging),
            services: Arc::new(ServiceManager::new(Arc::new(ServiceRegistry::new()))),
            data: Arc::new(RwLock::new(HashMap::new())),
            shutdown_notify: Arc::new(Notify::new()),
        }
    }

    /// 获取当前状态
    pub async fn get_state(&self) -> AppState {
        *self.state.read().await
    }

    /// 设置状态
    pub async fn set_state(&self, new_state: AppState) {
        let mut state = self.state.write().await;
        *state = new_state;
        tracing::info!("Application state changed to: {:?}", new_state);
    }

    /// 获取运行时间
    pub fn uptime(&self) -> chrono::Duration {
        Utc::now() - self.start_time
    }

    /// 存储数据
    pub async fn set_data(&self, key: String, value: serde_json::Value) {
        let mut data = self.data.write().await;
        data.insert(key, value);
    }

    /// 获取数据
    pub async fn get_data(&self, key: &str) -> Option<serde_json::Value> {
        let data = self.data.read().await;
        data.get(key).cloned()
    }

    /// 触发关闭
    pub fn shutdown(&self) {
        self.shutdown_notify.notify_waiters();
    }

    /// 等待关闭信号
    pub async fn wait_for_shutdown(&self) {
        self.shutdown_notify.notified().await;
    }
}

/// 应用程序主体
pub struct Application {
    /// 应用程序上下文
    pub context: Arc<AppContext>,
    /// 信号处理器
    signal_handler: Option<tokio::task::JoinHandle<()>>,
}

impl Application {
    /// 创建新的应用程序
    pub fn new(context: AppContext) -> Self {
        Self {
            context: Arc::new(context),
            signal_handler: None,
        }
    }

    /// 初始化应用程序
    pub async fn initialize(&mut self) -> SigmaXResult<()> {
        tracing::info!("Initializing application: {}", self.context.app_name);

        self.context.set_state(AppState::Initializing).await;

        // 配置已经在创建时加载
        tracing::info!("Configuration loaded successfully");

        // 初始化日志
        self.context.logging.init()?;
        tracing::info!("Logging initialized successfully");

        // 初始化服务管理器
        // 这里可以添加默认服务的注册
        tracing::info!("Service manager initialized");

        tracing::info!("Application initialization completed");
        Ok(())
    }

    /// 启动应用程序
    pub async fn start(&mut self) -> SigmaXResult<()> {
        tracing::info!("Starting application: {}", self.context.app_name);

        self.context.set_state(AppState::Starting).await;

        // 启动所有服务
        self.context.services.start_all().await?;
        tracing::info!("All services started successfully");

        // 设置信号处理
        self.setup_signal_handling().await?;
        tracing::info!("Signal handling setup completed");

        self.context.set_state(AppState::Running).await;
        tracing::info!("Application started successfully");

        Ok(())
    }

    /// 运行应用程序
    pub async fn run(&self) -> SigmaXResult<()> {
        tracing::info!("Application is now running");

        // 等待关闭信号
        self.context.wait_for_shutdown().await;

        tracing::info!("Shutdown signal received");
        Ok(())
    }

    /// 停止应用程序
    pub async fn stop(&mut self) -> SigmaXResult<()> {
        tracing::info!("Stopping application: {}", self.context.app_name);

        self.context.set_state(AppState::Stopping).await;

        // 停止信号处理
        if let Some(handle) = self.signal_handler.take() {
            handle.abort();
            tracing::info!("Signal handler stopped");
        }

        // 停止所有服务
        self.context.services.stop_all().await?;
        tracing::info!("All services stopped successfully");

        self.context.set_state(AppState::Stopped).await;
        tracing::info!("Application stopped successfully");

        Ok(())
    }

    /// 设置信号处理
    async fn setup_signal_handling(&mut self) -> SigmaXResult<()> {
        let context = Arc::clone(&self.context);

        let handle = tokio::spawn(async move {
            #[cfg(unix)]
            {
                let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())
                    .expect("Failed to create SIGTERM handler");
                let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())
                    .expect("Failed to create SIGINT handler");

                tokio::select! {
                    _ = sigterm.recv() => {
                        tracing::info!("Received SIGTERM signal");
                        context.shutdown();
                    }
                    _ = sigint.recv() => {
                        tracing::info!("Received SIGINT signal");
                        context.shutdown();
                    }
                    _ = signal::ctrl_c() => {
                        tracing::info!("Received Ctrl+C signal");
                        context.shutdown();
                    }
                }
            }

            #[cfg(windows)]
            {
                let mut ctrl_break = signal::windows::ctrl_break()
                    .expect("Failed to create Ctrl+Break handler");
                let mut ctrl_close = signal::windows::ctrl_close()
                    .expect("Failed to create close handler");

                tokio::select! {
                    _ = signal::ctrl_c() => {
                        tracing::info!("Received Ctrl+C signal");
                        context.shutdown();
                    }
                    _ = ctrl_break.recv() => {
                        tracing::info!("Received Ctrl+Break signal");
                        context.shutdown();
                    }
                    _ = ctrl_close.recv() => {
                        tracing::info!("Received close signal");
                        context.shutdown();
                    }
                }
            }
        });

        self.signal_handler = Some(handle);
        Ok(())
    }

    /// 优雅关闭
    pub async fn graceful_shutdown(&mut self) -> SigmaXResult<()> {
        tracing::info!("Initiating graceful shutdown");

        // 触发关闭信号
        self.context.shutdown();

        // 等待一段时间让正在进行的操作完成
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

        // 执行停止流程
        self.stop().await?;

        tracing::info!("Graceful shutdown completed");
        Ok(())
    }

    /// 获取应用程序状态信息
    pub async fn get_status(&self) -> AppStatus {
        AppStatus {
            name: self.context.app_name.clone(),
            version: self.context.app_version.clone(),
            state: self.context.get_state().await,
            uptime: self.context.uptime(),
            start_time: self.context.start_time,
        }
    }
}

/// 应用程序状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppStatus {
    pub name: String,
    pub version: String,
    pub state: AppState,
    pub uptime: chrono::Duration,
    pub start_time: DateTime<Utc>,
}

/// 应用程序构建器
pub struct ApplicationBuilder {
    app_name: Option<String>,
    app_version: Option<String>,
    config: Option<AppConfig>,
    logging: Option<LoggingManager>,
}

impl ApplicationBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            app_name: None,
            app_version: None,
            config: None,
            logging: None,
        }
    }

    /// 设置应用程序名称
    pub fn with_name(mut self, name: String) -> Self {
        self.app_name = Some(name);
        self
    }

    /// 设置应用程序版本
    pub fn with_version(mut self, version: String) -> Self {
        self.app_version = Some(version);
        self
    }

    /// 设置配置管理器
    pub fn with_config(mut self, config: AppConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// 设置日志管理器
    pub fn with_logging(mut self, logging: LoggingManager) -> Self {
        self.logging = Some(logging);
        self
    }

    /// 构建应用程序
    pub fn build(self) -> SigmaXResult<Application> {
        let app_name = self.app_name.ok_or_else(|| {
            SigmaXError::InvalidParameter("Application name is required".to_string())
        })?;

        let app_version = self.app_version.ok_or_else(|| {
            SigmaXError::InvalidParameter("Application version is required".to_string())
        })?;

        let config = self.config.ok_or_else(|| {
            SigmaXError::InvalidParameter("Configuration manager is required".to_string())
        })?;

        let logging = self.logging.ok_or_else(|| {
            SigmaXError::InvalidParameter("Logging manager is required".to_string())
        })?;

        let context = AppContext::new(app_name, app_version, config, logging);
        Ok(Application::new(context))
    }
}

impl Default for ApplicationBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{LoggingConfig, LogFormat, LogOutput};

    fn create_test_config() -> AppConfig {
        AppConfig::default()
    }

    fn create_test_logging() -> LoggingManager {
        let config = LoggingConfig {
            level: "info".to_string(),
            format: LogFormat::Text,
            output: LogOutput::Console,
            file_rotation: None,
        };
        LoggingManager::new(config)
    }

    #[test]
    fn test_app_context_creation() {
        let config = create_test_config();
        let logging = create_test_logging();

        let context = AppContext::new(
            "test-app".to_string(),
            "1.0.0".to_string(),
            config,
            logging,
        );

        assert_eq!(context.app_name, "test-app");
        assert_eq!(context.app_version, "1.0.0");
    }

    #[tokio::test]
    async fn test_app_context_state_management() {
        let config = create_test_config();
        let logging = create_test_logging();

        let context = AppContext::new(
            "test-app".to_string(),
            "1.0.0".to_string(),
            config,
            logging,
        );

        assert_eq!(context.get_state().await, AppState::Initializing);

        context.set_state(AppState::Running).await;
        assert_eq!(context.get_state().await, AppState::Running);
    }

    #[tokio::test]
    async fn test_app_context_data_storage() {
        let config = create_test_config();
        let logging = create_test_logging();

        let context = AppContext::new(
            "test-app".to_string(),
            "1.0.0".to_string(),
            config,
            logging,
        );

        let test_value = serde_json::json!({"test": "value"});
        context.set_data("test_key".to_string(), test_value.clone()).await;

        let retrieved = context.get_data("test_key").await;
        assert_eq!(retrieved, Some(test_value));

        let missing = context.get_data("missing_key").await;
        assert_eq!(missing, None);
    }

    #[test]
    fn test_application_builder() {
        let config = create_test_config();
        let logging = create_test_logging();

        let result = ApplicationBuilder::new()
            .with_name("test-app".to_string())
            .with_version("1.0.0".to_string())
            .with_config(config)
            .with_logging(logging)
            .build();

        assert!(result.is_ok());
        let app = result.unwrap();
        assert_eq!(app.context.app_name, "test-app");
        assert_eq!(app.context.app_version, "1.0.0");
    }

    #[test]
    fn test_application_builder_missing_fields() {
        let result = ApplicationBuilder::new()
            .with_name("test-app".to_string())
            .build();

        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_application_initialization() {
        let config = create_test_config();
        let logging = create_test_logging();

        let mut app = ApplicationBuilder::new()
            .with_name("test-app".to_string())
            .with_version("1.0.0".to_string())
            .with_config(config)
            .with_logging(logging)
            .build()
            .unwrap();

        let result = app.initialize().await;
        assert!(result.is_ok());
        assert_eq!(app.context.get_state().await, AppState::Initializing);
    }

    #[tokio::test]
    async fn test_app_status() {
        let config = create_test_config();
        let logging = create_test_logging();

        let app = ApplicationBuilder::new()
            .with_name("test-app".to_string())
            .with_version("1.0.0".to_string())
            .with_config(config)
            .with_logging(logging)
            .build()
            .unwrap();

        let status = app.get_status().await;
        assert_eq!(status.name, "test-app");
        assert_eq!(status.version, "1.0.0");
        assert_eq!(status.state, AppState::Initializing);
    }

    #[test]
    fn test_app_state_serialization() {
        let state = AppState::Running;
        let serialized = serde_json::to_string(&state).unwrap();
        let deserialized: AppState = serde_json::from_str(&serialized).unwrap();
        assert_eq!(state, deserialized);
    }
}
