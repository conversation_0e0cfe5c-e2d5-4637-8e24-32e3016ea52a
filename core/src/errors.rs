//! 错误类型定义

use thiserror::Error;
use std::fmt;
use validator::ValidationErrors;

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorSeverity {
    /// 信息级别 - 不影响系统运行
    Info,
    /// 警告级别 - 可能影响性能但不影响功能
    Warning,
    /// 错误级别 - 影响功能但系统可继续运行
    Error,
    /// 严重级别 - 可能导致系统不稳定
    Critical,
    /// 致命级别 - 系统无法继续运行
    Fatal,
}

/// 错误类别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorCategory {
    /// 业务逻辑错误
    Business,
    /// 系统错误
    System,
    /// 网络错误
    Network,
    /// 数据错误
    Data,
    /// 配置错误
    Configuration,
    /// 外部服务错误
    External,
}

/// SigmaX系统错误类型
#[derive(Error, Debug)]
pub enum SigmaXError {
    #[error("Exchange error: {0}")]
    Exchange(String),

    #[error("Order error: {0}")]
    Order(String),

    #[error("Strategy error: {0}")]
    Strategy(String),

    #[error("Risk management error: {0}")]
    Risk(String),

    #[error("Data error: {0}")]
    Data(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Configuration error: {0}")]
    Configuration(String),

    #[error("Database error: {0}")]
    Database(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Rate limit exceeded")]
    RateLimit,

    #[error("Internal error: {0}")]
    Internal(String),

    #[error("Risk management error: {0}")]
    RiskManagement(String),

    #[error("Invalid state: {0}")]
    InvalidState(String),

    #[error("Invalid operation: {0}")]
    InvalidOperation(String),

    #[error("Service error: {0}")]
    Service(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[error("Rate limit exceeded: {0}")]
    RateLimitExceeded(String),

    #[error("Circuit breaker is open: {0}")]
    CircuitBreakerOpen(String),
}

/// 错误上下文信息
#[derive(Debug, Clone)]
pub struct ErrorContext {
    /// 错误发生的模块
    pub module: String,
    /// 错误发生的函数
    pub function: String,
    /// 错误发生的行号
    pub line: Option<u32>,
    /// 额外的上下文信息
    pub details: Vec<String>,
}

impl ErrorContext {
    pub fn new(module: &str, function: &str) -> Self {
        Self {
            module: module.to_string(),
            function: function.to_string(),
            line: None,
            details: Vec::new(),
        }
    }

    pub fn with_line(mut self, line: u32) -> Self {
        self.line = Some(line);
        self
    }

    pub fn with_detail(mut self, detail: &str) -> Self {
        self.details.push(detail.to_string());
        self
    }
}

/// 增强的错误信息
#[derive(Debug)]
pub struct EnhancedError {
    /// 原始错误
    pub error: SigmaXError,
    /// 错误代码
    pub code: u32,
    /// 错误严重程度
    pub severity: ErrorSeverity,
    /// 错误类别
    pub category: ErrorCategory,
    /// 是否可重试
    pub retryable: bool,
    /// 错误上下文
    pub context: Option<ErrorContext>,
    /// 用户友好的错误消息
    pub user_message: Option<String>,
    /// 错误发生时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// SigmaX系统结果类型
pub type SigmaXResult<T> = Result<T, SigmaXError>;

impl SigmaXError {
    /// 添加错误上下文
    pub fn with_context(self, context: ErrorContext) -> EnhancedError {
        EnhancedError {
            code: self.error_code(),
            severity: self.severity(),
            category: self.category(),
            retryable: self.is_retryable(),
            user_message: Some(self.to_user_message()),
            timestamp: chrono::Utc::now(),
            context: Some(context),
            error: self,
        }
    }

    /// 获取错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            SigmaXError::Exchange(_) => 1001,
            SigmaXError::Order(_) => 1002,
            SigmaXError::Strategy(_) => 1003,
            SigmaXError::Risk(_) => 1004,
            SigmaXError::Data(_) => 1005,
            SigmaXError::Network(_) => 1006,
            SigmaXError::Config(_) => 1007,
            SigmaXError::Database(_) => 1008,
            SigmaXError::Serialization(_) => 1009,
            SigmaXError::InvalidParameter(_) => 1010,
            SigmaXError::NotFound(_) => 1011,
            SigmaXError::Unauthorized => 1012,
            SigmaXError::RateLimit => 1013,
            SigmaXError::Internal(_) => 1014,
            SigmaXError::RiskManagement(_) => 1015,
            SigmaXError::InvalidState(_) => 1016,
            SigmaXError::InvalidOperation(_) => 1017,
            SigmaXError::Service(_) => 1018,
            SigmaXError::ValidationError(_) => 1019,
            SigmaXError::NotImplemented(_) => 1020,
            SigmaXError::RateLimitExceeded(_) => 1021,
            SigmaXError::Configuration(_) => 1022,
            SigmaXError::CircuitBreakerOpen(_) => 1023,
        }
    }

    /// 判断错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            SigmaXError::Network(_) => true,
            SigmaXError::RateLimit => true,
            SigmaXError::Database(_) => true,
            SigmaXError::Exchange(_) => true,
            SigmaXError::Data(_) => false,
            SigmaXError::Config(_) => false,
            SigmaXError::InvalidParameter(_) => false,
            SigmaXError::NotFound(_) => false,
            SigmaXError::Unauthorized => false,
            SigmaXError::Order(_) => false,
            SigmaXError::Strategy(_) => false,
            SigmaXError::Risk(_) => false,
            SigmaXError::Serialization(_) => false,
            SigmaXError::Internal(_) => false,
            SigmaXError::RiskManagement(_) => false,
            SigmaXError::InvalidState(_) => false,
            SigmaXError::InvalidOperation(_) => false,
            SigmaXError::Service(_) => true,
            SigmaXError::ValidationError(_) => false,
            SigmaXError::NotImplemented(_) => false,
            SigmaXError::RateLimitExceeded(_) => true,
            SigmaXError::Configuration(_) => false,
            SigmaXError::CircuitBreakerOpen(_) => true,
        }
    }

    /// 获取用户友好的错误消息
    pub fn to_user_message(&self) -> String {
        match self {
            SigmaXError::Exchange(_) => "交易所连接出现问题，请稍后重试".to_string(),
            SigmaXError::Order(_) => "订单处理失败，请检查订单参数".to_string(),
            SigmaXError::Strategy(_) => "策略执行出现问题，请检查策略配置".to_string(),
            SigmaXError::Risk(_) => "风险控制触发，操作被阻止".to_string(),
            SigmaXError::Data(_) => "数据处理出现问题，请稍后重试".to_string(),
            SigmaXError::Network(_) => "网络连接不稳定，请检查网络设置".to_string(),
            SigmaXError::Config(_) => "配置文件有误，请检查配置设置".to_string(),
            SigmaXError::Database(_) => "数据库连接出现问题，请稍后重试".to_string(),
            SigmaXError::Serialization(_) => "数据格式错误，请检查输入数据".to_string(),
            SigmaXError::InvalidParameter(_) => "参数无效，请检查输入参数".to_string(),
            SigmaXError::NotFound(_) => "请求的资源不存在".to_string(),
            SigmaXError::Unauthorized => "访问权限不足，请检查认证信息".to_string(),
            SigmaXError::RateLimit => "请求过于频繁，请稍后重试".to_string(),
            SigmaXError::Internal(_) => "系统内部错误，请联系技术支持".to_string(),
            SigmaXError::RiskManagement(_) => "风险管理检查失败，操作被阻止".to_string(),
            SigmaXError::InvalidState(_) => "系统状态无效，请检查当前状态".to_string(),
            SigmaXError::InvalidOperation(_) => "操作无效，请检查操作参数".to_string(),
            SigmaXError::Service(_) => "服务不可用，请稍后重试".to_string(),
            SigmaXError::ValidationError(_) => "数据验证失败，请检查输入数据".to_string(),
            SigmaXError::NotImplemented(_) => "功能尚未实现".to_string(),
            SigmaXError::RateLimitExceeded(_) => "请求频率超限，请稍后重试".to_string(),
            SigmaXError::Configuration(_) => "配置错误，请检查系统配置".to_string(),
            SigmaXError::CircuitBreakerOpen(_) => "系统熔断保护已激活，请稍后重试".to_string(),
        }
    }

    /// 获取错误严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            SigmaXError::Config(_) => ErrorSeverity::Fatal,
            SigmaXError::Database(_) => ErrorSeverity::Critical,
            SigmaXError::Internal(_) => ErrorSeverity::Critical,
            SigmaXError::Risk(_) => ErrorSeverity::Warning,
            SigmaXError::Exchange(_) => ErrorSeverity::Error,
            SigmaXError::Network(_) => ErrorSeverity::Error,
            SigmaXError::Order(_) => ErrorSeverity::Error,
            SigmaXError::Strategy(_) => ErrorSeverity::Error,
            SigmaXError::Data(_) => ErrorSeverity::Error,
            SigmaXError::Serialization(_) => ErrorSeverity::Error,
            SigmaXError::InvalidParameter(_) => ErrorSeverity::Warning,
            SigmaXError::NotFound(_) => ErrorSeverity::Warning,
            SigmaXError::Unauthorized => ErrorSeverity::Warning,
            SigmaXError::RateLimit => ErrorSeverity::Info,
            SigmaXError::RiskManagement(_) => ErrorSeverity::Warning,
            SigmaXError::InvalidState(_) => ErrorSeverity::Error,
            SigmaXError::InvalidOperation(_) => ErrorSeverity::Warning,
            SigmaXError::Service(_) => ErrorSeverity::Error,
            SigmaXError::ValidationError(_) => ErrorSeverity::Warning,
            SigmaXError::NotImplemented(_) => ErrorSeverity::Info,
            SigmaXError::RateLimitExceeded(_) => ErrorSeverity::Warning,
            SigmaXError::Configuration(_) => ErrorSeverity::Error,
            SigmaXError::CircuitBreakerOpen(_) => ErrorSeverity::Critical,
        }
    }

    /// 获取错误类别
    pub fn category(&self) -> ErrorCategory {
        match self {
            SigmaXError::Exchange(_) => ErrorCategory::External,
            SigmaXError::Order(_) => ErrorCategory::Business,
            SigmaXError::Strategy(_) => ErrorCategory::Business,
            SigmaXError::Risk(_) => ErrorCategory::Business,
            SigmaXError::Data(_) => ErrorCategory::Data,
            SigmaXError::Network(_) => ErrorCategory::Network,
            SigmaXError::Config(_) => ErrorCategory::Configuration,
            SigmaXError::Database(_) => ErrorCategory::System,
            SigmaXError::Serialization(_) => ErrorCategory::Data,
            SigmaXError::InvalidParameter(_) => ErrorCategory::Business,
            SigmaXError::NotFound(_) => ErrorCategory::Business,
            SigmaXError::Unauthorized => ErrorCategory::System,
            SigmaXError::RateLimit => ErrorCategory::External,
            SigmaXError::Internal(_) => ErrorCategory::System,
            SigmaXError::RiskManagement(_) => ErrorCategory::Business,
            SigmaXError::InvalidState(_) => ErrorCategory::System,
            SigmaXError::InvalidOperation(_) => ErrorCategory::Business,
            SigmaXError::Service(_) => ErrorCategory::System,
            SigmaXError::ValidationError(_) => ErrorCategory::Business,
            SigmaXError::NotImplemented(_) => ErrorCategory::System,
            SigmaXError::RateLimitExceeded(_) => ErrorCategory::External,
            SigmaXError::Configuration(_) => ErrorCategory::Configuration,
            SigmaXError::CircuitBreakerOpen(_) => ErrorCategory::System,
        }
    }
}

impl From<anyhow::Error> for SigmaXError {
    fn from(err: anyhow::Error) -> Self {
        SigmaXError::Internal(err.to_string())
    }
}

impl From<serde_json::Error> for SigmaXError {
    fn from(err: serde_json::Error) -> Self {
        SigmaXError::Serialization(err.to_string())
    }
}

impl From<rust_decimal::Error> for SigmaXError {
    fn from(err: rust_decimal::Error) -> Self {
        SigmaXError::Data(format!("Decimal parsing error: {}", err))
    }
}

impl From<chrono::ParseError> for SigmaXError {
    fn from(err: chrono::ParseError) -> Self {
        SigmaXError::Data(format!("DateTime parsing error: {}", err))
    }
}

impl From<ValidationErrors> for SigmaXError {
    fn from(err: ValidationErrors) -> Self {
        let error_messages: Vec<String> = err
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                let field_name = field.to_string();
                errors.iter().map(move |error| {
                    let message = error.message
                        .as_ref()
                        .map(|m| m.to_string())
                        .unwrap_or_else(|| format!("Validation failed for field '{}'", field_name));
                    format!("{}: {}", field_name, message)
                })
            })
            .collect();

        SigmaXError::ValidationError(error_messages.join("; "))
    }
}

// reqwest错误转换在需要的模块中实现
// impl From<reqwest::Error> for SigmaXError {
//     fn from(err: reqwest::Error) -> Self {
//         SigmaXError::Network(err.to_string())
//     }
// }

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorSeverity::Info => write!(f, "INFO"),
            ErrorSeverity::Warning => write!(f, "WARNING"),
            ErrorSeverity::Error => write!(f, "ERROR"),
            ErrorSeverity::Critical => write!(f, "CRITICAL"),
            ErrorSeverity::Fatal => write!(f, "FATAL"),
        }
    }
}

impl fmt::Display for ErrorCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorCategory::Business => write!(f, "BUSINESS"),
            ErrorCategory::System => write!(f, "SYSTEM"),
            ErrorCategory::Network => write!(f, "NETWORK"),
            ErrorCategory::Data => write!(f, "DATA"),
            ErrorCategory::Configuration => write!(f, "CONFIG"),
            ErrorCategory::External => write!(f, "EXTERNAL"),
        }
    }
}

impl fmt::Display for EnhancedError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "[{}] [{}] [{}] {} (Code: {})",
            self.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
            self.severity,
            self.category,
            self.error,
            self.code
        )?;

        if let Some(context) = &self.context {
            write!(f, " at {}::{}", context.module, context.function)?;
            if let Some(line) = context.line {
                write!(f, ":{}", line)?;
            }
            if !context.details.is_empty() {
                write!(f, " ({})", context.details.join(", "))?;
            }
        }

        Ok(())
    }
}

/// 条件检查宏，如果条件不满足则返回错误
#[macro_export]
macro_rules! ensure {
    ($cond:expr, $err:expr) => {
        if !($cond) {
            return Err($err);
        }
    };
    ($cond:expr, $err:expr, $($arg:tt)*) => {
        if !($cond) {
            return Err($err);
        }
    };
}

/// 创建错误上下文的便捷宏
#[macro_export]
macro_rules! error_context {
    ($module:expr, $function:expr) => {
        $crate::errors::ErrorContext::new($module, $function)
    };
    ($module:expr, $function:expr, $line:expr) => {
        $crate::errors::ErrorContext::new($module, $function).with_line($line)
    };
}

/// 创建带上下文的错误的便捷宏
#[macro_export]
macro_rules! context_error {
    ($error:expr, $module:expr, $function:expr) => {
        $error.with_context($crate::error_context!($module, $function))
    };
    ($error:expr, $module:expr, $function:expr, $line:expr) => {
        $error.with_context($crate::error_context!($module, $function, $line))
    };
}

/// 错误链追踪
pub struct ErrorChain {
    errors: Vec<EnhancedError>,
}

impl ErrorChain {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
        }
    }

    pub fn push(&mut self, error: EnhancedError) {
        self.errors.push(error);
    }

    pub fn errors(&self) -> &[EnhancedError] {
        &self.errors
    }

    pub fn root_cause(&self) -> Option<&EnhancedError> {
        self.errors.first()
    }

    pub fn latest_error(&self) -> Option<&EnhancedError> {
        self.errors.last()
    }
}

impl fmt::Display for ErrorChain {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        writeln!(f, "Error Chain ({} errors):", self.errors.len())?;
        for (i, error) in self.errors.iter().enumerate() {
            writeln!(f, "  {}: {}", i + 1, error)?;
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_code() {
        let error = SigmaXError::Exchange("test".to_string());
        assert_eq!(error.error_code(), 1001);

        let error = SigmaXError::Order("test".to_string());
        assert_eq!(error.error_code(), 1002);
    }

    #[test]
    fn test_is_retryable() {
        let network_error = SigmaXError::Network("connection failed".to_string());
        assert!(network_error.is_retryable());

        let config_error = SigmaXError::Config("invalid config".to_string());
        assert!(!config_error.is_retryable());
    }

    #[test]
    fn test_to_user_message() {
        let error = SigmaXError::Network("connection timeout".to_string());
        let user_msg = error.to_user_message();
        assert_eq!(user_msg, "网络连接不稳定，请检查网络设置");
    }

    #[test]
    fn test_error_severity() {
        let config_error = SigmaXError::Config("missing config".to_string());
        assert_eq!(config_error.severity(), ErrorSeverity::Fatal);

        let rate_limit_error = SigmaXError::RateLimit;
        assert_eq!(rate_limit_error.severity(), ErrorSeverity::Info);
    }

    #[test]
    fn test_error_category() {
        let exchange_error = SigmaXError::Exchange("api error".to_string());
        assert_eq!(exchange_error.category(), ErrorCategory::External);

        let order_error = SigmaXError::Order("invalid order".to_string());
        assert_eq!(order_error.category(), ErrorCategory::Business);
    }

    #[test]
    fn test_error_context() {
        let context = ErrorContext::new("trading", "place_order")
            .with_line(42)
            .with_detail("order_id: 12345");

        assert_eq!(context.module, "trading");
        assert_eq!(context.function, "place_order");
        assert_eq!(context.line, Some(42));
        assert_eq!(context.details.len(), 1);
    }

    #[test]
    fn test_enhanced_error() {
        let error = SigmaXError::Order("invalid quantity".to_string());
        let context = ErrorContext::new("trading", "validate_order");
        let enhanced = error.with_context(context);

        assert_eq!(enhanced.code, 1002);
        assert_eq!(enhanced.severity, ErrorSeverity::Error);
        assert_eq!(enhanced.category, ErrorCategory::Business);
        assert!(!enhanced.retryable);
        assert!(enhanced.user_message.is_some());
        assert!(enhanced.context.is_some());
    }

    #[test]
    fn test_ensure_macro() {
        fn test_function(value: i32) -> SigmaXResult<i32> {
            ensure!(value > 0, SigmaXError::InvalidParameter("value must be positive".to_string()));
            Ok(value * 2)
        }

        assert!(test_function(5).is_ok());
        assert!(test_function(-1).is_err());
    }

    #[test]
    fn test_error_chain() {
        let mut chain = ErrorChain::new();

        let error1 = SigmaXError::Network("connection failed".to_string())
            .with_context(ErrorContext::new("network", "connect"));
        let error2 = SigmaXError::Exchange("api timeout".to_string())
            .with_context(ErrorContext::new("exchange", "get_price"));

        chain.push(error1);
        chain.push(error2);

        assert_eq!(chain.errors().len(), 2);
        assert!(chain.root_cause().is_some());
        assert!(chain.latest_error().is_some());
    }

    #[test]
    fn test_error_display() {
        let severity = ErrorSeverity::Critical;
        assert_eq!(severity.to_string(), "CRITICAL");

        let category = ErrorCategory::Network;
        assert_eq!(category.to_string(), "NETWORK");
    }
}
