//! 事件系统实现
//! 
//! 提供异步事件处理、事件总线和事件存储功能

use crate::{SigmaXResult, SigmaXError, OrderId, StrategyId};
use crate::models::{Order, Trade};
use serde::{Serialize, Deserialize};
use std::{
    collections::HashMap,
    sync::Arc,
    time::{SystemTime, UNIX_EPOCH},
};
use tokio::sync::{mpsc, RwLock};
use async_trait::async_trait;
use uuid::Uuid;

/// 事件ID类型
pub type EventId = Uuid;

/// 事件类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventType {
    OrderCreated,
    OrderUpdated,
    OrderCancelled,
    TradeExecuted,
    StrategyStarted,
    StrategyStoped,
    RiskAlert,
    SystemError,
    // 引擎事件
    EngineCreated,
    EngineStarted,
    EngineStopped,
    EnginePaused,
    EngineResumed,
    EngineError,
}

/// 风险警报信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    pub alert_type: String,
    pub message: String,
    pub severity: RiskSeverity,
    pub strategy_id: Option<StrategyId>,
    pub timestamp: u64,
}

/// 风险严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 系统事件定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemEvent {
    OrderCreated(Order),
    OrderUpdated(Order),
    OrderCancelled(OrderId),
    TradeExecuted(Trade),
    StrategyStarted(StrategyId),
    StrategyStoped(StrategyId),
    RiskAlert(RiskAlert),
    SystemError(String),
    // 引擎事件
    EngineCreated(crate::EngineId),
    EngineStarted(crate::EngineId),
    EngineStopped(crate::EngineId),
    EnginePaused(crate::EngineId),
    EngineResumed(crate::EngineId),
    EngineError(crate::EngineId, String),
}

impl SystemEvent {
    /// 获取事件类型
    pub fn event_type(&self) -> EventType {
        match self {
            SystemEvent::OrderCreated(_) => EventType::OrderCreated,
            SystemEvent::OrderUpdated(_) => EventType::OrderUpdated,
            SystemEvent::OrderCancelled(_) => EventType::OrderCancelled,
            SystemEvent::TradeExecuted(_) => EventType::TradeExecuted,
            SystemEvent::StrategyStarted(_) => EventType::StrategyStarted,
            SystemEvent::StrategyStoped(_) => EventType::StrategyStoped,
            SystemEvent::RiskAlert(_) => EventType::RiskAlert,
            SystemEvent::SystemError(_) => EventType::SystemError,
            SystemEvent::EngineCreated(_) => EventType::EngineCreated,
            SystemEvent::EngineStarted(_) => EventType::EngineStarted,
            SystemEvent::EngineStopped(_) => EventType::EngineStopped,
            SystemEvent::EnginePaused(_) => EventType::EnginePaused,
            SystemEvent::EngineResumed(_) => EventType::EngineResumed,
            SystemEvent::EngineError(_, _) => EventType::EngineError,
        }
    }
    
    /// 获取事件时间戳
    pub fn timestamp(&self) -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// 获取事件描述
    pub fn description(&self) -> String {
        match self {
            SystemEvent::OrderCreated(order) => format!("订单创建: {}", order.id),
            SystemEvent::OrderUpdated(order) => format!("订单更新: {}", order.id),
            SystemEvent::OrderCancelled(order_id) => format!("订单取消: {}", order_id),
            SystemEvent::TradeExecuted(trade) => format!("交易执行: {}", trade.id),
            SystemEvent::StrategyStarted(strategy_id) => format!("策略启动: {}", strategy_id),
            SystemEvent::StrategyStoped(strategy_id) => format!("策略停止: {}", strategy_id),
            SystemEvent::RiskAlert(alert) => format!("风险警报: {}", alert.message),
            SystemEvent::SystemError(error) => format!("系统错误: {}", error),
            SystemEvent::EngineCreated(engine_id) => format!("引擎创建: {}", engine_id),
            SystemEvent::EngineStarted(engine_id) => format!("引擎启动: {}", engine_id),
            SystemEvent::EngineStopped(engine_id) => format!("引擎停止: {}", engine_id),
            SystemEvent::EnginePaused(engine_id) => format!("引擎暂停: {}", engine_id),
            SystemEvent::EngineResumed(engine_id) => format!("引擎恢复: {}", engine_id),
            SystemEvent::EngineError(engine_id, error) => format!("引擎错误 {}: {}", engine_id, error),
        }
    }
}

/// 存储的事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredEvent {
    pub id: EventId,
    pub event_type: EventType,
    pub event_data: SystemEvent,
    pub timestamp: u64,
    pub processed: bool,
}

impl StoredEvent {
    pub fn new(event: SystemEvent) -> Self {
        Self {
            id: Uuid::new_v4(),
            event_type: event.event_type(),
            timestamp: event.timestamp(),
            event_data: event,
            processed: false,
        }
    }
}

/// 事件处理器trait
#[async_trait]
pub trait EventHandler: Send + Sync {
    /// 处理事件
    async fn handle(&self, event: &SystemEvent) -> SigmaXResult<()>;
    
    /// 获取处理器关注的事件类型
    fn event_types(&self) -> Vec<EventType>;
    
    /// 获取处理器名称
    fn name(&self) -> &str;
    
    /// 处理器是否启用
    fn is_enabled(&self) -> bool {
        true
    }
}

/// 事件总线配置
#[derive(Debug, Clone)]
pub struct EventBusConfig {
    pub buffer_size: usize,
    pub max_retries: usize,
    pub retry_delay_ms: u64,
    pub enable_persistence: bool,
}

impl Default for EventBusConfig {
    fn default() -> Self {
        Self {
            buffer_size: 1000,
            max_retries: 3,
            retry_delay_ms: 1000,
            enable_persistence: true,
        }
    }
}

/// 事件总线
pub struct EventBus {
    handlers: Arc<RwLock<HashMap<EventType, Vec<Arc<dyn EventHandler>>>>>,
    sender: Option<mpsc::UnboundedSender<SystemEvent>>,
    config: EventBusConfig,
    running: Arc<RwLock<bool>>,
}

impl EventBus {
    /// 创建新的事件总线
    pub fn new() -> Self {
        Self::with_config(EventBusConfig::default())
    }
    
    /// 使用配置创建事件总线
    pub fn with_config(config: EventBusConfig) -> Self {
        Self {
            handlers: Arc::new(RwLock::new(HashMap::new())),
            sender: None,
            config,
            running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 启动事件总线
    pub async fn start(&mut self) -> SigmaXResult<()> {
        let mut running = self.running.write().await;
        if *running {
            return Err(SigmaXError::Internal("事件总线已经在运行".to_string()));
        }
        
        let (sender, mut receiver) = mpsc::unbounded_channel::<SystemEvent>();
        self.sender = Some(sender);
        
        let handlers = Arc::clone(&self.handlers);
        let config = self.config.clone();
        
        // 启动事件处理循环
        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                Self::process_event(&handlers, &event, &config).await;
            }
        });
        
        *running = true;
        Ok(())
    }
    
    /// 停止事件总线
    pub async fn stop(&self) -> SigmaXResult<()> {
        let mut running = self.running.write().await;
        if !*running {
            return Ok(());
        }
        
        // 关闭发送端，这会导致接收循环结束
        // 注意：这里不能直接drop sender，因为它是Option类型且被借用
        *running = false;
        
        Ok(())
    }
    
    /// 订阅事件处理器
    pub async fn subscribe<H: EventHandler + 'static>(&self, handler: H) -> SigmaXResult<()> {
        let handler: Arc<dyn EventHandler> = Arc::new(handler);
        let mut handlers = self.handlers.write().await;

        for event_type in handler.event_types() {
            handlers
                .entry(event_type)
                .or_insert_with(Vec::new)
                .push(Arc::clone(&handler));
        }

        Ok(())
    }
    
    /// 发布单个事件
    pub async fn publish(&self, event: SystemEvent) -> SigmaXResult<()> {
        let running = self.running.read().await;
        if !*running {
            return Err(SigmaXError::Internal("事件总线未启动".to_string()));
        }
        
        if let Some(sender) = &self.sender {
            sender.send(event)
                .map_err(|e| SigmaXError::Internal(format!("发送事件失败: {}", e)))?;
        }
        
        Ok(())
    }
    
    /// 批量发布事件
    pub async fn publish_batch(&self, events: Vec<SystemEvent>) -> SigmaXResult<()> {
        for event in events {
            self.publish(event).await?;
        }
        Ok(())
    }
    
    /// 处理单个事件
    async fn process_event(
        handlers: &Arc<RwLock<HashMap<EventType, Vec<Arc<dyn EventHandler>>>>>,
        event: &SystemEvent,
        config: &EventBusConfig,
    ) {
        let event_type = event.event_type();
        let handlers_map = handlers.read().await;
        
        if let Some(event_handlers) = handlers_map.get(&event_type) {
            for handler in event_handlers {
                if !handler.is_enabled() {
                    continue;
                }
                
                // 重试机制
                let mut retries = 0;
                while retries <= config.max_retries {
                    match handler.handle(event).await {
                        Ok(_) => break,
                        Err(e) => {
                            retries += 1;
                            if retries > config.max_retries {
                                eprintln!(
                                    "处理器 {} 处理事件失败，已达到最大重试次数: {}",
                                    handler.name(),
                                    e
                                );
                            } else {
                                eprintln!(
                                    "处理器 {} 处理事件失败，第 {} 次重试: {}",
                                    handler.name(),
                                    retries,
                                    e
                                );
                                tokio::time::sleep(tokio::time::Duration::from_millis(
                                    config.retry_delay_ms,
                                ))
                                .await;
                            }
                        }
                    }
                }
            }
        }
    }
    
    /// 获取已注册的处理器数量
    pub async fn handler_count(&self) -> usize {
        let handlers = self.handlers.read().await;
        handlers.values().map(|v| v.len()).sum()
    }
    
    /// 检查事件总线是否正在运行
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
}

impl Default for EventBus {
    fn default() -> Self {
        Self::new()
    }
}

/// 事件存储Repository trait (在database中实现)
#[async_trait]
pub trait EventRepository: Send + Sync {
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId>;
    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>>;
    async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
    async fn get_events_by_timerange(&self, start: u64, end: u64, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
    async fn mark_event_processed(&self, event_id: EventId) -> SigmaXResult<()>;
    async fn get_unprocessed_events(&self, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
}

/// 事件存储器
pub struct EventStore {
    repository: Arc<dyn EventRepository>,
    event_bus: Option<Arc<EventBus>>,
}

impl EventStore {
    /// 创建新的事件存储器
    pub fn new(repository: Arc<dyn EventRepository>) -> Self {
        Self {
            repository,
            event_bus: None,
        }
    }

    /// 设置事件总线（用于自动发布事件）
    pub fn with_event_bus(mut self, event_bus: Arc<EventBus>) -> Self {
        self.event_bus = Some(event_bus);
        self
    }

    /// 追加事件到存储
    pub async fn append(&self, event: &SystemEvent) -> SigmaXResult<EventId> {
        let stored_event = StoredEvent::new(event.clone());
        let event_id = self.repository.save_event(&stored_event).await?;

        // 如果配置了事件总线，自动发布事件
        if let Some(event_bus) = &self.event_bus {
            if let Err(e) = event_bus.publish(event.clone()).await {
                eprintln!("发布事件到事件总线失败: {}", e);
            }
        }

        Ok(event_id)
    }

    /// 批量追加事件
    pub async fn append_batch(&self, events: &[SystemEvent]) -> SigmaXResult<Vec<EventId>> {
        let mut event_ids = Vec::new();

        for event in events {
            let event_id = self.append(event).await?;
            event_ids.push(event_id);
        }

        Ok(event_ids)
    }

    /// 获取事件
    pub async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>> {
        self.repository.get_event(event_id).await
    }

    /// 获取事件列表（分页）
    pub async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        self.repository.get_events(offset, limit).await
    }

    /// 按类型获取事件
    pub async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        self.repository.get_events_by_type(event_type, limit).await
    }

    /// 按时间范围获取事件
    pub async fn get_events_by_timerange(&self, start: u64, end: u64, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        self.repository.get_events_by_timerange(start, end, limit).await
    }

    /// 事件重放功能
    pub async fn replay_events(&self, from_timestamp: u64, to_timestamp: u64) -> SigmaXResult<Vec<SystemEvent>> {
        let stored_events = self.repository.get_events_by_timerange(from_timestamp, to_timestamp, 1000).await?;

        let mut events = Vec::new();
        for stored_event in stored_events {
            events.push(stored_event.event_data);
        }

        Ok(events)
    }

    /// 重放事件到事件总线
    pub async fn replay_to_bus(&self, from_timestamp: u64, to_timestamp: u64) -> SigmaXResult<usize> {
        if let Some(event_bus) = &self.event_bus {
            let events = self.replay_events(from_timestamp, to_timestamp).await?;
            let count = events.len();

            event_bus.publish_batch(events).await?;

            Ok(count)
        } else {
            Err(SigmaXError::Internal("未配置事件总线".to_string()))
        }
    }

    /// 标记事件为已处理
    pub async fn mark_processed(&self, event_id: EventId) -> SigmaXResult<()> {
        self.repository.mark_event_processed(event_id).await
    }

    /// 获取未处理的事件
    pub async fn get_unprocessed_events(&self, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        self.repository.get_unprocessed_events(limit).await
    }

    /// 处理未处理的事件
    pub async fn process_unprocessed_events(&self) -> SigmaXResult<usize> {
        let unprocessed_events = self.get_unprocessed_events(100).await?;
        let count = unprocessed_events.len();

        if let Some(event_bus) = &self.event_bus {
            for stored_event in unprocessed_events {
                // 发布到事件总线
                if let Err(e) = event_bus.publish(stored_event.event_data).await {
                    eprintln!("处理未处理事件失败: {}", e);
                    continue;
                }

                // 标记为已处理
                if let Err(e) = self.mark_processed(stored_event.id).await {
                    eprintln!("标记事件已处理失败: {}", e);
                }
            }
        }

        Ok(count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use tokio::time::{sleep, Duration};

    // 测试用的事件处理器
    struct TestEventHandler {
        name: String,
        event_types: Vec<EventType>,
        call_count: Arc<AtomicUsize>,
        should_fail: bool,
    }

    impl TestEventHandler {
        fn new(name: &str, event_types: Vec<EventType>) -> Self {
            Self {
                name: name.to_string(),
                event_types,
                call_count: Arc::new(AtomicUsize::new(0)),
                should_fail: false,
            }
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }

        fn call_count(&self) -> usize {
            self.call_count.load(Ordering::SeqCst)
        }
    }

    #[async_trait]
    impl EventHandler for TestEventHandler {
        async fn handle(&self, _event: &SystemEvent) -> SigmaXResult<()> {
            self.call_count.fetch_add(1, Ordering::SeqCst);

            if self.should_fail {
                Err(SigmaXError::Internal("测试失败".to_string()))
            } else {
                Ok(())
            }
        }

        fn event_types(&self) -> Vec<EventType> {
            self.event_types.clone()
        }

        fn name(&self) -> &str {
            &self.name
        }
    }

    #[tokio::test]
    async fn test_system_event_creation() {
        let order = Order {
            id: uuid::Uuid::new_v4(),
            strategy_id: Some(uuid::Uuid::new_v4()),
            exchange_id: crate::ExchangeId::Binance,
            trading_pair: crate::TradingPair::new("BTC", "USDT"),
            side: crate::OrderSide::Buy,
            order_type: crate::OrderType::Limit,
            quantity: rust_decimal::Decimal::new(1, 0),
            price: Some(rust_decimal::Decimal::new(50000, 0)),
            stop_price: None,
            status: crate::OrderStatus::Pending,
            filled_quantity: rust_decimal::Decimal::new(0, 0),
            average_price: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let event = SystemEvent::OrderCreated(order);
        assert_eq!(event.event_type(), EventType::OrderCreated);
        assert!(!event.description().is_empty());
        assert!(event.timestamp() > 0);
    }

    #[tokio::test]
    async fn test_stored_event_creation() {
        let event = SystemEvent::SystemError("测试错误".to_string());
        let stored_event = StoredEvent::new(event.clone());

        assert_eq!(stored_event.event_type, EventType::SystemError);
        assert!(!stored_event.processed);
        assert!(stored_event.timestamp > 0);

        match stored_event.event_data {
            SystemEvent::SystemError(msg) => assert_eq!(msg, "测试错误"),
            _ => panic!("事件类型不匹配"),
        }
    }

    #[tokio::test]
    async fn test_event_bus_lifecycle() {
        let mut event_bus = EventBus::new();

        // 初始状态
        assert!(!event_bus.is_running().await);
        assert_eq!(event_bus.handler_count().await, 0);

        // 启动
        event_bus.start().await.unwrap();
        assert!(event_bus.is_running().await);

        // 停止
        event_bus.stop().await.unwrap();
        assert!(!event_bus.is_running().await);
    }

    #[tokio::test]
    async fn test_event_handler_subscription() {
        let mut event_bus = EventBus::new();
        event_bus.start().await.unwrap();

        let handler1 = TestEventHandler::new("handler1", vec![EventType::OrderCreated]);
        let handler2 = TestEventHandler::new("handler2", vec![EventType::OrderCreated, EventType::TradeExecuted]);

        event_bus.subscribe(handler1).await.unwrap();
        event_bus.subscribe(handler2).await.unwrap();

        assert_eq!(event_bus.handler_count().await, 3); // handler1: 1, handler2: 2
    }

    #[tokio::test]
    async fn test_event_publishing_and_handling() {
        let mut event_bus = EventBus::new();
        event_bus.start().await.unwrap();

        let handler = TestEventHandler::new("test_handler", vec![EventType::SystemError]);
        let call_count = Arc::clone(&handler.call_count);

        event_bus.subscribe(handler).await.unwrap();

        // 发布事件
        let event = SystemEvent::SystemError("测试错误".to_string());
        event_bus.publish(event).await.unwrap();

        // 等待事件处理
        sleep(Duration::from_millis(100)).await;

        assert_eq!(call_count.load(Ordering::SeqCst), 1);
    }

    #[tokio::test]
    async fn test_event_batch_publishing() {
        let mut event_bus = EventBus::new();
        event_bus.start().await.unwrap();

        let handler = TestEventHandler::new("batch_handler", vec![EventType::SystemError]);
        let call_count = Arc::clone(&handler.call_count);

        event_bus.subscribe(handler).await.unwrap();

        // 批量发布事件
        let events = vec![
            SystemEvent::SystemError("错误1".to_string()),
            SystemEvent::SystemError("错误2".to_string()),
            SystemEvent::SystemError("错误3".to_string()),
        ];

        event_bus.publish_batch(events).await.unwrap();

        // 等待事件处理
        sleep(Duration::from_millis(100)).await;

        assert_eq!(call_count.load(Ordering::SeqCst), 3);
    }

    #[tokio::test]
    async fn test_event_handler_retry_mechanism() {
        let config = EventBusConfig {
            buffer_size: 100,
            max_retries: 2,
            retry_delay_ms: 10,
            enable_persistence: false,
        };

        let mut event_bus = EventBus::with_config(config);
        event_bus.start().await.unwrap();

        let handler = TestEventHandler::new("failing_handler", vec![EventType::SystemError]).with_failure();
        let call_count = Arc::clone(&handler.call_count);

        event_bus.subscribe(handler).await.unwrap();

        // 发布事件
        let event = SystemEvent::SystemError("测试错误".to_string());
        event_bus.publish(event).await.unwrap();

        // 等待重试完成
        sleep(Duration::from_millis(200)).await;

        // 应该被调用 3 次（初始 + 2次重试）
        assert_eq!(call_count.load(Ordering::SeqCst), 3);
    }

    #[tokio::test]
    async fn test_risk_alert_event() {
        let risk_alert = RiskAlert {
            alert_type: "高风险".to_string(),
            message: "持仓超过风险限制".to_string(),
            severity: RiskSeverity::High,
            strategy_id: Some(uuid::Uuid::new_v4()),
            timestamp: SystemEvent::SystemError("".to_string()).timestamp(),
        };

        let event = SystemEvent::RiskAlert(risk_alert.clone());
        assert_eq!(event.event_type(), EventType::RiskAlert);

        match event {
            SystemEvent::RiskAlert(alert) => {
                assert_eq!(alert.alert_type, "高风险");
                assert_eq!(alert.message, "持仓超过风险限制");
                assert!(matches!(alert.severity, RiskSeverity::High));
            }
            _ => panic!("事件类型不匹配"),
        }
    }

    #[tokio::test]
    async fn test_event_type_filtering() {
        let mut event_bus = EventBus::new();
        event_bus.start().await.unwrap();

        let order_handler = TestEventHandler::new("order_handler", vec![EventType::OrderCreated]);
        let trade_handler = TestEventHandler::new("trade_handler", vec![EventType::TradeExecuted]);
        let all_handler = TestEventHandler::new("all_handler", vec![
            EventType::OrderCreated,
            EventType::TradeExecuted,
            EventType::SystemError
        ]);

        let order_count = Arc::clone(&order_handler.call_count);
        let trade_count = Arc::clone(&trade_handler.call_count);
        let all_count = Arc::clone(&all_handler.call_count);

        event_bus.subscribe(order_handler).await.unwrap();
        event_bus.subscribe(trade_handler).await.unwrap();
        event_bus.subscribe(all_handler).await.unwrap();

        // 发布不同类型的事件
        event_bus.publish(SystemEvent::SystemError("错误".to_string())).await.unwrap();

        // 等待事件处理
        sleep(Duration::from_millis(100)).await;

        // 验证事件过滤
        assert_eq!(order_count.load(Ordering::SeqCst), 0);
        assert_eq!(trade_count.load(Ordering::SeqCst), 0);
        assert_eq!(all_count.load(Ordering::SeqCst), 1);
    }
}
