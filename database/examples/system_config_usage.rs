//! SystemConfigRepository 使用示例
//! 
//! 展示如何使用强类型配置模式管理系统配置

use std::sync::Arc;
use sigmax_core::{
    SigmaXResult, TradingConfig, RiskManagementConfig, SystemGeneralConfig, 
    NotificationConfig, StrategyTemplate, ExchangeSystemConfig, UserRoleConfig,
    ExchangeFees, ExchangeRateLimits, SystemConfigRecord
};
use sigmax_database::{
    DatabaseManager, RepositoryFactory, RepositoryConfig,
    repositories::traits::SystemConfigRepository
};

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化数据库连接
    let db_url = std::env::var("DATABASE_URL")
        .expect("DATABASE_URL environment variable is required");
    
    let db_manager = Arc::new(DatabaseManager::new_with_url(&db_url).await?);
    
    // 创建Repository工厂
    let factory_config = RepositoryConfig {
        database_manager: Some(db_manager.clone()),
    };
    let factory = RepositoryFactory::new(factory_config);
    
    // 创建SystemConfigRepository
    let config_repo = factory.create_sqlx_system_config_repository()?;
    
    println!("🐾 SystemConfigRepository 使用示例开始");
    
    // ============================================================================
    // 1. 强类型配置管理示例
    // ============================================================================
    
    println!("\n📋 1. 强类型配置管理");
    
    // 创建并保存交易配置
    let trading_config = TradingConfig {
        max_orders_per_strategy: 100,
        max_position_size: rust_decimal::Decimal::new(1000000, 0), // 1,000,000 USDT
        default_order_timeout: 3600, // 1小时
        min_order_interval: 1, // 1秒
        max_slippage_percent: 0.5, // 0.5%
    };
    
    config_repo.save_trading_config(&trading_config).await?;
    println!("✅ 交易配置已保存");
    
    // 读取交易配置
    let retrieved_trading_config = config_repo.get_trading_config().await?;
    println!("📖 读取的交易配置: max_orders={}, max_position={}", 
             retrieved_trading_config.max_orders_per_strategy,
             retrieved_trading_config.max_position_size);
    
    // 创建并保存风险管理配置
    let risk_config = RiskManagementConfig {
        max_drawdown_percent: 20.0,
        max_daily_loss_percent: 5.0,
        max_portfolio_risk_percent: 10.0,
        position_size_limit_percent: 25.0,
        stop_loss_percent: 2.0,
    };
    
    config_repo.save_risk_config(&risk_config).await?;
    println!("✅ 风险管理配置已保存");
    
    // 创建并保存系统配置
    let system_config = SystemGeneralConfig {
        maintenance_mode: false,
        max_concurrent_strategies: 50,
        data_retention_days: 365,
        backup_enabled: true,
        log_level: "INFO".to_string(),
    };
    
    config_repo.save_system_config(&system_config).await?;
    println!("✅ 系统配置已保存");
    
    // 创建并保存通知配置
    let notification_config = NotificationConfig {
        email_enabled: true,
        webhook_url: Some("https://hooks.slack.com/services/xxx".to_string()),
        slack_webhook: Some("https://hooks.slack.com/services/xxx".to_string()),
        telegram_bot_token: Some("bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11".to_string()),
        telegram_chat_id: Some("-1001234567890".to_string()),
    };
    
    config_repo.save_notification_config(&notification_config).await?;
    println!("✅ 通知配置已保存");
    
    // ============================================================================
    // 2. 复杂配置管理示例
    // ============================================================================
    
    println!("\n🎯 2. 复杂配置管理");
    
    // 创建策略模板
    let grid_strategy_template = StrategyTemplate {
        strategy_type: "asymmetric_volatility_grid".to_string(),
        parameters: serde_json::json!({
            "grid_levels": 15,
            "base_order_size": 100,
            "grid_spacing_percent": 1.0,
            "asymmetric_ratio": 0.6,
            "volatility_threshold": 0.02
        }),
        risk_management: serde_json::json!({
            "max_position_size": 50000,
            "stop_loss_percent": 3.0,
            "take_profit_percent": 10.0
        }),
    };
    
    config_repo.save_strategy_template("asymmetric_grid_btc_usdt", &grid_strategy_template).await?;
    println!("✅ 策略模板已保存: asymmetric_grid_btc_usdt");
    
    // 创建交易所配置
    let binance_config = ExchangeSystemConfig {
        fees: ExchangeFees {
            maker: 0.001,
            taker: 0.001,
        },
        rate_limits: ExchangeRateLimits {
            orders_per_second: 10,
            weight_per_minute: Some(6000),
            requests_per_minute: 1200,
        },
        api_endpoint: "https://api.binance.com".to_string(),
        min_notional: rust_decimal::Decimal::new(10, 0),
        websocket_endpoint: "wss://stream.binance.com:9443".to_string(),
        supported_order_types: vec![
            "MARKET".to_string(),
            "LIMIT".to_string(),
            "STOP_LOSS".to_string(),
            "STOP_LOSS_LIMIT".to_string(),
        ],
        simulation_mode: Some(false),
    };
    
    config_repo.save_exchange_config("binance", &binance_config).await?;
    println!("✅ 交易所配置已保存: binance");
    
    // 创建用户角色配置
    let trader_role = UserRoleConfig {
        permissions: vec![
            "strategies.view".to_string(),
            "strategies.create".to_string(),
            "strategies.start".to_string(),
            "strategies.stop".to_string(),
            "orders.view".to_string(),
            "trades.view".to_string(),
            "portfolio.view".to_string(),
        ],
    };
    
    config_repo.save_user_role_config("trader", &trader_role).await?;
    println!("✅ 用户角色配置已保存: trader");
    
    // ============================================================================
    // 3. 查询和统计示例
    // ============================================================================
    
    println!("\n📊 3. 查询和统计");
    
    // 获取所有策略模板
    let templates = config_repo.get_all_strategy_templates().await?;
    println!("📋 策略模板数量: {}", templates.len());
    for (name, template) in templates {
        println!("  - {}: {}", name, template.strategy_type);
    }
    
    // 获取所有交易所配置
    let exchanges = config_repo.get_all_exchange_configs().await?;
    println!("🏪 交易所配置数量: {}", exchanges.len());
    for (name, exchange) in exchanges {
        println!("  - {}: {}", name, exchange.api_endpoint);
    }
    
    // 获取所有用户角色
    let roles = config_repo.get_all_user_role_configs().await?;
    println!("👥 用户角色数量: {}", roles.len());
    for (name, role) in roles {
        println!("  - {}: {} 权限", name, role.permissions.len());
    }
    
    // 获取所有命名空间
    let namespaces = config_repo.get_all_namespaces().await?;
    println!("📁 配置命名空间: {:?}", namespaces);
    
    // 获取配置统计信息
    let stats = config_repo.get_config_statistics().await?;
    println!("📈 配置统计:");
    println!("  - 总配置数: {}", stats.total_configs);
    println!("  - 加密配置数: {}", stats.encrypted_configs);
    println!("  - 按命名空间分布:");
    for (namespace, count) in stats.configs_by_namespace {
        println!("    * {}: {} 个配置", namespace, count);
    }
    if let Some(last_updated) = stats.last_updated {
        println!("  - 最后更新时间: {}", last_updated.format("%Y-%m-%d %H:%M:%S UTC"));
    }
    
    // ============================================================================
    // 4. 命名空间操作示例
    // ============================================================================
    
    println!("\n🗂️ 4. 命名空间操作");
    
    // 获取trading命名空间的所有配置
    let trading_configs = config_repo.get_configs_by_namespace("trading").await?;
    println!("📋 trading命名空间配置数量: {}", trading_configs.len());
    for config in trading_configs {
        println!("  - {}: {:?}", config.key, config.value);
    }
    
    // ============================================================================
    // 5. 原始配置操作示例
    // ============================================================================
    
    println!("\n⚙️ 5. 原始配置操作");
    
    // 创建自定义配置
    let custom_config = SystemConfigRecord::new(
        "custom.feature_flag.new_ui",
        serde_json::json!(true),
        Some("新UI功能开关".to_string()),
        false,
    );
    
    config_repo.save_config(&custom_config).await?;
    println!("✅ 自定义配置已保存: {}", custom_config.key);
    
    // 读取自定义配置
    if let Some(retrieved) = config_repo.get_config("custom.feature_flag.new_ui").await? {
        let feature_enabled: bool = retrieved.get_value()?;
        println!("📖 新UI功能开关: {}", feature_enabled);
    }
    
    println!("\n🎉 SystemConfigRepository 使用示例完成！");
    println!("作为 Claude 4.0 sonnet，我已经成功实现了强类型配置模式的SystemConfig SQLx实现！");
    
    Ok(())
}

/// 辅助函数：展示配置验证
async fn demonstrate_config_validation() -> SigmaXResult<()> {
    use sigmax_core::Validatable;
    
    println!("\n🔍 配置验证示例");
    
    // 有效的交易配置
    let valid_config = TradingConfig {
        max_orders_per_strategy: 100,
        max_position_size: rust_decimal::Decimal::new(1000000, 0),
        default_order_timeout: 3600,
        min_order_interval: 1,
        max_slippage_percent: 0.5,
    };
    
    match valid_config.validate() {
        Ok(_) => println!("✅ 交易配置验证通过"),
        Err(e) => println!("❌ 交易配置验证失败: {}", e),
    }
    
    // 无效的交易配置
    let invalid_config = TradingConfig {
        max_orders_per_strategy: 0, // 无效值
        max_position_size: rust_decimal::Decimal::ZERO, // 无效值
        default_order_timeout: 3600,
        min_order_interval: 1,
        max_slippage_percent: 150.0, // 无效值
    };
    
    match invalid_config.validate() {
        Ok(_) => println!("✅ 无效配置意外通过验证"),
        Err(e) => println!("❌ 无效配置验证失败（预期）: {}", e),
    }
    
    Ok(())
}
