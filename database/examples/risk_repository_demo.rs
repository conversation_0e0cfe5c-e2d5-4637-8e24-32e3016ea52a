//! 风险 Repository 演示程序
//! 
//! 演示如何使用 Repository 与线上数据库进行交互

use sigmax_database::{Database, SqlRiskRepository};
use sigmax_database::repositories::traits::{RiskRepository, RiskCheckRecord, RiskRuleRecord};
use sigmax_core::SigmaXResult;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::sync::Arc;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("🚀 风险 Repository 演示程序启动");
    
    // 从环境变量获取数据库连接字符串
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| {
            println!("⚠️  未设置 DATABASE_URL 环境变量，使用默认连接");
            "postgresql://localhost:5432/sigmax".to_string()
        });
    
    println!("📡 连接数据库: {}", database_url);
    
    // 创建数据库连接
    let database = match Database::new(&database_url).await {
        Ok(db) => {
            println!("✅ 数据库连接成功");
            db
        }
        Err(e) => {
            println!("❌ 数据库连接失败: {}", e);
            println!("💡 请确保：");
            println!("   1. PostgreSQL 服务正在运行");
            println!("   2. 数据库 'sigmax' 存在");
            println!("   3. 连接参数正确");
            return Err(e);
        }
    };
    
    let risk_repo = SqlRiskRepository::new(Arc::new(database));
    
    println!("\n📋 测试风险规则查询");
    println!("=" .repeat(50));
    
    // 测试获取启用的风险规则
    match risk_repo.get_enabled_risk_rules().await {
        Ok(rules) => {
            println!("✅ 成功获取 {} 条启用的风险规则", rules.len());
            
            if rules.is_empty() {
                println!("📝 数据库中暂无启用的风险规则");
            } else {
                println!("\n🔍 前 5 条风险规则：");
                for (i, rule) in rules.iter().take(5).enumerate() {
                    println!("  {}. {} ({})", 
                        i + 1, 
                        rule.name, 
                        rule.rule_type
                    );
                    println!("     分类: {}", rule.category);
                    println!("     优先级: {}", rule.priority);
                    println!("     启用: {}", rule.enabled);
                    println!();
                }
            }
        }
        Err(e) => {
            println!("❌ 获取风险规则失败: {}", e);
        }
    }
    
    println!("\n📊 测试违规记录查询");
    println!("=" .repeat(50));
    
    // 测试获取最近的违规记录
    match risk_repo.get_recent_violations(10).await {
        Ok(violations) => {
            println!("✅ 成功获取 {} 条最近的违规记录", violations.len());
            
            if violations.is_empty() {
                println!("📝 数据库中暂无违规记录");
            } else {
                println!("\n🚨 最近的违规记录：");
                for (i, violation) in violations.iter().take(5).enumerate() {
                    println!("  {}. 规则: {}", i + 1, violation.rule_name);
                    println!("     严重程度: {}", violation.severity);
                    println!("     状态: {}", violation.status);
                    println!("     消息: {}", violation.message);
                    println!("     时间: {}", violation.created_at.format("%Y-%m-%d %H:%M:%S"));
                    println!();
                }
            }
        }
        Err(e) => {
            println!("❌ 获取违规记录失败: {}", e);
        }
    }
    
    println!("\n🧪 测试数据写入（可选）");
    println!("=" .repeat(50));
    
    // 创建一个测试风险检查记录
    let test_record = RiskCheckRecord {
        id: Uuid::new_v4(),
        timestamp: Utc::now(),
        trading_pair: "BTC/USDT".to_string(),
        side: "buy".to_string(),
        quantity: Decimal::new(1, 0), // 1.0
        price: Some(Decimal::new(50000, 0)), // 50000.0
        order_type: "limit".to_string(),
        result: "passed".to_string(),
        risk_score: Decimal::new(25, 1), // 2.5
        confidence_level: Some(Decimal::new(95, 0)), // 95.0
        violations: serde_json::json!([]),
        warnings: serde_json::json!([]),
        recommendations: serde_json::json!([]),
        applied_rules: serde_json::json!(["position_limit", "price_deviation"]),
        max_allowed_quantity: Some(Decimal::new(10, 0)), // 10.0
        suggested_price_min: Some(Decimal::new(49000, 0)), // 49000.0
        suggested_price_max: Some(Decimal::new(51000, 0)), // 51000.0
        alternative_suggestions: None,
        processing_time_ms: Some(150),
        rules_evaluated: Some(5),
        cache_hit_rate: Some(Decimal::new(80, 0)), // 80.0
        strategy_id: Some(Uuid::new_v4()),
        portfolio_id: Some(Uuid::new_v4()),
        engine_id: Some(Uuid::new_v4()),
        session_id: Some("demo_session".to_string()),
        created_at: Utc::now(),
        check_date: chrono::Utc::now().date_naive(),
    };
    
    println!("💾 尝试保存测试风险检查记录...");
    match risk_repo.save_risk_check(&test_record).await {
        Ok(_) => {
            println!("✅ 成功保存测试记录: {}", test_record.id);
            
            // 尝试读取刚保存的记录
            match risk_repo.get_risk_check(test_record.id).await {
                Ok(Some(retrieved)) => {
                    println!("✅ 成功读取保存的记录");
                    println!("   交易对: {}", retrieved.trading_pair);
                    println!("   结果: {}", retrieved.result);
                    println!("   风险评分: {}", retrieved.risk_score);
                }
                Ok(None) => {
                    println!("⚠️  未找到刚保存的记录");
                }
                Err(e) => {
                    println!("❌ 读取记录失败: {}", e);
                }
            }
        }
        Err(e) => {
            println!("⚠️  保存测试记录失败: {}", e);
            println!("💡 这可能是因为权限限制或表结构不匹配");
        }
    }
    
    println!("\n🎉 演示程序完成");
    println!("=" .repeat(50));
    println!("✅ Repository 与数据库连接正常");
    println!("✅ 基本查询功能工作正常");
    println!("✅ 数据结构与线上表结构匹配");
    
    Ok(())
}
