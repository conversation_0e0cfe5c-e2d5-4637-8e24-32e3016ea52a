//! 风险仓储使用示例
//!
//! 演示如何使用新实现的 RiskRepository 功能

use chrono::{Utc, NaiveDate};
use rust_decimal::Decimal;
use serde_json::json;
use std::str::FromStr;
use uuid::Uuid;

use sigmax_database::{
    DatabaseManager, SqlRiskRepository, RiskRepository,
    RiskCheckRecord, RiskRuleRecord, RiskStatisticsRecord, RiskViolationRecord,
    RiskQueryFilter, RiskRuleFilter, Pagination
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🐾 风险仓储功能演示开始！");

    // 注意：这里使用示例连接字符串，实际使用时请替换为真实的数据库连接
    let connection_string = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    
    // 创建数据库管理器
    let db_manager = DatabaseManager::new(connection_string).await?;
    
    // 创建风险仓储实例
    let risk_repo = SqlRiskRepository::new(std::sync::Arc::new(db_manager));

    println!("✅ 数据库连接成功，开始演示各项功能...\n");

    // 1. 演示风险检查记录功能
    demo_risk_checks(&risk_repo).await?;
    
    // 2. 演示风险规则管理功能
    demo_risk_rules(&risk_repo).await?;
    
    // 3. 演示风险统计功能
    demo_risk_statistics(&risk_repo).await?;
    
    // 4. 演示风险违规记录功能
    demo_risk_violations(&risk_repo).await?;

    println!("🎉 所有功能演示完成！风险仓储实现正常工作。");
    
    Ok(())
}

async fn demo_risk_checks(repo: &SqlRiskRepository) -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 === 风险检查记录功能演示 ===");
    
    // 创建示例风险检查记录
    let check_record = RiskCheckRecord {
        id: Uuid::new_v4(),
        timestamp: Utc::now(),
        trading_pair: "BTC_USDT".to_string(),
        side: "Buy".to_string(),
        quantity: Decimal::from_str("0.5")?,
        price: Some(Decimal::from_str("45000.0")?),
        order_type: "Limit".to_string(),
        passed: true,
        risk_score: Decimal::from_str("0.2")?,
        violations: json!([]),
        warnings: json!([]),
        recommendations: json!(["Order passes all risk checks"]),
        max_allowed_quantity: Some(Decimal::from_str("1.0")?),
        suggested_price_min: None,
        suggested_price_max: None,
        processing_time_ms: Some(150),
        strategy_id: Some(Uuid::new_v4()),
        engine_id: Some(Uuid::new_v4()),
        created_at: Utc::now(),
    };

    // 保存风险检查记录
    repo.save_risk_check(&check_record).await?;
    println!("✅ 保存风险检查记录成功: {}", check_record.id);

    // 查询风险检查记录
    if let Some(retrieved) = repo.get_risk_check(check_record.id).await? {
        println!("✅ 查询风险检查记录成功: {} - {}", retrieved.trading_pair, retrieved.side);
    }

    // 使用过滤器查询
    let filter = RiskQueryFilter {
        trading_pair: Some("BTC_USDT".to_string()),
        passed: Some(true),
        ..Default::default()
    };
    
    let pagination = Pagination { offset: 0, limit: 10 };
    let checks = repo.find_risk_checks(&filter, Some(&pagination)).await?;
    println!("✅ 过滤查询找到 {} 条风险检查记录", checks.len());

    // 统计记录数量
    let count = repo.count_risk_checks(&filter).await?;
    println!("✅ 符合条件的记录总数: {}\n", count);

    Ok(())
}

async fn demo_risk_rules(repo: &SqlRiskRepository) -> Result<(), Box<dyn std::error::Error>> {
    println!("⚙️ === 风险规则管理功能演示 ===");
    
    // 创建示例风险规则
    let rule = RiskRuleRecord {
        id: Uuid::new_v4(),
        name: "示例最大订单限制".to_string(),
        rule_type: "max_order_size".to_string(),
        description: Some("限制单笔订单最大金额".to_string()),
        parameters: json!({
            "currency": "USDT",
            "max_amount": 10000.0
        }),
        enabled: true,
        priority: 100,
        created_by: Some("system".to_string()),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // 保存风险规则
    repo.save_risk_rule(&rule).await?;
    println!("✅ 保存风险规则成功: {}", rule.name);

    // 查询风险规则
    if let Some(retrieved) = repo.get_risk_rule(rule.id).await? {
        println!("✅ 查询风险规则成功: {} ({})", retrieved.name, retrieved.rule_type);
    }

    // 获取启用的规则
    let enabled_rules = repo.get_enabled_risk_rules().await?;
    println!("✅ 当前启用的风险规则数量: {}", enabled_rules.len());

    // 按类型查询规则
    let rules_by_type = repo.get_risk_rules_by_type("max_order_size").await?;
    println!("✅ max_order_size 类型的规则数量: {}", rules_by_type.len());

    // 更新规则状态
    repo.update_risk_rule_status(rule.id, false).await?;
    println!("✅ 更新风险规则状态成功\n");

    Ok(())
}

async fn demo_risk_statistics(repo: &SqlRiskRepository) -> Result<(), Box<dyn std::error::Error>> {
    println!("📈 === 风险统计功能演示 ===");
    
    // 创建示例统计数据
    let stats = RiskStatisticsRecord {
        id: Uuid::new_v4(),
        date: Utc::now().date_naive(),
        total_checks: 100,
        passed_checks: 85,
        failed_checks: 15,
        pass_rate: Decimal::from_str("0.85")?,
        average_risk_score: Decimal::from_str("0.25")?,
        top_violations: json!([
            {"rule_type": "max_order_size", "count": 10},
            {"rule_type": "price_deviation", "count": 5}
        ]),
        risk_score_distribution: json!({
            "low": 60,
            "medium": 25,
            "high": 15
        }),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // 保存统计数据
    repo.save_risk_statistics(&stats).await?;
    println!("✅ 保存风险统计数据成功: {}", stats.date);

    // 查询统计数据
    if let Some(retrieved) = repo.get_risk_statistics_by_date(stats.date).await? {
        println!("✅ 查询统计数据成功: 通过率 {}%", retrieved.pass_rate * Decimal::from(100));
    }

    // 查询日期范围内的统计数据
    let start_date = NaiveDate::from_ymd_opt(2025, 6, 1).unwrap();
    let end_date = NaiveDate::from_ymd_opt(2025, 6, 30).unwrap();
    let range_stats = repo.get_risk_statistics_by_range(start_date, end_date).await?;
    println!("✅ 查询日期范围内统计数据: {} 条记录\n", range_stats.len());

    Ok(())
}

async fn demo_risk_violations(repo: &SqlRiskRepository) -> Result<(), Box<dyn std::error::Error>> {
    println!("⚠️ === 风险违规记录功能演示 ===");
    
    let check_id = Uuid::new_v4();
    let rule_id = Uuid::new_v4();
    
    // 创建示例违规记录
    let violation = RiskViolationRecord {
        id: Uuid::new_v4(),
        risk_check_id: check_id,
        rule_id,
        rule_name: "最大订单限制".to_string(),
        severity: "High".to_string(),
        message: "订单金额超过最大限制".to_string(),
        current_value: Some("15000".to_string()),
        limit_value: Some("10000".to_string()),
        created_at: Utc::now(),
    };

    // 保存违规记录
    repo.save_risk_violation(&violation).await?;
    println!("✅ 保存风险违规记录成功: {}", violation.message);

    // 按检查ID查询违规记录
    let violations_by_check = repo.get_violations_by_check_id(check_id).await?;
    println!("✅ 检查ID {} 的违规记录数量: {}", check_id, violations_by_check.len());

    // 按规则ID查询违规记录
    let violations_by_rule = repo.get_violations_by_rule_id(rule_id).await?;
    println!("✅ 规则ID {} 的违规记录数量: {}", rule_id, violations_by_rule.len());

    // 获取最近的违规记录
    let recent_violations = repo.get_recent_violations(5).await?;
    println!("✅ 最近的违规记录数量: {}\n", recent_violations.len());

    Ok(())
}
