//! Diesel数据库Schema定义
//! 这个文件通常由 `diesel print-schema` 命令自动生成

use diesel::prelude::*;

// 交易对表
table! {
    trading_pairs (id) {
        id -> Int4,
        symbol -> Varchar,
        base_asset -> Varchar,
        quote_asset -> Varchar,
        created_at -> Timestamptz,
    }
}

// 订单表
table! {
    orders (id) {
        id -> Uuid,
        trading_pair_id -> Int4,
        order_type -> Varchar,
        side -> Varchar,
        quantity -> Numeric,
        price -> Nullable<Numeric>,
        status -> Varchar,
        filled_quantity -> Numeric,
        average_price -> Nullable<Numeric>,
        exchange_id -> Nullable<Varchar>,
        exchange_order_id -> Nullable<Varchar>,
        strategy_id -> Nullable<Uuid>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

// 交易记录表
table! {
    trades (id) {
        id -> Uuid,
        order_id -> Uuid,
        trading_pair_id -> Int4,
        side -> Varchar,
        quantity -> Numeric,
        price -> Numeric,
        fee -> Numeric,
        fee_asset -> Nullable<Varchar>,
        exchange_id -> Varchar,
        exchange_trade_id -> Nullable<Varchar>,
        executed_at -> Timestamptz,
        created_at -> Timestamptz,
    }
}

// 策略表
table! {
    strategies (id) {
        id -> Uuid,
        name -> Varchar,
        strategy_type -> Varchar,
        config -> Jsonb,
        status -> Varchar,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

// 投资组合表
table! {
    portfolios (id) {
        id -> Uuid,
        name -> Varchar,
        initial_capital -> Numeric,
        current_capital -> Numeric,
        total_pnl -> Numeric,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

// 持仓表
table! {
    positions (id) {
        id -> Uuid,
        portfolio_id -> Uuid,
        trading_pair_id -> Int4,
        side -> Varchar,
        quantity -> Numeric,
        average_price -> Numeric,
        unrealized_pnl -> Numeric,
        realized_pnl -> Numeric,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

// 余额表
table! {
    balances (exchange_id, asset) {
        exchange_id -> Varchar,
        asset -> Varchar,
        free -> Numeric,
        locked -> Numeric,
        updated_at -> Timestamptz,
    }
}

// 定义表之间的关联关系
joinable!(orders -> trading_pairs (trading_pair_id));
joinable!(trades -> orders (order_id));
joinable!(trades -> trading_pairs (trading_pair_id));
joinable!(positions -> portfolios (portfolio_id));
joinable!(positions -> trading_pairs (trading_pair_id));

allow_tables_to_appear_in_same_query!(
    trading_pairs,
    orders,
    trades,
    strategies,
    portfolios,
    positions,
    balances,
);
