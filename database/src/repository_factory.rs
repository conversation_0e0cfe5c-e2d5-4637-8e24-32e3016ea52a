//! Repository Factory
//!
//! 提供工厂函数来创建具体的仓储实现，隐藏具体实现细节
//!
//! 注意：由于 Rust 中 async trait 不能直接用作 trait object，
//! 这里提供具体类型的工厂函数而不是返回 Box<dyn Trait>

use std::sync::Arc;
use sigmax_core::SigmaXResult;

use crate::repositories::sqlx::{
    SqlOrderRepository, SqlEnhancedOrderRepository, SqlTradeRepository, SqlStrategyRepository, SqlEventRepository, SqlRiskRepository, SqlSystemConfigRepository
};
use crate::repositories::mock_order_repository::MockOrderRepository;
use crate::DatabaseManager;

#[cfg(feature = "diesel")]
use crate::repositories::diesel::{
    DieselOrderRepository, DieselTradeRepository, DieselStrategyRepository
};

/// 仓储类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RepositoryType {
    /// SQLx 实现（默认）
    Sqlx,
    /// Diesel ORM 实现
    #[cfg(feature = "diesel")]
    Diesel,
    /// Mock 实现（用于测试）
    Mock,
}

impl Default for RepositoryType {
    fn default() -> Self {
        Self::Sqlx
    }
}

/// 仓储工厂配置
#[derive(Debug, Clone)]
pub struct RepositoryFactoryConfig {
    /// 仓储类型
    pub repository_type: RepositoryType,
    /// 数据库管理器
    pub database_manager: Option<Arc<DatabaseManager>>,
    /// Mock 配置
    pub mock_config: MockRepositoryConfig,
}

/// Mock 仓储配置
#[derive(Debug, Clone)]
pub struct MockRepositoryConfig {
    /// 是否模拟错误
    pub simulate_errors: bool,
    /// 错误概率 (0.0 - 1.0)
    pub error_probability: f64,
    /// 延迟模拟 (毫秒)
    pub delay_ms: u64,
}

impl Default for MockRepositoryConfig {
    fn default() -> Self {
        Self {
            simulate_errors: false,
            error_probability: 0.0,
            delay_ms: 0,
        }
    }
}

impl Default for RepositoryFactoryConfig {
    fn default() -> Self {
        Self {
            repository_type: RepositoryType::default(),
            database_manager: None,
            mock_config: MockRepositoryConfig::default(),
        }
    }
}

/// 仓储工厂
pub struct RepositoryFactory {
    config: RepositoryFactoryConfig,
}

impl RepositoryFactory {
    /// 创建新的仓储工厂
    pub fn new(config: RepositoryFactoryConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建工厂
    pub fn default_with_db(db_manager: Arc<DatabaseManager>) -> Self {
        Self {
            config: RepositoryFactoryConfig {
                database_manager: Some(db_manager),
                ..Default::default()
            }
        }
    }

    /// 创建用于测试的工厂
    pub fn for_testing() -> Self {
        Self {
            config: RepositoryFactoryConfig {
                repository_type: RepositoryType::Mock,
                ..Default::default()
            }
        }
    }

    /// 创建 SQLx 订单仓储
    pub fn create_sqlx_order_repository(&self) -> SigmaXResult<SqlOrderRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlOrderRepository::new(db.clone()))
    }

    /// 创建 SQLx 增强订单仓储
    pub fn create_sqlx_enhanced_order_repository(&self) -> SigmaXResult<SqlEnhancedOrderRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlEnhancedOrderRepository::new(db.clone()))
    }

    /// 创建 Mock 订单仓储
    pub fn create_mock_order_repository(&self) -> SigmaXResult<MockOrderRepository> {
        Ok(MockOrderRepository::new())
    }

    #[cfg(feature = "diesel")]
    /// 创建 Diesel 订单仓储
    pub fn create_diesel_order_repository(&self, pool: Arc<crate::repositories::diesel::DbPool>) -> SigmaXResult<DieselOrderRepository> {
        Ok(DieselOrderRepository::new(pool))
    }

    /// 创建 SQLx 交易仓储
    pub fn create_sqlx_trade_repository(&self) -> SigmaXResult<SqlTradeRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlTradeRepository::new(db.clone()))
    }

    #[cfg(feature = "diesel")]
    /// 创建 Diesel 交易仓储
    pub fn create_diesel_trade_repository(&self, pool: Arc<crate::repositories::diesel::DbPool>) -> SigmaXResult<DieselTradeRepository> {
        Ok(DieselTradeRepository::new(pool))
    }

    /// 创建 SQLx 策略仓储
    pub fn create_sqlx_strategy_repository(&self) -> SigmaXResult<SqlStrategyRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlStrategyRepository::new(db.clone()))
    }

    #[cfg(feature = "diesel")]
    /// 创建 Diesel 策略仓储
    pub fn create_diesel_strategy_repository(&self, pool: Arc<crate::repositories::diesel::DbPool>) -> SigmaXResult<DieselStrategyRepository> {
        Ok(DieselStrategyRepository::new(pool))
    }

    /// 创建 SQLx 事件仓储
    pub fn create_sqlx_event_repository(&self) -> SigmaXResult<SqlEventRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlEventRepository::new(db.clone()))
    }

    /// 创建 SQLx 风险仓储
    pub fn create_sqlx_risk_repository(&self) -> SigmaXResult<SqlRiskRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlRiskRepository::new(db.clone()))
    }

    /// 创建 SQLx 系统配置仓储
    pub fn create_sqlx_system_config_repository(&self) -> SigmaXResult<SqlSystemConfigRepository> {
        let db = self.config.database_manager.as_ref()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Database manager not provided".to_string()))?;
        Ok(SqlSystemConfigRepository::new(db.clone()))
    }


}

/// 便利函数：创建默认的 SQLx 仓储工厂
pub fn create_sqlx_factory(db_manager: Arc<DatabaseManager>) -> RepositoryFactory {
    RepositoryFactory::new(RepositoryFactoryConfig {
        repository_type: RepositoryType::Sqlx,
        database_manager: Some(db_manager),
        mock_config: MockRepositoryConfig::default(),
    })
}

/// 便利函数：创建测试用的 Mock 仓储工厂
pub fn create_mock_factory() -> RepositoryFactory {
    RepositoryFactory::new(RepositoryFactoryConfig {
        repository_type: RepositoryType::Mock,
        database_manager: None,
        mock_config: MockRepositoryConfig::default(),
    })
}

/// 便利函数：创建带错误模拟的 Mock 仓储工厂
pub fn create_mock_factory_with_errors(error_probability: f64) -> RepositoryFactory {
    RepositoryFactory::new(RepositoryFactoryConfig {
        repository_type: RepositoryType::Mock,
        database_manager: None,
        mock_config: MockRepositoryConfig {
            simulate_errors: true,
            error_probability,
            delay_ms: 0,
        },
    })
}

#[cfg(feature = "diesel")]
/// 便利函数：创建 Diesel 仓储工厂
pub fn create_diesel_factory(pool: Arc<crate::repositories::diesel::DbPool>) -> RepositoryFactory {
    // 注意：这里需要扩展 RepositoryFactoryConfig 来支持 Diesel 连接池
    // 暂时返回一个基本的配置
    RepositoryFactory::new(RepositoryFactoryConfig {
        repository_type: RepositoryType::Diesel,
        database_manager: None,
        mock_config: MockRepositoryConfig::default(),
    })
}
