//! Diesel ORM 模型定义

use diesel::prelude::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use rust_decimal::Decimal;

use crate::schema::*;
use sigmax_core::{OrderSide, OrderType, OrderStatus, StrategyStatus, ExchangeId};

// ============================================================================
// 交易对模型
// ============================================================================

#[derive(Debug, Clone, Queryable, Selectable, Identifiable, Serialize, Deserialize)]
#[diesel(table_name = trading_pairs)]
pub struct TradingPairModel {
    pub id: i32,
    pub symbol: String,
    pub base_asset: String,
    pub quote_asset: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = trading_pairs)]
pub struct NewTradingPair {
    pub symbol: String,
    pub base_asset: String,
    pub quote_asset: String,
}

// ============================================================================
// 订单模型
// ============================================================================

#[derive(Debug, Clone, Queryable, Selectable, Identifiable, Associations, Serialize, Deserialize)]
#[diesel(belongs_to(TradingPairModel, foreign_key = trading_pair_id))]
#[diesel(table_name = orders)]
pub struct OrderModel {
    pub id: Uuid,
    pub trading_pair_id: i32,
    pub order_type: String,
    pub side: String,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub status: String,
    pub filled_quantity: Decimal,
    pub average_price: Option<Decimal>,
    pub exchange_id: Option<String>,
    pub exchange_order_id: Option<String>,
    pub strategy_id: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = orders)]
pub struct NewOrder {
    pub id: Uuid,
    pub trading_pair_id: i32,
    pub order_type: String,
    pub side: String,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub status: String,
    pub filled_quantity: Decimal,
    pub average_price: Option<Decimal>,
    pub exchange_id: Option<String>,
    pub exchange_order_id: Option<String>,
    pub strategy_id: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, AsChangeset)]
#[diesel(table_name = orders)]
pub struct UpdateOrder {
    pub status: Option<String>,
    pub filled_quantity: Option<Decimal>,
    pub average_price: Option<Option<Decimal>>,
    pub updated_at: DateTime<Utc>,
}

// ============================================================================
// 交易记录模型
// ============================================================================

#[derive(Debug, Clone, Queryable, Selectable, Identifiable, Associations, Serialize, Deserialize)]
#[diesel(belongs_to(OrderModel, foreign_key = order_id))]
#[diesel(belongs_to(TradingPairModel, foreign_key = trading_pair_id))]
#[diesel(table_name = trades)]
pub struct TradeModel {
    pub id: Uuid,
    pub order_id: Uuid,
    pub trading_pair_id: i32,
    pub side: String,
    pub quantity: Decimal,
    pub price: Decimal,
    pub fee: Decimal,
    pub fee_asset: Option<String>,
    pub exchange_id: String,
    pub exchange_trade_id: Option<String>,
    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = trades)]
pub struct NewTrade {
    pub id: Uuid,
    pub order_id: Uuid,
    pub trading_pair_id: i32,
    pub side: String,
    pub quantity: Decimal,
    pub price: Decimal,
    pub fee: Decimal,
    pub fee_asset: Option<String>,
    pub exchange_id: String,
    pub exchange_trade_id: Option<String>,
    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

// ============================================================================
// 策略模型
// ============================================================================

#[derive(Debug, Clone, Queryable, Selectable, Identifiable, Serialize, Deserialize)]
#[diesel(table_name = strategies)]
pub struct StrategyModel {
    pub id: Uuid,
    pub name: String,
    pub strategy_type: String,
    pub config: serde_json::Value,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = strategies)]
pub struct NewStrategy {
    pub id: Uuid,
    pub name: String,
    pub strategy_type: String,
    pub config: serde_json::Value,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, AsChangeset)]
#[diesel(table_name = strategies)]
pub struct UpdateStrategy {
    pub config: Option<serde_json::Value>,
    pub status: Option<String>,
    pub updated_at: DateTime<Utc>,
}

// ============================================================================
// 类型转换实现
// ============================================================================

impl From<OrderModel> for sigmax_core::Order {
    fn from(model: OrderModel) -> Self {
        Self {
            id: model.id,
            strategy_id: model.strategy_id,
            exchange_id: model.exchange_id.map(|s| ExchangeId::from(s)).unwrap_or(ExchangeId::Simulator),
            trading_pair: sigmax_core::TradingPair::from_symbol(&format!("{}_{}", 
                model.trading_pair_id, model.trading_pair_id)).unwrap_or_default(), // 简化处理
            side: match model.side.as_str() {
                "Buy" => OrderSide::Buy,
                "Sell" => OrderSide::Sell,
                _ => OrderSide::Buy,
            },
            order_type: match model.order_type.as_str() {
                "Market" => OrderType::Market,
                "Limit" => OrderType::Limit,
                "StopLoss" => OrderType::StopLoss,
                "StopLimit" => OrderType::StopLimit,
                _ => OrderType::Market,
            },
            quantity: model.quantity,
            price: model.price,
            stop_price: None,
            status: match model.status.as_str() {
                "Pending" => OrderStatus::Pending,
                "PartiallyFilled" => OrderStatus::PartiallyFilled,
                "Filled" => OrderStatus::Filled,
                "Cancelled" => OrderStatus::Cancelled,
                "Rejected" => OrderStatus::Rejected,
                "Expired" => OrderStatus::Expired,
                _ => OrderStatus::Pending,
            },
            filled_quantity: model.filled_quantity,
            average_price: model.average_price,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }
}

impl From<sigmax_core::Order> for NewOrder {
    fn from(order: sigmax_core::Order) -> Self {
        Self {
            id: order.id,
            trading_pair_id: 1, // 简化处理，实际应该查询trading_pairs表
            order_type: format!("{:?}", order.order_type),
            side: format!("{:?}", order.side),
            quantity: order.quantity,
            price: order.price,
            status: format!("{:?}", order.status),
            filled_quantity: order.filled_quantity,
            average_price: order.average_price,
            exchange_id: Some(order.exchange_id.to_string()),
            exchange_order_id: None,
            strategy_id: order.strategy_id,
            created_at: order.created_at,
            updated_at: order.updated_at,
        }
    }
}
