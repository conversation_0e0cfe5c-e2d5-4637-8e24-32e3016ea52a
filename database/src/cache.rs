//! 缓存实现

use sigmax_core::{SigmaXResult, SigmaXError};
use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::RwLock;
use serde::{Serialize, de::DeserializeOwned};
use std::future::Future;
use async_trait::async_trait;

/// 缓存接口
#[async_trait]
pub trait Cache: Send + Sync {
    /// 设置缓存
    async fn set(&self, key: &str, value: &[u8], ttl: Option<Duration>) -> SigmaXResult<()>;

    /// 获取缓存
    async fn get(&self, key: &str) -> SigmaXResult<Option<Vec<u8>>>;

    /// 删除缓存
    async fn delete(&self, key: &str) -> SigmaXResult<()>;

    /// 清空缓存
    async fn clear(&self) -> SigmaXResult<()>;

    /// 检查键是否存在
    async fn exists(&self, key: &str) -> SigmaXResult<bool>;

    /// 设置过期时间
    async fn expire(&self, key: &str, ttl: Duration) -> SigmaXResult<()>;

    /// 获取缓存统计信息
    async fn stats(&self) -> SigmaXResult<CacheStats>;
}

/// 缓存条目
#[derive(Debug, Clone)]
struct CacheEntry {
    data: Vec<u8>,
    expires_at: Option<Instant>,
    created_at: Instant,
    access_count: u64,
    last_accessed: Instant,
}

impl CacheEntry {
    fn new(data: Vec<u8>, ttl: Option<Duration>) -> Self {
        let now = Instant::now();
        Self {
            data,
            expires_at: ttl.map(|duration| now + duration),
            created_at: now,
            access_count: 0,
            last_accessed: now,
        }
    }

    fn is_expired(&self) -> bool {
        self.expires_at.map_or(false, |expires| Instant::now() > expires)
    }

    fn access(&mut self) -> &[u8] {
        self.access_count += 1;
        self.last_accessed = Instant::now();
        &self.data
    }
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    pub max_entries: usize,
    pub default_ttl: Option<Duration>,
    pub cleanup_interval: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 10000,
            default_ttl: Some(Duration::from_secs(3600)), // 1小时
            cleanup_interval: Duration::from_secs(300),   // 5分钟
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub hit_count: u64,
    pub miss_count: u64,
    pub eviction_count: u64,
    pub memory_usage: usize,
    pub hit_rate: f64,
}

/// 内存缓存实现
pub struct MemoryCache {
    store: Arc<RwLock<HashMap<String, CacheEntry>>>,
    config: CacheConfig,
    stats: Arc<RwLock<CacheStats>>,
}

impl MemoryCache {
    pub fn new() -> Self {
        Self::with_config(CacheConfig::default())
    }

    pub fn with_config(config: CacheConfig) -> Self {
        Self {
            store: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats {
                total_entries: 0,
                hit_count: 0,
                miss_count: 0,
                eviction_count: 0,
                memory_usage: 0,
                hit_rate: 0.0,
            })),
        }
    }

    /// 清理过期条目
    async fn cleanup_expired(&self) -> SigmaXResult<()> {
        let mut store = self.store.write().await;
        let mut stats = self.stats.write().await;

        let initial_count = store.len();
        store.retain(|_, entry| !entry.is_expired());
        let removed_count = initial_count - store.len();

        stats.eviction_count += removed_count as u64;
        stats.total_entries = store.len();

        Ok(())
    }

    /// 驱逐最少使用的条目
    async fn evict_lru(&self) -> SigmaXResult<()> {
        let mut store = self.store.write().await;
        let mut stats = self.stats.write().await;

        if store.len() >= self.config.max_entries {
            // 找到最少访问的条目
            if let Some((lru_key, _)) = store
                .iter()
                .min_by_key(|(_, entry)| (entry.access_count, entry.last_accessed))
                .map(|(k, v)| (k.clone(), v.clone()))
            {
                store.remove(&lru_key);
                stats.eviction_count += 1;
                stats.total_entries = store.len();
            }
        }

        Ok(())
    }

    /// 更新统计信息
    async fn update_stats(&self) -> SigmaXResult<()> {
        let store = self.store.read().await;
        let mut stats = self.stats.write().await;

        stats.total_entries = store.len();
        stats.memory_usage = store
            .values()
            .map(|entry| entry.data.len())
            .sum();

        let total_requests = stats.hit_count + stats.miss_count;
        stats.hit_rate = if total_requests > 0 {
            stats.hit_count as f64 / total_requests as f64
        } else {
            0.0
        };

        Ok(())
    }
}

#[async_trait]
impl Cache for MemoryCache {
    async fn set(&self, key: &str, value: &[u8], ttl: Option<Duration>) -> SigmaXResult<()> {
        // 清理过期条目
        self.cleanup_expired().await?;

        // 如果需要，驱逐LRU条目
        self.evict_lru().await?;

        let mut store = self.store.write().await;
        let ttl = ttl.or(self.config.default_ttl);
        let entry = CacheEntry::new(value.to_vec(), ttl);

        store.insert(key.to_string(), entry);

        // 更新统计信息
        drop(store);
        self.update_stats().await?;

        Ok(())
    }

    async fn get(&self, key: &str) -> SigmaXResult<Option<Vec<u8>>> {
        let mut store = self.store.write().await;
        let mut stats = self.stats.write().await;

        if let Some(entry) = store.get_mut(key) {
            if entry.is_expired() {
                store.remove(key);
                stats.miss_count += 1;
                Ok(None)
            } else {
                let data = entry.access().to_vec();
                stats.hit_count += 1;
                Ok(Some(data))
            }
        } else {
            stats.miss_count += 1;
            Ok(None)
        }
    }

    async fn delete(&self, key: &str) -> SigmaXResult<()> {
        let mut store = self.store.write().await;
        store.remove(key);

        // 更新统计信息
        drop(store);
        self.update_stats().await?;

        Ok(())
    }

    async fn clear(&self) -> SigmaXResult<()> {
        let mut store = self.store.write().await;
        let mut stats = self.stats.write().await;

        store.clear();
        stats.total_entries = 0;
        stats.memory_usage = 0;

        Ok(())
    }

    async fn exists(&self, key: &str) -> SigmaXResult<bool> {
        let store = self.store.read().await;

        if let Some(entry) = store.get(key) {
            Ok(!entry.is_expired())
        } else {
            Ok(false)
        }
    }

    async fn expire(&self, key: &str, ttl: Duration) -> SigmaXResult<()> {
        let mut store = self.store.write().await;

        if let Some(entry) = store.get_mut(key) {
            entry.expires_at = Some(Instant::now() + ttl);
        }

        Ok(())
    }

    async fn stats(&self) -> SigmaXResult<CacheStats> {
        self.update_stats().await?;
        let stats = self.stats.read().await;
        Ok(stats.clone())
    }
}

/// 缓存管理器
pub struct CacheManager {
    cache: Arc<dyn Cache>,
}

impl CacheManager {
    pub fn new(cache: Arc<dyn Cache>) -> Self {
        Self { cache }
    }

    /// 获取或设置缓存（如果不存在则调用工厂函数）
    pub async fn get_or_set<T, F, Fut>(
        &self,
        key: &str,
        factory: F,
        ttl: Option<Duration>,
    ) -> SigmaXResult<T>
    where
        T: Serialize + DeserializeOwned,
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<T>>,
    {
        // 尝试从缓存获取
        if let Some(cached_data) = self.cache.get(key).await? {
            match serde_json::from_slice::<T>(&cached_data) {
                Ok(value) => return Ok(value),
                Err(_) => {
                    // 反序列化失败，删除无效缓存
                    self.cache.delete(key).await?;
                }
            }
        }

        // 缓存未命中，调用工厂函数
        let value = factory().await?;

        // 序列化并存储到缓存
        let serialized = serde_json::to_vec(&value)
            .map_err(|e| SigmaXError::Internal(format!("序列化失败: {}", e)))?;

        self.cache.set(key, &serialized, ttl).await?;

        Ok(value)
    }

    /// 按模式删除缓存键
    pub async fn invalidate_pattern(&self, _pattern: &str) -> SigmaXResult<()> {
        // 注意：这是一个简化实现，实际生产环境可能需要更高效的模式匹配
        // 对于内存缓存，我们需要遍历所有键
        // 在实际实现中，可能需要维护一个键的索引

        // 由于当前的Cache trait没有提供列出所有键的方法，
        // 这里我们返回一个错误，提示需要扩展接口
        Err(SigmaXError::Internal(
            "模式匹配删除需要扩展Cache接口以支持键列表".to_string()
        ))
    }

    /// 获取缓存统计信息
    pub async fn get_stats(&self) -> SigmaXResult<CacheStats> {
        self.cache.stats().await
    }

    /// 设置缓存值（泛型版本）
    pub async fn set<T: Serialize>(
        &self,
        key: &str,
        value: &T,
        ttl: Option<Duration>,
    ) -> SigmaXResult<()> {
        let serialized = serde_json::to_vec(value)
            .map_err(|e| SigmaXError::Internal(format!("序列化失败: {}", e)))?;

        self.cache.set(key, &serialized, ttl).await
    }

    /// 获取缓存值（泛型版本）
    pub async fn get<T: DeserializeOwned>(&self, key: &str) -> SigmaXResult<Option<T>> {
        if let Some(data) = self.cache.get(key).await? {
            let value = serde_json::from_slice::<T>(&data)
                .map_err(|e| SigmaXError::Internal(format!("反序列化失败: {}", e)))?;
            Ok(Some(value))
        } else {
            Ok(None)
        }
    }

    /// 删除缓存
    pub async fn delete(&self, key: &str) -> SigmaXResult<()> {
        self.cache.delete(key).await
    }

    /// 检查键是否存在
    pub async fn exists(&self, key: &str) -> SigmaXResult<bool> {
        self.cache.exists(key).await
    }

    /// 设置过期时间
    pub async fn expire(&self, key: &str, ttl: Duration) -> SigmaXResult<()> {
        self.cache.expire(key, ttl).await
    }

    /// 清空所有缓存
    pub async fn clear(&self) -> SigmaXResult<()> {
        self.cache.clear().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_memory_cache_basic_operations() {
        let cache = MemoryCache::new();

        // 测试设置和获取
        cache.set("key1", b"value1", None).await.unwrap();
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, Some(b"value1".to_vec()));

        // 测试不存在的键
        let result = cache.get("nonexistent").await.unwrap();
        assert_eq!(result, None);

        // 测试删除
        cache.delete("key1").await.unwrap();
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, None);
    }

    #[tokio::test]
    async fn test_memory_cache_ttl() {
        let cache = MemoryCache::new();

        // 设置带TTL的缓存
        cache.set("key1", b"value1", Some(Duration::from_millis(100))).await.unwrap();

        // 立即获取应该成功
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, Some(b"value1".to_vec()));

        // 等待过期
        sleep(Duration::from_millis(150)).await;

        // 过期后应该返回None
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, None);
    }

    #[tokio::test]
    async fn test_memory_cache_exists() {
        let cache = MemoryCache::new();

        // 测试不存在的键
        assert!(!cache.exists("key1").await.unwrap());

        // 设置键
        cache.set("key1", b"value1", None).await.unwrap();
        assert!(cache.exists("key1").await.unwrap());

        // 删除键
        cache.delete("key1").await.unwrap();
        assert!(!cache.exists("key1").await.unwrap());
    }

    #[tokio::test]
    async fn test_memory_cache_expire() {
        let cache = MemoryCache::new();

        // 设置永久缓存
        cache.set("key1", b"value1", None).await.unwrap();

        // 设置过期时间
        cache.expire("key1", Duration::from_millis(100)).await.unwrap();

        // 立即获取应该成功
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, Some(b"value1".to_vec()));

        // 等待过期
        sleep(Duration::from_millis(150)).await;

        // 过期后应该返回None
        let result = cache.get("key1").await.unwrap();
        assert_eq!(result, None);
    }

    #[tokio::test]
    async fn test_memory_cache_clear() {
        let cache = MemoryCache::new();

        // 设置多个键
        cache.set("key1", b"value1", None).await.unwrap();
        cache.set("key2", b"value2", None).await.unwrap();

        // 验证键存在
        assert!(cache.exists("key1").await.unwrap());
        assert!(cache.exists("key2").await.unwrap());

        // 清空缓存
        cache.clear().await.unwrap();

        // 验证键不存在
        assert!(!cache.exists("key1").await.unwrap());
        assert!(!cache.exists("key2").await.unwrap());
    }

    #[tokio::test]
    async fn test_memory_cache_stats() {
        let cache = MemoryCache::new();

        // 初始统计
        let stats = cache.stats().await.unwrap();
        assert_eq!(stats.total_entries, 0);
        assert_eq!(stats.hit_count, 0);
        assert_eq!(stats.miss_count, 0);

        // 设置一些数据
        cache.set("key1", b"value1", None).await.unwrap();
        cache.set("key2", b"value2", None).await.unwrap();

        // 命中和未命中
        cache.get("key1").await.unwrap(); // 命中
        cache.get("nonexistent").await.unwrap(); // 未命中

        let stats = cache.stats().await.unwrap();
        assert_eq!(stats.total_entries, 2);
        assert_eq!(stats.hit_count, 1);
        assert_eq!(stats.miss_count, 1);
        assert_eq!(stats.hit_rate, 0.5);
    }

    #[tokio::test]
    async fn test_cache_manager_get_or_set() {
        let cache = Arc::new(MemoryCache::new());
        let manager = CacheManager::new(cache);

        // 第一次调用应该执行工厂函数
        let result = manager.get_or_set::<String, _, _>(
            "key1",
            || async { Ok("computed_value".to_string()) },
            None,
        ).await.unwrap();
        assert_eq!(result, "computed_value");

        // 第二次调用应该从缓存获取
        let result = manager.get_or_set::<String, _, _>(
            "key1",
            || async { Ok("should_not_be_called".to_string()) },
            None,
        ).await.unwrap();
        assert_eq!(result, "computed_value");
    }

    #[tokio::test]
    async fn test_cache_manager_typed_operations() {
        let cache = Arc::new(MemoryCache::new());
        let manager = CacheManager::new(cache);

        // 测试设置和获取不同类型
        manager.set("string_key", &"hello".to_string(), None).await.unwrap();
        manager.set("number_key", &42i32, None).await.unwrap();
        manager.set("bool_key", &true, None).await.unwrap();

        // 获取并验证类型
        let string_val: Option<String> = manager.get("string_key").await.unwrap();
        assert_eq!(string_val, Some("hello".to_string()));

        let number_val: Option<i32> = manager.get("number_key").await.unwrap();
        assert_eq!(number_val, Some(42));

        let bool_val: Option<bool> = manager.get("bool_key").await.unwrap();
        assert_eq!(bool_val, Some(true));

        // 测试不存在的键
        let nonexistent: Option<String> = manager.get("nonexistent").await.unwrap();
        assert_eq!(nonexistent, None);
    }
}
