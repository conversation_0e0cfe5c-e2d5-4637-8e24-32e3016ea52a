//! 缓存配置仓储接口定义
//!
//! 专门负责缓存配置的数据访问和管理

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use sigmax_core::{SigmaXResult, CacheConfig};
use uuid::Uuid;

/// 缓存统计记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStatsRecord {
    /// 记录ID
    pub id: Uuid,
    /// 统计时间戳
    pub timestamp: DateTime<Utc>,
    /// 缓存类型
    pub cache_type: String,
    /// 总键数
    pub total_keys: u64,
    /// 内存使用量(MB)
    pub memory_usage_mb: f64,
    /// 内存限制(MB)
    pub memory_limit_mb: f64,
    /// 内存使用率
    pub memory_usage_percentage: f64,
    /// 命中率
    pub hit_rate: f64,
    /// 未命中率
    pub miss_rate: f64,
    /// 总操作数
    pub total_operations: u64,
    /// 命中次数
    pub hits: u64,
    /// 未命中次数
    pub misses: u64,
    /// 淘汰次数
    pub evictions: u64,
    /// 过期键数
    pub expired_keys: u64,
    /// 平均TTL(秒)
    pub average_ttl_seconds: f64,
    /// 运行时间(秒)
    pub uptime_seconds: u64,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 缓存性能记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachePerformanceRecord {
    /// 记录ID
    pub id: Uuid,
    /// 测量时间戳
    pub timestamp: DateTime<Utc>,
    /// 缓存类型
    pub cache_type: String,
    /// 平均响应时间(毫秒)
    pub average_response_time_ms: f64,
    /// P95响应时间(毫秒)
    pub p95_response_time_ms: f64,
    /// P99响应时间(毫秒)
    pub p99_response_time_ms: f64,
    /// 吞吐量(操作/秒)
    pub throughput_ops_per_second: f64,
    /// 错误率
    pub error_rate: f64,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 缓存健康检查记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheHealthRecord {
    /// 记录ID
    pub id: Uuid,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 健康状态
    pub status: String,
    /// 内存压力级别
    pub memory_pressure_level: String,
    /// 内存使用率
    pub memory_usage_percentage: f64,
    /// 淘汰率
    pub eviction_rate: f64,
    /// 碎片率
    pub fragmentation_ratio: f64,
    /// 检测到的问题
    pub issues: serde_json::Value,
    /// 建议措施
    pub recommendations: serde_json::Value,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 缓存告警记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheAlertRecord {
    /// 告警ID
    pub id: Uuid,
    /// 告警时间戳
    pub timestamp: DateTime<Utc>,
    /// 告警规则名称
    pub rule_name: String,
    /// 告警级别
    pub severity: String,
    /// 告警条件
    pub condition: String,
    /// 当前值
    pub current_value: f64,
    /// 阈值
    pub threshold_value: f64,
    /// 告警描述
    pub description: String,
    /// 是否已解决
    pub resolved: bool,
    /// 解决时间
    pub resolved_at: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 缓存配置仓储接口
/// 
/// 提供缓存配置的专业化数据访问功能
#[async_trait]
pub trait CacheRepository: Send + Sync {
    // ============================================================================
    // 缓存配置管理
    // ============================================================================

    /// 获取缓存配置
    async fn get_config(&self) -> SigmaXResult<CacheConfig>;

    /// 保存缓存配置
    async fn save_config(&self, config: &CacheConfig) -> SigmaXResult<()>;

    /// 更新缓存配置
    async fn update_config(&self, config: &CacheConfig) -> SigmaXResult<()>;

    /// 删除缓存配置
    async fn delete_config(&self) -> SigmaXResult<()>;

    // ============================================================================
    // 缓存统计管理
    // ============================================================================

    /// 记录缓存统计
    async fn record_stats(&self, stats: &CacheStatsRecord) -> SigmaXResult<()>;

    /// 获取最新缓存统计
    async fn get_latest_stats(&self, cache_type: Option<&str>) -> SigmaXResult<Vec<CacheStatsRecord>>;

    /// 获取历史缓存统计
    async fn get_stats_history(
        &self,
        cache_type: Option<&str>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheStatsRecord>>;

    /// 清理过期统计数据
    async fn cleanup_old_stats(&self, retention_days: u32) -> SigmaXResult<u64>;

    // ============================================================================
    // 缓存性能监控
    // ============================================================================

    /// 记录性能数据
    async fn record_performance(&self, performance: &CachePerformanceRecord) -> SigmaXResult<()>;

    /// 获取性能历史
    async fn get_performance_history(
        &self,
        cache_type: Option<&str>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CachePerformanceRecord>>;

    // ============================================================================
    // 缓存健康检查
    // ============================================================================

    /// 记录健康检查结果
    async fn record_health_check(&self, health: &CacheHealthRecord) -> SigmaXResult<()>;

    /// 获取最新健康状态
    async fn get_latest_health(&self) -> SigmaXResult<Option<CacheHealthRecord>>;

    /// 获取健康检查历史
    async fn get_health_history(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheHealthRecord>>;

    // ============================================================================
    // 缓存告警管理
    // ============================================================================

    /// 创建告警记录
    async fn create_alert(&self, alert: &CacheAlertRecord) -> SigmaXResult<()>;

    /// 解决告警
    async fn resolve_alert(&self, alert_id: Uuid) -> SigmaXResult<()>;

    /// 获取活跃告警
    async fn get_active_alerts(&self) -> SigmaXResult<Vec<CacheAlertRecord>>;

    /// 获取告警历史
    async fn get_alert_history(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheAlertRecord>>;

    // ============================================================================
    // 缓存管理操作
    // ============================================================================

    /// 清理缓存数据
    async fn clear_cache_data(&self, cache_types: Option<Vec<String>>) -> SigmaXResult<u64>;

    /// 获取缓存键信息
    async fn get_cache_keys(
        &self,
        cache_type: Option<&str>,
        pattern: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> SigmaXResult<Vec<serde_json::Value>>;

    /// 预热缓存
    async fn warm_cache(
        &self,
        cache_types: Vec<String>,
        priority_keys: Option<Vec<String>>,
    ) -> SigmaXResult<()>;
}
