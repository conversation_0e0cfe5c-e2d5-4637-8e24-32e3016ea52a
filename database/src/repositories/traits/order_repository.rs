//! 订单仓储接口定义

use sigmax_core::{Order, OrderId, OrderStatus, StrategyId, SigmaXResult};

/// 订单仓库
pub trait OrderRepository: Send + Sync {
    /// 保存订单
    async fn save_order(&self, order: &Order) -> SigmaXResult<()>;

    /// 获取订单
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<Order>>;

    /// 获取所有订单
    async fn get_all_orders(&self) -> SigmaXResult<Vec<Order>>;

    /// 按状态获取订单
    async fn get_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>>;

    /// 按策略获取订单
    async fn get_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>>;
}
