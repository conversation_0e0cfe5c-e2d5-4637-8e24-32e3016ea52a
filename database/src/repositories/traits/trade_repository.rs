//! 交易记录仓储接口定义

use chrono::{DateTime, Utc};
use sigmax_core::{Trade, TradingPair, SigmaXResult};

/// 交易记录仓库
pub trait TradeRepository: Send + Sync {
    /// 保存交易记录
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()>;

    /// 获取所有交易记录
    async fn get_trades(&self) -> SigmaXResult<Vec<Trade>>;

    /// 按交易对获取交易记录
    async fn get_trades_by_pair(&self, pair: &TradingPair) -> SigmaXResult<Vec<Trade>>;

    /// 按时间范围获取交易记录
    async fn get_trades_by_timerange(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Trade>>;
}
