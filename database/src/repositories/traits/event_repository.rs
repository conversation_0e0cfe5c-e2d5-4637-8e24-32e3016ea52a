//! 事件仓储接口定义

use sigmax_core::{StoredEvent, EventId, EventType, SigmaXResult};

/// 事件仓库
pub trait EventRepository: Send + Sync {
    /// 保存事件
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId>;

    /// 获取事件
    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>>;

    /// 获取事件列表（分页）
    async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;

    /// 按类型获取事件
    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;

    /// 按时间范围获取事件
    async fn get_events_by_timerange(&self, start: u64, end: u64, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;

    /// 标记事件为已处理
    async fn mark_event_processed(&self, event_id: EventId) -> SigmaXResult<()>;

    /// 获取未处理的事件
    async fn get_unprocessed_events(&self, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
}
