//! 风险管理仓储接口定义
//!
//! 基于现有数据库表结构设计的风险管理数据访问接口

use async_trait::async_trait;
use chrono::{DateTime, Utc, NaiveDate};
use serde::{Serialize, Deserialize};
use sigmax_core::SigmaXResult;
use uuid::Uuid;

/// 风险检查记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckRecord {
    /// 检查ID
    pub id: Uuid,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 交易对
    pub trading_pair: String,
    /// 交易方向
    pub side: String,
    /// 数量
    pub quantity: rust_decimal::Decimal,
    /// 价格
    pub price: Option<rust_decimal::Decimal>,
    /// 订单类型
    pub order_type: String,
    /// 是否通过检查
    pub passed: bool,
    /// 风险评分
    pub risk_score: rust_decimal::Decimal,
    /// 违规信息
    pub violations: serde_json::Value,
    /// 警告信息
    pub warnings: serde_json::Value,
    /// 建议信息
    pub recommendations: serde_json::Value,
    /// 最大允许数量
    pub max_allowed_quantity: Option<rust_decimal::Decimal>,
    /// 建议最小价格
    pub suggested_price_min: Option<rust_decimal::Decimal>,
    /// 建议最大价格
    pub suggested_price_max: Option<rust_decimal::Decimal>,
    /// 处理时间（毫秒）
    pub processing_time_ms: Option<i32>,
    /// 策略ID
    pub strategy_id: Option<Uuid>,
    /// 引擎ID
    pub engine_id: Option<Uuid>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 风险规则配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskRuleRecord {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub name: String,
    /// 规则类型
    pub rule_type: String,
    /// 规则描述
    pub description: Option<String>,
    /// 规则参数
    pub parameters: serde_json::Value,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: i32,
    /// 创建者
    pub created_by: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 风险统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskStatisticsRecord {
    /// 统计ID
    pub id: Uuid,
    /// 统计日期
    pub date: NaiveDate,
    /// 总检查次数
    pub total_checks: i32,
    /// 通过检查次数
    pub passed_checks: i32,
    /// 失败检查次数
    pub failed_checks: i32,
    /// 通过率
    pub pass_rate: rust_decimal::Decimal,
    /// 平均风险评分
    pub average_risk_score: rust_decimal::Decimal,
    /// 主要违规类型
    pub top_violations: serde_json::Value,
    /// 风险评分分布
    pub risk_score_distribution: serde_json::Value,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 风险违规记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskViolationRecord {
    /// 违规ID
    pub id: Uuid,
    /// 关联的风险检查ID
    pub risk_check_id: Uuid,
    /// 违反的规则ID
    pub rule_id: Uuid,
    /// 规则名称
    pub rule_name: String,
    /// 严重程度
    pub severity: String,
    /// 违规消息
    pub message: String,
    /// 当前值
    pub current_value: Option<String>,
    /// 限制值
    pub limit_value: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 风险查询过滤器
#[derive(Debug, Clone, Default)]
pub struct RiskQueryFilter {
    /// 交易对过滤
    pub trading_pair: Option<String>,
    /// 策略ID过滤
    pub strategy_id: Option<Uuid>,
    /// 引擎ID过滤
    pub engine_id: Option<Uuid>,
    /// 是否通过过滤
    pub passed: Option<bool>,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 最小风险评分
    pub min_risk_score: Option<rust_decimal::Decimal>,
    /// 最大风险评分
    pub max_risk_score: Option<rust_decimal::Decimal>,
}

/// 风险规则查询过滤器
#[derive(Debug, Clone, Default)]
pub struct RiskRuleFilter {
    /// 规则类型过滤
    pub rule_type: Option<String>,
    /// 是否启用过滤
    pub enabled: Option<bool>,
    /// 创建者过滤
    pub created_by: Option<String>,
}

/// 分页参数
#[derive(Debug, Clone)]
pub struct Pagination {
    /// 偏移量
    pub offset: usize,
    /// 限制数量
    pub limit: usize,
}

/// 风险仓储接口
#[async_trait]
pub trait RiskRepository: Send + Sync {
    // ============================================================================
    // 风险检查记录管理
    // ============================================================================
    
    /// 保存风险检查记录
    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()>;
    
    /// 根据ID获取风险检查记录
    async fn get_risk_check(&self, id: Uuid) -> SigmaXResult<Option<RiskCheckRecord>>;
    
    /// 根据条件查询风险检查记录
    async fn find_risk_checks(
        &self,
        filter: &RiskQueryFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskCheckRecord>>;
    
    /// 统计风险检查记录数量
    async fn count_risk_checks(&self, filter: &RiskQueryFilter) -> SigmaXResult<u64>;
    
    // ============================================================================
    // 风险规则管理
    // ============================================================================
    
    /// 保存风险规则
    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()>;
    
    /// 根据ID获取风险规则
    async fn get_risk_rule(&self, id: Uuid) -> SigmaXResult<Option<RiskRuleRecord>>;
    
    /// 获取所有启用的风险规则
    async fn get_enabled_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 根据类型获取风险规则
    async fn get_risk_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 根据条件查询风险规则
    async fn find_risk_rules(
        &self,
        filter: &RiskRuleFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 更新风险规则状态
    async fn update_risk_rule_status(&self, id: Uuid, enabled: bool) -> SigmaXResult<()>;
    
    /// 删除风险规则
    async fn delete_risk_rule(&self, id: Uuid) -> SigmaXResult<()>;
    
    // ============================================================================
    // 风险统计数据管理
    // ============================================================================
    
    /// 保存风险统计数据
    async fn save_risk_statistics(&self, stats: &RiskStatisticsRecord) -> SigmaXResult<()>;
    
    /// 根据日期获取风险统计数据
    async fn get_risk_statistics_by_date(&self, date: NaiveDate) -> SigmaXResult<Option<RiskStatisticsRecord>>;
    
    /// 获取日期范围内的风险统计数据
    async fn get_risk_statistics_by_range(
        &self,
        start_date: NaiveDate,
        end_date: NaiveDate
    ) -> SigmaXResult<Vec<RiskStatisticsRecord>>;
    
    // ============================================================================
    // 风险违规记录管理
    // ============================================================================

    /// 保存风险违规记录
    async fn save_risk_violation(&self, violation: &RiskViolationRecord) -> SigmaXResult<()>;

    /// 根据风险检查ID获取违规记录
    async fn get_violations_by_check_id(&self, check_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>>;

    /// 根据规则ID获取违规记录
    async fn get_violations_by_rule_id(&self, rule_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>>;

    /// 获取最近的违规记录
    async fn get_recent_violations(&self, limit: usize) -> SigmaXResult<Vec<RiskViolationRecord>>;

    // ============================================================================
    // 风险配置管理 - 新增的统一配置管理接口
    // ============================================================================

    /// 获取风险配置
    async fn get_risk_config(&self) -> SigmaXResult<sigmax_core::RiskManagementConfig>;

    /// 根据ID获取风险配置
    async fn get_risk_config_by_id(&self, id: Uuid) -> SigmaXResult<Option<sigmax_core::RiskManagementConfig>>;

    /// 根据策略类型获取风险配置
    async fn get_risk_config_by_strategy_type(&self, strategy_type: &str) -> SigmaXResult<Option<sigmax_core::RiskManagementConfig>>;

    /// 保存风险配置
    async fn save_risk_config(&self, config: &sigmax_core::RiskManagementConfig) -> SigmaXResult<()>;

    /// 更新风险配置
    async fn update_risk_config(&self, id: Uuid, config: &sigmax_core::RiskManagementConfig) -> SigmaXResult<()>;

    /// 删除风险配置
    async fn delete_risk_config(&self, id: Uuid) -> SigmaXResult<()>;

    /// 获取所有启用的风险配置
    async fn get_enabled_risk_configs(&self) -> SigmaXResult<Vec<sigmax_core::RiskManagementConfig>>;

    /// 重置风险配置为默认值
    async fn reset_risk_config(&self) -> SigmaXResult<()>;

}
