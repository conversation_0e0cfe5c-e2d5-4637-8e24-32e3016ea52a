//! 通知仓储接口定义
//!
//! 专门负责通知相关数据的访问和管理

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, NotificationConfig};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

/// 通知记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationRecord {
    /// 通知ID
    pub id: Uuid,
    /// 通知类型
    pub notification_type: String,
    /// 接收者
    pub recipient: String,
    /// 标题
    pub title: String,
    /// 内容
    pub content: String,
    /// 发送状态
    pub status: NotificationStatus,
    /// 重试次数
    pub retry_count: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 发送时间
    pub sent_at: Option<DateTime<Utc>>,
}

/// 通知状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationStatus {
    /// 待发送
    Pending,
    /// 已发送
    Sent,
    /// 发送失败
    Failed,
    /// 已取消
    Cancelled,
}

/// 通知仓储接口
#[async_trait]
pub trait NotificationRepository: Send + Sync {
    // ============================================================================
    // 通知配置管理
    // ============================================================================

    /// 获取通知配置
    async fn get_notification_config(&self) -> SigmaXResult<NotificationConfig>;

    /// 保存通知配置
    async fn save_notification_config(&self, config: &NotificationConfig) -> SigmaXResult<()>;

    /// 重置通知配置为默认值
    async fn reset_notification_config(&self) -> SigmaXResult<()>;

    // ============================================================================
    // 通知记录管理
    // ============================================================================

    /// 保存通知记录
    async fn save_notification(&self, notification: &NotificationRecord) -> SigmaXResult<()>;

    /// 根据ID获取通知记录
    async fn get_notification(&self, id: Uuid) -> SigmaXResult<Option<NotificationRecord>>;

    /// 获取待发送的通知
    async fn get_pending_notifications(&self, limit: usize) -> SigmaXResult<Vec<NotificationRecord>>;

    /// 更新通知状态
    async fn update_notification_status(&self, id: Uuid, status: NotificationStatus, sent_at: Option<DateTime<Utc>>) -> SigmaXResult<()>;

    /// 增加重试次数
    async fn increment_retry_count(&self, id: Uuid) -> SigmaXResult<()>;

    /// 获取通知历史
    async fn get_notification_history(&self, limit: usize, offset: usize) -> SigmaXResult<Vec<NotificationRecord>>;

    /// 删除过期通知
    async fn delete_expired_notifications(&self, before: DateTime<Utc>) -> SigmaXResult<u64>;
}
