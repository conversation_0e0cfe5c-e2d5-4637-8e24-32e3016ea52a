//! Diesel 交易记录仓储实现

use std::sync::Arc;
use diesel::prelude::*;
use chrono::{DateTime, Utc};
use sigmax_core::{
    Trade, TradingPair, SigmaXResult, SigmaXError
};

use crate::repositories::traits::TradeRepository;
use crate::diesel_models::*;
use crate::schema::*;
use super::{DbPool, DbConnection};

/// Diesel 交易仓库实现
pub struct DieselTradeRepository {
    pool: Arc<DbPool>,
}

impl DieselTradeRepository {
    pub fn new(pool: Arc<DbPool>) -> Self {
        Self { pool }
    }
    
    fn get_connection(&self) -> SigmaXResult<DbConnection> {
        self.pool.get()
            .map_err(|e| SigmaXError::Database(format!("Failed to get database connection: {}", e)))
    }
}

impl TradeRepository for DieselTradeRepository {
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        let mut conn = self.get_connection()?;
        let new_trade = NewTrade {
            id: trade.id,
            order_id: trade.order_id,
            trading_pair_id: 1, // 简化处理
            side: format!("{:?}", trade.side),
            quantity: trade.quantity,
            price: trade.price,
            fee: trade.fee,
            fee_asset: trade.fee_asset.clone(),
            exchange_id: trade.exchange_id.to_string(),
            exchange_trade_id: None,
            executed_at: trade.executed_at,
            created_at: trade.created_at,
        };
        
        diesel::insert_into(trades::table)
            .values(&new_trade)
            .on_conflict(trades::id)
            .do_nothing()
            .execute(&mut conn)
            .map_err(|e| SigmaXError::Database(format!("Failed to save trade: {}", e)))?;
        
        Ok(())
    }
    
    async fn get_trades(&self) -> SigmaXResult<Vec<Trade>> {
        let mut conn = self.get_connection()?;
        
        let trade_models = trades::table
            .order(trades::executed_at.desc())
            .load::<TradeModel>(&mut conn)
            .map_err(|e| SigmaXError::Database(format!("Failed to get trades: {}", e)))?;
        
        // 简化转换，实际应该实现完整的From trait
        let trades = trade_models.into_iter().map(|model| Trade {
            id: model.id,
            order_id: model.order_id,
            exchange_id: sigmax_core::ExchangeId::from(model.exchange_id),
            trading_pair: TradingPair::new("BTC", "USDT"), // 简化处理
            side: match model.side.as_str() {
                "Buy" => sigmax_core::OrderSide::Buy,
                "Sell" => sigmax_core::OrderSide::Sell,
                _ => sigmax_core::OrderSide::Buy,
            },
            quantity: model.quantity,
            price: model.price,
            fee: model.fee,
            fee_asset: model.fee_asset,
            executed_at: model.executed_at,
            created_at: model.created_at,
        }).collect();
        
        Ok(trades)
    }
    
    async fn get_trades_by_pair(&self, _pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        // 简化实现，实际应该根据交易对过滤
        self.get_trades().await
    }
    
    async fn get_trades_by_timerange(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Trade>> {
        let mut conn = self.get_connection()?;
        
        let trade_models = trades::table
            .filter(trades::executed_at.between(start, end))
            .order(trades::executed_at.desc())
            .load::<TradeModel>(&mut conn)
            .map_err(|e| SigmaXError::Database(format!("Failed to get trades by timerange: {}", e)))?;
        
        // 简化转换
        let trades = trade_models.into_iter().map(|model| Trade {
            id: model.id,
            order_id: model.order_id,
            exchange_id: sigmax_core::ExchangeId::from(model.exchange_id),
            trading_pair: TradingPair::new("BTC", "USDT"),
            side: match model.side.as_str() {
                "Buy" => sigmax_core::OrderSide::Buy,
                "Sell" => sigmax_core::OrderSide::Sell,
                _ => sigmax_core::OrderSide::Buy,
            },
            quantity: model.quantity,
            price: model.price,
            fee: model.fee,
            fee_asset: model.fee_asset,
            executed_at: model.executed_at,
            created_at: model.created_at,
        }).collect();
        
        Ok(trades)
    }
}
