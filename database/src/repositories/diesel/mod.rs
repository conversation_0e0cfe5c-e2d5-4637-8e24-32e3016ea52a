//! Diesel ORM Repository Implementations
//!
//! 基于 Diesel ORM 的数据仓储实现

pub mod diesel_order_repository;
pub mod diesel_trade_repository;
pub mod diesel_strategy_repository;

// 重新导出所有实现
pub use diesel_order_repository::DieselOrderRepository;
pub use diesel_trade_repository::DieselTradeRepository;
pub use diesel_strategy_repository::DieselStrategyRepository;

// 公共类型别名
use diesel::r2d2::{ConnectionManager, Pool};
use diesel::PgConnection;

/// 数据库连接池类型
pub type DbPool = Pool<ConnectionManager<PgConnection>>;

/// 数据库连接类型
pub type DbConnection = diesel::r2d2::PooledConnection<ConnectionManager<PgConnection>>;
