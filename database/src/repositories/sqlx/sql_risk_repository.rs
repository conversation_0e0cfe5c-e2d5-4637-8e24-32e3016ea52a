//! SQL 风险管理仓储实现
//!
//! 基于 SQLx 的风险管理数据访问实现

use std::sync::Arc;
use async_trait::async_trait;
use chrono::NaiveDate;
use sqlx::Row;
use sigmax_core::{SigmaXResult, SigmaXError, RiskManagementConfig};
use uuid::Uuid;

use crate::DatabaseManager;
use crate::repositories::traits::risk_repository::{
    RiskRepository, RiskCheckRecord, RiskRuleRecord, RiskStatisticsRecord, 
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter, Pagination
};

/// SQL 风险仓储实现
pub struct SqlRiskRepository {
    db: Arc<DatabaseManager>,
}

impl SqlRiskRepository {
    /// 创建新的 SQL 风险仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为风险检查记录
    fn row_to_risk_check(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskCheckRecord> {
        Ok(RiskCheckRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            trading_pair: row.try_get("trading_pair")
                .map_err(|e| SigmaXError::Database(format!("获取trading_pair失败: {}", e)))?,
            side: row.try_get("side")
                .map_err(|e| SigmaXError::Database(format!("获取side失败: {}", e)))?,
            quantity: row.try_get("quantity")
                .map_err(|e| SigmaXError::Database(format!("获取quantity失败: {}", e)))?,
            price: row.try_get("price")
                .map_err(|e| SigmaXError::Database(format!("获取price失败: {}", e)))?,
            order_type: row.try_get("order_type")
                .map_err(|e| SigmaXError::Database(format!("获取order_type失败: {}", e)))?,
            passed: row.try_get("passed")
                .map_err(|e| SigmaXError::Database(format!("获取passed失败: {}", e)))?,
            risk_score: row.try_get("risk_score")
                .map_err(|e| SigmaXError::Database(format!("获取risk_score失败: {}", e)))?,
            violations: row.try_get("violations")
                .map_err(|e| SigmaXError::Database(format!("获取violations失败: {}", e)))?,
            warnings: row.try_get("warnings")
                .map_err(|e| SigmaXError::Database(format!("获取warnings失败: {}", e)))?,
            recommendations: row.try_get("recommendations")
                .map_err(|e| SigmaXError::Database(format!("获取recommendations失败: {}", e)))?,
            max_allowed_quantity: row.try_get("max_allowed_quantity")
                .map_err(|e| SigmaXError::Database(format!("获取max_allowed_quantity失败: {}", e)))?,
            suggested_price_min: row.try_get("suggested_price_min")
                .map_err(|e| SigmaXError::Database(format!("获取suggested_price_min失败: {}", e)))?,
            suggested_price_max: row.try_get("suggested_price_max")
                .map_err(|e| SigmaXError::Database(format!("获取suggested_price_max失败: {}", e)))?,
            processing_time_ms: row.try_get("processing_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取processing_time_ms失败: {}", e)))?,
            strategy_id: row.try_get("strategy_id")
                .map_err(|e| SigmaXError::Database(format!("获取strategy_id失败: {}", e)))?,
            engine_id: row.try_get("engine_id")
                .map_err(|e| SigmaXError::Database(format!("获取engine_id失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为风险规则记录
    fn row_to_risk_rule(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskRuleRecord> {
        Ok(RiskRuleRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            name: row.try_get("name")
                .map_err(|e| SigmaXError::Database(format!("获取name失败: {}", e)))?,
            rule_type: row.try_get("rule_type")
                .map_err(|e| SigmaXError::Database(format!("获取rule_type失败: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::Database(format!("获取description失败: {}", e)))?,
            parameters: row.try_get("parameters")
                .map_err(|e| SigmaXError::Database(format!("获取parameters失败: {}", e)))?,
            enabled: row.try_get("enabled")
                .map_err(|e| SigmaXError::Database(format!("获取enabled失败: {}", e)))?,
            priority: row.try_get("priority")
                .map_err(|e| SigmaXError::Database(format!("获取priority失败: {}", e)))?,
            created_by: row.try_get("created_by")
                .map_err(|e| SigmaXError::Database(format!("获取created_by失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为风险统计记录
    fn row_to_risk_statistics(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskStatisticsRecord> {
        Ok(RiskStatisticsRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            date: row.try_get("date")
                .map_err(|e| SigmaXError::Database(format!("获取date失败: {}", e)))?,
            total_checks: row.try_get("total_checks")
                .map_err(|e| SigmaXError::Database(format!("获取total_checks失败: {}", e)))?,
            passed_checks: row.try_get("passed_checks")
                .map_err(|e| SigmaXError::Database(format!("获取passed_checks失败: {}", e)))?,
            failed_checks: row.try_get("failed_checks")
                .map_err(|e| SigmaXError::Database(format!("获取failed_checks失败: {}", e)))?,
            pass_rate: row.try_get("pass_rate")
                .map_err(|e| SigmaXError::Database(format!("获取pass_rate失败: {}", e)))?,
            average_risk_score: row.try_get("average_risk_score")
                .map_err(|e| SigmaXError::Database(format!("获取average_risk_score失败: {}", e)))?,
            top_violations: row.try_get("top_violations")
                .map_err(|e| SigmaXError::Database(format!("获取top_violations失败: {}", e)))?,
            risk_score_distribution: row.try_get("risk_score_distribution")
                .map_err(|e| SigmaXError::Database(format!("获取risk_score_distribution失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为风险违规记录
    fn row_to_risk_violation(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskViolationRecord> {
        Ok(RiskViolationRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            risk_check_id: row.try_get("risk_check_id")
                .map_err(|e| SigmaXError::Database(format!("获取risk_check_id失败: {}", e)))?,
            rule_id: row.try_get("rule_id")
                .map_err(|e| SigmaXError::Database(format!("获取rule_id失败: {}", e)))?,
            rule_name: row.try_get("rule_name")
                .map_err(|e| SigmaXError::Database(format!("获取rule_name失败: {}", e)))?,
            severity: row.try_get("severity")
                .map_err(|e| SigmaXError::Database(format!("获取severity失败: {}", e)))?,
            message: row.try_get("message")
                .map_err(|e| SigmaXError::Database(format!("获取message失败: {}", e)))?,
            current_value: row.try_get("current_value")
                .map_err(|e| SigmaXError::Database(format!("获取current_value失败: {}", e)))?,
            limit_value: row.try_get("limit_value")
                .map_err(|e| SigmaXError::Database(format!("获取limit_value失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
        })
    }

    /// 构建风险检查查询的 WHERE 子句
    fn build_risk_check_where_clause(&self, filter: &RiskQueryFilter) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(trading_pair) = &filter.trading_pair {
            conditions.push(format!("trading_pair = ${}", param_index));
            params.push(trading_pair.clone());
            param_index += 1;
        }

        if let Some(strategy_id) = &filter.strategy_id {
            conditions.push(format!("strategy_id = ${}", param_index));
            params.push(strategy_id.to_string());
            param_index += 1;
        }

        if let Some(engine_id) = &filter.engine_id {
            conditions.push(format!("engine_id = ${}", param_index));
            params.push(engine_id.to_string());
            param_index += 1;
        }

        if let Some(passed) = filter.passed {
            conditions.push(format!("passed = ${}", param_index));
            params.push(passed.to_string());
            param_index += 1;
        }

        if let Some(start_time) = &filter.start_time {
            conditions.push(format!("timestamp >= ${}", param_index));
            params.push(start_time.to_rfc3339());
            param_index += 1;
        }

        if let Some(end_time) = &filter.end_time {
            conditions.push(format!("timestamp <= ${}", param_index));
            params.push(end_time.to_rfc3339());
            param_index += 1;
        }

        if let Some(min_score) = &filter.min_risk_score {
            conditions.push(format!("risk_score >= ${}", param_index));
            params.push(min_score.to_string());
            param_index += 1;
        }

        if let Some(max_score) = &filter.max_risk_score {
            conditions.push(format!("risk_score <= ${}", param_index));
            params.push(max_score.to_string());
            param_index += 1;
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }
}

#[async_trait]
impl RiskRepository for SqlRiskRepository {
    // ============================================================================
    // 风险检查记录管理
    // ============================================================================

    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_checks (
                id, timestamp, trading_pair, side, quantity, price, order_type,
                passed, risk_score, violations, warnings, recommendations,
                max_allowed_quantity, suggested_price_min, suggested_price_max,
                processing_time_ms, strategy_id, engine_id, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
            )
            ON CONFLICT (id) DO UPDATE SET
                timestamp = EXCLUDED.timestamp,
                trading_pair = EXCLUDED.trading_pair,
                side = EXCLUDED.side,
                quantity = EXCLUDED.quantity,
                price = EXCLUDED.price,
                order_type = EXCLUDED.order_type,
                passed = EXCLUDED.passed,
                risk_score = EXCLUDED.risk_score,
                violations = EXCLUDED.violations,
                warnings = EXCLUDED.warnings,
                recommendations = EXCLUDED.recommendations,
                max_allowed_quantity = EXCLUDED.max_allowed_quantity,
                suggested_price_min = EXCLUDED.suggested_price_min,
                suggested_price_max = EXCLUDED.suggested_price_max,
                processing_time_ms = EXCLUDED.processing_time_ms,
                strategy_id = EXCLUDED.strategy_id,
                engine_id = EXCLUDED.engine_id
            "#
        )
        .bind(&record.id)
        .bind(&record.timestamp)
        .bind(&record.trading_pair)
        .bind(&record.side)
        .bind(&record.quantity)
        .bind(&record.price)
        .bind(&record.order_type)
        .bind(&record.passed)
        .bind(&record.risk_score)
        .bind(&record.violations)
        .bind(&record.warnings)
        .bind(&record.recommendations)
        .bind(&record.max_allowed_quantity)
        .bind(&record.suggested_price_min)
        .bind(&record.suggested_price_max)
        .bind(&record.processing_time_ms)
        .bind(&record.strategy_id)
        .bind(&record.engine_id)
        .bind(&record.created_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险检查记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_risk_check(&self, id: Uuid) -> SigmaXResult<Option<RiskCheckRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, timestamp, trading_pair, side, quantity, price, order_type,
                   passed, risk_score, violations, warnings, recommendations,
                   max_allowed_quantity, suggested_price_min, suggested_price_max,
                   processing_time_ms, strategy_id, engine_id, created_at
            FROM risk_checks
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取风险检查记录失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_check(row)?)),
            None => Ok(None),
        }
    }

    async fn find_risk_checks(
        &self,
        filter: &RiskQueryFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskCheckRecord>> {
        let pool = self.db.pool();
        let (where_clause, _params) = self.build_risk_check_where_clause(filter);

        let mut query = format!(
            r#"
            SELECT id, timestamp, trading_pair, side, quantity, price, order_type,
                   passed, risk_score, violations, warnings, recommendations,
                   max_allowed_quantity, suggested_price_min, suggested_price_max,
                   processing_time_ms, strategy_id, engine_id, created_at
            FROM risk_checks
            {}
            ORDER BY timestamp DESC
            "#,
            where_clause
        );

        if let Some(page) = pagination {
            query.push_str(&format!(" LIMIT {} OFFSET {}", page.limit, page.offset));
        }

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(trading_pair) = &filter.trading_pair {
            sql_query = sql_query.bind(trading_pair);
        }
        if let Some(strategy_id) = &filter.strategy_id {
            sql_query = sql_query.bind(strategy_id);
        }
        if let Some(engine_id) = &filter.engine_id {
            sql_query = sql_query.bind(engine_id);
        }
        if let Some(passed) = filter.passed {
            sql_query = sql_query.bind(passed);
        }
        if let Some(start_time) = &filter.start_time {
            sql_query = sql_query.bind(start_time);
        }
        if let Some(end_time) = &filter.end_time {
            sql_query = sql_query.bind(end_time);
        }
        if let Some(min_score) = &filter.min_risk_score {
            sql_query = sql_query.bind(min_score);
        }
        if let Some(max_score) = &filter.max_risk_score {
            sql_query = sql_query.bind(max_score);
        }

        let rows = sql_query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询风险检查记录失败: {}", e)))?;

        let mut records = Vec::new();
        for row in rows {
            records.push(self.row_to_risk_check(row)?);
        }

        Ok(records)
    }

    async fn count_risk_checks(&self, filter: &RiskQueryFilter) -> SigmaXResult<u64> {
        let pool = self.db.pool();
        let (where_clause, _params) = self.build_risk_check_where_clause(filter);

        let query = format!(
            "SELECT COUNT(*) as count FROM risk_checks {}",
            where_clause
        );

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(trading_pair) = &filter.trading_pair {
            sql_query = sql_query.bind(trading_pair);
        }
        if let Some(strategy_id) = &filter.strategy_id {
            sql_query = sql_query.bind(strategy_id);
        }
        if let Some(engine_id) = &filter.engine_id {
            sql_query = sql_query.bind(engine_id);
        }
        if let Some(passed) = filter.passed {
            sql_query = sql_query.bind(passed);
        }
        if let Some(start_time) = &filter.start_time {
            sql_query = sql_query.bind(start_time);
        }
        if let Some(end_time) = &filter.end_time {
            sql_query = sql_query.bind(end_time);
        }
        if let Some(min_score) = &filter.min_risk_score {
            sql_query = sql_query.bind(min_score);
        }
        if let Some(max_score) = &filter.max_risk_score {
            sql_query = sql_query.bind(max_score);
        }

        let row = sql_query
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("统计风险检查记录失败: {}", e)))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| SigmaXError::Database(format!("获取统计结果失败: {}", e)))?;

        Ok(count as u64)
    }

    // ============================================================================
    // 风险规则管理
    // ============================================================================

    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_rules (
                id, name, rule_type, description, parameters, enabled, priority,
                created_by, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
            )
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                rule_type = EXCLUDED.rule_type,
                description = EXCLUDED.description,
                parameters = EXCLUDED.parameters,
                enabled = EXCLUDED.enabled,
                priority = EXCLUDED.priority,
                created_by = EXCLUDED.created_by,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&rule.id)
        .bind(&rule.name)
        .bind(&rule.rule_type)
        .bind(&rule.description)
        .bind(&rule.parameters)
        .bind(&rule.enabled)
        .bind(&rule.priority)
        .bind(&rule.created_by)
        .bind(&rule.created_at)
        .bind(&rule.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险规则失败: {}", e)))?;

        Ok(())
    }

    async fn get_risk_rule(&self, id: Uuid) -> SigmaXResult<Option<RiskRuleRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, rule_type, description, parameters, enabled, priority,
                   created_by, created_at, updated_at
            FROM risk_rules
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取风险规则失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_rule(row)?)),
            None => Ok(None),
        }
    }

    async fn get_enabled_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, rule_type, description, parameters, enabled, priority,
                   created_by, created_at, updated_at
            FROM risk_rules
            WHERE enabled = true
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取启用的风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn get_risk_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, rule_type, description, parameters, enabled, priority,
                   created_by, created_at, updated_at
            FROM risk_rules
            WHERE rule_type = $1 AND enabled = true
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .bind(rule_type)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("按类型获取风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn find_risk_rules(
        &self,
        filter: &RiskRuleFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let mut conditions = Vec::new();
        let mut param_index = 1;

        if let Some(rule_type) = &filter.rule_type {
            conditions.push(format!("rule_type = ${}", param_index));
            param_index += 1;
        }

        if let Some(enabled) = filter.enabled {
            conditions.push(format!("enabled = ${}", param_index));
            param_index += 1;
        }

        if let Some(created_by) = &filter.created_by {
            conditions.push(format!("created_by = ${}", param_index));
            param_index += 1;
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        let mut query = format!(
            r#"
            SELECT id, name, rule_type, description, parameters, enabled, priority,
                   created_by, created_at, updated_at
            FROM risk_rules
            {}
            ORDER BY priority DESC, created_at ASC
            "#,
            where_clause
        );

        if let Some(page) = pagination {
            query.push_str(&format!(" LIMIT {} OFFSET {}", page.limit, page.offset));
        }

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(rule_type) = &filter.rule_type {
            sql_query = sql_query.bind(rule_type);
        }
        if let Some(enabled) = filter.enabled {
            sql_query = sql_query.bind(enabled);
        }
        if let Some(created_by) = &filter.created_by {
            sql_query = sql_query.bind(created_by);
        }

        let rows = sql_query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn update_risk_rule_status(&self, id: Uuid, enabled: bool) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE risk_rules
            SET enabled = $1, updated_at = NOW()
            WHERE id = $2
            "#
        )
        .bind(enabled)
        .bind(&id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新风险规则状态失败: {}", e)))?;

        Ok(())
    }

    async fn delete_risk_rule(&self, id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query("DELETE FROM risk_rules WHERE id = $1")
            .bind(&id)
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("删除风险规则失败: {}", e)))?;

        Ok(())
    }

    // ============================================================================
    // 风险统计数据管理
    // ============================================================================

    async fn save_risk_statistics(&self, stats: &RiskStatisticsRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_statistics (
                id, date, total_checks, passed_checks, failed_checks, pass_rate,
                average_risk_score, top_violations, risk_score_distribution,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
            )
            ON CONFLICT (date) DO UPDATE SET
                total_checks = EXCLUDED.total_checks,
                passed_checks = EXCLUDED.passed_checks,
                failed_checks = EXCLUDED.failed_checks,
                pass_rate = EXCLUDED.pass_rate,
                average_risk_score = EXCLUDED.average_risk_score,
                top_violations = EXCLUDED.top_violations,
                risk_score_distribution = EXCLUDED.risk_score_distribution,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&stats.id)
        .bind(&stats.date)
        .bind(&stats.total_checks)
        .bind(&stats.passed_checks)
        .bind(&stats.failed_checks)
        .bind(&stats.pass_rate)
        .bind(&stats.average_risk_score)
        .bind(&stats.top_violations)
        .bind(&stats.risk_score_distribution)
        .bind(&stats.created_at)
        .bind(&stats.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险统计数据失败: {}", e)))?;

        Ok(())
    }

    async fn get_risk_statistics_by_date(&self, date: NaiveDate) -> SigmaXResult<Option<RiskStatisticsRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, date, total_checks, passed_checks, failed_checks, pass_rate,
                   average_risk_score, top_violations, risk_score_distribution,
                   created_at, updated_at
            FROM risk_statistics
            WHERE date = $1
            "#
        )
        .bind(&date)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取风险统计数据失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_statistics(row)?)),
            None => Ok(None),
        }
    }

    async fn get_risk_statistics_by_range(
        &self,
        start_date: NaiveDate,
        end_date: NaiveDate
    ) -> SigmaXResult<Vec<RiskStatisticsRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, date, total_checks, passed_checks, failed_checks, pass_rate,
                   average_risk_score, top_violations, risk_score_distribution,
                   created_at, updated_at
            FROM risk_statistics
            WHERE date >= $1 AND date <= $2
            ORDER BY date ASC
            "#
        )
        .bind(&start_date)
        .bind(&end_date)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取日期范围内风险统计数据失败: {}", e)))?;

        let mut statistics = Vec::new();
        for row in rows {
            statistics.push(self.row_to_risk_statistics(row)?);
        }

        Ok(statistics)
    }

    // ============================================================================
    // 风险违规记录管理
    // ============================================================================

    async fn save_risk_violation(&self, violation: &RiskViolationRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_violations (
                id, risk_check_id, rule_id, rule_name, severity, message,
                current_value, limit_value, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9
            )
            ON CONFLICT (id) DO UPDATE SET
                risk_check_id = EXCLUDED.risk_check_id,
                rule_id = EXCLUDED.rule_id,
                rule_name = EXCLUDED.rule_name,
                severity = EXCLUDED.severity,
                message = EXCLUDED.message,
                current_value = EXCLUDED.current_value,
                limit_value = EXCLUDED.limit_value
            "#
        )
        .bind(&violation.id)
        .bind(&violation.risk_check_id)
        .bind(&violation.rule_id)
        .bind(&violation.rule_name)
        .bind(&violation.severity)
        .bind(&violation.message)
        .bind(&violation.current_value)
        .bind(&violation.limit_value)
        .bind(&violation.created_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险违规记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_violations_by_check_id(&self, check_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, severity, message,
                   current_value, limit_value, created_at
            FROM risk_violations
            WHERE risk_check_id = $1
            ORDER BY created_at DESC
            "#
        )
        .bind(&check_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据检查ID获取违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }

    async fn get_violations_by_rule_id(&self, rule_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, severity, message,
                   current_value, limit_value, created_at
            FROM risk_violations
            WHERE rule_id = $1
            ORDER BY created_at DESC
            "#
        )
        .bind(&rule_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据规则ID获取违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }

    async fn get_recent_violations(&self, limit: usize) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, severity, message,
                   current_value, limit_value, created_at
            FROM risk_violations
            ORDER BY created_at DESC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取最近违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }

    // ============================================================================
    // 风险配置管理实现 - 使用新的 risk_config 表
    // ============================================================================

    async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig> {
        // 获取全局默认配置（strategy_type 为 NULL）
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, description, strategy_type, enabled, risk_parameters, created_at, updated_at, created_by
            FROM risk_config
            WHERE strategy_type IS NULL AND enabled = true
            ORDER BY created_at ASC
            LIMIT 1
            "#
        )
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取默认风险配置失败: {}", e)))?;

        match row {
            Some(row) => self.row_to_risk_config(row),
            None => {
                // 如果没有找到配置，返回默认配置
                Ok(RiskManagementConfig::default())
            }
        }
    }

    async fn get_risk_config_by_id(&self, id: Uuid) -> SigmaXResult<Option<RiskManagementConfig>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, description, strategy_type, enabled, risk_parameters, created_at, updated_at, created_by
            FROM risk_config
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据ID获取风险配置失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_config(row)?)),
            None => Ok(None),
        }
    }

    async fn get_risk_config_by_strategy_type(&self, strategy_type: &str) -> SigmaXResult<Option<RiskManagementConfig>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, description, strategy_type, enabled, risk_parameters, created_at, updated_at, created_by
            FROM risk_config
            WHERE strategy_type = $1 AND enabled = true
            ORDER BY created_at ASC
            LIMIT 1
            "#
        )
        .bind(strategy_type)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据策略类型获取风险配置失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_config(row)?)),
            None => Ok(None),
        }
    }

    async fn save_risk_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 序列化风险参数
        let risk_parameters_json = serde_json::to_value(&config.risk_parameters)
            .map_err(|e| SigmaXError::Database(format!("序列化风险参数失败: {}", e)))?;

        let config_id = if let Some(id_str) = &config.id {
            Uuid::parse_str(id_str)
                .map_err(|e| SigmaXError::Database(format!("解析配置ID失败: {}", e)))?
        } else {
            Uuid::new_v4()
        };

        sqlx::query(
            r#"
            INSERT INTO risk_config (id, name, description, strategy_type, enabled, risk_parameters, created_at, updated_at, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                strategy_type = EXCLUDED.strategy_type,
                enabled = EXCLUDED.enabled,
                risk_parameters = EXCLUDED.risk_parameters,
                updated_at = EXCLUDED.updated_at,
                created_by = EXCLUDED.created_by
            "#
        )
        .bind(&config_id)
        .bind(&config.name)
        .bind(&config.description)
        .bind(&config.strategy_type)
        .bind(config.enabled)
        .bind(&risk_parameters_json)
        .bind(&config.created_at.unwrap_or_else(chrono::Utc::now))
        .bind(&config.updated_at.unwrap_or_else(chrono::Utc::now))
        .bind(&config.created_by)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险配置失败: {}", e)))?;

        Ok(())
    }

    async fn update_risk_config(&self, id: Uuid, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 序列化风险参数
        let risk_parameters_json = serde_json::to_value(&config.risk_parameters)
            .map_err(|e| SigmaXError::Database(format!("序列化风险参数失败: {}", e)))?;

        sqlx::query(
            r#"
            UPDATE risk_config
            SET name = $2, description = $3, strategy_type = $4, enabled = $5,
                risk_parameters = $6, updated_at = $7, created_by = $8
            WHERE id = $1
            "#
        )
        .bind(&id)
        .bind(&config.name)
        .bind(&config.description)
        .bind(&config.strategy_type)
        .bind(config.enabled)
        .bind(&risk_parameters_json)
        .bind(&config.updated_at.unwrap_or_else(chrono::Utc::now))
        .bind(&config.created_by)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新风险配置失败: {}", e)))?;

        Ok(())
    }

    async fn delete_risk_config(&self, id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query("DELETE FROM risk_config WHERE id = $1")
            .bind(&id)
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("删除风险配置失败: {}", e)))?;

        Ok(())
    }

    async fn get_enabled_risk_configs(&self) -> SigmaXResult<Vec<RiskManagementConfig>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, description, strategy_type, enabled, risk_parameters, created_at, updated_at, created_by
            FROM risk_config
            WHERE enabled = true
            ORDER BY strategy_type NULLS FIRST, created_at ASC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取启用的风险配置失败: {}", e)))?;

        let mut configs = Vec::new();
        for row in rows {
            configs.push(self.row_to_risk_config(row)?);
        }

        Ok(configs)
    }

    async fn reset_risk_config(&self) -> SigmaXResult<()> {
        // 重置为默认配置
        let default_config = RiskManagementConfig::default();
        self.save_risk_config(&default_config).await
    }
}

impl SqlRiskRepository {
    /// 将数据库行转换为风险配置对象
    fn row_to_risk_config(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskManagementConfig> {
        let id: Uuid = row.try_get("id")
            .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?;
        let name: String = row.try_get("name")
            .map_err(|e| SigmaXError::Database(format!("获取name失败: {}", e)))?;
        let description: Option<String> = row.try_get("description")
            .map_err(|e| SigmaXError::Database(format!("获取description失败: {}", e)))?;
        let strategy_type: Option<String> = row.try_get("strategy_type")
            .map_err(|e| SigmaXError::Database(format!("获取strategy_type失败: {}", e)))?;
        let enabled: bool = row.try_get("enabled")
            .map_err(|e| SigmaXError::Database(format!("获取enabled失败: {}", e)))?;
        let risk_parameters_json: serde_json::Value = row.try_get("risk_parameters")
            .map_err(|e| SigmaXError::Database(format!("获取risk_parameters失败: {}", e)))?;
        let created_at: chrono::DateTime<chrono::Utc> = row.try_get("created_at")
            .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?;
        let updated_at: chrono::DateTime<chrono::Utc> = row.try_get("updated_at")
            .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?;
        let created_by: Option<String> = row.try_get("created_by")
            .map_err(|e| SigmaXError::Database(format!("获取created_by失败: {}", e)))?;

        // 反序列化风险参数
        let risk_parameters: sigmax_core::RiskConfigParameters = serde_json::from_value(risk_parameters_json)
            .map_err(|e| SigmaXError::Database(format!("反序列化风险参数失败: {}", e)))?;

        Ok(RiskManagementConfig {
            id: Some(id.to_string()),
            name,
            description,
            strategy_type,
            enabled,
            risk_parameters,
            created_at: Some(created_at),
            updated_at: Some(updated_at),
            created_by,
        })
    }
}
