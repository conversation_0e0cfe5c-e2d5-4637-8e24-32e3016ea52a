//! SQL 通知仓储实现
//!
//! 基于 SQLx 的通知数据访问实现

use std::sync::Arc;
use async_trait::async_trait;
use sqlx::Row;
use chrono::{DateTime, Utc};
use uuid::Uuid;
use sigmax_core::{SigmaXResult, SigmaXError, NotificationConfig};

use crate::DatabaseManager;
use crate::repositories::traits::notification_repository::{
    NotificationRepository, NotificationRecord, NotificationStatus
};

/// SQL 通知仓储实现
pub struct SqlNotificationRepository {
    db: Arc<DatabaseManager>,
}

impl SqlNotificationRepository {
    /// 创建新的 SQL 通知仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为通知记录
    fn row_to_notification_record(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<NotificationRecord> {
        let status_str: String = row.get("status");
        let status = match status_str.as_str() {
            "Pending" => NotificationStatus::Pending,
            "Sent" => NotificationStatus::Sent,
            "Failed" => NotificationStatus::Failed,
            "Cancelled" => NotificationStatus::Cancelled,
            _ => return Err(SigmaXError::Database(format!("Unknown notification status: {}", status_str))),
        };

        Ok(NotificationRecord {
            id: row.get("id"),
            notification_type: row.get("notification_type"),
            recipient: row.get("recipient"),
            title: row.get("title"),
            content: row.get("content"),
            status,
            retry_count: row.get("retry_count"),
            created_at: row.get("created_at"),
            sent_at: row.get("sent_at"),
        })
    }
}

#[async_trait]
impl NotificationRepository for SqlNotificationRepository {
    // ============================================================================
    // 通知配置管理
    // ============================================================================

    async fn get_notification_config(&self) -> SigmaXResult<NotificationConfig> {
        let pool = self.db.pool();

        // 从 system_config 表中获取通知配置
        let rows = sqlx::query(
            r#"
            SELECT key, value 
            FROM system_config 
            WHERE key LIKE 'notification.%'
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取通知配置失败: {}", e)))?;

        // 构建通知配置对象
        let mut config = NotificationConfig::default();

        for row in rows {
            let key: String = row.get("key");
            let value_json: serde_json::Value = row.get("value");

            // 从 JSON 值中提取字符串或数字
            let value_str = match &value_json {
                serde_json::Value::String(s) => s.clone(),
                serde_json::Value::Number(n) => n.to_string(),
                serde_json::Value::Bool(b) => b.to_string(),
                _ => continue, // 跳过无法转换的值
            };

            match key.as_str() {
                "notification.email_enabled" => {
                    config.email_enabled = value_str.parse()
                        .map_err(|e| SigmaXError::Database(format!("解析 email_enabled 失败: {}", e)))?;
                }
                "notification.webhook_url" => {
                    config.webhook_url = Some(value_str);
                }
                "notification.slack_webhook" => {
                    config.slack_webhook = Some(value_str);
                }
                "notification.telegram_bot_token" => {
                    config.telegram_bot_token = Some(value_str);
                }
                "notification.telegram_chat_id" => {
                    config.telegram_chat_id = Some(value_str);
                }
                _ => {} // 忽略未知的配置项
            }
        }

        Ok(config)
    }

    async fn save_notification_config(&self, config: &NotificationConfig) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 开始事务
        let mut tx = pool.begin()
            .await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        // 保存各个配置项
        let config_items = [
            ("notification.email_enabled", config.email_enabled.to_string(), "启用邮件通知"),
            ("notification.webhook_url", config.webhook_url.clone().unwrap_or_default(), "Webhook URL"),
            ("notification.slack_webhook", config.slack_webhook.clone().unwrap_or_default(), "Slack Webhook URL"),
            ("notification.telegram_bot_token", config.telegram_bot_token.clone().unwrap_or_default(), "Telegram Bot Token"),
            ("notification.telegram_chat_id", config.telegram_chat_id.clone().unwrap_or_default(), "Telegram Chat ID"),
        ];

        for (key, value, description) in config_items {
            sqlx::query(
                r#"
                INSERT INTO system_config (key, value, description, is_encrypted, created_at, updated_at)
                VALUES ($1, $2, $3, false, now(), now())
                ON CONFLICT (key) DO UPDATE SET
                    value = EXCLUDED.value,
                    description = EXCLUDED.description,
                    updated_at = now()
                "#
            )
            .bind(key)
            .bind(value)
            .bind(description)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("保存通知配置项 {} 失败: {}", key, e)))?;
        }

        // 提交事务
        tx.commit()
            .await
            .map_err(|e| SigmaXError::Database(format!("提交通知配置事务失败: {}", e)))?;

        Ok(())
    }

    async fn reset_notification_config(&self) -> SigmaXResult<()> {
        // 重置为默认配置
        let default_config = NotificationConfig::default();
        self.save_notification_config(&default_config).await
    }

    // ============================================================================
    // 通知记录管理
    // ============================================================================

    async fn save_notification(&self, notification: &NotificationRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO notifications (
                id, notification_type, recipient, title, content, status, 
                retry_count, created_at, sent_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9
            )
            ON CONFLICT (id) DO UPDATE SET
                notification_type = EXCLUDED.notification_type,
                recipient = EXCLUDED.recipient,
                title = EXCLUDED.title,
                content = EXCLUDED.content,
                status = EXCLUDED.status,
                retry_count = EXCLUDED.retry_count,
                sent_at = EXCLUDED.sent_at
            "#
        )
        .bind(&notification.id)
        .bind(&notification.notification_type)
        .bind(&notification.recipient)
        .bind(&notification.title)
        .bind(&notification.content)
        .bind(&format!("{:?}", notification.status))
        .bind(&notification.retry_count)
        .bind(&notification.created_at)
        .bind(&notification.sent_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存通知记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_notification(&self, id: Uuid) -> SigmaXResult<Option<NotificationRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, notification_type, recipient, title, content, 
                   status, retry_count, created_at, sent_at
            FROM notifications
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取通知记录失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_notification_record(row)?)),
            None => Ok(None),
        }
    }

    async fn get_pending_notifications(&self, limit: usize) -> SigmaXResult<Vec<NotificationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, notification_type, recipient, title, content, 
                   status, retry_count, created_at, sent_at
            FROM notifications
            WHERE status = 'Pending'
            ORDER BY created_at ASC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取待发送通知失败: {}", e)))?;

        let mut notifications = Vec::new();
        for row in rows {
            notifications.push(self.row_to_notification_record(row)?);
        }

        Ok(notifications)
    }

    async fn update_notification_status(&self, id: Uuid, status: NotificationStatus, sent_at: Option<DateTime<Utc>>) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE notifications SET
                status = $2,
                sent_at = $3
            WHERE id = $1
            "#
        )
        .bind(&id)
        .bind(&format!("{:?}", status))
        .bind(&sent_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新通知状态失败: {}", e)))?;

        Ok(())
    }

    async fn increment_retry_count(&self, id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE notifications SET
                retry_count = retry_count + 1
            WHERE id = $1
            "#
        )
        .bind(&id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("增加重试次数失败: {}", e)))?;

        Ok(())
    }

    async fn get_notification_history(&self, limit: usize, offset: usize) -> SigmaXResult<Vec<NotificationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, notification_type, recipient, title, content, 
                   status, retry_count, created_at, sent_at
            FROM notifications
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#
        )
        .bind(limit as i64)
        .bind(offset as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取通知历史失败: {}", e)))?;

        let mut notifications = Vec::new();
        for row in rows {
            notifications.push(self.row_to_notification_record(row)?);
        }

        Ok(notifications)
    }

    async fn delete_expired_notifications(&self, before: DateTime<Utc>) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let result = sqlx::query(
            r#"
            DELETE FROM notifications
            WHERE created_at < $1 AND status IN ('Sent', 'Failed', 'Cancelled')
            "#
        )
        .bind(&before)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("删除过期通知失败: {}", e)))?;

        Ok(result.rows_affected())
    }
}
