//! SQL 策略仓储实现

use std::sync::Arc;
use sqlx::Row;
use uuid::Uuid;
use sigmax_core::{
    StrategyId, StrategyState, StrategyInfo, SigmaXResult, SigmaXError, StrategyStatus, TradingPair,
    StrategySystemConfig, StrategyTemplate
};
use crate::DatabaseManager;
use crate::repositories::traits::StrategyRepository;

/// SQL策略仓库实现
pub struct SqlStrategyRepository {
    db: Arc<DatabaseManager>,
}

impl SqlStrategyRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为策略配置
    fn row_to_strategy_config(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<StrategySystemConfig> {
        let strategy_parameters_json: serde_json::Value = row.try_get("strategy_parameters")
            .map_err(|e| SigmaXError::Database(format!("Failed to get strategy_parameters: {}", e)))?;

        let config: StrategySystemConfig = serde_json::from_value(strategy_parameters_json)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize strategy config: {}", e)))?;

        Ok(config)
    }

    /// 将策略配置转换为JSONB
    fn config_to_jsonb(&self, config: &StrategySystemConfig) -> SigmaXResult<serde_json::Value> {
        serde_json::to_value(config)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to serialize strategy config: {}", e)))
    }
}

impl StrategyRepository for SqlStrategyRepository {
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 从配置中提取策略名称和类型
        let (strategy_name, strategy_type) = self.extract_strategy_info_from_config(&state.config)?;

        sqlx::query(
            r#"
            INSERT INTO strategies (
                id, name, strategy_type, config, status, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7
            )
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                strategy_type = EXCLUDED.strategy_type,
                config = EXCLUDED.config,
                status = EXCLUDED.status,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&strategy_id)
        .bind(&strategy_name)
        .bind(&strategy_type)
        .bind(&state.config)
        .bind(&format!("{:?}", state.status))
        .bind(&state.created_at)
        .bind(&state.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to save strategy state: {}", e)))?;

        Ok(())
    }

    async fn load_strategy_state(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, strategy_type, description, config, risk_config,
                   status :: text as status, created_at, updated_at
            FROM strategies WHERE id = $1
            "#
        )
        .bind(&strategy_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to load strategy state: {}", e)))?;

        if let Some(row) = row {
            Ok(Some(self.row_to_strategy_state(row)?))
        } else {
            Ok(None)
        }
    }

    async fn get_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, strategy_type, config, status :: text as status,
                   created_at, updated_at
            FROM strategies
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get all strategies: {}", e)))?;

        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_info(row)?);
        }

        Ok(strategies)
    }

    // ============================================================================
    // 策略配置管理
    // ============================================================================

    async fn get_strategy_config(&self) -> SigmaXResult<StrategySystemConfig> {
        let pool = self.db.pool();

        // 从 strategy_config 表中获取策略配置
        let row = sqlx::query(
            r#"
            SELECT strategy_parameters
            FROM strategy_config
            WHERE enabled = true
            ORDER BY created_at DESC
            LIMIT 1
            "#
        )
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取策略配置失败: {}", e)))?;

        match row {
            Some(row) => self.row_to_strategy_config(row),
            None => {
                // 如果没有找到配置，返回默认配置
                Ok(StrategySystemConfig::default())
            }
        }
    }

    async fn save_strategy_config(&self, config: &StrategySystemConfig) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 序列化策略参数
        let strategy_parameters_json = self.config_to_jsonb(config)?;

        let mut tx = pool.begin()
            .await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        // 禁用所有现有配置
        sqlx::query("UPDATE strategy_config SET enabled = false")
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("禁用现有策略配置失败: {}", e)))?;

        // 插入新配置
        let config_id = Uuid::new_v4();

        sqlx::query(
            r#"
            INSERT INTO strategy_config (id, name, description, enabled, strategy_parameters, created_at, updated_at, created_by)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW(), $6)
            "#
        )
        .bind(&config_id)
        .bind("default")
        .bind("Default strategy configuration")
        .bind(true)
        .bind(&strategy_parameters_json)
        .bind("system")
        .execute(&mut *tx)
        .await
        .map_err(|e| SigmaXError::Database(format!("插入策略配置失败: {}", e)))?;

        tx.commit()
            .await
            .map_err(|e| SigmaXError::Database(format!("提交策略配置事务失败: {}", e)))?;

        Ok(())
    }

    async fn reset_strategy_config(&self) -> SigmaXResult<()> {
        // 重置为默认配置
        let default_config = StrategySystemConfig::default();
        self.save_strategy_config(&default_config).await
    }

    // ============================================================================
    // 策略模板管理
    // ============================================================================

    async fn get_strategy_template(&self, template_name: &str) -> SigmaXResult<Option<StrategyTemplate>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            "SELECT value FROM system_config WHERE key = $1"
        )
        .bind(&format!("strategy_template.{}", template_name))
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取策略模板失败: {}", e)))?;

        match row {
            Some(row) => {
                let template_json: serde_json::Value = row.get("value");
                let template = serde_json::from_value(template_json)
                    .map_err(|e| SigmaXError::Serialization(e.to_string()))?;
                Ok(Some(template))
            }
            None => Ok(None),
        }
    }

    async fn get_all_strategy_templates(&self) -> SigmaXResult<Vec<(String, StrategyTemplate)>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT key, value
            FROM system_config
            WHERE key LIKE 'strategy_template.%'
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取所有策略模板失败: {}", e)))?;

        let mut templates = Vec::new();
        for row in rows {
            let key: String = row.get("key");
            let template_json: serde_json::Value = row.get("value");

            // 提取模板名称（去掉 "strategy_template." 前缀）
            let template_name = key.strip_prefix("strategy_template.")
                .unwrap_or(&key)
                .to_string();

            let template: StrategyTemplate = serde_json::from_value(template_json)
                .map_err(|e| SigmaXError::Serialization(e.to_string()))?;

            templates.push((template_name, template));
        }

        Ok(templates)
    }

    async fn save_strategy_template(&self, template_name: &str, template: &StrategyTemplate) -> SigmaXResult<()> {
        let pool = self.db.pool();

        let template_json = serde_json::to_string(template)
            .map_err(|e| SigmaXError::Serialization(e.to_string()))?;

        sqlx::query(
            r#"
            INSERT INTO system_config (key, value, description, is_encrypted, created_at, updated_at)
            VALUES ($1, $2, $3, false, now(), now())
            ON CONFLICT (key) DO UPDATE SET
                value = EXCLUDED.value,
                updated_at = now()
            "#
        )
        .bind(&format!("strategy_template.{}", template_name))
        .bind(&template_json)
        .bind(&format!("策略模板: {}", template_name))
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存策略模板失败: {}", e)))?;

        Ok(())
    }

    async fn delete_strategy_template(&self, template_name: &str) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            "DELETE FROM system_config WHERE key = $1"
        )
        .bind(&format!("strategy_template.{}", template_name))
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("删除策略模板失败: {}", e)))?;

        Ok(())
    }
}

impl SqlStrategyRepository {
    /// 从配置中提取策略信息
    fn extract_strategy_info_from_config(&self, config: &serde_json::Value) -> SigmaXResult<(String, String)> {
        // 尝试从配置中提取策略名称和类型
        let strategy_name = config.get("name")
            .and_then(|v| v.as_str())
            .unwrap_or("Unnamed Strategy")
            .to_string();

        let strategy_type = config.get("strategy_type")
            .and_then(|v| v.as_str())
            .or_else(|| config.get("type").and_then(|v| v.as_str()))
            .unwrap_or("AsymmetricGrid")
            .to_string();

        Ok((strategy_name, strategy_type))
    }

    /// 从配置中提取交易对信息
    fn extract_trading_pair_from_config(&self, config: &serde_json::Value) -> SigmaXResult<TradingPair> {
        // 尝试从配置中提取交易对信息
        if let Some(trading_pair_obj) = config.get("trading_pair") {
            if let (Some(base), Some(quote)) = (
                trading_pair_obj.get("base").and_then(|v| v.as_str()),
                trading_pair_obj.get("quote").and_then(|v| v.as_str())
            ) {
                return Ok(TradingPair::new(base.to_string(), quote.to_string()));
            }
        }

        // 尝试从其他可能的字段中提取
        if let Some(symbol) = config.get("symbol").and_then(|v| v.as_str()) {
            return TradingPair::from_symbol(symbol)
                .map_err(|e| SigmaXError::Database(format!("Invalid trading pair symbol: {}", e)));
        }

        // 默认值
        Ok(TradingPair::new("BTC", "USDT"))
    }

    fn row_to_strategy_state(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<StrategyState> {
        let status_str: String = row.get("status");
        let status = match status_str.as_str() {
            "Created" => StrategyStatus::Created,
            "Running" => StrategyStatus::Running,
            "Paused" => StrategyStatus::Paused,
            "Stopped" => StrategyStatus::Stopped,
            "Error" => StrategyStatus::Error,
            _ => return Err(SigmaXError::Database(format!("Unknown strategy status: {}", status_str))),
        };

        Ok(StrategyState {
            strategy_id: row.get("id"),
            status,
            config: row.get("config"),
            state_data: serde_json::Value::Null, // TODO: 添加到数据库表
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }

    fn row_to_strategy_info(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<StrategyInfo> {
        let status_str: String = row.get("status");
        let status = match status_str.as_str() {
            "Created" => StrategyStatus::Created,
            "Running" => StrategyStatus::Running,
            "Paused" => StrategyStatus::Paused,
            "Stopped" => StrategyStatus::Stopped,
            "Error" => StrategyStatus::Error,
            _ => return Err(SigmaXError::Database(format!("Unknown strategy status: {}", status_str))),
        };

        // 尝试从配置中提取交易对信息
        let config: serde_json::Value = row.get("config");
        let trading_pair = self.extract_trading_pair_from_config(&config)?;

        Ok(StrategyInfo {
            id: row.get("id"),
            name: row.get("name"),
            strategy_type: row.get("strategy_type"),
            status,
            trading_pair,
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }
}

impl SqlStrategyRepository {
    // ============================================================================
    // 批量操作
    // ============================================================================

    /// 批量保存策略状态
    pub async fn save_strategy_states_batch(&self, strategy_states: &[(StrategyId, &StrategyState)]) -> SigmaXResult<()> {
        if strategy_states.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        for (strategy_id, state) in strategy_states {
            // 从配置中提取策略名称和类型
            let (strategy_name, strategy_type) = self.extract_strategy_info_from_config(&state.config)?;

            sqlx::query(
                r#"
                INSERT INTO strategies (
                    id, name, strategy_type, config, status, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7
                )
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    strategy_type = EXCLUDED.strategy_type,
                    config = EXCLUDED.config,
                    status = EXCLUDED.status,
                    updated_at = EXCLUDED.updated_at
                "#
            )
            .bind(strategy_id)
            .bind(&strategy_name)
            .bind(&strategy_type)
            .bind(&state.config)
            .bind(&format!("{:?}", state.status))
            .bind(&state.created_at)
            .bind(&state.updated_at)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量保存策略状态失败: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::Database(format!("提交事务失败: {}", e)))?;

        Ok(())
    }

    /// 批量更新策略状态
    pub async fn update_strategy_status_batch(&self, updates: &[(StrategyId, StrategyStatus)]) -> SigmaXResult<()> {
        if updates.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        for (strategy_id, status) in updates {
            sqlx::query(
                r#"
                UPDATE strategies SET
                    status = $2,
                    updated_at = NOW()
                WHERE id = $1
                "#
            )
            .bind(strategy_id)
            .bind(&format!("{:?}", status))
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量更新策略状态失败: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::Database(format!("提交事务失败: {}", e)))?;

        Ok(())
    }

    /// 批量删除策略
    pub async fn delete_strategies_batch(&self, strategy_ids: &[StrategyId]) -> SigmaXResult<()> {
        if strategy_ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new("DELETE FROM strategies WHERE id = ANY(");
        query_builder.push_bind(strategy_ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量删除策略失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::Database("没有策略被删除".to_string()));
        }

        Ok(())
    }

    /// 批量获取策略状态
    pub async fn load_strategy_states_batch(&self, strategy_ids: &[StrategyId]) -> SigmaXResult<Vec<(StrategyId, Option<StrategyState>)>> {
        if strategy_ids.is_empty() {
            return Ok(Vec::new());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new(
            r#"
            SELECT id, name, strategy_type, config, status :: text as status,
                   created_at, updated_at
            FROM strategies
            WHERE id = ANY(
            "#
        );
        query_builder.push_bind(strategy_ids);
        query_builder.push(") ORDER BY created_at DESC");

        let rows = query_builder.build()
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量获取策略状态失败: {}", e)))?;

        let mut result = Vec::new();
        let mut found_ids = std::collections::HashSet::new();

        for row in rows {
            let strategy_id: StrategyId = row.get("id");
            let strategy_state = self.row_to_strategy_state(row)?;
            found_ids.insert(strategy_id);
            result.push((strategy_id, Some(strategy_state)));
        }

        // 添加未找到的策略ID
        for &strategy_id in strategy_ids {
            if !found_ids.contains(&strategy_id) {
                result.push((strategy_id, None));
            }
        }

        Ok(result)
    }
}
