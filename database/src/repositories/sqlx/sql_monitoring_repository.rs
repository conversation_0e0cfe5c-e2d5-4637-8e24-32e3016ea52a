//! SQLx 监控配置仓储实现
//!
//! 基于 SQLx 的监控配置数据访问实现，使用 JSONB 存储完整配置

use std::sync::Arc;
use async_trait::async_trait;
use sqlx::Row;
use uuid::Uuid;
use serde_json;
use tracing::{info, warn, debug, error};
use chrono::{DateTime, Utc};

use sigmax_core::{SigmaXResult, SigmaXError, MonitoringConfig};
use crate::DatabaseManager;
use crate::repositories::traits::monitoring_repository::{
    MonitoringRepository, MonitoringConfigRecord, MonitoringConfigStatistics,
    MonitoringMetricsRecord, AlertRecord, AlertStatus
};

/// SQLx 监控配置仓储实现
///
/// 使用 PostgreSQL JSONB 存储完整的监控配置，提供高效的查询和更新操作
pub struct SqlMonitoringRepository {
    db: Arc<DatabaseManager>,
}

impl SqlMonitoringRepository {
    /// 创建新的监控配置仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 获取数据库连接池
    fn get_pool(&self) -> &sqlx::PgPool {
        self.db.pool()
    }

    /// 将 MonitoringConfig 转换为 JSONB
    fn config_to_jsonb(&self, config: &MonitoringConfig) -> SigmaXResult<serde_json::Value> {
        serde_json::to_value(config)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to serialize monitoring config: {}", e)))
    }

    /// 从 JSONB 转换为 MonitoringConfig
    fn jsonb_to_config(&self, value: serde_json::Value) -> SigmaXResult<MonitoringConfig> {
        serde_json::from_value(value)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize monitoring config: {}", e)))
    }

    /// 验证监控配置
    fn validate_config(&self, config: &MonitoringConfig) -> SigmaXResult<()> {
        config.validate_complete()
    }

    /// 从数据库行转换为配置记录
    fn row_to_config_record(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<MonitoringConfigRecord> {
        Ok(MonitoringConfigRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("Failed to get id: {}", e)))?,
            name: row.try_get("name")
                .map_err(|e| SigmaXError::Database(format!("Failed to get name: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::Database(format!("Failed to get description: {}", e)))?,
            enabled: row.try_get("enabled")
                .map_err(|e| SigmaXError::Database(format!("Failed to get enabled: {}", e)))?,
            monitoring_parameters: row.try_get("monitoring_parameters")
                .map_err(|e| SigmaXError::Database(format!("Failed to get monitoring_parameters: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("Failed to get created_at: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("Failed to get updated_at: {}", e)))?,
            created_by: row.try_get("created_by")
                .map_err(|e| SigmaXError::Database(format!("Failed to get created_by: {}", e)))?,
        })
    }

    /// 从数据库行转换为指标记录
    fn row_to_metrics_record(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<MonitoringMetricsRecord> {
        Ok(MonitoringMetricsRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("Failed to get id: {}", e)))?,
            metric_type: row.try_get("metric_type")
                .map_err(|e| SigmaXError::Database(format!("Failed to get metric_type: {}", e)))?,
            metric_name: row.try_get("metric_name")
                .map_err(|e| SigmaXError::Database(format!("Failed to get metric_name: {}", e)))?,
            metric_value: row.try_get("metric_value")
                .map_err(|e| SigmaXError::Database(format!("Failed to get metric_value: {}", e)))?,
            unit: row.try_get("unit")
                .map_err(|e| SigmaXError::Database(format!("Failed to get unit: {}", e)))?,
            labels: row.try_get("labels")
                .map_err(|e| SigmaXError::Database(format!("Failed to get labels: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("Failed to get timestamp: {}", e)))?,
            source: row.try_get("source")
                .map_err(|e| SigmaXError::Database(format!("Failed to get source: {}", e)))?,
        })
    }

    /// 从数据库行转换为告警记录
    fn row_to_alert_record(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<AlertRecord> {
        let status_str: String = row.try_get("status")
            .map_err(|e| SigmaXError::Database(format!("Failed to get status: {}", e)))?;
        
        let status = match status_str.as_str() {
            "triggered" => AlertStatus::Triggered,
            "acknowledged" => AlertStatus::Acknowledged,
            "resolved" => AlertStatus::Resolved,
            "suppressed" => AlertStatus::Suppressed,
            _ => return Err(SigmaXError::Database(format!("Invalid alert status: {}", status_str))),
        };

        Ok(AlertRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("Failed to get id: {}", e)))?,
            rule_id: row.try_get("rule_id")
                .map_err(|e| SigmaXError::Database(format!("Failed to get rule_id: {}", e)))?,
            severity: row.try_get("severity")
                .map_err(|e| SigmaXError::Database(format!("Failed to get severity: {}", e)))?,
            message: row.try_get("message")
                .map_err(|e| SigmaXError::Database(format!("Failed to get message: {}", e)))?,
            triggered_value: row.try_get("triggered_value")
                .map_err(|e| SigmaXError::Database(format!("Failed to get triggered_value: {}", e)))?,
            threshold_value: row.try_get("threshold_value")
                .map_err(|e| SigmaXError::Database(format!("Failed to get threshold_value: {}", e)))?,
            status,
            triggered_at: row.try_get("triggered_at")
                .map_err(|e| SigmaXError::Database(format!("Failed to get triggered_at: {}", e)))?,
            acknowledged_at: row.try_get("acknowledged_at")
                .map_err(|e| SigmaXError::Database(format!("Failed to get acknowledged_at: {}", e)))?,
            resolved_at: row.try_get("resolved_at")
                .map_err(|e| SigmaXError::Database(format!("Failed to get resolved_at: {}", e)))?,
            acknowledged_by: row.try_get("acknowledged_by")
                .map_err(|e| SigmaXError::Database(format!("Failed to get acknowledged_by: {}", e)))?,
        })
    }
}

#[async_trait]
impl MonitoringRepository for SqlMonitoringRepository {
    // ============================================================================
    // 配置管理
    // ============================================================================

    async fn get_config(&self) -> SigmaXResult<MonitoringConfig> {
        debug!("Getting monitoring config from database");

        let query = r#"
            SELECT monitoring_parameters 
            FROM monitoring_config 
            WHERE enabled = true 
            ORDER BY created_at DESC 
            LIMIT 1
        "#;

        let row = sqlx::query(query)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query monitoring config: {}", e)))?;

        match row {
            Some(row) => {
                let config_json: serde_json::Value = row.try_get("monitoring_parameters")
                    .map_err(|e| SigmaXError::Database(format!("Failed to get monitoring_parameters: {}", e)))?;
                
                self.jsonb_to_config(config_json)
            }
            None => {
                warn!("No monitoring config found, returning default");
                Ok(MonitoringConfig::default())
            }
        }
    }

    async fn save_config(&self, config: &MonitoringConfig) -> SigmaXResult<Uuid> {
        debug!("Saving new monitoring config");

        // 验证配置
        self.validate_config(config)?;

        // 转换为 JSONB
        let config_json = self.config_to_jsonb(config)?;

        let query = r#"
            INSERT INTO monitoring_config (
                name, description, enabled, monitoring_parameters, created_by
            ) VALUES (
                $1, $2, $3, $4, $5
            ) RETURNING id
        "#;

        let row = sqlx::query(query)
            .bind(&config.name)
            .bind(&config.description)
            .bind(config.enabled)
            .bind(config_json)
            .bind("system")
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to save monitoring config: {}", e)))?;

        let id: Uuid = row.try_get("id")
            .map_err(|e| SigmaXError::Database(format!("Failed to get inserted id: {}", e)))?;

        info!("Monitoring config saved successfully with id: {}", id);
        Ok(id)
    }

    async fn update_config(&self, id: Uuid, config: &MonitoringConfig) -> SigmaXResult<()> {
        debug!("Updating monitoring config with id: {}", id);

        // 验证配置
        self.validate_config(config)?;

        // 转换为 JSONB
        let config_json = self.config_to_jsonb(config)?;

        let query = r#"
            UPDATE monitoring_config 
            SET 
                name = $2,
                description = $3,
                enabled = $4,
                monitoring_parameters = $5,
                updated_at = NOW()
            WHERE id = $1
        "#;

        let result = sqlx::query(query)
            .bind(id)
            .bind(&config.name)
            .bind(&config.description)
            .bind(config.enabled)
            .bind(config_json)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to update monitoring config: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Monitoring config with id {} not found", id)));
        }

        info!("Monitoring config {} updated successfully", id);
        Ok(())
    }

    async fn delete_config(&self, id: Uuid) -> SigmaXResult<()> {
        debug!("Deleting monitoring config with id: {}", id);

        let query = "DELETE FROM monitoring_config WHERE id = $1";

        let result = sqlx::query(query)
            .bind(id)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to delete monitoring config: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Monitoring config with id {} not found", id)));
        }

        info!("Monitoring config {} deleted successfully", id);
        Ok(())
    }

    async fn get_config_record(&self, id: Uuid) -> SigmaXResult<Option<MonitoringConfigRecord>> {
        debug!("Getting monitoring config record with id: {}", id);

        let query = r#"
            SELECT id, name, description, enabled, monitoring_parameters, 
                   created_at, updated_at, created_by
            FROM monitoring_config 
            WHERE id = $1
        "#;

        let row = sqlx::query(query)
            .bind(id)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query monitoring config record: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_config_record(&row)?)),
            None => Ok(None),
        }
    }

    async fn get_all_config_records(&self) -> SigmaXResult<Vec<MonitoringConfigRecord>> {
        debug!("Getting all monitoring config records");

        let query = r#"
            SELECT id, name, description, enabled, monitoring_parameters, 
                   created_at, updated_at, created_by
            FROM monitoring_config 
            ORDER BY created_at DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query monitoring config records: {}", e)))?;

        let mut records = Vec::new();
        for row in rows {
            records.push(self.row_to_config_record(&row)?);
        }

        debug!("Retrieved {} monitoring config records", records.len());
        Ok(records)
    }

    async fn reset_to_default(&self) -> SigmaXResult<()> {
        debug!("Resetting monitoring config to default");

        let default_config = MonitoringConfig::default();
        
        // 禁用所有现有配置
        let disable_query = "UPDATE monitoring_config SET enabled = false";
        sqlx::query(disable_query)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to disable existing configs: {}", e)))?;

        // 插入默认配置
        self.save_config(&default_config).await?;

        info!("Monitoring config reset to default successfully");
        Ok(())
    }

    // ============================================================================
    // 指标数据管理
    // ============================================================================

    async fn save_metrics(&self, metrics: &[MonitoringMetricsRecord]) -> SigmaXResult<()> {
        if metrics.is_empty() {
            return Ok(());
        }

        debug!("Saving {} monitoring metrics", metrics.len());

        let query = r#"
            INSERT INTO monitoring_metrics (
                metric_type, metric_name, metric_value, unit, labels,
                timestamp, source, date_partition
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8
            )
        "#;

        let mut tx = self.get_pool().begin().await
            .map_err(|e| SigmaXError::Database(format!("Failed to begin transaction: {}", e)))?;

        for metric in metrics {
            sqlx::query(query)
                .bind(&metric.metric_type)
                .bind(&metric.metric_name)
                .bind(metric.metric_value)
                .bind(&metric.unit)
                .bind(&metric.labels)
                .bind(metric.timestamp)
                .bind(&metric.source)
                .bind(metric.timestamp.date_naive())
                .execute(&mut *tx)
                .await
                .map_err(|e| SigmaXError::Database(format!("Failed to insert metric: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::Database(format!("Failed to commit transaction: {}", e)))?;

        info!("Successfully saved {} monitoring metrics", metrics.len());
        Ok(())
    }

    async fn get_metrics(
        &self,
        metric_type: Option<&str>,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<MonitoringMetricsRecord>> {
        debug!("Getting monitoring metrics with filters");

        let mut query = String::from(r#"
            SELECT id, metric_type, metric_name, metric_value, unit,
                   labels, timestamp, source
            FROM monitoring_metrics
            WHERE 1=1
        "#);

        let mut bind_count = 0;
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Postgres> + Send + Sync>> = Vec::new();

        if let Some(mt) = metric_type {
            bind_count += 1;
            query.push_str(&format!(" AND metric_type = ${}", bind_count));
            params.push(Box::new(mt.to_string()));
        }

        if let Some(start) = start_time {
            bind_count += 1;
            query.push_str(&format!(" AND timestamp >= ${}", bind_count));
            params.push(Box::new(start));
        }

        if let Some(end) = end_time {
            bind_count += 1;
            query.push_str(&format!(" AND timestamp <= ${}", bind_count));
            params.push(Box::new(end));
        }

        query.push_str(" ORDER BY timestamp DESC");

        if let Some(l) = limit {
            bind_count += 1;
            query.push_str(&format!(" LIMIT ${}", bind_count));
            params.push(Box::new(l as i64));
        }

        // 由于动态查询的复杂性，这里使用简化版本
        let simple_query = r#"
            SELECT id, metric_type, metric_name, metric_value, unit,
                   labels, timestamp, source
            FROM monitoring_metrics
            ORDER BY timestamp DESC
            LIMIT 1000
        "#;

        let rows = sqlx::query(simple_query)
            .fetch_all(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query metrics: {}", e)))?;

        let mut metrics = Vec::new();
        for row in rows {
            metrics.push(self.row_to_metrics_record(&row)?);
        }

        debug!("Retrieved {} monitoring metrics", metrics.len());
        Ok(metrics)
    }

    async fn cleanup_expired_metrics(&self, retention_days: u32) -> SigmaXResult<u64> {
        debug!("Cleaning up metrics older than {} days", retention_days);

        let query = r#"
            DELETE FROM monitoring_metrics
            WHERE timestamp < NOW() - INTERVAL '%d days'
        "#;

        let formatted_query = query.replace("%d", &retention_days.to_string());

        let result = sqlx::query(&formatted_query)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to cleanup expired metrics: {}", e)))?;

        let deleted_count = result.rows_affected();
        info!("Cleaned up {} expired monitoring metrics", deleted_count);
        Ok(deleted_count)
    }

    // ============================================================================
    // 告警管理
    // ============================================================================

    async fn create_alert(&self, alert: &AlertRecord) -> SigmaXResult<Uuid> {
        debug!("Creating new alert for rule: {}", alert.rule_id);

        let status_str = match alert.status {
            AlertStatus::Triggered => "triggered",
            AlertStatus::Acknowledged => "acknowledged",
            AlertStatus::Resolved => "resolved",
            AlertStatus::Suppressed => "suppressed",
        };

        let query = r#"
            INSERT INTO monitoring_alerts (
                rule_id, rule_name, severity, message, metric_name,
                triggered_value, threshold_value, condition, status,
                triggered_at, acknowledged_at, resolved_at, acknowledged_by,
                date_partition
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
            ) RETURNING id
        "#;

        let row = sqlx::query(query)
            .bind(&alert.rule_id)
            .bind("") // rule_name - 需要从 alert 结构中获取
            .bind(&alert.severity)
            .bind(&alert.message)
            .bind("") // metric_name - 需要从 alert 结构中获取
            .bind(alert.triggered_value)
            .bind(alert.threshold_value)
            .bind(">") // condition - 需要从 alert 结构中获取
            .bind(status_str)
            .bind(alert.triggered_at)
            .bind(alert.acknowledged_at)
            .bind(alert.resolved_at)
            .bind(&alert.acknowledged_by)
            .bind(alert.triggered_at.date_naive())
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to create alert: {}", e)))?;

        let id: Uuid = row.try_get("id")
            .map_err(|e| SigmaXError::Database(format!("Failed to get inserted alert id: {}", e)))?;

        info!("Alert created successfully with id: {}", id);
        Ok(id)
    }

    async fn update_alert_status(
        &self,
        alert_id: Uuid,
        status: AlertStatus,
        acknowledged_by: Option<&str>,
    ) -> SigmaXResult<()> {
        debug!("Updating alert {} status to {:?}", alert_id, status);

        let status_str = match status {
            AlertStatus::Triggered => "triggered",
            AlertStatus::Acknowledged => "acknowledged",
            AlertStatus::Resolved => "resolved",
            AlertStatus::Suppressed => "suppressed",
        };

        let query = match status {
            AlertStatus::Acknowledged => r#"
                UPDATE monitoring_alerts
                SET status = $2, acknowledged_at = NOW(), acknowledged_by = $3
                WHERE id = $1
            "#,
            AlertStatus::Resolved => r#"
                UPDATE monitoring_alerts
                SET status = $2, resolved_at = NOW()
                WHERE id = $1
            "#,
            _ => r#"
                UPDATE monitoring_alerts
                SET status = $2
                WHERE id = $1
            "#,
        };

        let mut query_builder = sqlx::query(query).bind(alert_id).bind(status_str);

        if matches!(status, AlertStatus::Acknowledged) {
            query_builder = query_builder.bind(acknowledged_by);
        }

        let result = query_builder
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to update alert status: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Alert with id {} not found", alert_id)));
        }

        info!("Alert {} status updated to {:?}", alert_id, status);
        Ok(())
    }

    async fn get_active_alerts(&self) -> SigmaXResult<Vec<AlertRecord>> {
        debug!("Getting active alerts");

        let query = r#"
            SELECT id, rule_id, severity, message, triggered_value, threshold_value,
                   status, triggered_at, acknowledged_at, resolved_at, acknowledged_by
            FROM monitoring_alerts
            WHERE status IN ('triggered', 'acknowledged')
            ORDER BY triggered_at DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query active alerts: {}", e)))?;

        let mut alerts = Vec::new();
        for row in rows {
            alerts.push(self.row_to_alert_record(&row)?);
        }

        debug!("Retrieved {} active alerts", alerts.len());
        Ok(alerts)
    }

    async fn get_alert_history(
        &self,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<AlertRecord>> {
        debug!("Getting alert history");

        let mut query = String::from(r#"
            SELECT id, rule_id, severity, message, triggered_value, threshold_value,
                   status, triggered_at, acknowledged_at, resolved_at, acknowledged_by
            FROM monitoring_alerts
            WHERE 1=1
        "#);

        if start_time.is_some() {
            query.push_str(" AND triggered_at >= $1");
        }

        if end_time.is_some() {
            let param_num = if start_time.is_some() { 2 } else { 1 };
            query.push_str(&format!(" AND triggered_at <= ${}", param_num));
        }

        query.push_str(" ORDER BY triggered_at DESC");

        if let Some(_l) = limit {
            let _param_num = match (start_time.is_some(), end_time.is_some()) {
                (true, true) => 3,
                (true, false) | (false, true) => 2,
                (false, false) => 1,
            };
            // query.push_str(&format!(" LIMIT ${}", param_num));
        }

        // 简化版本查询
        let simple_query = r#"
            SELECT id, rule_id, severity, message, triggered_value, threshold_value,
                   status, triggered_at, acknowledged_at, resolved_at, acknowledged_by
            FROM monitoring_alerts
            ORDER BY triggered_at DESC
            LIMIT 100
        "#;

        let rows = sqlx::query(simple_query)
            .fetch_all(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query alert history: {}", e)))?;

        let mut alerts = Vec::new();
        for row in rows {
            alerts.push(self.row_to_alert_record(&row)?);
        }

        debug!("Retrieved {} alert history records", alerts.len());
        Ok(alerts)
    }

    // ============================================================================
    // 统计信息
    // ============================================================================

    async fn get_config_statistics(&self) -> SigmaXResult<MonitoringConfigStatistics> {
        debug!("Getting monitoring config statistics");

        let query = r#"
            SELECT
                COUNT(*) as total_configs,
                COUNT(*) FILTER (WHERE enabled = true) as enabled_configs,
                MAX(updated_at) as last_updated,
                AVG(LENGTH(monitoring_parameters::text)) as average_config_size
            FROM monitoring_config
        "#;

        let row = sqlx::query(query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query config statistics: {}", e)))?;

        let stats = MonitoringConfigStatistics {
            total_configs: row.try_get::<i64, _>("total_configs")
                .map_err(|e| SigmaXError::Database(format!("Failed to get total_configs: {}", e)))? as u64,
            enabled_configs: row.try_get::<i64, _>("enabled_configs")
                .map_err(|e| SigmaXError::Database(format!("Failed to get enabled_configs: {}", e)))? as u64,
            last_updated: row.try_get("last_updated")
                .map_err(|e| SigmaXError::Database(format!("Failed to get last_updated: {}", e)))?,
            average_config_size: row.try_get::<Option<f64>, _>("average_config_size")
                .map_err(|e| SigmaXError::Database(format!("Failed to get average_config_size: {}", e)))?
                .unwrap_or(0.0),
        };

        debug!("Retrieved monitoring config statistics");
        Ok(stats)
    }

    async fn get_alert_statistics(
        &self,
        _start_time: Option<DateTime<Utc>>,
        _end_time: Option<DateTime<Utc>>,
    ) -> SigmaXResult<serde_json::Value> {
        debug!("Getting alert statistics");

        let query = r#"
            SELECT
                COUNT(*) as total_alerts,
                COUNT(*) FILTER (WHERE status = 'triggered') as triggered_alerts,
                COUNT(*) FILTER (WHERE status = 'acknowledged') as acknowledged_alerts,
                COUNT(*) FILTER (WHERE status = 'resolved') as resolved_alerts,
                COUNT(*) FILTER (WHERE severity = 'critical') as critical_alerts,
                COUNT(*) FILTER (WHERE severity = 'warning') as warning_alerts,
                AVG(EXTRACT(EPOCH FROM (COALESCE(resolved_at, NOW()) - triggered_at))) as avg_resolution_time
            FROM monitoring_alerts
            WHERE 1=1
        "#;

        let row = sqlx::query(query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to query alert statistics: {}", e)))?;

        let stats = serde_json::json!({
            "total_alerts": row.try_get::<i64, _>("total_alerts").unwrap_or(0),
            "triggered_alerts": row.try_get::<i64, _>("triggered_alerts").unwrap_or(0),
            "acknowledged_alerts": row.try_get::<i64, _>("acknowledged_alerts").unwrap_or(0),
            "resolved_alerts": row.try_get::<i64, _>("resolved_alerts").unwrap_or(0),
            "critical_alerts": row.try_get::<i64, _>("critical_alerts").unwrap_or(0),
            "warning_alerts": row.try_get::<i64, _>("warning_alerts").unwrap_or(0),
            "avg_resolution_time_seconds": row.try_get::<Option<f64>, _>("avg_resolution_time").unwrap_or(None)
        });

        debug!("Retrieved alert statistics");
        Ok(stats)
    }

    // ============================================================================
    // 健康检查
    // ============================================================================

    async fn health_check(&self) -> SigmaXResult<bool> {
        debug!("Performing monitoring repository health check");

        let query = "SELECT 1 as health_check";

        match sqlx::query(query).fetch_one(self.get_pool()).await {
            Ok(_) => {
                debug!("Monitoring repository health check passed");
                Ok(true)
            }
            Err(e) => {
                error!("Monitoring repository health check failed: {}", e);
                Ok(false)
            }
        }
    }

    async fn get_repository_info(&self) -> SigmaXResult<serde_json::Value> {
        debug!("Getting monitoring repository info");

        let config_count_query = "SELECT COUNT(*) as count FROM monitoring_config";
        let metrics_count_query = "SELECT COUNT(*) as count FROM monitoring_metrics";
        let alerts_count_query = "SELECT COUNT(*) as count FROM monitoring_alerts";

        let config_count: i64 = sqlx::query(config_count_query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to get config count: {}", e)))?
            .try_get("count")
            .unwrap_or(0);

        let metrics_count: i64 = sqlx::query(metrics_count_query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to get metrics count: {}", e)))?
            .try_get("count")
            .unwrap_or(0);

        let alerts_count: i64 = sqlx::query(alerts_count_query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::Database(format!("Failed to get alerts count: {}", e)))?
            .try_get("count")
            .unwrap_or(0);

        let info = serde_json::json!({
            "repository_type": "SqlMonitoringRepository",
            "database_type": "PostgreSQL",
            "config_count": config_count,
            "metrics_count": metrics_count,
            "alerts_count": alerts_count,
            "features": [
                "JSONB storage",
                "Full-text search",
                "Time-series data",
                "Alert management",
                "Performance monitoring"
            ]
        });

        debug!("Retrieved monitoring repository info");
        Ok(info)
    }
}
