//! SQLx 系统配置仓储实现
//!
//! 提供基于SQLx的强类型系统配置管理

use std::sync::Arc;
use std::collections::HashMap;
use sqlx::{Row, Postgres, Transaction};
use chrono::{DateTime, Utc};
use sigmax_core::{
    SigmaXResult, SigmaXError, SystemConfigRecord, SystemConfigFilter, SystemConfigBatchResult,
    TradingConfig, RiskManagementConfig, SystemGeneralConfig, NotificationConfig,
    ApiConfig, DatabaseSystemConfig, CacheConfig, MonitoringConfig, StrategySystemConfig,
    StrategyTemplate, ExchangeSystemConfig, UserRoleConfig
};
use crate::DatabaseManager;
use crate::repositories::traits::{SystemConfigRepository, ConfigStatistics};

/// SQLx系统配置仓储实现
pub struct SqlSystemConfigRepository {
    db: Arc<DatabaseManager>,
}

impl SqlSystemConfigRepository {
    /// 创建新的系统配置仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 从数据库行构建SystemConfigRecord
    fn row_to_config_record(row: &sqlx::postgres::PgRow) -> SigmaXResult<SystemConfigRecord> {
        Ok(SystemConfigRecord {
            id: row.try_get("id").map_err(|e| SigmaXError::Database(e.to_string()))?,
            key: row.try_get("key").map_err(|e| SigmaXError::Database(e.to_string()))?,
            value: row.try_get("value").map_err(|e| SigmaXError::Database(e.to_string()))?,
            description: row.try_get("description").map_err(|e| SigmaXError::Database(e.to_string()))?,
            is_encrypted: row.try_get("is_encrypted").map_err(|e| SigmaXError::Database(e.to_string()))?,
            created_at: row.try_get("created_at").map_err(|e| SigmaXError::Database(e.to_string()))?,
            updated_at: row.try_get("updated_at").map_err(|e| SigmaXError::Database(e.to_string()))?,
        })
    }

    /// 构建WHERE子句和参数
    fn build_filter_query(filter: &SystemConfigFilter) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(ref pattern) = filter.key_pattern {
            conditions.push(format!("key ILIKE ${}", param_index));
            params.push(format!("%{}%", pattern));
            param_index += 1;
        }

        if let Some(ref namespace) = filter.namespace {
            conditions.push(format!("key LIKE ${}", param_index));
            params.push(format!("{}%", namespace));
            param_index += 1;
        }

        if let Some(is_encrypted) = filter.is_encrypted {
            conditions.push(format!("is_encrypted = ${}", param_index));
            params.push(is_encrypted.to_string());
            param_index += 1;
        }

        if let Some(created_after) = filter.created_after {
            conditions.push(format!("created_at >= ${}", param_index));
            params.push(created_after.to_rfc3339());
            param_index += 1;
        }

        if let Some(created_before) = filter.created_before {
            conditions.push(format!("created_at <= ${}", param_index));
            params.push(created_before.to_rfc3339());
            param_index += 1;
        }

        if let Some(updated_after) = filter.updated_after {
            conditions.push(format!("updated_at >= ${}", param_index));
            params.push(updated_after.to_rfc3339());
            param_index += 1;
        }

        if let Some(updated_before) = filter.updated_before {
            conditions.push(format!("updated_at <= ${}", param_index));
            params.push(updated_before.to_rfc3339());
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }

    /// 从配置记录列表构建强类型配置
    async fn build_typed_config<T>(&self, namespace: &str) -> SigmaXResult<T>
    where
        T: serde::de::DeserializeOwned + Default,
    {
        let configs = self.get_configs_by_namespace(namespace).await?;
        
        if configs.is_empty() {
            return Ok(T::default());
        }

        // 构建配置映射
        let mut config_map = serde_json::Map::new();
        for config in configs {
            let key_name = config.get_key_name();
            config_map.insert(key_name, config.value);
        }

        let config_value = serde_json::Value::Object(config_map);
        serde_json::from_value(config_value)
            .map_err(|e| SigmaXError::Config(format!("Failed to deserialize {} config: {}", namespace, e)))
    }

    /// 保存强类型配置到数据库
    async fn save_typed_config<T>(&self, namespace: &str, config: &T, description_prefix: &str) -> SigmaXResult<()>
    where
        T: serde::Serialize,
    {
        let config_value = serde_json::to_value(config)
            .map_err(|e| SigmaXError::Config(format!("Failed to serialize {} config: {}", namespace, e)))?;

        if let serde_json::Value::Object(map) = config_value {
            let mut configs = Vec::new();
            
            for (key, value) in map {
                let full_key = format!("{}.{}", namespace, key);
                let description = Some(format!("{} - {}", description_prefix, key));
                
                let config_record = SystemConfigRecord::new(
                    full_key,
                    value,
                    description,
                    false, // 默认不加密
                );
                configs.push(config_record);
            }

            self.batch_save_configs(&configs).await?;
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl SystemConfigRepository for SqlSystemConfigRepository {
    // ============================================================================
    // 基础CRUD操作
    // ============================================================================

    async fn get_config(&self, key: &str) -> SigmaXResult<Option<SystemConfigRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            "SELECT id, key, value, description, is_encrypted, created_at, updated_at 
             FROM system_config WHERE key = $1"
        )
        .bind(key)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        match row {
            Some(row) => Ok(Some(Self::row_to_config_record(&row)?)),
            None => Ok(None),
        }
    }

    async fn get_configs(&self, keys: &[String]) -> SigmaXResult<Vec<SystemConfigRecord>> {
        if keys.is_empty() {
            return Ok(Vec::new());
        }

        let pool = self.db.pool();
        let placeholders: Vec<String> = (1..=keys.len()).map(|i| format!("${}", i)).collect();
        let query = format!(
            "SELECT id, key, value, description, is_encrypted, created_at, updated_at 
             FROM system_config WHERE key = ANY(ARRAY[{}])",
            placeholders.join(",")
        );

        let mut query_builder = sqlx::query(&query);
        for key in keys {
            query_builder = query_builder.bind(key);
        }

        let rows = query_builder
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let mut configs = Vec::new();
        for row in rows {
            configs.push(Self::row_to_config_record(&row)?);
        }

        Ok(configs)
    }

    async fn get_all_configs(&self) -> SigmaXResult<Vec<SystemConfigRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            "SELECT id, key, value, description, is_encrypted, created_at, updated_at 
             FROM system_config ORDER BY key"
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let mut configs = Vec::new();
        for row in rows {
            configs.push(Self::row_to_config_record(&row)?);
        }

        Ok(configs)
    }

    async fn find_configs(&self, filter: &SystemConfigFilter) -> SigmaXResult<Vec<SystemConfigRecord>> {
        let pool = self.db.pool();
        let (where_clause, params) = Self::build_filter_query(filter);

        let query = format!(
            "SELECT id, key, value, description, is_encrypted, created_at, updated_at 
             FROM system_config {} ORDER BY key",
            where_clause
        );

        let mut query_builder = sqlx::query(&query);
        for param in params {
            query_builder = query_builder.bind(param);
        }

        let rows = query_builder
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let mut configs = Vec::new();
        for row in rows {
            configs.push(Self::row_to_config_record(&row)?);
        }

        Ok(configs)
    }

    async fn save_config(&self, config: &SystemConfigRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO system_config (key, value, description, is_encrypted, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (key) DO UPDATE SET
                value = EXCLUDED.value,
                description = EXCLUDED.description,
                is_encrypted = EXCLUDED.is_encrypted,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&config.key)
        .bind(&config.value)
        .bind(&config.description)
        .bind(config.is_encrypted)
        .bind(config.created_at)
        .bind(config.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        Ok(())
    }

    async fn update_config(&self, config: &SystemConfigRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        let result = sqlx::query(
            "UPDATE system_config SET value = $1, description = $2, is_encrypted = $3, updated_at = $4 
             WHERE key = $5"
        )
        .bind(&config.value)
        .bind(&config.description)
        .bind(config.is_encrypted)
        .bind(config.updated_at)
        .bind(&config.key)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Config with key '{}' not found", config.key)));
        }

        Ok(())
    }

    async fn delete_config(&self, key: &str) -> SigmaXResult<()> {
        let pool = self.db.pool();

        let result = sqlx::query("DELETE FROM system_config WHERE key = $1")
            .bind(key)
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Config with key '{}' not found", key)));
        }

        Ok(())
    }

    async fn batch_save_configs(&self, configs: &[SystemConfigRecord]) -> SigmaXResult<SystemConfigBatchResult> {
        let start_time = std::time::Instant::now();
        let mut result = SystemConfigBatchResult::new();

        for config in configs {
            match self.save_config(config).await {
                Ok(_) => result.add_success(),
                Err(e) => result.add_failure(config.key.clone(), e.to_string()),
            }
        }

        result.set_duration(start_time.elapsed().as_millis() as u64);
        Ok(result)
    }

    async fn batch_update_configs(&self, configs: &[SystemConfigRecord]) -> SigmaXResult<SystemConfigBatchResult> {
        let start_time = std::time::Instant::now();
        let mut result = SystemConfigBatchResult::new();

        for config in configs {
            match self.update_config(config).await {
                Ok(_) => result.add_success(),
                Err(e) => result.add_failure(config.key.clone(), e.to_string()),
            }
        }

        result.set_duration(start_time.elapsed().as_millis() as u64);
        Ok(result)
    }

    async fn batch_delete_configs(&self, keys: &[String]) -> SigmaXResult<SystemConfigBatchResult> {
        let start_time = std::time::Instant::now();
        let mut result = SystemConfigBatchResult::new();

        for key in keys {
            match self.delete_config(key).await {
                Ok(_) => result.add_success(),
                Err(e) => result.add_failure(key.clone(), e.to_string()),
            }
        }

        result.set_duration(start_time.elapsed().as_millis() as u64);
        Ok(result)
    }

    // ============================================================================
    // 强类型配置访问
    // ============================================================================

    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig> {
        self.build_typed_config("trading").await
    }

    async fn save_trading_config(&self, config: &TradingConfig) -> SigmaXResult<()> {
        self.save_typed_config("trading", config, "交易配置").await
    }

    async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig> {
        self.build_typed_config("risk").await
    }

    async fn save_risk_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()> {
        self.save_typed_config("risk", config, "风险管理配置").await
    }

    async fn get_system_config(&self) -> SigmaXResult<SystemGeneralConfig> {
        self.build_typed_config("system").await
    }

    async fn save_system_config(&self, config: &SystemGeneralConfig) -> SigmaXResult<()> {
        self.save_typed_config("system", config, "系统配置").await
    }

    async fn get_notification_config(&self) -> SigmaXResult<NotificationConfig> {
        self.build_typed_config("notifications").await
    }

    async fn save_notification_config(&self, config: &NotificationConfig) -> SigmaXResult<()> {
        self.save_typed_config("notifications", config, "通知配置").await
    }

    async fn get_api_config(&self) -> SigmaXResult<ApiConfig> {
        self.build_typed_config("api").await
    }

    async fn save_api_config(&self, config: &ApiConfig) -> SigmaXResult<()> {
        self.save_typed_config("api", config, "API配置").await
    }

    async fn get_database_config(&self) -> SigmaXResult<DatabaseSystemConfig> {
        self.build_typed_config("database").await
    }

    async fn save_database_config(&self, config: &DatabaseSystemConfig) -> SigmaXResult<()> {
        self.save_typed_config("database", config, "数据库配置").await
    }

    async fn get_cache_config(&self) -> SigmaXResult<CacheConfig> {
        self.build_typed_config("cache").await
    }

    async fn save_cache_config(&self, config: &CacheConfig) -> SigmaXResult<()> {
        self.save_typed_config("cache", config, "缓存配置").await
    }

    async fn get_monitoring_config(&self) -> SigmaXResult<MonitoringConfig> {
        self.build_typed_config("monitoring").await
    }

    async fn save_monitoring_config(&self, config: &MonitoringConfig) -> SigmaXResult<()> {
        self.save_typed_config("monitoring", config, "监控配置").await
    }

    async fn get_strategy_config(&self) -> SigmaXResult<StrategySystemConfig> {
        self.build_typed_config("strategy").await
    }

    async fn save_strategy_config(&self, config: &StrategySystemConfig) -> SigmaXResult<()> {
        self.save_typed_config("strategy", config, "策略配置").await
    }

    // ============================================================================
    // 复杂配置管理 - 策略模板
    // ============================================================================

    async fn get_strategy_template(&self, template_name: &str) -> SigmaXResult<Option<StrategyTemplate>> {
        let key = format!("strategy_templates.{}", template_name);
        match self.get_config(&key).await? {
            Some(config) => Ok(Some(config.get_value()?)),
            None => Ok(None),
        }
    }

    async fn get_all_strategy_templates(&self) -> SigmaXResult<Vec<(String, StrategyTemplate)>> {
        let configs = self.get_configs_by_namespace("strategy_templates").await?;
        let mut templates = Vec::new();

        for config in configs {
            let template_name = config.get_key_name();
            let template: StrategyTemplate = config.get_value()?;
            templates.push((template_name, template));
        }

        Ok(templates)
    }

    async fn save_strategy_template(&self, template_name: &str, template: &StrategyTemplate) -> SigmaXResult<()> {
        let key = format!("strategy_templates.{}", template_name);
        let description = Some(format!("{}策略模板", template_name));

        let mut config = SystemConfigRecord::new(key, serde_json::Value::Null, description, false);
        config.set_value(template)?;

        self.save_config(&config).await
    }

    async fn delete_strategy_template(&self, template_name: &str) -> SigmaXResult<()> {
        let key = format!("strategy_templates.{}", template_name);
        self.delete_config(&key).await
    }

    // ============================================================================
    // 交易所配置管理
    // ============================================================================

    async fn get_exchange_config(&self, exchange_name: &str) -> SigmaXResult<Option<ExchangeSystemConfig>> {
        let key = format!("exchange_config.{}", exchange_name);
        match self.get_config(&key).await? {
            Some(config) => Ok(Some(config.get_value()?)),
            None => Ok(None),
        }
    }

    async fn get_all_exchange_configs(&self) -> SigmaXResult<Vec<(String, ExchangeSystemConfig)>> {
        let configs = self.get_configs_by_namespace("exchange_config").await?;
        let mut exchanges = Vec::new();

        for config in configs {
            let exchange_name = config.get_key_name();
            let exchange_config: ExchangeSystemConfig = config.get_value()?;
            exchanges.push((exchange_name, exchange_config));
        }

        Ok(exchanges)
    }

    async fn save_exchange_config(&self, exchange_name: &str, config: &ExchangeSystemConfig) -> SigmaXResult<()> {
        let key = format!("exchange_config.{}", exchange_name);
        let description = Some(format!("{}交易所配置", exchange_name));

        let mut config_record = SystemConfigRecord::new(key, serde_json::Value::Null, description, false);
        config_record.set_value(config)?;

        self.save_config(&config_record).await
    }

    async fn delete_exchange_config(&self, exchange_name: &str) -> SigmaXResult<()> {
        let key = format!("exchange_config.{}", exchange_name);
        self.delete_config(&key).await
    }

    // ============================================================================
    // 用户角色配置管理
    // ============================================================================

    async fn get_user_role_config(&self, role_name: &str) -> SigmaXResult<Option<UserRoleConfig>> {
        let key = format!("user_roles.{}", role_name);
        match self.get_config(&key).await? {
            Some(config) => Ok(Some(config.get_value()?)),
            None => Ok(None),
        }
    }

    async fn get_all_user_role_configs(&self) -> SigmaXResult<Vec<(String, UserRoleConfig)>> {
        let configs = self.get_configs_by_namespace("user_roles").await?;
        let mut roles = Vec::new();

        for config in configs {
            let role_name = config.get_key_name();
            let role_config: UserRoleConfig = config.get_value()?;
            roles.push((role_name, role_config));
        }

        Ok(roles)
    }

    async fn save_user_role_config(&self, role_name: &str, config: &UserRoleConfig) -> SigmaXResult<()> {
        let key = format!("user_roles.{}", role_name);
        let description = Some(format!("{}角色权限", role_name));

        let mut config_record = SystemConfigRecord::new(key, serde_json::Value::Null, description, false);
        config_record.set_value(config)?;

        self.save_config(&config_record).await
    }

    async fn delete_user_role_config(&self, role_name: &str) -> SigmaXResult<()> {
        let key = format!("user_roles.{}", role_name);
        self.delete_config(&key).await
    }

    // ============================================================================
    // 命名空间操作
    // ============================================================================

    async fn get_configs_by_namespace(&self, namespace: &str) -> SigmaXResult<Vec<SystemConfigRecord>> {
        let filter = SystemConfigFilter::new().with_namespace(namespace);
        self.find_configs(&filter).await
    }

    async fn delete_configs_by_namespace(&self, namespace: &str) -> SigmaXResult<SystemConfigBatchResult> {
        let configs = self.get_configs_by_namespace(namespace).await?;
        let keys: Vec<String> = configs.into_iter().map(|c| c.key).collect();
        self.batch_delete_configs(&keys).await
    }

    async fn get_all_namespaces(&self) -> SigmaXResult<Vec<String>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            "SELECT DISTINCT split_part(key, '.', 1) as namespace
             FROM system_config
             WHERE key LIKE '%.%'
             ORDER BY namespace"
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let mut namespaces = Vec::new();
        for row in rows {
            let namespace: String = row.try_get("namespace")
                .map_err(|e| SigmaXError::Database(e.to_string()))?;
            namespaces.push(namespace);
        }

        Ok(namespaces)
    }

    // ============================================================================
    // 配置历史和备份（简化实现）
    // ============================================================================

    async fn get_config_history(&self, key: &str, _limit: Option<u32>) -> SigmaXResult<Vec<SystemConfigRecord>> {
        // 简化实现：只返回当前配置
        match self.get_config(key).await? {
            Some(config) => Ok(vec![config]),
            None => Ok(Vec::new()),
        }
    }

    async fn create_config_backup(&self, _backup_name: &str) -> SigmaXResult<()> {
        // 简化实现：暂不支持备份功能
        Ok(())
    }

    async fn restore_from_backup(&self, _backup_name: &str) -> SigmaXResult<SystemConfigBatchResult> {
        // 简化实现：暂不支持恢复功能
        Ok(SystemConfigBatchResult::new())
    }

    async fn get_config_statistics(&self) -> SigmaXResult<ConfigStatistics> {
        let pool = self.db.pool();

        // 获取总配置数量
        let total_row = sqlx::query("SELECT COUNT(*) as total FROM system_config")
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let total_configs: i64 = total_row.try_get("total")
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        // 获取加密配置数量
        let encrypted_row = sqlx::query("SELECT COUNT(*) as encrypted FROM system_config WHERE is_encrypted = true")
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let encrypted_configs: i64 = encrypted_row.try_get("encrypted")
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        // 获取最近更新时间
        let last_updated_row = sqlx::query("SELECT MAX(updated_at) as last_updated FROM system_config")
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let last_updated: Option<DateTime<Utc>> = last_updated_row.try_get("last_updated")
            .map_err(|e| SigmaXError::Database(e.to_string()))?;

        // 获取按命名空间分组的配置数量
        let namespace_rows = sqlx::query(
            "SELECT split_part(key, '.', 1) as namespace, COUNT(*) as count
             FROM system_config
             WHERE key LIKE '%.%'
             GROUP BY split_part(key, '.', 1)"
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        let mut configs_by_namespace = HashMap::new();
        for row in namespace_rows {
            let namespace: String = row.try_get("namespace")
                .map_err(|e| SigmaXError::Database(e.to_string()))?;
            let count: i64 = row.try_get("count")
                .map_err(|e| SigmaXError::Database(e.to_string()))?;
            configs_by_namespace.insert(namespace, count as u64);
        }

        Ok(ConfigStatistics {
            total_configs: total_configs as u64,
            configs_by_namespace,
            encrypted_configs: encrypted_configs as u64,
            last_updated,
            total_size_bytes: 0, // 简化实现
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use chrono::Utc;
    use sigmax_core::{
        SystemConfigRecord, SystemConfigFilter, TradingConfig, RiskManagementConfig,
        SystemGeneralConfig, NotificationConfig, StrategyTemplate, ExchangeSystemConfig,
        UserRoleConfig, ExchangeFees, ExchangeRateLimits
    };
    use crate::DatabaseManager;

    async fn setup_test_db() -> Arc<DatabaseManager> {
        // 使用模拟数据库进行测试
        let db_manager = DatabaseManager::new_mock()
            .expect("Failed to create mock database manager");

        Arc::new(db_manager)
    }

    #[tokio::test]
    async fn test_config_record_creation() {
        let config = SystemConfigRecord::new(
            "test.key",
            serde_json::json!("test_value"),
            Some("测试配置".to_string()),
            false,
        );

        assert_eq!(config.key, "test.key");
        assert_eq!(config.value, serde_json::json!("test_value"));
        assert_eq!(config.description, Some("测试配置".to_string()));
        assert!(!config.is_encrypted);
    }

    #[tokio::test]
    async fn test_config_record_methods() {
        let mut config = SystemConfigRecord::new(
            "test.namespace.key",
            serde_json::json!("test_value"),
            Some("测试配置".to_string()),
            false,
        );

        // 测试命名空间检查
        assert!(config.is_in_namespace("test"));
        assert!(!config.is_in_namespace("other"));

        // 测试获取命名空间
        assert_eq!(config.get_namespace(), Some("test".to_string()));

        // 测试获取键名
        assert_eq!(config.get_key_name(), "namespace.key");

        // 测试更新值
        config.update_value(serde_json::json!("new_value"));
        assert_eq!(config.value, serde_json::json!("new_value"));

        // 测试类型化值设置和获取
        assert!(config.set_value("typed_value").is_ok());
        let retrieved: String = config.get_value().unwrap();
        assert_eq!(retrieved, "typed_value");
    }

    #[tokio::test]
    async fn test_config_validation() {
        use sigmax_core::Validatable;

        // 测试有效配置
        let valid_config = SystemConfigRecord::new(
            "valid.key",
            serde_json::json!("value"),
            Some("Valid config".to_string()),
            false,
        );
        assert!(valid_config.validate().is_ok());

        // 测试无效配置（空键名）
        let invalid_config = SystemConfigRecord::new(
            "",
            serde_json::json!("value"),
            Some("Invalid config".to_string()),
            false,
        );
        assert!(invalid_config.validate().is_err());

        // 测试交易配置验证
        let valid_trading_config = TradingConfig {
            max_orders_per_strategy: 100,
            max_position_size: rust_decimal::Decimal::new(1000000, 0),
            default_order_timeout: 3600,
            min_order_interval: 1,
            max_slippage_percent: 0.5,
        };
        assert!(valid_trading_config.validate().is_ok());

        let invalid_trading_config = TradingConfig {
            max_orders_per_strategy: 0, // 无效值
            max_position_size: rust_decimal::Decimal::ZERO, // 无效值
            default_order_timeout: 3600,
            min_order_interval: 1,
            max_slippage_percent: 150.0, // 无效值
        };
        assert!(invalid_trading_config.validate().is_err());
    }

    #[tokio::test]
    async fn test_filter_builder() {
        let filter = SystemConfigFilter::new()
            .with_namespace("trading")
            .with_encryption(false)
            .with_key_pattern("max_orders");

        assert_eq!(filter.namespace, Some("trading".to_string()));
        assert_eq!(filter.is_encrypted, Some(false));
        assert_eq!(filter.key_pattern, Some("max_orders".to_string()));
    }

    #[tokio::test]
    async fn test_batch_result() {
        let mut result = SystemConfigBatchResult::new();

        assert_eq!(result.total_count(), 0);
        assert_eq!(result.success_rate(), 0.0);
        assert!(!result.has_failures());

        result.add_success();
        result.add_success();
        result.add_failure("key1".to_string(), "error1".to_string());

        assert_eq!(result.success_count, 2);
        assert_eq!(result.failure_count, 1);
        assert_eq!(result.total_count(), 3);
        assert!((result.success_rate() - 0.6666666666666666).abs() < f64::EPSILON);
        assert!(result.has_failures());

        result.set_duration(1000);
        assert_eq!(result.duration_ms, 1000);
    }
}
