//! SQLx Repository Implementations
//!
//! 基于 SQLx 的数据仓储实现

pub mod sql_order_repository;
pub mod sql_enhanced_order_repository;
pub mod sql_trade_repository;
pub mod sql_strategy_repository;
pub mod sql_event_repository;
pub mod sql_risk_repository;
pub mod sql_system_config_repository;
pub mod sql_notification_repository;
pub mod sql_api_repository;
pub mod sql_trading_repository;
pub mod sql_monitoring_repository;
pub mod sql_cache_repository;

// 重新导出所有实现
pub use sql_order_repository::SqlOrderRepository;
pub use sql_enhanced_order_repository::SqlEnhancedOrderRepository;
pub use sql_trade_repository::SqlTradeRepository;
pub use sql_strategy_repository::SqlStrategyRepository;
pub use sql_event_repository::SqlEventRepository;
pub use sql_risk_repository::SqlRiskRepository;
pub use sql_system_config_repository::SqlSystemConfigRepository;
pub use sql_notification_repository::SqlNotificationRepository;
pub use sql_api_repository::SqlApiRepository;
pub use sql_trading_repository::SqlTradingRepository;
pub use sql_monitoring_repository::SqlMonitoringRepository;
pub use sql_cache_repository::SqlCacheRepository;
