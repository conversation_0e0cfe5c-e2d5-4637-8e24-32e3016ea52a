//! 存储实现

use sigmax_core::SigmaXResult;

/// 存储接口
pub trait Storage: Send + Sync {
    /// 保存数据
    async fn save(&self, key: &str, data: &[u8]) -> SigmaXResult<()>;
    
    /// 获取数据
    async fn get(&self, key: &str) -> SigmaXResult<Option<Vec<u8>>>;
    
    /// 删除数据
    async fn delete(&self, key: &str) -> SigmaXResult<()>;
}

/// 内存存储实现
pub struct MemoryStorage {
    // 实现细节
}

impl MemoryStorage {
    pub fn new() -> Self {
        Self {}
    }
}

impl Storage for MemoryStorage {
    async fn save(&self, _key: &str, _data: &[u8]) -> SigmaXResult<()> {
        todo!("实现内存存储保存")
    }
    
    async fn get(&self, _key: &str) -> SigmaXResult<Option<Vec<u8>>> {
        todo!("实现内存存储获取")
    }
    
    async fn delete(&self, _key: &str) -> SigmaXResult<()> {
        todo!("实现内存存储删除")
    }
}
