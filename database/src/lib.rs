//! SigmaX Memory Bank Module
//! 
//! 系统状态和历史数据存储，包括数据库管理、缓存和数据仓库

pub mod database;
pub mod cache;
pub mod storage;
pub mod repositories;
pub mod repository_factory;
pub mod performance;

// 保持向后兼容的旧模块导出
pub mod repository {
    //! 向后兼容模块
    //!
    //! 此模块保持向后兼容性，重新导出新的 repositories 模块内容

    pub use crate::repositories::*;
}

// 重新导出主要类型
pub use cache::{Cache, MemoryCache, CacheManager, CacheConfig, CacheStats};
pub use storage::{Storage, MemoryStorage};
pub use database::DatabaseManager;

// 重新导出仓储接口 (Traits)
pub use repositories::traits::{
    OrderRepository, TradeRepository, StrategyRepository, EventRepository,
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort,
    OrderSortBy, SortDirection, OrderStatistics, OrderRepositoryBuilder,
    RiskRepository, RiskCheckRecord, RiskRuleRecord, RiskStatisticsRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter,
    SystemConfigRepository, ConfigStatistics
};

// 重新导出仓储工厂
pub use repository_factory::{
    RepositoryFactory, RepositoryType, RepositoryFactoryConfig, MockRepositoryConfig,
    create_sqlx_factory, create_mock_factory, create_mock_factory_with_errors
};

// 可选的 Diesel 工厂函数
#[cfg(feature = "diesel")]
pub use repository_factory::create_diesel_factory;

// 为了向后兼容，临时导出具体实现（仅用于 web 模块）
pub use repositories::sqlx::{
    SqlOrderRepository, SqlEnhancedOrderRepository, SqlTradeRepository, SqlStrategyRepository, SqlEventRepository, SqlRiskRepository
};

// 导出性能监控相关类型
pub use performance::{
    PerformanceMonitor, PerformanceConfig, PerformanceStats, QueryMetrics, SlowQuery,
    ConnectionPoolStatus, QueryType
};

// Diesel ORM 模块 (可选)
#[cfg(feature = "diesel")]
pub mod schema;
#[cfg(feature = "diesel")]
pub mod diesel_models;
