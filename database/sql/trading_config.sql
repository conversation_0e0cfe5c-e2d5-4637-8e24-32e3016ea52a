-- ============================================================================
-- SigmaX 交易配置表
-- ============================================================================
-- 
-- 专门存储交易相关配置，使用JSONB格式存储完整的交易参数
-- 支持高效查询和索引优化
--
-- 创建时间: 2025-06-30
-- 作者: Claude 4.0 sonnet
-- 版本: v1.0
-- ============================================================================

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS "public"."trading_config";

-- 删除序列（如果存在）
DROP SEQUENCE IF EXISTS trading_config_id_seq;
CREATE SEQUENCE trading_config_id_seq START 1;

-- 创建交易配置表
CREATE TABLE "public"."trading_config" (
    -- 主键
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 配置基本信息
    "name" VARCHAR(100) NOT NULL DEFAULT 'default',
    "description" TEXT DEFAULT '默认交易配置',
    "enabled" BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 交易参数 (JSONB存储完整配置)
    "trading_parameters" JSONB NOT NULL,
    
    -- 元数据
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "created_by" VARCHAR(100) DEFAULT 'system'
);

-- 创建索引
CREATE INDEX idx_trading_config_enabled ON trading_config(enabled);
CREATE INDEX idx_trading_config_name ON trading_config(name);
CREATE INDEX idx_trading_config_parameters_gin ON trading_config USING gin(trading_parameters);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_trading_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_trading_config_updated_at
    BEFORE UPDATE ON trading_config
    FOR EACH ROW
    EXECUTE FUNCTION update_trading_config_updated_at();

-- 插入默认配置
INSERT INTO "public"."trading_config" (
    "name",
    "description", 
    "enabled",
    "trading_parameters",
    "created_by"
) VALUES (
    'default',
    '系统默认交易配置',
    TRUE,
    '{
        "max_orders_per_strategy": 100,
        "max_position_size": "1000000",
        "default_order_timeout": 3600,
        "min_order_interval": 1,
        "max_slippage_percent": 0.5
    }'::jsonb,
    'system'
);

-- 添加注释
COMMENT ON TABLE trading_config IS '交易配置表 - 存储系统交易相关配置参数';
COMMENT ON COLUMN trading_config.id IS '配置唯一标识符';
COMMENT ON COLUMN trading_config.name IS '配置名称';
COMMENT ON COLUMN trading_config.description IS '配置描述';
COMMENT ON COLUMN trading_config.enabled IS '是否启用此配置';
COMMENT ON COLUMN trading_config.trading_parameters IS '交易参数JSON配置';
COMMENT ON COLUMN trading_config.created_at IS '创建时间';
COMMENT ON COLUMN trading_config.updated_at IS '最后更新时间';
COMMENT ON COLUMN trading_config.created_by IS '创建者';

-- 验证数据
SELECT 
    id,
    name,
    description,
    enabled,
    jsonb_pretty(trading_parameters) as config,
    created_at,
    created_by
FROM trading_config;
