/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:11:34
*/


-- ----------------------------
-- Table structure for trading_pairs
-- ----------------------------
DROP TABLE IF EXISTS "public"."trading_pairs";
CREATE TABLE "public"."trading_pairs" (
  "id" int4 NOT NULL DEFAULT nextval('trading_pairs_id_seq'::regclass),
  "symbol" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "base_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quote_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "exchange_id" int4,
  "is_active" bool NOT NULL DEFAULT true,
  "min_quantity" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "max_quantity" numeric(20,8),
  "quantity_precision" int4 NOT NULL DEFAULT 8,
  "price_precision" int4 NOT NULL DEFAULT 8,
  "min_price" numeric(20,8),
  "max_price" numeric(20,8),
  "tick_size" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "min_notional" numeric(20,8) NOT NULL DEFAULT 0.001,
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;

-- ----------------------------
-- Records of trading_pairs
-- ----------------------------
BEGIN;
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (1, 'BTC/USDT', 'BTC', 'USDT', 1, 't', 0.00001000, NULL, 5, 2, NULL, NULL, 0.01000000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (2, 'ETH/USDT', 'ETH', 'USDT', 1, 't', 0.00100000, NULL, 3, 2, NULL, NULL, 0.01000000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (3, 'BNB/USDT', 'BNB', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (4, 'ADA/USDT', 'ADA', 'USDT', 1, 't', 1.00000000, NULL, 0, 6, NULL, NULL, 0.00000100, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (5, 'SOL/USDT', 'SOL', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (6, 'DOT/USDT', 'DOT', 'USDT', 1, 't', 0.10000000, NULL, 1, 4, NULL, NULL, 0.00010000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (7, 'MATIC/USDT', 'MATIC', 'USDT', 1, 't', 1.00000000, NULL, 0, 6, NULL, NULL, 0.00000100, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (8, 'AVAX/USDT', 'AVAX', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (9, 'LINK/USDT', 'LINK', 'USDT', 1, 't', 0.01000000, NULL, 2, 4, NULL, NULL, 0.00010000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
COMMIT;

-- ----------------------------
-- Indexes structure for table trading_pairs
-- ----------------------------
CREATE INDEX "idx_trading_pairs_active" ON "public"."trading_pairs" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_base_asset" ON "public"."trading_pairs" USING btree (
  "base_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_exchange" ON "public"."trading_pairs" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_quote_asset" ON "public"."trading_pairs" USING btree (
  "quote_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_symbol" ON "public"."trading_pairs" USING btree (
  "symbol" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table trading_pairs
-- ----------------------------
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_symbol_key" UNIQUE ("symbol");

-- ----------------------------
-- Primary Key structure for table trading_pairs
-- ----------------------------
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_pkey" PRIMARY KEY ("id");
