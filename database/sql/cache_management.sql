/*
 缓存管理相关表结构
 
 支持扩展的缓存配置、统计、性能监控、健康检查和告警功能
 
 Date: 30/06/2025
*/

-- ----------------------------
-- Table structure for cache_stats
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_stats";
CREATE TABLE "public"."cache_stats" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "cache_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "total_keys" bigint NOT NULL DEFAULT 0,
  "memory_usage_mb" decimal(10,2) NOT NULL DEFAULT 0,
  "memory_limit_mb" decimal(10,2) NOT NULL DEFAULT 0,
  "memory_usage_percentage" decimal(5,2) NOT NULL DEFAULT 0,
  "hit_rate" decimal(5,4) NOT NULL DEFAULT 0,
  "miss_rate" decimal(5,4) NOT NULL DEFAULT 0,
  "total_operations" bigint NOT NULL DEFAULT 0,
  "hits" bigint NOT NULL DEFAULT 0,
  "misses" bigint NOT NULL DEFAULT 0,
  "evictions" bigint NOT NULL DEFAULT 0,
  "expired_keys" bigint NOT NULL DEFAULT 0,
  "average_ttl_seconds" decimal(10,2) NOT NULL DEFAULT 0,
  "uptime_seconds" bigint NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- ----------------------------
-- Table structure for cache_performance
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_performance";
CREATE TABLE "public"."cache_performance" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "cache_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "average_response_time_ms" decimal(10,3) NOT NULL DEFAULT 0,
  "p95_response_time_ms" decimal(10,3) NOT NULL DEFAULT 0,
  "p99_response_time_ms" decimal(10,3) NOT NULL DEFAULT 0,
  "throughput_ops_per_second" decimal(10,2) NOT NULL DEFAULT 0,
  "error_rate" decimal(5,4) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- ----------------------------
-- Table structure for cache_health
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_health";
CREATE TABLE "public"."cache_health" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'unknown',
  "memory_pressure_level" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'normal',
  "memory_usage_percentage" decimal(5,2) NOT NULL DEFAULT 0,
  "eviction_rate" decimal(5,4) NOT NULL DEFAULT 0,
  "fragmentation_ratio" decimal(5,4) NOT NULL DEFAULT 0,
  "issues" jsonb,
  "recommendations" jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- ----------------------------
-- Table structure for cache_alerts
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_alerts";
CREATE TABLE "public"."cache_alerts" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "rule_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "severity" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "condition" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "current_value" decimal(15,4) NOT NULL,
  "threshold_value" decimal(15,4) NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "resolved" bool NOT NULL DEFAULT false,
  "resolved_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- ----------------------------
-- Indexes for cache_stats
-- ----------------------------
CREATE INDEX "idx_cache_stats_timestamp" ON "public"."cache_stats" USING btree (
  "timestamp" DESC NULLS LAST
);
CREATE INDEX "idx_cache_stats_cache_type" ON "public"."cache_stats" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_stats_cache_type_timestamp" ON "public"."cache_stats" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" DESC NULLS LAST
);

-- ----------------------------
-- Indexes for cache_performance
-- ----------------------------
CREATE INDEX "idx_cache_performance_timestamp" ON "public"."cache_performance" USING btree (
  "timestamp" DESC NULLS LAST
);
CREATE INDEX "idx_cache_performance_cache_type" ON "public"."cache_performance" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_performance_cache_type_timestamp" ON "public"."cache_performance" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" DESC NULLS LAST
);

-- ----------------------------
-- Indexes for cache_health
-- ----------------------------
CREATE INDEX "idx_cache_health_timestamp" ON "public"."cache_health" USING btree (
  "timestamp" DESC NULLS LAST
);
CREATE INDEX "idx_cache_health_status" ON "public"."cache_health" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Indexes for cache_alerts
-- ----------------------------
CREATE INDEX "idx_cache_alerts_timestamp" ON "public"."cache_alerts" USING btree (
  "timestamp" DESC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_resolved" ON "public"."cache_alerts" USING btree (
  "resolved" ASC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_severity" ON "public"."cache_alerts" USING btree (
  "severity" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_rule_name" ON "public"."cache_alerts" USING btree (
  "rule_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key constraints
-- ----------------------------
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."cache_performance" ADD CONSTRAINT "cache_performance_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."cache_alerts" ADD CONSTRAINT "cache_alerts_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Check constraints
-- ----------------------------
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_memory_usage_check" 
  CHECK (memory_usage_percentage >= 0 AND memory_usage_percentage <= 100);
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_hit_rate_check" 
  CHECK (hit_rate >= 0 AND hit_rate <= 1);
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_miss_rate_check" 
  CHECK (miss_rate >= 0 AND miss_rate <= 1);

ALTER TABLE "public"."cache_performance" ADD CONSTRAINT "cache_performance_error_rate_check" 
  CHECK (error_rate >= 0 AND error_rate <= 1);

ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_memory_usage_check" 
  CHECK (memory_usage_percentage >= 0 AND memory_usage_percentage <= 100);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_eviction_rate_check" 
  CHECK (eviction_rate >= 0 AND eviction_rate <= 1);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_fragmentation_check" 
  CHECK (fragmentation_ratio >= 0 AND fragmentation_ratio <= 1);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_status_check" 
  CHECK (status IN ('healthy', 'warning', 'critical', 'unknown'));
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_memory_pressure_check" 
  CHECK (memory_pressure_level IN ('low', 'normal', 'high', 'critical'));

ALTER TABLE "public"."cache_alerts" ADD CONSTRAINT "cache_alerts_severity_check" 
  CHECK (severity IN ('info', 'warning', 'critical', 'emergency'));

-- ----------------------------
-- Comments
-- ----------------------------
COMMENT ON TABLE "public"."cache_stats" IS '缓存统计数据表';
COMMENT ON COLUMN "public"."cache_stats"."cache_type" IS '缓存类型（如：redis, memory, disk等）';
COMMENT ON COLUMN "public"."cache_stats"."hit_rate" IS '命中率（0-1之间）';
COMMENT ON COLUMN "public"."cache_stats"."miss_rate" IS '未命中率（0-1之间）';

COMMENT ON TABLE "public"."cache_performance" IS '缓存性能监控数据表';
COMMENT ON COLUMN "public"."cache_performance"."average_response_time_ms" IS '平均响应时间（毫秒）';
COMMENT ON COLUMN "public"."cache_performance"."p95_response_time_ms" IS 'P95响应时间（毫秒）';
COMMENT ON COLUMN "public"."cache_performance"."p99_response_time_ms" IS 'P99响应时间（毫秒）';

COMMENT ON TABLE "public"."cache_health" IS '缓存健康检查数据表';
COMMENT ON COLUMN "public"."cache_health"."status" IS '健康状态：healthy, warning, critical, unknown';
COMMENT ON COLUMN "public"."cache_health"."memory_pressure_level" IS '内存压力级别：low, normal, high, critical';

COMMENT ON TABLE "public"."cache_alerts" IS '缓存告警记录表';
COMMENT ON COLUMN "public"."cache_alerts"."severity" IS '告警级别：info, warning, critical, emergency';
COMMENT ON COLUMN "public"."cache_alerts"."resolved" IS '是否已解决';

-- ----------------------------
-- 数据保留策略（可选）
-- ----------------------------
-- 创建分区表以提高查询性能（可选，适用于大量数据）
-- 这里提供示例，实际使用时可以根据需要启用

-- 为 cache_stats 创建按月分区（示例）
-- CREATE TABLE cache_stats_y2025m06 PARTITION OF cache_stats
--   FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');

-- 为 cache_performance 创建按月分区（示例）
-- CREATE TABLE cache_performance_y2025m06 PARTITION OF cache_performance
--   FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');

-- 创建清理旧数据的函数（示例）
-- CREATE OR REPLACE FUNCTION cleanup_old_cache_data(retention_days INTEGER DEFAULT 30)
-- RETURNS INTEGER AS $$
-- DECLARE
--   deleted_count INTEGER := 0;
-- BEGIN
--   -- 清理旧的统计数据
--   DELETE FROM cache_stats WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
--   GET DIAGNOSTICS deleted_count = ROW_COUNT;
--   
--   -- 清理旧的性能数据
--   DELETE FROM cache_performance WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
--   
--   -- 清理旧的健康检查数据
--   DELETE FROM cache_health WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
--   
--   RETURN deleted_count;
-- END;
-- $$ LANGUAGE plpgsql;
