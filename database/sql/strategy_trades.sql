/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:11:16
*/


-- ----------------------------
-- Table structure for strategy_trades
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_trades";
CREATE TABLE "public"."strategy_trades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trade_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "value" numeric(20,8) NOT NULL,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "cumulative_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "signal_type" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Records of strategy_trades
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Primary Key structure for table strategy_trades
-- ----------------------------
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table strategy_trades
-- ----------------------------
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
