/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:11:10
*/


-- ----------------------------
-- Table structure for strategy_positions
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_positions";
CREATE TABLE "public"."strategy_positions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "average_price" numeric(20,8) NOT NULL,
  "current_price" numeric(20,8),
  "market_value" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "opened_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Records of strategy_positions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Uniques structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_trading_pair_key" UNIQUE ("strategy_id", "trading_pair");

-- ----------------------------
-- Primary Key structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
