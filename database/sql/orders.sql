/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:09:55
*/


-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."orders";
CREATE TABLE "public"."orders" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "strategy_id" uuid,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "parent_order_id" uuid,
  "order_type" "public"."order_type" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "stop_price" numeric(20,8),
  "status" "public"."order_status" NOT NULL DEFAULT 'Pending'::order_status,
  "filled_quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "remaining_quantity" numeric(20,8) GENERATED ALWAYS AS (
(quantity - filled_quantity)
) STORED,
  "average_price" numeric(20,8),
  "total_fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "time_in_force" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'GTC'::character varying,
  "client_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "tags" jsonb DEFAULT '{}'::jsonb,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "submitted_at" timestamptz(6),
  "filled_at" timestamptz(6),
  "cancelled_at" timestamptz(6)
)
;
ALTER TABLE "public"."orders" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of orders
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table orders
-- ----------------------------
CREATE INDEX "idx_orders_active_status" ON "public"."orders" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
) WHERE status = ANY (ARRAY['Pending'::order_status, 'PartiallyFilled'::order_status]);
CREATE INDEX "idx_orders_client_order_id" ON "public"."orders" USING btree (
  "client_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_created_at" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_created_status" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_fk" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_order_id" ON "public"."orders" USING btree (
  "exchange_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_status" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_metadata_gin" ON "public"."orders" USING gin (
  "metadata" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_orders_parent" ON "public"."orders" USING btree (
  "parent_order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_side" ON "public"."orders" USING btree (
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_stats" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_status" ON "public"."orders" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy_fk" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy_status" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_time_range" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair_fk" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair_status" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table orders
-- ----------------------------
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_stop_price_check" CHECK (stop_price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_filled_quantity_check" CHECK (filled_quantity >= 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_filled_quantity" CHECK (filled_quantity <= quantity);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_price_for_limit" CHECK (order_type = 'Market'::order_type OR (order_type = ANY (ARRAY['Limit'::order_type, 'StopLimit'::order_type])) AND price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_stop_price_for_stop" CHECK ((order_type <> ALL (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) OR (order_type = ANY (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) AND stop_price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_average_price_check" CHECK (average_price > 0::numeric);

-- ----------------------------
-- Primary Key structure for table orders
-- ----------------------------
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");
