/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:05:57
*/


-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS "public"."audit_logs";
CREATE TABLE "public"."audit_logs" (
  "id" int8 NOT NULL DEFAULT nextval('audit_logs_id_seq'::regclass),
  "table_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "operation" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "old_values" jsonb,
  "new_values" jsonb,
  "changed_by" varchar(100) COLLATE "pg_catalog"."default",
  "changed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "ip_address" inet,
  "user_agent" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of audit_logs
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table audit_logs
-- ----------------------------
CREATE INDEX "idx_audit_logs_changed_at" ON "public"."audit_logs" USING btree (
  "changed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_logs_operation" ON "public"."audit_logs" USING btree (
  "operation" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_logs_table_record" ON "public"."audit_logs" USING btree (
  "table_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table audit_logs
-- ----------------------------
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_operation_check" CHECK (operation::text = ANY (ARRAY['INSERT'::character varying, 'UPDATE'::character varying, 'DELETE'::character varying]::text[]));

-- ----------------------------
-- Primary Key structure for table audit_logs
-- ----------------------------
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");
