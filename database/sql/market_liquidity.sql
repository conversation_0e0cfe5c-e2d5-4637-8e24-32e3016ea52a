/*
 Market Liquidity Table
 用于存储市场流动性数据，支持流动性指标计算
*/

-- ----------------------------
-- Table structure for market_liquidity
-- ----------------------------
DROP TABLE IF EXISTS "public"."market_liquidity";
CREATE TABLE "public"."market_liquidity" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "bid_depth_1" numeric(20,8) DEFAULT 0,
  "ask_depth_1" numeric(20,8) DEFAULT 0,
  "bid_depth_5" numeric(20,8) DEFAULT 0,
  "ask_depth_5" numeric(20,8) DEFAULT 0,
  "bid_depth_10" numeric(20,8) DEFAULT 0,
  "ask_depth_10" numeric(20,8) DEFAULT 0,
  "total_bid_volume" numeric(20,8) DEFAULT 0,
  "total_ask_volume" numeric(20,8) DEFAULT 0,
  "spread_percentage" numeric(10,6) DEFAULT 0,
  "mid_price" numeric(20,8) DEFAULT 0,
  "volume_24h" numeric(20,8) DEFAULT 0,
  "trades_count_24h" int4 DEFAULT 0,
  "liquidity_score" numeric(10,4) DEFAULT 0,
  "order_book_data" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) DEFAULT now()
);
ALTER TABLE "public"."market_liquidity" OWNER TO "neondb_owner";

-- ----------------------------
-- Indexes structure for table market_liquidity
-- ----------------------------
CREATE INDEX "idx_market_liquidity_trading_pair_id" ON "public"."market_liquidity" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_market_liquidity_exchange_id" ON "public"."market_liquidity" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_market_liquidity_timestamp" ON "public"."market_liquidity" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

CREATE INDEX "idx_market_liquidity_pair_exchange_time" ON "public"."market_liquidity" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table market_liquidity
-- ----------------------------
ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "market_liquidity_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table market_liquidity
-- ----------------------------
ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "market_liquidity_trading_pair_id_fkey" 
  FOREIGN KEY ("trading_pair_id") REFERENCES "public"."trading_pairs" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "market_liquidity_exchange_id_fkey" 
  FOREIGN KEY ("exchange_id") REFERENCES "public"."exchanges" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Check constraints for table market_liquidity
-- ----------------------------
ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "check_market_liquidity_depths_non_negative" 
  CHECK (bid_depth_1 >= 0 AND ask_depth_1 >= 0 AND bid_depth_5 >= 0 AND ask_depth_5 >= 0 AND bid_depth_10 >= 0 AND ask_depth_10 >= 0);

ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "check_market_liquidity_volumes_non_negative" 
  CHECK (total_bid_volume >= 0 AND total_ask_volume >= 0 AND volume_24h >= 0);

ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "check_market_liquidity_spread_valid" 
  CHECK (spread_percentage >= 0);

ALTER TABLE "public"."market_liquidity" ADD CONSTRAINT "check_market_liquidity_score_valid" 
  CHECK (liquidity_score >= 0 AND liquidity_score <= 100);

-- ----------------------------
-- Comments for table market_liquidity
-- ----------------------------
COMMENT ON TABLE "public"."market_liquidity" IS '市场流动性数据表，存储订单簿深度和流动性指标';
COMMENT ON COLUMN "public"."market_liquidity"."trading_pair_id" IS '交易对ID';
COMMENT ON COLUMN "public"."market_liquidity"."exchange_id" IS '交易所ID';
COMMENT ON COLUMN "public"."market_liquidity"."timestamp" IS '数据时间戳';
COMMENT ON COLUMN "public"."market_liquidity"."bid_depth_1" IS '买1档深度';
COMMENT ON COLUMN "public"."market_liquidity"."ask_depth_1" IS '卖1档深度';
COMMENT ON COLUMN "public"."market_liquidity"."bid_depth_5" IS '买5档深度';
COMMENT ON COLUMN "public"."market_liquidity"."ask_depth_5" IS '卖5档深度';
COMMENT ON COLUMN "public"."market_liquidity"."bid_depth_10" IS '买10档深度';
COMMENT ON COLUMN "public"."market_liquidity"."ask_depth_10" IS '卖10档深度';
COMMENT ON COLUMN "public"."market_liquidity"."total_bid_volume" IS '总买单量';
COMMENT ON COLUMN "public"."market_liquidity"."total_ask_volume" IS '总卖单量';
COMMENT ON COLUMN "public"."market_liquidity"."spread_percentage" IS '买卖价差百分比';
COMMENT ON COLUMN "public"."market_liquidity"."mid_price" IS '中间价';
COMMENT ON COLUMN "public"."market_liquidity"."volume_24h" IS '24小时交易量';
COMMENT ON COLUMN "public"."market_liquidity"."trades_count_24h" IS '24小时交易次数';
COMMENT ON COLUMN "public"."market_liquidity"."liquidity_score" IS '流动性评分（0-100）';
COMMENT ON COLUMN "public"."market_liquidity"."order_book_data" IS '完整订单簿数据（JSON格式）';
