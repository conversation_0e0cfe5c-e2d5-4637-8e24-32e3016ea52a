/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:11:21
*/


-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_config";
CREATE TABLE "public"."system_config" (
  "id" int4 NOT NULL DEFAULT nextval('system_config_id_seq'::regclass),
  "key" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "value" jsonb NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "is_encrypted" bool NOT NULL DEFAULT false,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;

-- ----------------------------
-- Records of system_config
-- ----------------------------
BEGIN;
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (1, 'trading.max_orders_per_strategy', '100', '每个策略最大订单数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (2, 'trading.max_position_size', '1000000', '最大持仓金额(USDT)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (3, 'trading.default_order_timeout', '3600', '默认订单超时时间(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (4, 'trading.min_order_interval', '1', '最小订单间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (5, 'trading.max_slippage_percent', '0.5', '最大滑点百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (6, 'risk.max_drawdown_percent', '20', '最大回撤百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (7, 'risk.max_daily_loss_percent', '5', '最大日损失百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (8, 'risk.max_portfolio_risk_percent', '10', '最大投资组合风险百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (9, 'risk.position_size_limit_percent', '25', '单个持仓大小限制百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (10, 'risk.stop_loss_percent', '2', '默认止损百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (11, 'system.maintenance_mode', 'false', '系统维护模式', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (12, 'system.max_concurrent_strategies', '50', '最大并发策略数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (13, 'system.data_retention_days', '365', '数据保留天数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (14, 'system.backup_enabled', 'true', '备份开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (15, 'system.log_level', '"INFO"', '日志级别', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (16, 'notifications.email_enabled', 'true', '邮件通知开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (17, 'notifications.webhook_url', '""', 'Webhook通知URL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (18, 'notifications.slack_webhook', '""', 'Slack Webhook URL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (19, 'notifications.telegram_bot_token', '""', 'Telegram Bot Token', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (20, 'notifications.telegram_chat_id', '""', 'Telegram Chat ID', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (21, 'api.rate_limit_per_minute', '1000', 'API每分钟请求限制', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (22, 'api.max_request_size', '1048576', 'API最大请求大小(字节)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (23, 'api.timeout_seconds', '30', 'API超时时间(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (24, 'api.cors_enabled', 'true', 'CORS开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (25, 'database.connection_pool_size', '20', '数据库连接池大小', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (26, 'database.query_timeout_seconds', '30', '数据库查询超时时间', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (27, 'database.auto_vacuum_enabled', 'true', '自动清理开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (28, 'cache.redis_enabled', 'true', 'Redis缓存开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (29, 'cache.default_ttl_seconds', '3600', '默认缓存TTL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (30, 'cache.max_memory_mb', '512', '最大缓存内存(MB)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (31, 'monitoring.metrics_enabled', 'true', '指标监控开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (32, 'monitoring.health_check_interval', '60', '健康检查间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (33, 'monitoring.alert_threshold_cpu', '80', 'CPU告警阈值', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (34, 'monitoring.alert_threshold_memory', '85', '内存告警阈值', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (35, 'strategy.default_grid_levels', '10', '默认网格层数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (36, 'strategy.default_grid_spacing', '1', '默认网格间距百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (37, 'strategy.max_active_orders', '20', '最大活跃订单数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (38, 'strategy.rebalance_interval', '300', '重平衡间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (39, 'strategy_templates.grid_trading', '{"type": "grid_trading", "parameters": {"grid_levels": 10, "base_order_size": 100, "max_active_orders": 20, "stop_loss_percent": 5.0, "take_profit_percent": 0.5, "grid_spacing_percent": 1.0}, "risk_management": {"max_position_size": 10000, "max_drawdown_percent": 10, "position_size_percent": 5}}', '网格交易策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (40, 'strategy_templates.dca', '{"type": "dollar_cost_averaging", "parameters": {"max_orders": 10, "order_size": 100, "take_profit_percent": 5.0, "order_interval_minutes": 60, "price_drop_threshold_percent": 2.0}, "risk_management": {"max_investment": 5000, "stop_loss_percent": 15.0}}', '定投策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (41, 'strategy_templates.momentum', '{"type": "momentum_trading", "parameters": {"lookback_period": 20, "momentum_threshold": 0.02, "exit_signal_strength": 0.3, "entry_signal_strength": 0.7, "position_size_percent": 10}, "risk_management": {"stop_loss_percent": 3.0, "take_profit_percent": 6.0, "max_holding_time_hours": 24}}', '动量交易策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (42, 'exchange_config.binance', '{"fees": {"maker": 0.001, "taker": 0.001}, "rate_limits": {"orders_per_second": 10, "weight_per_minute": 6000, "requests_per_minute": 1200}, "api_endpoint": "https://api.binance.com", "min_notional": 10, "websocket_endpoint": "wss://stream.binance.com:9443", "supported_order_types": ["MARKET", "LIMIT", "STOP_LOSS", "STOP_LOSS_LIMIT"]}', 'Binance交易所配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (43, 'exchange_config.coinbase', '{"fees": {"maker": 0.005, "taker": 0.005}, "rate_limits": {"orders_per_second": 5, "requests_per_minute": 600}, "api_endpoint": "https://api.pro.coinbase.com", "min_notional": 10, "websocket_endpoint": "wss://ws-feed.pro.coinbase.com", "supported_order_types": ["market", "limit", "stop"]}', 'Coinbase交易所配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (44, 'exchange_config.simulator', '{"fees": {"maker": 0.001, "taker": 0.001}, "rate_limits": {"orders_per_second": 100, "requests_per_minute": 6000}, "api_endpoint": "http://localhost:8080", "min_notional": 1, "simulation_mode": true, "websocket_endpoint": "ws://localhost:8080/ws", "supported_order_types": ["MARKET", "LIMIT", "STOP_LOSS", "STOP_LOSS_LIMIT"]}', '模拟器配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (45, 'user_roles.admin', '{"permissions": ["system.manage", "users.manage", "strategies.manage", "portfolios.manage", "orders.manage", "trades.view", "config.manage"]}', '管理员角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (46, 'user_roles.trader', '{"permissions": ["strategies.create", "strategies.update", "strategies.delete", "portfolios.view", "portfolios.update", "orders.create", "orders.cancel", "trades.view"]}', '交易员角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (47, 'user_roles.viewer', '{"permissions": ["strategies.view", "portfolios.view", "orders.view", "trades.view"]}', '查看者角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
COMMIT;

-- ----------------------------
-- Indexes structure for table system_config
-- ----------------------------
CREATE INDEX "idx_system_config_key" ON "public"."system_config" USING btree (
  "key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_system_config_value_gin" ON "public"."system_config" USING gin (
  "value" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Uniques structure for table system_config
-- ----------------------------
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_key_key" UNIQUE ("key");

-- ----------------------------
-- Primary Key structure for table system_config
-- ----------------------------
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_pkey" PRIMARY KEY ("id");
