/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:59
*/


-- ----------------------------
-- Table structure for strategies
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategies";
CREATE TABLE "public"."strategies" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "strategy_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "state_data" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "status" "public"."strategy_status" NOT NULL DEFAULT 'Created'::strategy_status,
  "trading_pair_id" int4,
  "portfolio_id" uuid,
  "risk_config" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "created_by" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "started_at" timestamptz(6),
  "stopped_at" timestamptz(6)
)
;
ALTER TABLE "public"."strategies" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of strategies
-- ----------------------------
BEGIN;
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('b7f6148c-**************-9ce28b8cefc5', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:03:44.217383+00', '2025-06-10 09:03:44.217383+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('2b2e8662-bb1c-4dda-8559-d9b1131fc8ff', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:07:09.773998+00', '2025-06-10 09:07:09.773998+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('e37be5a2-d742-4c9e-9e0e-bb2c6f3f9aa6', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:07:47.98633+00', '2025-06-10 09:07:47.98633+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('3f3d6d16-40de-4ae3-bde8-3d6649964169', '测试策略', 'grid', '网格策略测试', '{"enabled": true, "parameters": {"grid_levels": 10, "grid_spacing": 0.01, "base_order_size": 100}, "trading_pairs": ["BTCUSDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "20.00", "max_leverage": "3.00", "max_daily_loss": "1000.00", "max_position_size": "5000.00", "stop_loss_percentage": "5.00", "take_profit_percentage": "10.00"}', '{}', NULL, '2025-06-13 18:08:14.372164+00', '2025-06-13 18:08:14.372164+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('dd9a4fd0-2b6f-4270-8b63-9e078ab9d056', 'Adaptive Volatility Grid Strategy Test', 'adaptive_volatility_grid', NULL, '{"enabled": true, "parameters": {"grid_levels": 5, "grid_spacing": 0.02, "base_order_size": 100.0, "reference_price": 400.0, "adaptive_spacing": true, "max_position_size": 1000.0, "volatility_window": 20}, "trading_pairs": ["BNB_USDT"], "initial_capital": "10000.0"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-18 16:53:26.595711+00', '2025-06-18 16:53:26.595711+00', NULL, NULL);
COMMIT;

-- ----------------------------
-- Indexes structure for table strategies
-- ----------------------------
CREATE INDEX "idx_strategies_active" ON "public"."strategies" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
) WHERE status = ANY (ARRAY['Running'::strategy_status, 'Paused'::strategy_status]);
CREATE INDEX "idx_strategies_config_gin" ON "public"."strategies" USING gin (
  "config" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_strategies_created_at" ON "public"."strategies" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_name" ON "public"."strategies" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_name_gin" ON "public"."strategies" USING gin (
  to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops"
);
CREATE INDEX "idx_strategies_status" ON "public"."strategies" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_strategy_type" ON "public"."strategies" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_trading_pair" ON "public"."strategies" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_type" ON "public"."strategies" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table strategies
-- ----------------------------
ALTER TABLE "public"."strategies" ADD CONSTRAINT "strategies_pkey" PRIMARY KEY ("id");
