/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:08
*/


-- ----------------------------
-- Table structure for portfolios
-- ----------------------------
DROP TABLE IF EXISTS "public"."portfolios";
CREATE TABLE "public"."portfolios" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "initial_capital" numeric(20,8) NOT NULL,
  "current_capital" numeric(20,8) NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "win_rate" numeric(5,4) DEFAULT 0,
  "sharpe_ratio" numeric(10,6),
  "max_drawdown" numeric(10,6),
  "config" jsonb DEFAULT '{}'::jsonb,
  "risk_metrics" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "is_active" bool NOT NULL DEFAULT true,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."portfolios" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of portfolios
-- ----------------------------
BEGIN;
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('d78f64cc-da82-4dbd-a64f-d1f45e6bb420', 'Default Portfolio', '默认投资组合', 100000.00000000, 100000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 't', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('9a926f63-c4dc-4891-9495-b7b46deba304', 'Demo Portfolio', '演示投资组合', 10000.00000000, 10000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 't', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('00e7e6ea-c645-4d3e-b9a4-75470ec47122', 'Test Portfolio', '测试投资组合', 1000.00000000, 1000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 'f', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
COMMIT;

-- ----------------------------
-- Indexes structure for table portfolios
-- ----------------------------
CREATE INDEX "idx_portfolios_active" ON "public"."portfolios" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_created_at" ON "public"."portfolios" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_name" ON "public"."portfolios" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_name_gin" ON "public"."portfolios" USING gin (
  to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops"
);

-- ----------------------------
-- Checks structure for table portfolios
-- ----------------------------
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_initial_capital_check" CHECK (initial_capital > 0::numeric);

-- ----------------------------
-- Primary Key structure for table portfolios
-- ----------------------------
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_pkey" PRIMARY KEY ("id");
