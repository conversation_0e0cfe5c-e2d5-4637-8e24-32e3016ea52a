/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:08:54
*/


-- ----------------------------
-- Table structure for events
-- ----------------------------
DROP TABLE IF EXISTS "public"."events";
CREATE TABLE "public"."events" (
  "id" int8 NOT NULL DEFAULT nextval('events_id_seq'::regclass),
  "event_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "event_data" jsonb NOT NULL,
  "aggregate_id" varchar(100) COLLATE "pg_catalog"."default",
  "aggregate_type" varchar(50) COLLATE "pg_catalog"."default",
  "version" int4 NOT NULL DEFAULT 1,
  "correlation_id" uuid,
  "causation_id" uuid,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "processed_at" timestamptz(6)
)
;
ALTER TABLE "public"."events" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of events
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table events
-- ----------------------------
CREATE INDEX "idx_events_type" ON "public"."events" USING btree (
  "event_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table events
-- ----------------------------
ALTER TABLE "public"."events" ADD CONSTRAINT "events_pkey" PRIMARY KEY ("id");
