/*
 * =================================================================
 * SigmaX Trading System - 全数据库最终校正脚本 (V3)
 * =================================================================
 *
 * 此脚本为最终版本，已整合并修复了所有 SQL 文件中的问题：
 * 1. 新增了缺失的自定义枚举类型 (ENUM)，以解决 'type does not exist' 错误。
 * 2. 新增了缺失的序列（sequence）定义。
 * 3. 调整了所有语句的逻辑顺序，确保可直接执行。
 * 4. 包含了所有表的数据、索引和约束，无任何省略。
 *
 * 正确执行顺序: 1.类型 -> 2.序列 -> 3.表 -> 4.数据 -> 5.索引和约束
 
 CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
 
 *
 */

-- ----------------------------
-- 1. 自定义枚举类型定义 (ENUM Type Definitions)
-- ----------------------------

DROP TYPE IF EXISTS "public"."exchange_status";
CREATE TYPE "public"."exchange_status" AS ENUM ('Active', 'Inactive', 'Delisted');

DROP TYPE IF EXISTS "public"."order_type";
CREATE TYPE "public"."order_type" AS ENUM ('Market', 'Limit', 'StopLoss', 'StopLimit', 'TakeProfit', 'TakeProfitLimit');

DROP TYPE IF EXISTS "public"."order_side";
CREATE TYPE "public"."order_side" AS ENUM ('Buy', 'Sell');

DROP TYPE IF EXISTS "public"."order_status";
CREATE TYPE "public"."order_status" AS ENUM ('Pending', 'Open', 'PartiallyFilled', 'Filled', 'Cancelled', 'Rejected', 'Expired');

DROP TYPE IF EXISTS "public"."strategy_status";
CREATE TYPE "public"."strategy_status" AS ENUM ('Created', 'Running', 'Paused', 'Stopped', 'Error');


-- ----------------------------
-- 2. 序列定义 (Sequence Definitions)
-- ----------------------------

DROP SEQUENCE IF EXISTS "public"."audit_logs_id_seq";
CREATE SEQUENCE "public"."audit_logs_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."backtest_files_id_seq";
CREATE SEQUENCE "public"."backtest_files_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."candles_id_seq";
CREATE SEQUENCE "public"."candles_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."events_id_seq";
CREATE SEQUENCE "public"."events_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."exchanges_id_seq";
CREATE SEQUENCE "public"."exchanges_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."system_config_id_seq";
CREATE SEQUENCE "public"."system_config_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1;
DROP SEQUENCE IF EXISTS "public"."trading_pairs_id_seq";
CREATE SEQUENCE "public"."trading_pairs_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1;


-- ----------------------------
-- 3. 表结构定义 (Table Structures)
-- ----------------------------

-- Table structure for audit_logs
DROP TABLE IF EXISTS "public"."audit_logs";
CREATE TABLE "public"."audit_logs" (
  "id" int8 NOT NULL DEFAULT nextval('audit_logs_id_seq'::regclass),
  "table_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "operation" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "old_values" jsonb,
  "new_values" jsonb,
  "changed_by" varchar(100) COLLATE "pg_catalog"."default",
  "changed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "ip_address" inet,
  "user_agent" text COLLATE "pg_catalog"."default"
);

-- Table structure for backtest_files
DROP TABLE IF EXISTS "public"."backtest_files";
CREATE TABLE "public"."backtest_files" (
  "id" int4 NOT NULL DEFAULT nextval('backtest_files_id_seq'::regclass),
  "filename" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "timeframe" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "candle_count" int4 NOT NULL DEFAULT 0,
  "start_time" timestamptz(6),
  "end_time" timestamptz(6),
  "file_size" int8,
  "file_hash" varchar(64) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for backtest_results
DROP TABLE IF EXISTS "public"."backtest_results";
CREATE TABLE "public"."backtest_results" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "engine_id" uuid NOT NULL,
  "config" jsonb NOT NULL,
  "start_time" timestamptz(6) NOT NULL,
  "end_time" timestamptz(6) NOT NULL,
  "initial_capital" numeric(20,8) NOT NULL,
  "final_capital" numeric(20,8) NOT NULL,
  "total_return" numeric(20,8) NOT NULL,
  "total_return_percentage" numeric(10,4) NOT NULL,
  "max_drawdown" numeric(20,8) NOT NULL,
  "max_drawdown_percentage" numeric(10,4) NOT NULL,
  "sharpe_ratio" numeric(10,4),
  "profit_factor" numeric(10,4),
  "win_rate" numeric(10,4),
  "total_trades" int4 NOT NULL DEFAULT 0,
  "winning_trades" int4 NOT NULL DEFAULT 0,
  "losing_trades" int4 NOT NULL DEFAULT 0,
  "average_win" numeric(20,8),
  "average_loss" numeric(20,8),
  "largest_win" numeric(20,8),
  "largest_loss" numeric(20,8),
  "status" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Created'::character varying,
  "created_at" timestamptz(6) DEFAULT now(),
  "completed_at" timestamptz(6)
);

-- Table structure for backtest_portfolio_snapshots
DROP TABLE IF EXISTS "public"."backtest_portfolio_snapshots";
CREATE TABLE "public"."backtest_portfolio_snapshots" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "backtest_id" uuid NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "total_value" numeric(20,8) NOT NULL,
  "cash_balance" numeric(20,8) NOT NULL,
  "positions" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for backtest_trades
DROP TABLE IF EXISTS "public"."backtest_trades";
CREATE TABLE "public"."backtest_trades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "backtest_id" uuid NOT NULL,
  "trade_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "value" numeric(20,8) NOT NULL,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "strategy_id" varchar(100) COLLATE "pg_catalog"."default",
  "pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "cumulative_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for trading_pairs
DROP TABLE IF EXISTS "public"."trading_pairs";
CREATE TABLE "public"."trading_pairs" (
  "id" int4 NOT NULL DEFAULT nextval('trading_pairs_id_seq'::regclass),
  "symbol" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "base_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quote_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "exchange_id" int4,
  "is_active" bool NOT NULL DEFAULT true,
  "min_quantity" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "max_quantity" numeric(20,8),
  "quantity_precision" int4 NOT NULL DEFAULT 8,
  "price_precision" int4 NOT NULL DEFAULT 8,
  "min_price" numeric(20,8),
  "max_price" numeric(20,8),
  "tick_size" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "min_notional" numeric(20,8) NOT NULL DEFAULT 0.001,
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- Table structure for candles
DROP TABLE IF EXISTS "public"."candles";
CREATE TABLE "public"."candles" (
  "id" int8 NOT NULL DEFAULT nextval('candles_id_seq'::regclass),
  "trading_pair_id" int4 NOT NULL,
  "timeframe" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "open_time" timestamptz(6) NOT NULL,
  "close_time" timestamptz(6) NOT NULL,
  "open_price" numeric(20,8) NOT NULL,
  "high_price" numeric(20,8) NOT NULL,
  "low_price" numeric(20,8) NOT NULL,
  "close_price" numeric(20,8) NOT NULL,
  "volume" numeric(20,8) NOT NULL,
  "quote_volume" numeric(20,8) NOT NULL,
  "trades_count" int4 NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for engines
DROP TABLE IF EXISTS "public"."engines";
CREATE TABLE "public"."engines" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "engine_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Created'::character varying,
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "trading_pairs" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "initial_capital" numeric(20,8),
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for events
DROP TABLE IF EXISTS "public"."events";
CREATE TABLE "public"."events" (
  "id" int8 NOT NULL DEFAULT nextval('events_id_seq'::regclass),
  "event_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "event_data" jsonb NOT NULL,
  "aggregate_id" varchar(100) COLLATE "pg_catalog"."default",
  "aggregate_type" varchar(50) COLLATE "pg_catalog"."default",
  "version" int4 NOT NULL DEFAULT 1,
  "correlation_id" uuid,
  "causation_id" uuid,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "processed_at" timestamptz(6)
);

-- Table structure for exchanges
DROP TABLE IF EXISTS "public"."exchanges";
CREATE TABLE "public"."exchanges" (
  "id" int4 NOT NULL DEFAULT nextval('exchanges_id_seq'::regclass),
  "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "display_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "status" "public"."exchange_status" NOT NULL DEFAULT 'Active'::exchange_status,
  "api_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "websocket_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "withdrawal_fee_rate" numeric(10,6) DEFAULT 0.0005,
  "min_order_amount" numeric(20,8),
  "max_order_amount" numeric(20,8),
  "supported_order_types" "public"."order_type"[],
  "config" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- Table structure for orders
DROP TABLE IF EXISTS "public"."orders";
CREATE TABLE "public"."orders" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "strategy_id" uuid,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "parent_order_id" uuid,
  "order_type" "public"."order_type" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "stop_price" numeric(20,8),
  "status" "public"."order_status" NOT NULL DEFAULT 'Pending'::order_status,
  "filled_quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "remaining_quantity" numeric(20,8) GENERATED ALWAYS AS ((quantity - filled_quantity)) STORED,
  "average_price" numeric(20,8),
  "total_fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "time_in_force" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'GTC'::character varying,
  "client_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "tags" jsonb DEFAULT '{}'::jsonb,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "submitted_at" timestamptz(6),
  "filled_at" timestamptz(6),
  "cancelled_at" timestamptz(6)
);

-- Table structure for portfolios
DROP TABLE IF EXISTS "public"."portfolios";
CREATE TABLE "public"."portfolios" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "initial_capital" numeric(20,8) NOT NULL,
  "current_capital" numeric(20,8) NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "win_rate" numeric(5,4) DEFAULT 0,
  "sharpe_ratio" numeric(10,6),
  "max_drawdown" numeric(10,6),
  "config" jsonb DEFAULT '{}'::jsonb,
  "risk_metrics" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "is_active" bool NOT NULL DEFAULT true,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- Table structure for positions
DROP TABLE IF EXISTS "public"."positions";
CREATE TABLE "public"."positions" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "portfolio_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "average_price" numeric(20,8) NOT NULL DEFAULT 0,
  "market_price" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "cost_basis" numeric(20,8) GENERATED ALWAYS AS ((quantity * average_price)) STORED,
  "market_value" numeric(20,8),
  "last_updated_price_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- Table structure for strategies
DROP TABLE IF EXISTS "public"."strategies";
CREATE TABLE "public"."strategies" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "strategy_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "state_data" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "status" "public"."strategy_status" NOT NULL DEFAULT 'Created'::strategy_status,
  "trading_pair_id" int4,
  "portfolio_id" uuid,
  "risk_config" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "created_by" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "started_at" timestamptz(6),
  "stopped_at" timestamptz(6)
);

-- Table structure for risk_rules
DROP TABLE IF EXISTS "public"."risk_rules";
CREATE TABLE "public"."risk_rules" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "rule_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "parameters" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "enabled" bool NOT NULL DEFAULT true,
  "priority" int4 NOT NULL DEFAULT 100,
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for risk_checks
DROP TABLE IF EXISTS "public"."risk_checks";
CREATE TABLE "public"."risk_checks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "passed" bool NOT NULL,
  "risk_score" numeric(10,4) NOT NULL DEFAULT 0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "processing_time_ms" int4,
  "strategy_id" uuid,
  "engine_id" uuid,
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for risk_statistics
DROP TABLE IF EXISTS "public"."risk_statistics";
CREATE TABLE "public"."risk_statistics" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "date" date NOT NULL,
  "total_checks" int4 NOT NULL DEFAULT 0,
  "passed_checks" int4 NOT NULL DEFAULT 0,
  "failed_checks" int4 NOT NULL DEFAULT 0,
  "pass_rate" numeric(10,4) NOT NULL DEFAULT 0,
  "average_risk_score" numeric(10,4) NOT NULL DEFAULT 0,
  "top_violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "risk_score_distribution" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for risk_violations
DROP TABLE IF EXISTS "public"."risk_violations";
CREATE TABLE "public"."risk_violations" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "risk_check_id" uuid NOT NULL,
  "rule_id" uuid NOT NULL,
  "rule_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "severity" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Medium'::character varying,
  "message" text COLLATE "pg_catalog"."default" NOT NULL,
  "current_value" varchar(255) COLLATE "pg_catalog"."default",
  "limit_value" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for strategy_performance
DROP TABLE IF EXISTS "public"."strategy_performance";
CREATE TABLE "public"."strategy_performance" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "winning_trades" int4 NOT NULL DEFAULT 0,
  "losing_trades" int4 NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "win_rate" numeric(10,4) NOT NULL DEFAULT 0,
  "sharpe_ratio" numeric(10,4),
  "max_drawdown" numeric(20,8) NOT NULL DEFAULT 0,
  "max_drawdown_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "current_positions" int4 NOT NULL DEFAULT 0,
  "active_orders" int4 NOT NULL DEFAULT 0,
  "last_signal_time" timestamptz(6),
  "uptime_seconds" int8 NOT NULL DEFAULT 0,
  "error_count" int4 NOT NULL DEFAULT 0,
  "last_error" text COLLATE "pg_catalog"."default",
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for strategy_positions
DROP TABLE IF EXISTS "public"."strategy_positions";
CREATE TABLE "public"."strategy_positions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "average_price" numeric(20,8) NOT NULL,
  "current_price" numeric(20,8),
  "market_value" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "opened_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) DEFAULT now()
);

-- Table structure for strategy_trades
DROP TABLE IF EXISTS "public"."strategy_trades";
CREATE TABLE "public"."strategy_trades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trade_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "value" numeric(20,8) NOT NULL,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "cumulative_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "signal_type" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now()
);

-- Table structure for system_config
DROP TABLE IF EXISTS "public"."system_config";
CREATE TABLE "public"."system_config" (
  "id" int4 NOT NULL DEFAULT nextval('system_config_id_seq'::regclass),
  "key" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "value" jsonb NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "is_encrypted" bool NOT NULL DEFAULT false,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);

-- Table structure for trades
DROP TABLE IF EXISTS "public"."trades";
CREATE TABLE "public"."trades" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "order_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_trade_id" varchar(100) COLLATE "pg_catalog"."default",
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "quote_quantity" numeric(20,8) GENERATED ALWAYS AS ((quantity * price)) STORED,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "commission_rate" numeric(10,6),
  "is_maker" bool,
  "trade_time" timestamptz(6),
  "executed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "metadata" jsonb DEFAULT '{}'::jsonb
);

-- ----------------------------
-- 4. 数据记录 (Records)
-- ----------------------------

-- Records of exchanges
BEGIN;
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (1, 'binance', 'Binance', 'Active', 'https://api.binance.com', 'wss://stream.binance.com:9443', 0.001000, 0.000500, NULL, NULL, '{Market,Limit,StopLoss,StopLimit}', '{"rate_limit": 1200, "weight_limit": 6000}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (2, 'coinbase', 'Coinbase Pro', 'Active', 'https://api.pro.coinbase.com', 'wss://ws-feed.pro.coinbase.com', 0.005000, 0.002500, NULL, NULL, '{Market,Limit}', '{"sandbox": "https://api-public.sandbox.pro.coinbase.com", "rate_limit": 10}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (3, 'kraken', 'Kraken', 'Active', 'https://api.kraken.com', 'wss://ws.kraken.com', 0.002600, 0.000500, NULL, NULL, '{Market,Limit,StopLoss}', '{"tier": "starter", "rate_limit": 1}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (4, 'simulator', 'Simulator', 'Active', 'http://localhost:8080/api/simulator', 'ws://localhost:8080/ws/simulator', 0.000000, 0.000000, NULL, NULL, '{Market,Limit,StopLoss,StopLimit}', '{"simulated": true, "initial_balance": 100000}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
COMMIT;

-- Records of portfolios
BEGIN;
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('d78f64cc-da82-4dbd-a64f-d1f45e6bb420', 'Default Portfolio', '默认投资组合', 100000.00000000, 100000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 't', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('9a926f63-c4dc-4891-9495-b7b46deba304', 'Demo Portfolio', '演示投资组合', 10000.00000000, 10000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 't', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
INSERT INTO "public"."portfolios" ("id", "name", "description", "initial_capital", "current_capital", "total_pnl", "total_fees", "total_trades", "win_rate", "sharpe_ratio", "max_drawdown", "config", "risk_metrics", "performance_metrics", "is_active", "created_at", "updated_at") VALUES ('00e7e6ea-c645-4d3e-b9a4-75470ec47122', 'Test Portfolio', '测试投资组合', 1000.00000000, 1000.00000000, 0.00000000, 0.00000000, 0, 0.0000, NULL, NULL, '{}', '{}', '{}', 'f', '2025-06-10 03:35:31.014134+00', '2025-06-10 03:35:31.014134+00');
COMMIT;

-- Records of risk_checks
BEGIN;
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('8b0d4963-ff1d-4116-9704-2ddc343cf68d', '2025-06-10 08:58:53.850393+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 0.4000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '[]', 500.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 08:58:53.850393+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('ba4227b7-d45b-4fc0-92ef-74143843b02c', '2025-06-10 08:58:54.407981+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 08:58:54.407981+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('e9a8d55b-a786-4918-ae92-c636c6d5e7ec', '2025-06-10 09:01:41.935278+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 0.8000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 185, NULL, NULL, '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('de91c05c-31d8-4eb1-b238-593815a59ad7', '2025-06-10 09:01:42.706432+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 124, NULL, NULL, '2025-06-10 09:01:42.706432+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '2025-06-10 09:03:42.325633+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 1.2000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 175, NULL, NULL, '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('e729c2d1-0a1a-4ee3-9643-0ffbaecd9eeb', '2025-06-10 09:03:43.202021+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 171, NULL, NULL, '2025-06-10 09:03:43.202021+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('22cbd19f-3937-486c-8a86-60b949d4ac17', '2025-06-10 09:07:07.781793+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 1.6000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "f3701c22-d50d-437f-a481-cf294ef01d67", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 192, NULL, NULL, '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('4ead36e2-a116-4784-a8e1-e0ad4d52000e', '2025-06-10 09:07:09.081252+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 115, NULL, NULL, '2025-06-10 09:07:09.081252+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('317f0432-30bf-4c5e-9aa6-48a67406c35c', '2025-06-10 09:07:46.430018+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 2.0000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "f3701c22-d50d-437f-a481-cf294ef01d67", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "ba178e3e-32ed-43c8-b858-813ddfe95640", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 177, NULL, NULL, '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('ca3f5868-5457-477f-82bf-b70ef6342ad7', '2025-06-10 09:52:16.696479+00', 'BNB_USDT', 'Buy', 50.00000000, 350.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 2000.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 09:52:16.696479+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('70041fbe-76b7-4866-99cd-ac35e6b898cf', '2025-06-13 18:16:56.774183+00', 'BTCUSDT', 'buy', 0.10000000, 50000.00000000, 'limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 644, NULL, NULL, '2025-06-13 18:16:56.774183+00');
COMMIT;

-- Records of risk_rules
BEGIN;
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 08:58:53.638576+00', '2025-06-10 08:58:53.638576+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:01:41.706399+00', '2025-06-10 09:01:41.706399+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:03:42.115619+00', '2025-06-10 09:03:42.115619+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:07:07.588823+00', '2025-06-10 09:07:07.588823+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('ba178e3e-32ed-43c8-b858-813ddfe95640', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:07:46.21853+00', '2025-06-10 09:07:46.21853+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('1a510ecf-0be9-4b73-a758-d5b9115a0e47', '最大订单金额限制', 'order_size_limit', NULL, '{"currency": "USDT", "max_order_amount": 10000}', 't', 1, NULL, '2025-06-13 18:06:05.364539+00', '2025-06-13 18:06:05.364539+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('48aca072-0122-4a54-9e2c-970c04080996', '最大订单金额限制', 'order_size_limit', NULL, '{"currency": "USDT", "max_order_amount": 10000}', 't', 1, NULL, '2025-06-13 18:08:28.770397+00', '2025-06-13 18:08:28.770397+00');
COMMIT;

-- Records of risk_violations
BEGIN;
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('31c909d4-dd65-4bcf-b207-aa6043d6596c', '8b0d4963-ff1d-4116-9704-2ddc343cf68d', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 08:58:53.850393+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('940248d9-69ed-4a5d-ad73-9e3aa621a883', 'e9a8d55b-a786-4918-ae92-c636c6d5e7ec', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('7aa71deb-4016-402e-8733-313c9e925b57', 'e9a8d55b-a786-4918-ae92-c636c6d5e7ec', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('47e7d7ab-df73-4001-a769-b1e283c28a85', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('c9826e02-000a-4065-b76e-7abea6f48452', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('8d1c7f27-b571-45ce-b2bf-3f62770b30f5', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('34b7542e-6827-46cf-b553-956232cc734d', '22cbd19f-3937-486c-8a86-60b949d4ac17', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ce12711d-f814-4955-8167-9392b381b801', '22cbd19f-3937-486c-8a86-60b949d4ac17', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('f0dbda57-d510-4674-9ad2-3c917f154fa6', '22cbd19f-3937-486c-8a86-60b949d4ac17', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ae418ff7-008b-4c6a-9b99-1dad13b7e816', '22cbd19f-3937-486c-8a86-60b949d4ac17', 'f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('9ee2a364-603b-48f7-a77c-517633174aa5', '317f0432-30bf-4c5e-9aa6-48a67406c35c', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('b22e8b35-16f2-470d-90ae-53c46f580bf6', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('c0965a4e-a253-40f4-80d4-9f4ec98a3ed3', '317f0432-30bf-4c5e-9aa6-48a67406c35c', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ee5dacd7-eb77-46e5-b176-6bf07df8f309', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('a969edca-5c81-4ffc-a2ca-742dda4f09fa', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'ba178e3e-32ed-43c8-b858-813ddfe95640', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
COMMIT;

-- Records of strategies
BEGIN;
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('b7f6148c-5670-4375-9339-9ce28b8cefc5', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:03:44.217383+00', '2025-06-10 09:03:44.217383+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('2b2e8662-bb1c-4dda-8559-d9b1131fc8ff', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:07:09.773998+00', '2025-06-10 09:07:09.773998+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('e37be5a2-d742-4c9e-9e0e-bb2c6f3f9aa6', 'Test Grid Strategy', 'grid', '测试网格策略', '{"enabled": true, "parameters": {"grid_count": 10, "price_range": 0.1, "grid_spacing": 0.01}, "trading_pairs": ["BTC_USDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-10 09:07:47.98633+00', '2025-06-10 09:07:47.98633+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('3f3d6d16-40de-4ae3-bde8-3d6649964169', '测试策略', 'grid', '网格策略测试', '{"enabled": true, "parameters": {"grid_levels": 10, "grid_spacing": 0.01, "base_order_size": 100}, "trading_pairs": ["BTCUSDT"], "initial_capital": "10000.00"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "20.00", "max_leverage": "3.00", "max_daily_loss": "1000.00", "max_position_size": "5000.00", "stop_loss_percentage": "5.00", "take_profit_percentage": "10.00"}', '{}', NULL, '2025-06-13 18:08:14.372164+00', '2025-06-13 18:08:14.372164+00', NULL, NULL);
INSERT INTO "public"."strategies" ("id", "name", "strategy_type", "description", "config", "state_data", "status", "trading_pair_id", "portfolio_id", "risk_config", "performance_metrics", "created_by", "created_at", "updated_at", "started_at", "stopped_at") VALUES ('dd9a4fd0-2b6f-4270-8b63-9e078ab9d056', 'Adaptive Volatility Grid Strategy Test', 'adaptive_volatility_grid', NULL, '{"enabled": true, "parameters": {"grid_levels": 5, "grid_spacing": 0.02, "base_order_size": 100.0, "reference_price": 400.0, "adaptive_spacing": true, "max_position_size": 1000.0, "volatility_window": 20}, "trading_pairs": ["BNB_USDT"], "initial_capital": "10000.0"}', '{}', 'Created', NULL, NULL, '{"max_drawdown": "500.00", "max_leverage": "1.0", "max_daily_loss": "100.00", "max_position_size": "1000.00", "stop_loss_percentage": "5.0", "take_profit_percentage": "10.0"}', '{}', NULL, '2025-06-18 16:53:26.595711+00', '2025-06-18 16:53:26.595711+00', NULL, NULL);
COMMIT;

-- Records of strategy_performance
BEGIN;
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('d354882a-84db-4d24-a32c-8147464f0c5d', 'b7f6148c-5670-4375-9339-9ce28b8cefc5', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:03:44.735291+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('6106c557-bc65-45d0-b174-67fcb4c97448', '2b2e8662-bb1c-4dda-8559-d9b1131fc8ff', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:07:10.276922+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('fe4b16cf-2e1a-4244-9fa0-1ae338351491', 'e37be5a2-d742-4c9e-9e0e-bb2c6f3f9aa6', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:07:48.376613+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('8f3b2e48-fd0d-45ae-a8c2-b6dcb842fd5a', '3f3d6d16-40de-4ae3-bde8-3d6649964169', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-13 18:08:15.652652+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('e7b45c09-1e2a-4d09-a288-04f88cacab40', 'dd9a4fd0-2b6f-4270-8b63-9e078ab9d056', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-18 16:53:27.198507+00');
COMMIT;

-- Records of system_config
BEGIN;
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (1, 'trading.max_orders_per_strategy', '100', '每个策略最大订单数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (2, 'trading.max_position_size', '1000000', '最大持仓金额(USDT)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (3, 'trading.default_order_timeout', '3600', '默认订单超时时间(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (4, 'trading.min_order_interval', '1', '最小订单间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (5, 'trading.max_slippage_percent', '0.5', '最大滑点百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (6, 'risk.max_drawdown_percent', '20', '最大回撤百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (7, 'risk.max_daily_loss_percent', '5', '最大日损失百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (8, 'risk.max_portfolio_risk_percent', '10', '最大投资组合风险百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (9, 'risk.position_size_limit_percent', '25', '单个持仓大小限制百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (10, 'risk.stop_loss_percent', '2', '默认止损百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (11, 'system.maintenance_mode', 'false', '系统维护模式', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (12, 'system.max_concurrent_strategies', '50', '最大并发策略数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (13, 'system.data_retention_days', '365', '数据保留天数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (14, 'system.backup_enabled', 'true', '备份开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (15, 'system.log_level', '"INFO"', '日志级别', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (16, 'notifications.email_enabled', 'true', '邮件通知开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (17, 'notifications.webhook_url', '""', 'Webhook通知URL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (18, 'notifications.slack_webhook', '""', 'Slack Webhook URL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (19, 'notifications.telegram_bot_token', '""', 'Telegram Bot Token', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (20, 'notifications.telegram_chat_id', '""', 'Telegram Chat ID', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (21, 'api.rate_limit_per_minute', '1000', 'API每分钟请求限制', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (22, 'api.max_request_size', '1048576', 'API最大请求大小(字节)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (23, 'api.timeout_seconds', '30', 'API超时时间(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (24, 'api.cors_enabled', 'true', 'CORS开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (25, 'database.connection_pool_size', '20', '数据库连接池大小', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (26, 'database.query_timeout_seconds', '30', '数据库查询超时时间', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (27, 'database.auto_vacuum_enabled', 'true', '自动清理开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (28, 'cache.redis_enabled', 'true', 'Redis缓存开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (29, 'cache.default_ttl_seconds', '3600', '默认缓存TTL', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (30, 'cache.max_memory_mb', '512', '最大缓存内存(MB)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (31, 'monitoring.metrics_enabled', 'true', '指标监控开关', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (32, 'monitoring.health_check_interval', '60', '健康检查间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (33, 'monitoring.alert_threshold_cpu', '80', 'CPU告警阈值', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (34, 'monitoring.alert_threshold_memory', '85', '内存告警阈值', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (35, 'strategy.default_grid_levels', '10', '默认网格层数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (36, 'strategy.default_grid_spacing', '1', '默认网格间距百分比', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (37, 'strategy.max_active_orders', '20', '最大活跃订单数', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (38, 'strategy.rebalance_interval', '300', '重平衡间隔(秒)', 'f', '2025-06-10 03:35:30.938332+00', '2025-06-10 03:35:30.938332+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (39, 'strategy_templates.grid_trading', '{"type": "grid_trading", "parameters": {"grid_levels": 10, "base_order_size": 100, "max_active_orders": 20, "stop_loss_percent": 5.0, "take_profit_percent": 0.5, "grid_spacing_percent": 1.0}, "risk_management": {"max_position_size": 10000, "max_drawdown_percent": 10, "position_size_percent": 5}}', '网格交易策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (40, 'strategy_templates.dca', '{"type": "dollar_cost_averaging", "parameters": {"max_orders": 10, "order_size": 100, "take_profit_percent": 5.0, "order_interval_minutes": 60, "price_drop_threshold_percent": 2.0}, "risk_management": {"max_investment": 5000, "stop_loss_percent": 15.0}}', '定投策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (41, 'strategy_templates.momentum', '{"type": "momentum_trading", "parameters": {"lookback_period": 20, "momentum_threshold": 0.02, "exit_signal_strength": 0.3, "entry_signal_strength": 0.7, "position_size_percent": 10}, "risk_management": {"stop_loss_percent": 3.0, "take_profit_percent": 6.0, "max_holding_time_hours": 24}}', '动量交易策略模板', 'f', '2025-06-10 03:35:31.091936+00', '2025-06-10 03:35:31.091936+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (42, 'exchange_config.binance', '{"fees": {"maker": 0.001, "taker": 0.001}, "rate_limits": {"orders_per_second": 10, "weight_per_minute": 6000, "requests_per_minute": 1200}, "api_endpoint": "https://api.binance.com", "min_notional": 10, "websocket_endpoint": "wss://stream.binance.com:9443", "supported_order_types": ["MARKET", "LIMIT", "STOP_LOSS", "STOP_LOSS_LIMIT"]}', 'Binance交易所配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (43, 'exchange_config.coinbase', '{"fees": {"maker": 0.005, "taker": 0.005}, "rate_limits": {"orders_per_second": 5, "requests_per_minute": 600}, "api_endpoint": "https://api.pro.coinbase.com", "min_notional": 10, "websocket_endpoint": "wss://ws-feed.pro.coinbase.com", "supported_order_types": ["market", "limit", "stop"]}', 'Coinbase交易所配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (44, 'exchange_config.simulator', '{"fees": {"maker": 0.001, "taker": 0.001}, "rate_limits": {"orders_per_second": 100, "requests_per_minute": 6000}, "api_endpoint": "http://localhost:8080", "min_notional": 1, "simulation_mode": true, "websocket_endpoint": "ws://localhost:8080/ws", "supported_order_types": ["MARKET", "LIMIT", "STOP_LOSS", "STOP_LOSS_LIMIT"]}', '模拟器配置', 'f', '2025-06-10 03:35:31.150913+00', '2025-06-10 03:35:31.150913+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (45, 'user_roles.admin', '{"permissions": ["system.manage", "users.manage", "strategies.manage", "portfolios.manage", "orders.manage", "trades.view", "config.manage"]}', '管理员角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (46, 'user_roles.trader', '{"permissions": ["strategies.create", "strategies.update", "strategies.delete", "portfolios.view", "portfolios.update", "orders.create", "orders.cancel", "trades.view"]}', '交易员角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
INSERT INTO "public"."system_config" ("id", "key", "value", "description", "is_encrypted", "created_at", "updated_at") VALUES (47, 'user_roles.viewer', '{"permissions": ["strategies.view", "portfolios.view", "orders.view", "trades.view"]}', '查看者角色权限', 'f', '2025-06-10 03:35:31.21141+00', '2025-06-10 03:35:31.21141+00');
COMMIT;

-- Records of trading_pairs
BEGIN;
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (1, 'BTC/USDT', 'BTC', 'USDT', 1, 't', 0.00001000, NULL, 5, 2, NULL, NULL, 0.01000000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (2, 'ETH/USDT', 'ETH', 'USDT', 1, 't', 0.00100000, NULL, 3, 2, NULL, NULL, 0.01000000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (3, 'BNB/USDT', 'BNB', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (4, 'ADA/USDT', 'ADA', 'USDT', 1, 't', 1.00000000, NULL, 0, 6, NULL, NULL, 0.00000100, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (5, 'SOL/USDT', 'SOL', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (6, 'DOT/USDT', 'DOT', 'USDT', 1, 't', 0.10000000, NULL, 1, 4, NULL, NULL, 0.00010000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (7, 'MATIC/USDT', 'MATIC', 'USDT', 1, 't', 1.00000000, NULL, 0, 6, NULL, NULL, 0.00000100, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (8, 'AVAX/USDT', 'AVAX', 'USDT', 1, 't', 0.01000000, NULL, 2, 3, NULL, NULL, 0.00100000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."trading_pairs" ("id", "symbol", "base_asset", "quote_asset", "exchange_id", "is_active", "min_quantity", "max_quantity", "quantity_precision", "price_precision", "min_price", "max_price", "tick_size", "min_notional", "trading_fee_rate", "created_at", "updated_at") VALUES (9, 'LINK/USDT', 'LINK', 'USDT', 1, 't', 0.01000000, NULL, 2, 4, NULL, NULL, 0.00010000, 10.00000000, 0.001000, '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
COMMIT;

-- ----------------------------
-- 5. 主键、索引和约束 (Primary Keys, Indexes, and Constraints)
-- ----------------------------

-- Primary Keys
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."backtest_files" ADD CONSTRAINT "backtest_files_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."backtest_portfolio_snapshots" ADD CONSTRAINT "backtest_portfolio_snapshots_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."backtest_results" ADD CONSTRAINT "backtest_results_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."backtest_trades" ADD CONSTRAINT "backtest_trades_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."engines" ADD CONSTRAINT "engines_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."events" ADD CONSTRAINT "events_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "risk_rules_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "risk_statistics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."strategies" ADD CONSTRAINT "strategies_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_pkey" PRIMARY KEY ("id");

-- Indexes
CREATE INDEX "idx_audit_logs_changed_at" ON "public"."audit_logs" USING btree ("changed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_audit_logs_operation" ON "public"."audit_logs" USING btree ("operation" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_audit_logs_table_record" ON "public"."audit_logs" USING btree ("table_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST, "record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_files_timeframe" ON "public"."backtest_files" USING btree ("timeframe" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_files_trading_pair" ON "public"."backtest_files" USING btree ("trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_portfolio_snapshots_backtest_id" ON "public"."backtest_portfolio_snapshots" USING btree ("backtest_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_portfolio_snapshots_timestamp" ON "public"."backtest_portfolio_snapshots" USING btree ("timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_results_created_at" ON "public"."backtest_results" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_results_engine_id" ON "public"."backtest_results" USING btree ("engine_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_results_status" ON "public"."backtest_results" USING btree ("status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_trades_backtest_id" ON "public"."backtest_trades" USING btree ("backtest_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_trades_timestamp" ON "public"."backtest_trades" USING btree ("timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_backtest_trades_trading_pair" ON "public"."backtest_trades" USING btree ("trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_candles_trading_pair_timeframe_time" ON "public"."candles" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "timeframe" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST, "open_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_engines_engine_type" ON "public"."engines" USING btree ("engine_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_engines_status" ON "public"."engines" USING btree ("status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_events_type" ON "public"."events" USING btree ("event_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_exchanges_name" ON "public"."exchanges" USING btree ("name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_exchanges_status" ON "public"."exchanges" USING btree ("status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_active_status" ON "public"."orders" USING btree ("status" "pg_catalog"."enum_ops" ASC NULLS LAST, "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST) WHERE status = ANY (ARRAY['Pending'::order_status, 'PartiallyFilled'::order_status]);
CREATE INDEX "idx_orders_client_order_id" ON "public"."orders" USING btree ("client_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_created_at" ON "public"."orders" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_created_status" ON "public"."orders" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST, "status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_exchange" ON "public"."orders" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_exchange_fk" ON "public"."orders" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_exchange_order_id" ON "public"."orders" USING btree ("exchange_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_exchange_status" ON "public"."orders" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_metadata_gin" ON "public"."orders" USING gin ("metadata" "pg_catalog"."jsonb_ops");
CREATE INDEX "idx_orders_parent" ON "public"."orders" USING btree ("parent_order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_side" ON "public"."orders" USING btree ("side" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_stats" ON "public"."orders" USING btree ("strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST, "status" "pg_catalog"."enum_ops" ASC NULLS LAST, "side" "pg_catalog"."enum_ops" ASC NULLS LAST, "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_status" ON "public"."orders" USING btree ("status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_strategy" ON "public"."orders" USING btree ("strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_strategy_fk" ON "public"."orders" USING btree ("strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_strategy_status" ON "public"."orders" USING btree ("strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST, "status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_time_range" ON "public"."orders" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST, "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_trading_pair" ON "public"."orders" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_trading_pair_fk" ON "public"."orders" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_orders_trading_pair_status" ON "public"."orders" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_portfolios_active" ON "public"."portfolios" USING btree ("is_active" "pg_catalog"."bool_ops" ASC NULLS LAST);
CREATE INDEX "idx_portfolios_created_at" ON "public"."portfolios" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_portfolios_name" ON "public"."portfolios" USING btree ("name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_portfolios_name_gin" ON "public"."portfolios" USING gin (to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops");
CREATE INDEX "idx_positions_exchange" ON "public"."positions" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_positions_portfolio" ON "public"."positions" USING btree ("portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_positions_portfolio_fk" ON "public"."positions" USING btree ("portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_positions_portfolio_id" ON "public"."positions" USING btree ("portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_positions_trading_pair" ON "public"."positions" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_positions_updated_at" ON "public"."positions" USING btree ("updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_active" ON "public"."strategies" USING btree ("status" "pg_catalog"."enum_ops" ASC NULLS LAST, "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST) WHERE status = ANY (ARRAY['Running'::strategy_status, 'Paused'::strategy_status]);
CREATE INDEX "idx_strategies_config_gin" ON "public"."strategies" USING gin ("config" "pg_catalog"."jsonb_ops");
CREATE INDEX "idx_strategies_created_at" ON "public"."strategies" USING btree ("created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_name" ON "public"."strategies" USING btree ("name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_name_gin" ON "public"."strategies" USING gin (to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops");
CREATE INDEX "idx_strategies_status" ON "public"."strategies" USING btree ("status" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_strategy_type" ON "public"."strategies" USING btree ("strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_trading_pair" ON "public"."strategies" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_strategies_type" ON "public"."strategies" USING btree ("strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_system_config_key" ON "public"."system_config" USING btree ("key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_system_config_value_gin" ON "public"."system_config" USING gin ("value" "pg_catalog"."jsonb_ops");
CREATE INDEX "idx_trades_exchange" ON "public"."trades" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_exchange_time" ON "public"."trades" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_exchange_trade_id" ON "public"."trades" USING btree ("exchange_trade_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_executed_at" ON "public"."trades" USING btree ("executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_order" ON "public"."trades" USING btree ("order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_order_fk" ON "public"."trades" USING btree ("order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_order_id" ON "public"."trades" USING btree ("order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_order_time" ON "public"."trades" USING btree ("order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST, "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_pair_time" ON "public"."trades" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_side" ON "public"."trades" USING btree ("side" "pg_catalog"."enum_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_stats" ON "public"."trades" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST, "side" "pg_catalog"."enum_ops" ASC NULLS LAST, "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_time_range" ON "public"."trades" USING btree ("executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST, "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST);
CREATE INDEX "idx_trades_trading_pair" ON "public"."trades" USING btree ("trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_trading_pairs_active" ON "public"."trading_pairs" USING btree ("is_active" "pg_catalog"."bool_ops" ASC NULLS LAST);
CREATE INDEX "idx_trading_pairs_base_asset" ON "public"."trading_pairs" USING btree ("base_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_trading_pairs_exchange" ON "public"."trading_pairs" USING btree ("exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST);
CREATE INDEX "idx_trading_pairs_quote_asset" ON "public"."trading_pairs" USING btree ("quote_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX "idx_trading_pairs_symbol" ON "public"."trading_pairs" USING btree ("symbol" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);


-- Unique Constraints
ALTER TABLE "public"."backtest_files" ADD CONSTRAINT "backtest_files_filename_key" UNIQUE ("filename");
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_timeframe_open_time_key" UNIQUE ("trading_pair_id", "timeframe", "open_time");
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_name_key" UNIQUE ("name");
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_portfolio_id_trading_pair_id_exchange_id_side_key" UNIQUE ("portfolio_id", "trading_pair_id", "exchange_id", "side");
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "risk_statistics_date_key" UNIQUE ("date");
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_trading_pair_key" UNIQUE ("strategy_id", "trading_pair");
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_key_key" UNIQUE ("key");
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_symbol_key" UNIQUE ("symbol");

-- Check Constraints
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_operation_check" CHECK (operation::text = ANY (ARRAY['INSERT'::character varying, 'UPDATE'::character varying, 'DELETE'::character varying]::text[]));
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_stop_price_check" CHECK (stop_price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_filled_quantity_check" CHECK (filled_quantity >= 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_filled_quantity" CHECK (filled_quantity <= quantity);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_price_for_limit" CHECK (order_type = 'Market'::order_type OR (order_type = ANY (ARRAY['Limit'::order_type, 'StopLimit'::order_type])) AND price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_stop_price_for_stop" CHECK ((order_type <> ALL (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) OR (order_type = ANY (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) AND stop_price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_average_price_check" CHECK (average_price > 0::numeric);
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_initial_capital_check" CHECK (initial_capital > 0::numeric);
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_average_price_check" CHECK (average_price >= 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_fee_check" CHECK (fee >= 0::numeric);

-- Foreign Keys
ALTER TABLE "public"."backtest_portfolio_snapshots" ADD CONSTRAINT "backtest_portfolio_snapshots_backtest_id_fkey" FOREIGN KEY ("backtest_id") REFERENCES "public"."backtest_results" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."backtest_trades" ADD CONSTRAINT "backtest_trades_backtest_id_fkey" FOREIGN KEY ("backtest_id") REFERENCES "public"."backtest_results" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_fkey" FOREIGN KEY ("trading_pair_id") REFERENCES "public"."trading_pairs" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_engine_id_fkey" FOREIGN KEY ("engine_id") REFERENCES "public"."engines" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_risk_check_id_fkey" FOREIGN KEY ("risk_check_id") REFERENCES "public"."risk_checks" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_rule_id_fkey" FOREIGN KEY ("rule_id") REFERENCES "public"."risk_rules" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;