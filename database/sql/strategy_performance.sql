-- SigmaX Trading System Database Schema
-- Strategy Performance Table
--
-- This file contains the strategy_performance table definition
-- Note: This file requires enum types to be created first
-- Run 00_create_enums.sql before running this file


-- ----------------------------
-- Table structure for strategy_performance
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_performance";
CREATE TABLE "public"."strategy_performance" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "winning_trades" int4 NOT NULL DEFAULT 0,
  "losing_trades" int4 NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "win_rate" numeric(10,4) NOT NULL DEFAULT 0,
  "sharpe_ratio" numeric(10,4),
  "max_drawdown" numeric(20,8) NOT NULL DEFAULT 0,
  "max_drawdown_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "current_positions" int4 NOT NULL DEFAULT 0,
  "active_orders" int4 NOT NULL DEFAULT 0,
  "last_signal_time" timestamptz(6),
  "uptime_seconds" int8 NOT NULL DEFAULT 0,
  "error_count" int4 NOT NULL DEFAULT 0,
  "last_error" text COLLATE "pg_catalog"."default",
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Records of strategy_performance
-- ----------------------------
BEGIN;
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('d354882a-84db-4d24-a32c-8147464f0c5d', 'b7f6148c-5670-4375-9339-9ce28b8cefc5', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:03:44.735291+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('6106c557-bc65-45d0-b174-67fcb4c97448', '2b2e8662-bb1c-4dda-8559-d9b1131fc8ff', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:07:10.276922+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('fe4b16cf-2e1a-4244-9fa0-1ae338351491', 'e37be5a2-d742-4c9e-9e0e-bb2c6f3f9aa6', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-10 09:07:48.376613+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('8f3b2e48-fd0d-45ae-a8c2-b6dcb842fd5a', '3f3d6d16-40de-4ae3-bde8-3d6649964169', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-13 18:08:15.652652+00');
INSERT INTO "public"."strategy_performance" ("id", "strategy_id", "total_trades", "winning_trades", "losing_trades", "total_pnl", "realized_pnl", "unrealized_pnl", "win_rate", "sharpe_ratio", "max_drawdown", "max_drawdown_percentage", "current_positions", "active_orders", "last_signal_time", "uptime_seconds", "error_count", "last_error", "updated_at") VALUES ('e7b45c09-1e2a-4d09-a288-04f88cacab40', 'dd9a4fd0-2b6f-4270-8b63-9e078ab9d056', 0, 0, 0, 0.00000000, 0.00000000, 0.00000000, 0.0000, NULL, 0.00000000, 0.0000, 0, 0, NULL, 0, 0, NULL, '2025-06-18 16:53:27.198507+00');
COMMIT;

-- ----------------------------
-- Primary Key structure for table strategy_performance
-- ----------------------------
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table strategy_performance
-- ----------------------------
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
