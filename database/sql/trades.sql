/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:11:26
*/


-- ----------------------------
-- Table structure for trades
-- ----------------------------
DROP TABLE IF EXISTS "public"."trades";
CREATE TABLE "public"."trades" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "order_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_trade_id" varchar(100) COLLATE "pg_catalog"."default",
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "quote_quantity" numeric(20,8) GENERATED ALWAYS AS (
(quantity * price)
) STORED,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "commission_rate" numeric(10,6),
  "is_maker" bool,
  "trade_time" timestamptz(6),
  "executed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "metadata" jsonb DEFAULT '{}'::jsonb
)
;

-- ----------------------------
-- Records of trades
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table trades
-- ----------------------------
CREATE INDEX "idx_trades_exchange" ON "public"."trades" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_exchange_time" ON "public"."trades" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_exchange_trade_id" ON "public"."trades" USING btree (
  "exchange_trade_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_executed_at" ON "public"."trades" USING btree (
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_fk" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_id" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_time" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_pair_time" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_side" ON "public"."trades" USING btree (
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_stats" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_time_range" ON "public"."trades" USING btree (
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_trading_pair" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table trades
-- ----------------------------
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_fee_check" CHECK (fee >= 0::numeric);

-- ----------------------------
-- Primary Key structure for table trades
-- ----------------------------
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_pkey" PRIMARY KEY ("id");
