/*
 优化后的风控规则表
 
 主要改进：
 1. 添加完整的索引策略
 2. 使用 ENUM 类型替代 VARCHAR
 3. 添加必要的约束检查
 4. 完善外键关系
 5. 添加版本控制和审计
 6. 优化 JSONB 字段索引
*/

-- ----------------------------
-- 创建 ENUM 类型
-- ----------------------------
DO $$ BEGIN
    CREATE TYPE rule_category AS ENUM (
        'basic',           -- 基础风控
        'position',        -- 持仓风控
        'trading',         -- 交易风控
        'market',          -- 市场风控
        'time_based',      -- 时间风控
        'advanced'         -- 高级风控
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE rule_status AS ENUM (
        'active',          -- 激活
        'inactive',        -- 停用
        'testing',         -- 测试中
        'deprecated'       -- 已废弃
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ----------------------------
-- 删除旧表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_rules" CASCADE;

-- ----------------------------
-- 优化后的风控规则表
-- ----------------------------
CREATE TABLE "public"."risk_rules" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  
  -- 规则分类和类型
  "category" rule_category NOT NULL,
  "rule_type" VARCHAR(100) NOT NULL,
  
  -- 规则配置
  "parameters" JSONB NOT NULL DEFAULT '{}',
  "conditions" JSONB DEFAULT '{}',
  
  -- 控制字段
  "enabled" BOOLEAN NOT NULL DEFAULT true,
  "status" rule_status NOT NULL DEFAULT 'active',
  "priority" INTEGER NOT NULL DEFAULT 100,
  
  -- 适用范围
  "strategy_type" VARCHAR(100),
  "trading_pairs" JSONB DEFAULT '[]',
  
  -- 执行统计
  "execution_count" BIGINT DEFAULT 0,
  "success_count" BIGINT DEFAULT 0,
  "failure_count" BIGINT DEFAULT 0,
  "last_executed_at" TIMESTAMPTZ,
  "average_execution_time_ms" DECIMAL(10,3) DEFAULT 0,
  
  -- 版本控制
  "version" INTEGER NOT NULL DEFAULT 1,
  "previous_version_id" UUID REFERENCES risk_rules(id),
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  "updated_by" VARCHAR(255),
  
  -- 软删除
  "deleted_at" TIMESTAMPTZ,
  "deleted_by" VARCHAR(255)
);

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."risk_rules" IS '优化后的风控规则表 - 包含完整的规则定义、版本控制和审计功能';
COMMENT ON COLUMN "public"."risk_rules"."category" IS '规则分类：basic, position, trading, market, time_based, advanced';
COMMENT ON COLUMN "public"."risk_rules"."rule_type" IS '具体规则类型：order_size_limit, trading_frequency等';
COMMENT ON COLUMN "public"."risk_rules"."parameters" IS '规则参数：包含所有阈值和配置';
COMMENT ON COLUMN "public"."risk_rules"."conditions" IS '执行条件：规则何时生效的条件';
COMMENT ON COLUMN "public"."risk_rules"."priority" IS '执行优先级：数值越大越先执行';
COMMENT ON COLUMN "public"."risk_rules"."strategy_type" IS '适用策略类型：NULL表示全局规则';
COMMENT ON COLUMN "public"."risk_rules"."trading_pairs" IS '适用交易对：空数组表示所有交易对';
COMMENT ON COLUMN "public"."risk_rules"."version" IS '规则版本号：每次修改递增';
COMMENT ON COLUMN "public"."risk_rules"."previous_version_id" IS '前一版本的规则ID';

-- ----------------------------
-- 性能优化索引
-- ----------------------------

-- 基础查询索引
CREATE INDEX "idx_risk_rules_category" ON "public"."risk_rules" ("category");
CREATE INDEX "idx_risk_rules_type" ON "public"."risk_rules" ("rule_type");
CREATE INDEX "idx_risk_rules_enabled" ON "public"."risk_rules" ("enabled");
CREATE INDEX "idx_risk_rules_status" ON "public"."risk_rules" ("status");
CREATE INDEX "idx_risk_rules_strategy" ON "public"."risk_rules" ("strategy_type");

-- 复合索引：查询性能优化
CREATE INDEX "idx_risk_rules_execution" ON "public"."risk_rules" 
  ("enabled", "status", "priority" DESC, "category");

CREATE INDEX "idx_risk_rules_active" ON "public"."risk_rules" 
  ("enabled", "deleted_at") WHERE deleted_at IS NULL;

CREATE INDEX "idx_risk_rules_priority_enabled" ON "public"."risk_rules" 
  ("priority" DESC, "enabled") WHERE enabled = true AND deleted_at IS NULL;

-- 时间相关索引
CREATE INDEX "idx_risk_rules_updated_at" ON "public"."risk_rules" ("updated_at");
CREATE INDEX "idx_risk_rules_last_executed" ON "public"."risk_rules" ("last_executed_at");

-- JSONB 字段索引
CREATE INDEX "idx_risk_rules_parameters_gin" ON "public"."risk_rules" USING gin("parameters");
CREATE INDEX "idx_risk_rules_conditions_gin" ON "public"."risk_rules" USING gin("conditions");
CREATE INDEX "idx_risk_rules_trading_pairs_gin" ON "public"."risk_rules" USING gin("trading_pairs");

-- 版本控制索引
CREATE INDEX "idx_risk_rules_version" ON "public"."risk_rules" ("version", "name");

-- ----------------------------
-- 数据完整性约束
-- ----------------------------

-- 优先级范围约束
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_priority_range" 
  CHECK (priority >= 0 AND priority <= 1000);

-- 版本号约束
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_version_positive" 
  CHECK (version > 0);

-- 执行统计约束
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_execution_counts_non_negative" 
  CHECK (execution_count >= 0 AND success_count >= 0 AND failure_count >= 0);

ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_success_failure_sum" 
  CHECK (success_count + failure_count <= execution_count);

-- 执行时间约束
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_execution_time_non_negative" 
  CHECK (average_execution_time_ms >= 0);

-- 软删除约束
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "check_deleted_consistency" 
  CHECK ((deleted_at IS NULL AND deleted_by IS NULL) OR (deleted_at IS NOT NULL AND deleted_by IS NOT NULL));

-- 名称唯一性约束（排除软删除的记录）
CREATE UNIQUE INDEX "idx_risk_rules_name_unique" ON "public"."risk_rules" ("name") 
  WHERE deleted_at IS NULL;

-- 规则类型和策略类型组合唯一性（防止重复规则）
CREATE UNIQUE INDEX "idx_risk_rules_type_strategy_unique" ON "public"."risk_rules" 
  ("rule_type", "strategy_type") WHERE deleted_at IS NULL;

-- ----------------------------
-- 触发器：自动更新 updated_at
-- ----------------------------
CREATE OR REPLACE FUNCTION update_risk_rules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_rules_updated_at
    BEFORE UPDATE ON "public"."risk_rules"
    FOR EACH ROW
    EXECUTE FUNCTION update_risk_rules_updated_at();

-- ----------------------------
-- 触发器：版本控制
-- ----------------------------
CREATE OR REPLACE FUNCTION handle_risk_rules_versioning()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果是更新操作且参数发生变化，创建新版本
    IF TG_OP = 'UPDATE' AND (
        OLD.parameters != NEW.parameters OR 
        OLD.conditions != NEW.conditions OR
        OLD.priority != NEW.priority OR
        OLD.enabled != NEW.enabled
    ) THEN
        NEW.version = OLD.version + 1;
        NEW.previous_version_id = OLD.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_rules_versioning
    BEFORE UPDATE ON "public"."risk_rules"
    FOR EACH ROW
    EXECUTE FUNCTION handle_risk_rules_versioning();

-- ----------------------------
-- ----------------------------
-- 初始数据
-- ----------------------------

-- 基础风控规则
INSERT INTO "public"."risk_rules" (
  "id", "name", "description", "category", "rule_type",
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES
(
  '10000000-0000-0000-0000-000000000001',
  '全局订单金额限制',
  '限制单笔订单的最大金额，防止过大订单造成风险',
  'basic',
  'order_amount_limit',
  '{"max_amount": 10000, "currency": "USDT", "check_mode": "absolute", "include_fees": true}',
  true,
  100,
  NULL,
  'system'
),
(
  '10000000-0000-0000-0000-000000000002',
  '最大回撤保护',
  '当回撤超过设定阈值时阻止新订单',
  'basic',
  'max_drawdown_protection',
  '{"max_drawdown_percent": 20.0, "calculation_period": "daily", "base_amount": "initial_capital"}',
  true,
  95,
  NULL,
  'system'
),
(
  '20000000-0000-0000-0000-000000000001',
  '日交易频率限制',
  '限制每日最大交易次数，防止过度交易',
  'trading',
  'daily_trading_frequency',
  '{"max_daily_trades": 100, "count_mode": "orders", "reset_time": "00:00:00", "timezone": "UTC"}',
  true,
  90,
  NULL,
  'system'
),
(
  '30000000-0000-0000-0000-000000000001',
  '单个持仓大小限制',
  '限制单个交易对的最大持仓',
  'position',
  'single_position_limit',
  '{"max_position_percent": 25.0, "base_amount": "total_capital", "include_unrealized_pnl": true}',
  true,
  80,
  NULL,
  'system'
),
(
  '40000000-0000-0000-0000-000000000001',
  '市场波动率保护',
  '当市场波动率过高时限制交易',
  'market',
  'volatility_threshold',
  '{"max_volatility_percent": 30.0, "calculation_period": "24h", "volatility_type": "realized"}',
  true,
  75,
  NULL,
  'system'
);

-- ----------------------------
-- 权限设置
-- ----------------------------
ALTER TABLE "public"."risk_rules" OWNER TO "neondb_owner";
