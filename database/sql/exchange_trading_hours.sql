/*
 Exchange Trading Hours Table
 用于存储各交易所的交易时间配置，支持市场开放状态检查
*/

-- ----------------------------
-- Table structure for exchange_trading_hours
-- ----------------------------
DROP TABLE IF EXISTS "public"."exchange_trading_hours";
CREATE TABLE "public"."exchange_trading_hours" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "exchange_id" int4 NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default",
  "market_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'spot',
  "timezone" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'UTC',
  "is_24_7" bool NOT NULL DEFAULT true,
  "monday_open" time,
  "monday_close" time,
  "tuesday_open" time,
  "tuesday_close" time,
  "wednesday_open" time,
  "wednesday_close" time,
  "thursday_open" time,
  "thursday_close" time,
  "friday_open" time,
  "friday_close" time,
  "saturday_open" time,
  "saturday_close" time,
  "sunday_open" time,
  "sunday_close" time,
  "holidays" jsonb DEFAULT '[]'::jsonb,
  "maintenance_windows" jsonb DEFAULT '[]'::jsonb,
  "special_hours" jsonb DEFAULT '{}'::jsonb,
  "enabled" bool NOT NULL DEFAULT true,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);
ALTER TABLE "public"."exchange_trading_hours" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of exchange_trading_hours
-- ----------------------------
BEGIN;
-- 加密货币交易所（24/7）
INSERT INTO "public"."exchange_trading_hours" ("exchange_id", "trading_pair", "market_type", "timezone", "is_24_7", "enabled") 
VALUES (1, NULL, 'spot', 'UTC', true, true); -- Binance

INSERT INTO "public"."exchange_trading_hours" ("exchange_id", "trading_pair", "market_type", "timezone", "is_24_7", "enabled") 
VALUES (2, NULL, 'spot', 'UTC', true, true); -- Coinbase

INSERT INTO "public"."exchange_trading_hours" ("exchange_id", "trading_pair", "market_type", "timezone", "is_24_7", "enabled") 
VALUES (3, NULL, 'spot', 'UTC', true, true); -- Kraken

INSERT INTO "public"."exchange_trading_hours" ("exchange_id", "trading_pair", "market_type", "timezone", "is_24_7", "enabled") 
VALUES (4, NULL, 'spot', 'UTC', true, true); -- Simulator

-- 示例：传统股票市场交易时间（如果需要）
INSERT INTO "public"."exchange_trading_hours" ("exchange_id", "trading_pair", "market_type", "timezone", "is_24_7", 
  "monday_open", "monday_close", "tuesday_open", "tuesday_close", "wednesday_open", "wednesday_close",
  "thursday_open", "thursday_close", "friday_open", "friday_close", "enabled") 
VALUES (5, NULL, 'stock', 'America/New_York', false, 
  '09:30:00', '16:00:00', '09:30:00', '16:00:00', '09:30:00', '16:00:00',
  '09:30:00', '16:00:00', '09:30:00', '16:00:00', false); -- 示例股票市场
COMMIT;

-- ----------------------------
-- Indexes structure for table exchange_trading_hours
-- ----------------------------
CREATE INDEX "idx_exchange_trading_hours_exchange_id" ON "public"."exchange_trading_hours" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_exchange_trading_hours_trading_pair" ON "public"."exchange_trading_hours" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_exchange_trading_hours_market_type" ON "public"."exchange_trading_hours" USING btree (
  "market_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_exchange_trading_hours_enabled" ON "public"."exchange_trading_hours" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table exchange_trading_hours
-- ----------------------------
ALTER TABLE "public"."exchange_trading_hours" ADD CONSTRAINT "exchange_trading_hours_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table exchange_trading_hours
-- ----------------------------
ALTER TABLE "public"."exchange_trading_hours" ADD CONSTRAINT "exchange_trading_hours_exchange_id_fkey" 
  FOREIGN KEY ("exchange_id") REFERENCES "public"."exchanges" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Unique constraints for table exchange_trading_hours
-- ----------------------------
ALTER TABLE "public"."exchange_trading_hours" ADD CONSTRAINT "exchange_trading_hours_unique" 
  UNIQUE ("exchange_id", "trading_pair", "market_type");

-- ----------------------------
-- Comments for table exchange_trading_hours
-- ----------------------------
COMMENT ON TABLE "public"."exchange_trading_hours" IS '交易所交易时间配置表，用于判断市场开放状态';
COMMENT ON COLUMN "public"."exchange_trading_hours"."exchange_id" IS '交易所ID';
COMMENT ON COLUMN "public"."exchange_trading_hours"."trading_pair" IS '交易对（NULL表示适用于所有交易对）';
COMMENT ON COLUMN "public"."exchange_trading_hours"."market_type" IS '市场类型（spot, futures, options等）';
COMMENT ON COLUMN "public"."exchange_trading_hours"."timezone" IS '时区';
COMMENT ON COLUMN "public"."exchange_trading_hours"."is_24_7" IS '是否24/7交易';
COMMENT ON COLUMN "public"."exchange_trading_hours"."holidays" IS '节假日列表（JSON格式）';
COMMENT ON COLUMN "public"."exchange_trading_hours"."maintenance_windows" IS '维护时间窗口（JSON格式）';
COMMENT ON COLUMN "public"."exchange_trading_hours"."special_hours" IS '特殊交易时间（JSON格式）';
