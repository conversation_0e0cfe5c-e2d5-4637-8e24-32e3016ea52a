/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:08:33
*/


-- ----------------------------
-- Table structure for candles
-- ----------------------------
DROP TABLE IF EXISTS "public"."candles";
CREATE TABLE "public"."candles" (
  "id" int8 NOT NULL DEFAULT nextval('candles_id_seq'::regclass),
  "trading_pair_id" int4 NOT NULL,
  "timeframe" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "open_time" timestamptz(6) NOT NULL,
  "close_time" timestamptz(6) NOT NULL,
  "open_price" numeric(20,8) NOT NULL,
  "high_price" numeric(20,8) NOT NULL,
  "low_price" numeric(20,8) NOT NULL,
  "close_price" numeric(20,8) NOT NULL,
  "volume" numeric(20,8) NOT NULL,
  "quote_volume" numeric(20,8) NOT NULL,
  "trades_count" int4 NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."candles" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of candles
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table candles
-- ----------------------------
CREATE INDEX "idx_candles_trading_pair_timeframe_time" ON "public"."candles" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timeframe" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "open_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_timeframe_open_time_key" UNIQUE ("trading_pair_id", "timeframe", "open_time");

-- ----------------------------
-- Primary Key structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_fkey" FOREIGN KEY ("trading_pair_id") REFERENCES "public"."trading_pairs" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
