/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:08:46
*/


-- ----------------------------
-- Table structure for engines
-- ----------------------------
DROP TABLE IF EXISTS "public"."engines";
CREATE TABLE "public"."engines" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "engine_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Created'::character varying,
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "trading_pairs" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "initial_capital" numeric(20,8),
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."engines" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of engines
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table engines
-- ----------------------------
CREATE INDEX "idx_engines_engine_type" ON "public"."engines" USING btree (
  "engine_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_engines_status" ON "public"."engines" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table engines
-- ----------------------------
ALTER TABLE "public"."engines" ADD CONSTRAINT "engines_pkey" PRIMARY KEY ("id");
