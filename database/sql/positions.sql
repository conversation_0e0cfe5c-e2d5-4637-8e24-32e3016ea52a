/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:18
*/


-- ----------------------------
-- Table structure for positions
-- ----------------------------
DROP TABLE IF EXISTS "public"."positions";
CREATE TABLE "public"."positions" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "portfolio_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "average_price" numeric(20,8) NOT NULL DEFAULT 0,
  "market_price" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "cost_basis" numeric(20,8) GENERATED ALWAYS AS (
(quantity * average_price)
) STORED,
  "market_value" numeric(20,8),
  "last_updated_price_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."positions" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of positions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Indexes structure for table positions
-- ----------------------------
CREATE INDEX "idx_positions_exchange" ON "public"."positions" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio_fk" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio_id" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_trading_pair" ON "public"."positions" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_updated_at" ON "public"."positions" USING btree (
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_portfolio_id_trading_pair_id_exchange_id_side_key" UNIQUE ("portfolio_id", "trading_pair_id", "exchange_id", "side");

-- ----------------------------
-- Checks structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_average_price_check" CHECK (average_price >= 0::numeric);

-- ----------------------------
-- Primary Key structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_pkey" PRIMARY KEY ("id");
