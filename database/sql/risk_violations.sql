/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:53
*/


-- ----------------------------
-- Table structure for risk_violations
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_violations";
CREATE TABLE "public"."risk_violations" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "risk_check_id" uuid NOT NULL,
  "rule_id" uuid NOT NULL,
  "rule_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "severity" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Medium'::character varying,
  "message" text COLLATE "pg_catalog"."default" NOT NULL,
  "current_value" varchar(255) COLLATE "pg_catalog"."default",
  "limit_value" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."risk_violations" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_violations
-- ----------------------------
BEGIN;
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('31c909d4-dd65-4bcf-b207-aa6043d6596c', '8b0d4963-ff1d-4116-9704-2ddc343cf68d', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 08:58:53.850393+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('940248d9-69ed-4a5d-ad73-9e3aa621a883', 'e9a8d55b-a786-4918-ae92-c636c6d5e7ec', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('7aa71deb-4016-402e-8733-313c9e925b57', 'e9a8d55b-a786-4918-ae92-c636c6d5e7ec', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('47e7d7ab-df73-4001-a769-b1e283c28a85', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('c9826e02-000a-4065-b76e-7abea6f48452', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('8d1c7f27-b571-45ce-b2bf-3f62770b30f5', '489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('34b7542e-6827-46cf-b553-956232cc734d', '22cbd19f-3937-486c-8a86-60b949d4ac17', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ce12711d-f814-4955-8167-9392b381b801', '22cbd19f-3937-486c-8a86-60b949d4ac17', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('f0dbda57-d510-4674-9ad2-3c917f154fa6', '22cbd19f-3937-486c-8a86-60b949d4ac17', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ae418ff7-008b-4c6a-9b99-1dad13b7e816', '22cbd19f-3937-486c-8a86-60b949d4ac17', 'f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('9ee2a364-603b-48f7-a77c-517633174aa5', '317f0432-30bf-4c5e-9aa6-48a67406c35c', '60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('b22e8b35-16f2-470d-90ae-53c46f580bf6', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('c0965a4e-a253-40f4-80d4-9f4ec98a3ed3', '317f0432-30bf-4c5e-9aa6-48a67406c35c', '490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('ee5dacd7-eb77-46e5-b176-6bf07df8f309', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_violations" ("id", "risk_check_id", "rule_id", "rule_name", "severity", "message", "current_value", "limit_value", "created_at") VALUES ('a969edca-5c81-4ffc-a2ca-742dda4f09fa', '317f0432-30bf-4c5e-9aa6-48a67406c35c', 'ba178e3e-32ed-43c8-b858-813ddfe95640', 'Test Max Order Size', 'High', 'Order size exceeds maximum allowed limit', '600', '500', '2025-06-10 09:07:46.430018+00');
COMMIT;

-- ----------------------------
-- Primary Key structure for table risk_violations
-- ----------------------------
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table risk_violations
-- ----------------------------
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_risk_check_id_fkey" FOREIGN KEY ("risk_check_id") REFERENCES "public"."risk_checks" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_rule_id_fkey" FOREIGN KEY ("rule_id") REFERENCES "public"."risk_rules" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
