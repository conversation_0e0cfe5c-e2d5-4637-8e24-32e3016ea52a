-- risk_violations 表结构（从线上数据库导出）
-- 导出时间: 2025-08-05 18:15:50

DROP TABLE IF EXISTS "public"."risk_violations" CASCADE;

CREATE TABLE "public"."risk_violations" (
  "id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "risk_check_id" UUID NOT NULL,
  "rule_id" UUID NOT NULL,
  "rule_name" VARCHAR(255) NOT NULL,
  "rule_version" INTEGER DEFAULT 1,
  "severity" USER-DEFINED NOT NULL DEFAULT 'medium'::violation_severity,
  "status" USER-DEFINED NOT NULL DEFAULT 'active'::violation_status,
  "message" TEXT NOT NULL,
  "detailed_message" TEXT,
  "current_value" VARCHAR(255),
  "limit_value" VARCHAR(255),
  "threshold_exceeded_by" DECIMAL(20,8),
  "threshold_exceeded_percent" DECIMAL(10,4),
  "trading_pair" VARCHAR(50),
  "strategy_id" UUID,
  "portfolio_id" UUID,
  "order_context" JSONB DEFAULT '{}'::jsonb,
  "potential_loss" DECIMAL(20,8),
  "risk_contribution" DECIMAL(10,4),
  "impact_score" DECIMAL(5,2) DEFAULT 0,
  "auto_resolved" BOOLEAN DEFAULT false,
  "resolution_action" VARCHAR(100),
  "resolution_notes" TEXT,
  "resolved_at" TIMESTAMPTZ,
  "resolved_by" VARCHAR(255),
  "notification_sent" BOOLEAN DEFAULT false,
  "notification_sent_at" TIMESTAMPTZ,
  "escalation_level" INTEGER DEFAULT 0,
  "escalated_at" TIMESTAMPTZ,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  PRIMARY KEY ("id")
);

-- 索引

CREATE INDEX idx_risk_violations_check_id ON public.risk_violations USING btree (risk_check_id);
CREATE INDEX idx_risk_violations_rule_id ON public.risk_violations USING btree (rule_id);
CREATE INDEX idx_risk_violations_severity ON public.risk_violations USING btree (severity);
CREATE INDEX idx_risk_violations_status ON public.risk_violations USING btree (status);
CREATE INDEX idx_risk_violations_created_at ON public.risk_violations USING btree (created_at DESC);
CREATE INDEX idx_risk_violations_resolved_at ON public.risk_violations USING btree (resolved_at DESC);
CREATE INDEX idx_risk_violations_active ON public.risk_violations USING btree (status, severity, created_at DESC) WHERE (status = 'active'::violation_status);
CREATE INDEX idx_risk_violations_critical ON public.risk_violations USING btree (severity, created_at DESC) WHERE (severity = 'critical'::violation_severity);
CREATE INDEX idx_risk_violations_unresolved ON public.risk_violations USING btree (status, created_at DESC) WHERE (status = ANY (ARRAY['active'::violation_status, 'escalated'::violation_status]));
CREATE INDEX idx_risk_violations_strategy ON public.risk_violations USING btree (strategy_id, status, created_at DESC) WHERE (strategy_id IS NOT NULL);
CREATE INDEX idx_risk_violations_trading_pair ON public.risk_violations USING btree (trading_pair, severity, created_at DESC) WHERE (trading_pair IS NOT NULL);
CREATE INDEX idx_risk_violations_rule_analysis ON public.risk_violations USING btree (rule_id, rule_name, severity, created_at DESC);
CREATE INDEX idx_risk_violations_rule_version ON public.risk_violations USING btree (rule_id, rule_version, created_at DESC);
CREATE INDEX idx_risk_violations_impact_score ON public.risk_violations USING btree (impact_score DESC);
CREATE INDEX idx_risk_violations_potential_loss ON public.risk_violations USING btree (potential_loss DESC);
CREATE INDEX idx_risk_violations_notification_pending ON public.risk_violations USING btree (notification_sent, severity, created_at) WHERE (notification_sent = false);
CREATE INDEX idx_risk_violations_escalation ON public.risk_violations USING btree (escalation_level, escalated_at DESC) WHERE (escalation_level > 0);
CREATE INDEX idx_risk_violations_order_context_gin ON public.risk_violations USING gin (order_context);

-- 约束

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_by_positive" CHECK ((threshold_exceeded_by IS NULL) OR (threshold_exceeded_by >= (0)::numeric));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_percent_positive" CHECK ((threshold_exceeded_percent IS NULL) OR (threshold_exceeded_percent >= (0)::numeric));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_potential_loss_positive" CHECK ((potential_loss IS NULL) OR (potential_loss >= (0)::numeric));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_risk_contribution_range" CHECK ((risk_contribution IS NULL) OR ((risk_contribution >= (0)::numeric) AND (risk_contribution <= (100)::numeric)));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_impact_score_range" CHECK ((impact_score >= (0)::numeric) AND (impact_score <= (100)::numeric));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_level_positive" CHECK (escalation_level >= 0);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_rule_version_positive" CHECK (rule_version > 0);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolution_consistency" CHECK (((status = 'resolved'::violation_status) AND (resolved_at IS NOT NULL) AND (resolved_by IS NOT NULL)) OR ((status <> 'resolved'::violation_status) AND ((resolved_at IS NULL) OR (resolved_by IS NULL))));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_consistency" CHECK (((escalation_level > 0) AND (escalated_at IS NOT NULL)) OR ((escalation_level = 0) AND (escalated_at IS NULL)));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_consistency" CHECK (((notification_sent = true) AND (notification_sent_at IS NOT NULL)) OR ((notification_sent = false) AND (notification_sent_at IS NULL)));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolved_after_created" CHECK ((resolved_at IS NULL) OR (resolved_at >= created_at));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalated_after_created" CHECK ((escalated_at IS NULL) OR (escalated_at >= created_at));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_after_created" CHECK ((notification_sent_at IS NULL) OR (notification_sent_at >= created_at));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_1_not_null" CHECK id IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_2_not_null" CHECK risk_check_id IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_3_not_null" CHECK rule_id IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_4_not_null" CHECK rule_name IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_6_not_null" CHECK severity IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_7_not_null" CHECK status IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_8_not_null" CHECK message IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_30_not_null" CHECK created_at IS NOT NULL;
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "2200_17963_31_not_null" CHECK updated_at IS NOT NULL;

ALTER TABLE "public"."risk_violations" OWNER TO "neondb_owner";