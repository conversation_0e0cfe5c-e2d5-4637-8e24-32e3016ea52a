/*
 优化后的风险预算使用表
 
 主要改进：
 1. 优化表结构设计
 2. 添加完整的索引策略
 3. 完善数据完整性约束
 4. 添加自动计算字段
 5. 支持实时风险预算监控
*/

-- ----------------------------
-- 创建 ENUM 类型
-- ----------------------------
DO $$ BEGIN
    CREATE TYPE budget_allocation_type AS ENUM (
        'strategy',        -- 策略分配
        'asset_class',     -- 资产类别分配
        'geographic',      -- 地理分配
        'sector',          -- 行业分配
        'time_based'       -- 时间段分配
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE budget_status AS ENUM (
        'normal',          -- 正常
        'warning',         -- 警告（接近限制）
        'critical',        -- 严重（超出限制）
        'suspended'        -- 暂停（预算耗尽）
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ----------------------------
-- 删除旧表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_budget_usage" CASCADE;
DROP TABLE IF EXISTS "public"."risk_budget_allocations" CASCADE;

-- ----------------------------
-- 优化后的风险预算分配表
-- ----------------------------
CREATE TABLE "public"."risk_budget_allocations" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  
  -- 分配信息
  "allocation_type" budget_allocation_type NOT NULL,
  "allocation_key" VARCHAR(100) NOT NULL,
  "parent_allocation_id" UUID REFERENCES risk_budget_allocations(id),
  
  -- 预算配置
  "total_budget" DECIMAL(20,8) NOT NULL,
  "allocated_amount" DECIMAL(20,8) NOT NULL,
  "allocation_percentage" DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN total_budget > 0 
    THEN (allocated_amount / total_budget * 100) 
    ELSE 0 END
  ) STORED,
  
  -- 限制设置
  "min_allocation" DECIMAL(20,8) DEFAULT 0,
  "max_allocation" DECIMAL(20,8),
  "warning_threshold" DECIMAL(5,2) DEFAULT 80.0,
  "critical_threshold" DECIMAL(5,2) DEFAULT 95.0,
  
  -- 当前使用情况
  "current_usage" DECIMAL(20,8) DEFAULT 0,
  "usage_percentage" DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN allocated_amount > 0 
    THEN (current_usage / allocated_amount * 100) 
    ELSE 0 END
  ) STORED,
  "available_budget" DECIMAL(20,8) GENERATED ALWAYS AS (allocated_amount - current_usage) STORED,
  
  -- 状态信息
  "status" budget_status GENERATED ALWAYS AS (
    CASE 
      WHEN current_usage >= allocated_amount THEN 'suspended'
      WHEN (current_usage / NULLIF(allocated_amount, 0) * 100) >= 95.0 THEN 'critical'
      WHEN (current_usage / NULLIF(allocated_amount, 0) * 100) >= 80.0 THEN 'warning'
      ELSE 'normal'
    END
  ) STORED,
  
  -- 时间设置
  "effective_from" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "effective_until" TIMESTAMPTZ,
  "reset_frequency" VARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly, quarterly, yearly
  "last_reset_at" TIMESTAMPTZ,
  "next_reset_at" TIMESTAMPTZ,
  
  -- 控制字段
  "is_active" BOOLEAN NOT NULL DEFAULT true,
  "auto_reset" BOOLEAN DEFAULT true,
  "allow_overdraft" BOOLEAN DEFAULT false,
  "overdraft_limit" DECIMAL(20,8) DEFAULT 0,
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  "updated_by" VARCHAR(255)
);

-- ----------------------------
-- 优化后的风险预算使用记录表
-- ----------------------------
CREATE TABLE "public"."risk_budget_usage" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "timestamp" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- 关联信息
  "allocation_id" UUID NOT NULL REFERENCES risk_budget_allocations(id),
  "strategy_id" UUID,
  "portfolio_id" UUID,
  "trading_pair" VARCHAR(50),
  
  -- 预算使用详情
  "operation_type" VARCHAR(50) NOT NULL, -- allocate, release, adjust, reset
  "amount_change" DECIMAL(20,8) NOT NULL, -- 正数表示使用，负数表示释放
  "previous_usage" DECIMAL(20,8) NOT NULL,
  "new_usage" DECIMAL(20,8) NOT NULL,
  "reason" TEXT,
  
  -- 风险指标
  "var_estimate" DECIMAL(20,8) DEFAULT 0,
  "stress_test_loss" DECIMAL(20,8) DEFAULT 0,
  "current_positions_risk" DECIMAL(20,8) DEFAULT 0,
  "pending_orders_risk" DECIMAL(20,8) DEFAULT 0,
  "correlation_risk" DECIMAL(20,8) DEFAULT 0,
  
  -- 市场环境
  "market_volatility" DECIMAL(10,4),
  "liquidity_score" DECIMAL(5,2),
  "market_stress_level" VARCHAR(20),
  
  -- 预警信息
  "breach_alerts" JSONB DEFAULT '[]',
  "risk_metrics" JSONB DEFAULT '{}',
  "additional_context" JSONB DEFAULT '{}',
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  
  -- 分区字段
  "usage_date" DATE NOT NULL DEFAULT CURRENT_DATE
) PARTITION BY RANGE ("usage_date");

-- ----------------------------
-- 创建分区表（最近3个月 + 未来1个月）
-- ----------------------------
CREATE TABLE "public"."risk_budget_usage_2024_12" PARTITION OF "public"."risk_budget_usage"
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE "public"."risk_budget_usage_2025_01" PARTITION OF "public"."risk_budget_usage"
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE "public"."risk_budget_usage_2025_02" PARTITION OF "public"."risk_budget_usage"
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE "public"."risk_budget_usage_default" PARTITION OF "public"."risk_budget_usage"
DEFAULT;

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."risk_budget_allocations" IS '优化后的风险预算分配表 - 支持层次化预算管理和自动状态计算';
COMMENT ON TABLE "public"."risk_budget_usage" IS '优化后的风险预算使用记录表 - 按日期分区，包含详细的风险指标';

COMMENT ON COLUMN "public"."risk_budget_allocations"."allocation_type" IS '分配类型：strategy, asset_class, geographic, sector, time_based';
COMMENT ON COLUMN "public"."risk_budget_allocations"."allocation_percentage" IS '分配百分比：自动计算的分配占总预算的比例';
COMMENT ON COLUMN "public"."risk_budget_allocations"."usage_percentage" IS '使用百分比：自动计算的使用占分配的比例';
COMMENT ON COLUMN "public"."risk_budget_allocations"."status" IS '预算状态：自动计算的状态（normal, warning, critical, suspended）';
COMMENT ON COLUMN "public"."risk_budget_allocations"."reset_frequency" IS '重置频率：daily, weekly, monthly, quarterly, yearly';

COMMENT ON COLUMN "public"."risk_budget_usage"."operation_type" IS '操作类型：allocate, release, adjust, reset';
COMMENT ON COLUMN "public"."risk_budget_usage"."amount_change" IS '金额变化：正数表示使用，负数表示释放';

-- ----------------------------
-- 性能优化索引 - risk_budget_allocations
-- ----------------------------

-- 基础查询索引
CREATE INDEX "idx_risk_budget_allocations_type" ON "public"."risk_budget_allocations" ("allocation_type");
CREATE INDEX "idx_risk_budget_allocations_key" ON "public"."risk_budget_allocations" ("allocation_key");
CREATE INDEX "idx_risk_budget_allocations_active" ON "public"."risk_budget_allocations" ("is_active");
CREATE INDEX "idx_risk_budget_allocations_status" ON "public"."risk_budget_allocations" ("status");

-- 层次结构索引
CREATE INDEX "idx_risk_budget_allocations_parent" ON "public"."risk_budget_allocations" ("parent_allocation_id");
CREATE INDEX "idx_risk_budget_allocations_hierarchy" ON "public"."risk_budget_allocations" 
  ("parent_allocation_id", "allocation_type", "is_active");

-- 预算监控索引
CREATE INDEX "idx_risk_budget_allocations_usage" ON "public"."risk_budget_allocations" 
  ("usage_percentage" DESC, "status") WHERE is_active = true;

CREATE INDEX "idx_risk_budget_allocations_critical" ON "public"."risk_budget_allocations" 
  ("status", "usage_percentage" DESC) WHERE status IN ('warning', 'critical', 'suspended');

-- 时间相关索引
CREATE INDEX "idx_risk_budget_allocations_effective" ON "public"."risk_budget_allocations" 
  ("effective_from", "effective_until") WHERE is_active = true;

CREATE INDEX "idx_risk_budget_allocations_reset" ON "public"."risk_budget_allocations" 
  ("next_reset_at") WHERE auto_reset = true AND is_active = true;

-- ----------------------------
-- 性能优化索引 - risk_budget_usage
-- ----------------------------

-- 基础查询索引
CREATE INDEX "idx_risk_budget_usage_allocation" ON "public"."risk_budget_usage" ("allocation_id", "timestamp" DESC);
CREATE INDEX "idx_risk_budget_usage_timestamp" ON "public"."risk_budget_usage" ("timestamp" DESC);
CREATE INDEX "idx_risk_budget_usage_operation" ON "public"."risk_budget_usage" ("operation_type", "timestamp" DESC);

-- 关联查询索引
CREATE INDEX "idx_risk_budget_usage_strategy" ON "public"."risk_budget_usage" 
  ("strategy_id", "timestamp" DESC) WHERE strategy_id IS NOT NULL;

CREATE INDEX "idx_risk_budget_usage_portfolio" ON "public"."risk_budget_usage" 
  ("portfolio_id", "timestamp" DESC) WHERE portfolio_id IS NOT NULL;

CREATE INDEX "idx_risk_budget_usage_trading_pair" ON "public"."risk_budget_usage" 
  ("trading_pair", "timestamp" DESC) WHERE trading_pair IS NOT NULL;

-- 风险分析索引
CREATE INDEX "idx_risk_budget_usage_var" ON "public"."risk_budget_usage" ("var_estimate" DESC, "timestamp" DESC);
CREATE INDEX "idx_risk_budget_usage_stress" ON "public"."risk_budget_usage" ("stress_test_loss" DESC, "timestamp" DESC);

-- JSONB 字段索引
CREATE INDEX "idx_risk_budget_usage_alerts_gin" ON "public"."risk_budget_usage" USING gin("breach_alerts");
CREATE INDEX "idx_risk_budget_usage_metrics_gin" ON "public"."risk_budget_usage" USING gin("risk_metrics");

-- ----------------------------
-- 唯一约束
-- ----------------------------

-- 确保分配类型和键值的唯一性
CREATE UNIQUE INDEX "idx_risk_budget_allocations_unique" ON "public"."risk_budget_allocations" 
  ("allocation_type", "allocation_key") WHERE is_active = true;

-- ----------------------------
-- 数据完整性约束 - risk_budget_allocations
-- ----------------------------

-- 金额约束
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_budget_amounts_positive" 
  CHECK (total_budget > 0 AND allocated_amount > 0);

ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_allocation_within_total" 
  CHECK (allocated_amount <= total_budget);

ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_min_max_allocation" 
  CHECK (min_allocation >= 0 AND (max_allocation IS NULL OR max_allocation >= min_allocation));

ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_allocation_within_limits" 
  CHECK (
    allocated_amount >= min_allocation AND 
    (max_allocation IS NULL OR allocated_amount <= max_allocation)
  );

-- 阈值约束
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_threshold_ranges" 
  CHECK (
    warning_threshold >= 0 AND warning_threshold <= 100 AND
    critical_threshold >= 0 AND critical_threshold <= 100 AND
    warning_threshold <= critical_threshold
  );

-- 透支约束
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_overdraft_limit" 
  CHECK (overdraft_limit >= 0);

-- 时间约束
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_effective_period" 
  CHECK (effective_until IS NULL OR effective_until > effective_from);

-- 重置频率约束
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_reset_frequency" 
  CHECK (reset_frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly'));

-- ----------------------------
-- 数据完整性约束 - risk_budget_usage
-- ----------------------------

-- 使用量约束
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_usage_amounts_consistency" 
  CHECK (new_usage = previous_usage + amount_change);

ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_usage_amounts_non_negative" 
  CHECK (previous_usage >= 0 AND new_usage >= 0);

-- 风险指标约束
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_risk_metrics_non_negative" 
  CHECK (
    var_estimate >= 0 AND stress_test_loss >= 0 AND 
    current_positions_risk >= 0 AND pending_orders_risk >= 0 AND 
    correlation_risk >= 0
  );

-- 市场指标约束
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_market_metrics_ranges" 
  CHECK (
    (market_volatility IS NULL OR market_volatility >= 0) AND
    (liquidity_score IS NULL OR (liquidity_score >= 0 AND liquidity_score <= 100))
  );

-- 操作类型约束
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_operation_type" 
  CHECK (operation_type IN ('allocate', 'release', 'adjust', 'reset'));

-- ----------------------------
-- 外键约束
-- ----------------------------
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "fk_risk_budget_usage_strategy" 
  FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE SET NULL;

-- 注意：如果 portfolios 表存在，取消注释下面的约束
-- ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "fk_risk_budget_usage_portfolio" 
--   FOREIGN KEY ("portfolio_id") REFERENCES "public"."portfolios" ("id") ON DELETE SET NULL;

-- ----------------------------
-- 触发器：自动更新字段
-- ----------------------------
CREATE OR REPLACE FUNCTION update_risk_budget_allocations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_budget_allocations_updated_at
    BEFORE UPDATE ON "public"."risk_budget_allocations"
    FOR EACH ROW
    EXECUTE FUNCTION update_risk_budget_allocations_updated_at();

-- ----------------------------
-- 触发器：自动设置分区字段
-- ----------------------------
CREATE OR REPLACE FUNCTION set_risk_budget_usage_partition_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.usage_date = DATE(NEW.timestamp);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_budget_usage_partition_date
    BEFORE INSERT ON "public"."risk_budget_usage"
    FOR EACH ROW
    EXECUTE FUNCTION set_risk_budget_usage_partition_date();

-- ----------------------------
-- 权限设置
-- ----------------------------
ALTER TABLE "public"."risk_budget_allocations" OWNER TO "neondb_owner";
ALTER TABLE "public"."risk_budget_usage" OWNER TO "neondb_owner";
