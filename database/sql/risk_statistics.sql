/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:42
*/


-- ----------------------------
-- Table structure for risk_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_statistics";
CREATE TABLE "public"."risk_statistics" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "date" date NOT NULL,
  "total_checks" int4 NOT NULL DEFAULT 0,
  "passed_checks" int4 NOT NULL DEFAULT 0,
  "failed_checks" int4 NOT NULL DEFAULT 0,
  "pass_rate" numeric(10,4) NOT NULL DEFAULT 0,
  "average_risk_score" numeric(10,4) NOT NULL DEFAULT 0,
  "top_violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "risk_score_distribution" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."risk_statistics" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_statistics
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Uniques structure for table risk_statistics
-- ----------------------------
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "risk_statistics_date_key" UNIQUE ("date");

-- ----------------------------
-- Primary Key structure for table risk_statistics
-- ----------------------------
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "risk_statistics_pkey" PRIMARY KEY ("id");
