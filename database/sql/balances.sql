/*
 Navicat Premium Data Transfer

 Source Server         : SigmaX Database
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : *************:55432
 Source Catalog        : mx
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 03/07/2025 15:30:00
*/


-- ----------------------------
-- Table structure for balances
-- ----------------------------
DROP TABLE IF EXISTS "public"."balances";
CREATE TABLE "public"."balances" (
  "exchange_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "asset" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "free" numeric(20,8) NOT NULL DEFAULT 0,
  "locked" numeric(20,8) NOT NULL DEFAULT 0,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);
ALTER TABLE "public"."balances" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of balances
-- ----------------------------
BEGIN;
-- 示例数据：模拟器交易所的初始余额
INSERT INTO "public"."balances" ("exchange_id", "asset", "free", "locked", "updated_at") VALUES ('simulator', 'USDT', 100000.00000000, 0.00000000, now());
INSERT INTO "public"."balances" ("exchange_id", "asset", "free", "locked", "updated_at") VALUES ('simulator', 'BTC', 0.00000000, 0.00000000, now());
INSERT INTO "public"."balances" ("exchange_id", "asset", "free", "locked", "updated_at") VALUES ('simulator', 'ETH', 0.00000000, 0.00000000, now());
COMMIT;

-- ----------------------------
-- Indexes structure for table balances
-- ----------------------------
CREATE INDEX "idx_balances_exchange_id" ON "public"."balances" USING btree (
  "exchange_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_balances_asset" ON "public"."balances" USING btree (
  "asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_balances_updated_at" ON "public"."balances" USING btree (
  "updated_at" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table balances
-- ----------------------------
ALTER TABLE "public"."balances" ADD CONSTRAINT "balances_pkey" PRIMARY KEY ("exchange_id", "asset");

-- ----------------------------
-- Check constraints for table balances
-- ----------------------------
ALTER TABLE "public"."balances" ADD CONSTRAINT "check_balances_free_non_negative" CHECK (free >= 0);
ALTER TABLE "public"."balances" ADD CONSTRAINT "check_balances_locked_non_negative" CHECK (locked >= 0);

-- ----------------------------
-- Comments for table balances
-- ----------------------------
COMMENT ON TABLE "public"."balances" IS '账户余额表，存储各交易所的资产余额信息';
COMMENT ON COLUMN "public"."balances"."exchange_id" IS '交易所ID';
COMMENT ON COLUMN "public"."balances"."asset" IS '资产名称（如BTC、ETH、USDT）';
COMMENT ON COLUMN "public"."balances"."free" IS '可用余额';
COMMENT ON COLUMN "public"."balances"."locked" IS '锁定余额（挂单中）';
COMMENT ON COLUMN "public"."balances"."updated_at" IS '最后更新时间';
