/*
 优化后的风控检查表
 
 主要改进：
 1. 使用 ENUM 类型替代 VARCHAR
 2. 添加分区策略（按日期分区）
 3. 完善索引和约束
 4. 优化 JSONB 字段
 5. 添加外键关系
*/

-- ----------------------------
-- 创建 ENUM 类型
-- ----------------------------
DO $$ BEGIN
    CREATE TYPE order_side AS ENUM ('buy', 'sell');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE order_type AS ENUM (
        'market',          -- 市价单
        'limit',           -- 限价单
        'stop',            -- 止损单
        'stop_limit',      -- 止损限价单
        'take_profit',     -- 止盈单
        'take_profit_limit' -- 止盈限价单
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE check_result AS ENUM ('passed', 'failed', 'warning', 'error');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ----------------------------
-- 删除旧表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks" CASCADE;

-- ----------------------------
-- 优化后的风控检查表（分区表）
-- ----------------------------
CREATE TABLE "public"."risk_checks" (
  -- 基础字段
  "id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- 订单信息
  "trading_pair" VARCHAR(50) NOT NULL,
  "side" order_side NOT NULL,
  "quantity" DECIMAL(20,8) NOT NULL,
  "price" DECIMAL(20,8),
  "order_type" order_type NOT NULL,
  "order_value" DECIMAL(20,8) GENERATED ALWAYS AS (quantity * COALESCE(price, 0)) STORED,
  
  -- 检查结果
  "result" check_result NOT NULL,
  "risk_score" DECIMAL(5,2) NOT NULL DEFAULT 0,
  "confidence_level" DECIMAL(5,2) DEFAULT 95.0,
  
  -- 详细信息
  "violations" JSONB NOT NULL DEFAULT '[]',
  "warnings" JSONB NOT NULL DEFAULT '[]',
  "recommendations" JSONB NOT NULL DEFAULT '[]',
  "applied_rules" JSONB NOT NULL DEFAULT '[]',
  
  -- 建议值
  "max_allowed_quantity" DECIMAL(20,8),
  "suggested_price_min" DECIMAL(20,8),
  "suggested_price_max" DECIMAL(20,8),
  "alternative_suggestions" JSONB DEFAULT '[]',
  
  -- 性能指标
  "processing_time_ms" INTEGER,
  "rules_evaluated" INTEGER DEFAULT 0,
  "cache_hit_rate" DECIMAL(5,2) DEFAULT 0,
  
  -- 关联信息
  "strategy_id" UUID,
  "portfolio_id" UUID,
  "engine_id" UUID,
  "session_id" VARCHAR(100),
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  
  -- 分区字段
  "check_date" DATE NOT NULL DEFAULT CURRENT_DATE,
  
  -- 主键包含分区字段
  PRIMARY KEY ("id", "check_date")
) PARTITION BY RANGE ("check_date");

-- ----------------------------
-- 创建分区表（最近3个月 + 未来1个月）
-- ----------------------------

-- 当前月份分区
CREATE TABLE "public"."risk_checks_2024_12" PARTITION OF "public"."risk_checks"
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- 下个月分区
CREATE TABLE "public"."risk_checks_2025_01" PARTITION OF "public"."risk_checks"
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 未来月份分区
CREATE TABLE "public"."risk_checks_2025_02" PARTITION OF "public"."risk_checks"
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 默认分区（处理未来日期）
CREATE TABLE "public"."risk_checks_default" PARTITION OF "public"."risk_checks"
DEFAULT;

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."risk_checks" IS '优化后的风控检查表 - 按日期分区，包含完整的检查结果和性能指标';
COMMENT ON COLUMN "public"."risk_checks"."result" IS '检查结果：passed, failed, warning, error';
COMMENT ON COLUMN "public"."risk_checks"."risk_score" IS '风险评分：0-100，数值越高风险越大';
COMMENT ON COLUMN "public"."risk_checks"."confidence_level" IS '置信度：检查结果的可信度百分比';
COMMENT ON COLUMN "public"."risk_checks"."order_value" IS '订单价值：自动计算的订单总价值';
COMMENT ON COLUMN "public"."risk_checks"."applied_rules" IS '应用的规则列表：记录哪些规则参与了检查';
COMMENT ON COLUMN "public"."risk_checks"."cache_hit_rate" IS '缓存命中率：本次检查的缓存使用效率';

-- ----------------------------
-- 性能优化索引（在主表上创建）
-- ----------------------------

-- 时间相关索引
CREATE INDEX "idx_risk_checks_timestamp" ON "public"."risk_checks" ("timestamp");
CREATE INDEX "idx_risk_checks_date" ON "public"."risk_checks" ("check_date");

-- 查询优化索引
CREATE INDEX "idx_risk_checks_trading_pair" ON "public"."risk_checks" ("trading_pair");
CREATE INDEX "idx_risk_checks_result" ON "public"."risk_checks" ("result");
CREATE INDEX "idx_risk_checks_strategy" ON "public"."risk_checks" ("strategy_id");

-- 复合索引
CREATE INDEX "idx_risk_checks_pair_result_time" ON "public"."risk_checks" 
  ("trading_pair", "result", "timestamp" DESC);

CREATE INDEX "idx_risk_checks_strategy_time" ON "public"."risk_checks" 
  ("strategy_id", "timestamp" DESC) WHERE strategy_id IS NOT NULL;

CREATE INDEX "idx_risk_checks_failed_recent" ON "public"."risk_checks" 
  ("result", "timestamp" DESC) WHERE result = 'failed';

-- 风险分析索引
CREATE INDEX "idx_risk_checks_risk_score" ON "public"."risk_checks" ("risk_score" DESC);
CREATE INDEX "idx_risk_checks_high_risk" ON "public"."risk_checks" 
  ("risk_score", "timestamp" DESC) WHERE risk_score >= 70;

-- JSONB 字段索引
CREATE INDEX "idx_risk_checks_violations_gin" ON "public"."risk_checks" USING gin("violations");
CREATE INDEX "idx_risk_checks_warnings_gin" ON "public"."risk_checks" USING gin("warnings");
CREATE INDEX "idx_risk_checks_applied_rules_gin" ON "public"."risk_checks" USING gin("applied_rules");

-- 性能分析索引
CREATE INDEX "idx_risk_checks_processing_time" ON "public"."risk_checks" ("processing_time_ms");
CREATE INDEX "idx_risk_checks_slow_queries" ON "public"."risk_checks" 
  ("processing_time_ms", "timestamp" DESC) WHERE processing_time_ms > 1000;

-- ----------------------------
-- 数据完整性约束
-- ----------------------------

-- 数值范围约束
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_quantity_positive" 
  CHECK (quantity > 0);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_price_positive" 
  CHECK (price IS NULL OR price > 0);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_risk_score_range" 
  CHECK (risk_score >= 0 AND risk_score <= 100);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_confidence_level_range" 
  CHECK (confidence_level >= 0 AND confidence_level <= 100);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_processing_time_positive" 
  CHECK (processing_time_ms >= 0);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_rules_evaluated_positive" 
  CHECK (rules_evaluated >= 0);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_cache_hit_rate_range" 
  CHECK (cache_hit_rate >= 0 AND cache_hit_rate <= 100);

-- 建议值约束
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_max_allowed_quantity_positive" 
  CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_suggested_prices_positive" 
  CHECK (
    (suggested_price_min IS NULL OR suggested_price_min > 0) AND
    (suggested_price_max IS NULL OR suggested_price_max > 0) AND
    (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max)
  );

-- 逻辑约束
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_market_order_no_price" 
  CHECK (order_type != 'market' OR price IS NULL);

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_limit_order_has_price" 
  CHECK (order_type = 'market' OR price IS NOT NULL);

-- ----------------------------
-- 外键约束
-- ----------------------------
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "fk_risk_checks_strategy" 
  FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE SET NULL;

-- 注意：如果 portfolios 表存在，取消注释下面的约束
-- ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "fk_risk_checks_portfolio" 
--   FOREIGN KEY ("portfolio_id") REFERENCES "public"."portfolios" ("id") ON DELETE SET NULL;

-- ----------------------------
-- 触发器：自动设置分区字段
-- ----------------------------
CREATE OR REPLACE FUNCTION set_risk_checks_partition_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.check_date = DATE(NEW.timestamp);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_checks_partition_date
    BEFORE INSERT ON "public"."risk_checks"
    FOR EACH ROW
    EXECUTE FUNCTION set_risk_checks_partition_date();

-- ----------------------------
-- 分区管理函数
-- ----------------------------
CREATE OR REPLACE FUNCTION create_monthly_risk_checks_partition(partition_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- 计算分区名称和日期范围
    partition_name := 'risk_checks_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := DATE_TRUNC('month', partition_date);
    end_date := start_date + INTERVAL '1 month';
    
    -- 创建分区表
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF risk_checks FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    -- 创建分区特定的索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (timestamp DESC)',
                   'idx_' || partition_name || '_timestamp', partition_name);
                   
    EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (trading_pair, result)',
                   'idx_' || partition_name || '_pair_result', partition_name);
END;
$$ LANGUAGE plpgsql;

-- ----------------------------
-- 权限设置
-- ----------------------------
ALTER TABLE "public"."risk_checks" OWNER TO "neondb_owner";
