/*
 Portfolio Snapshots Table
 用于存储投资组合的历史快照数据，支持回撤计算和净资产历史跟踪
*/

-- ----------------------------
-- Table structure for portfolio_snapshots
-- ----------------------------
DROP TABLE IF EXISTS "public"."portfolio_snapshots";
CREATE TABLE "public"."portfolio_snapshots" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "portfolio_id" uuid NOT NULL,
  "strategy_id" uuid,
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "total_value" numeric(20,8) NOT NULL,
  "cash_balance" numeric(20,8) NOT NULL DEFAULT 0,
  "position_value" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "drawdown_from_peak" numeric(10,6) DEFAULT 0,
  "peak_value" numeric(20,8) NOT NULL DEFAULT 0,
  "positions" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "balances" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "risk_metrics" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) DEFAULT now()
);
ALTER TABLE "public"."portfolio_snapshots" OWNER TO "neondb_owner";

-- ----------------------------
-- Indexes structure for table portfolio_snapshots
-- ----------------------------
CREATE INDEX "idx_portfolio_snapshots_portfolio_id" ON "public"."portfolio_snapshots" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

CREATE INDEX "idx_portfolio_snapshots_strategy_id" ON "public"."portfolio_snapshots" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

CREATE INDEX "idx_portfolio_snapshots_timestamp" ON "public"."portfolio_snapshots" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

CREATE INDEX "idx_portfolio_snapshots_portfolio_timestamp" ON "public"."portfolio_snapshots" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table portfolio_snapshots
-- ----------------------------
ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "portfolio_snapshots_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table portfolio_snapshots
-- ----------------------------
ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "portfolio_snapshots_portfolio_id_fkey" 
  FOREIGN KEY ("portfolio_id") REFERENCES "public"."portfolios" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "portfolio_snapshots_strategy_id_fkey" 
  FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------
-- Check constraints for table portfolio_snapshots
-- ----------------------------
ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "check_portfolio_snapshots_total_value_non_negative" 
  CHECK (total_value >= 0);

ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "check_portfolio_snapshots_peak_value_non_negative" 
  CHECK (peak_value >= 0);

ALTER TABLE "public"."portfolio_snapshots" ADD CONSTRAINT "check_portfolio_snapshots_drawdown_valid" 
  CHECK (drawdown_from_peak >= 0 AND drawdown_from_peak <= 1);

-- ----------------------------
-- Comments for table portfolio_snapshots
-- ----------------------------
COMMENT ON TABLE "public"."portfolio_snapshots" IS '投资组合快照表，存储投资组合的历史状态数据，用于回撤计算和风险分析';
COMMENT ON COLUMN "public"."portfolio_snapshots"."portfolio_id" IS '投资组合ID';
COMMENT ON COLUMN "public"."portfolio_snapshots"."strategy_id" IS '策略ID（可选）';
COMMENT ON COLUMN "public"."portfolio_snapshots"."timestamp" IS '快照时间戳';
COMMENT ON COLUMN "public"."portfolio_snapshots"."total_value" IS '总价值（现金+持仓价值）';
COMMENT ON COLUMN "public"."portfolio_snapshots"."cash_balance" IS '现金余额';
COMMENT ON COLUMN "public"."portfolio_snapshots"."position_value" IS '持仓价值';
COMMENT ON COLUMN "public"."portfolio_snapshots"."unrealized_pnl" IS '未实现盈亏';
COMMENT ON COLUMN "public"."portfolio_snapshots"."realized_pnl" IS '已实现盈亏';
COMMENT ON COLUMN "public"."portfolio_snapshots"."total_fees" IS '累计手续费';
COMMENT ON COLUMN "public"."portfolio_snapshots"."drawdown_from_peak" IS '从峰值的回撤比例（0-1）';
COMMENT ON COLUMN "public"."portfolio_snapshots"."peak_value" IS '历史峰值';
COMMENT ON COLUMN "public"."portfolio_snapshots"."positions" IS '持仓详情（JSON格式）';
COMMENT ON COLUMN "public"."portfolio_snapshots"."balances" IS '余额详情（JSON格式）';
COMMENT ON COLUMN "public"."portfolio_snapshots"."risk_metrics" IS '风险指标（JSON格式）';
