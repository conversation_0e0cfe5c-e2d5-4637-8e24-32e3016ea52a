/*
 Navicat Premium Data Transfer

 Source Server         : SigmaX Risk Config
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 30/06/2025 12:00:00
*/


-- ----------------------------
-- Table structure for risk_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_config";
CREATE TABLE "public"."risk_config" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "strategy_type" varchar(100) COLLATE "pg_catalog"."default",
  "enabled" bool NOT NULL DEFAULT true,
  "risk_parameters" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."risk_config" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_config - 插入默认配置
-- ----------------------------
BEGIN;

-- 全局默认风险配置
INSERT INTO "public"."risk_config" ("id", "name", "description", "strategy_type", "enabled", "risk_parameters", "created_at", "updated_at", "created_by") VALUES 
('00000000-0000-0000-0000-000000000001', '全局默认风险配置', '系统默认的全局风险管理配置', NULL, true, '{
  "basic": {
    "max_drawdown_percent": "20",
    "max_daily_loss_percent": "5",
    "max_portfolio_risk_percent": "10",
    "position_size_limit_percent": "25",
    "stop_loss_percent": "2",
    "max_order_amount": "10000",
    "max_total_position": "100000"
  },
  "position": {
    "max_position_per_symbol": "20",
    "max_leverage": "3",
    "concentration_limit": "30",
    "correlation_limit": "70",
    "sector_concentration_limit": "40"
  },
  "trading": {
    "max_daily_trades": 100,
    "max_hourly_trades": 10,
    "min_order_interval": 60,
    "max_slippage_tolerance": "0.01",
    "max_price_deviation": "0.02"
  },
  "time_based": {
    "trading_hours": [
      {"day_of_week": 1, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 2, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 3, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 4, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 5, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 6, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 0, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"}
    ],
    "blackout_dates": [],
    "market_close_behavior": "Continue",
    "timeout_settings": {
      "order_timeout": 3600,
      "connection_timeout": 30,
      "response_timeout": 10
    }
  },
  "market": {
    "volatility_threshold": "30",
    "min_liquidity_requirement": "10000",
    "price_impact_limit": "0.01",
    "correlation_limit": "70",
    "var_limit": "5000",
    "confidence_level": "0.95"
  },
  "advanced": {
    "dynamic_adjustment": {
      "enabled": false,
      "adjustment_frequency": 60,
      "volatility_factor": "0.1",
      "liquidity_factor": "0.1",
      "sentiment_factor": "0.05"
    },
    "stress_test": {
      "enabled": false,
      "test_frequency": 24,
      "extreme_price_move": "20",
      "liquidity_shock": "50",
      "correlation_shock": "80"
    },
    "risk_budget": {
      "total_risk_budget": "100000",
      "strategy_allocation": {},
      "asset_class_allocation": {},
      "geographic_allocation": {}
    },
    "emergency_measures": {
      "emergency_stop_threshold": "50",
      "auto_reduce_threshold": "30",
      "notification_threshold": "20",
      "emergency_contacts": [],
      "emergency_actions": []
    }
  }
}', now(), now(), 'system');

-- 网格策略专用风险配置
INSERT INTO "public"."risk_config" ("id", "name", "description", "strategy_type", "enabled", "risk_parameters", "created_at", "updated_at", "created_by") VALUES 
('00000000-0000-0000-0000-000000000002', '网格策略风险配置', '专门为网格交易策略设计的风险配置', 'grid_trading', true, '{
  "basic": {
    "max_drawdown_percent": "15",
    "max_daily_loss_percent": "3",
    "max_portfolio_risk_percent": "8",
    "position_size_limit_percent": "20",
    "stop_loss_percent": "1.5",
    "max_order_amount": "5000",
    "max_total_position": "50000"
  },
  "position": {
    "max_position_per_symbol": "15",
    "max_leverage": "2",
    "concentration_limit": "25",
    "correlation_limit": "60",
    "sector_concentration_limit": "30"
  },
  "trading": {
    "max_daily_trades": 200,
    "max_hourly_trades": 20,
    "min_order_interval": 30,
    "max_slippage_tolerance": "0.005",
    "max_price_deviation": "0.01"
  },
  "time_based": {
    "trading_hours": [
      {"day_of_week": 1, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 2, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 3, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 4, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 5, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 6, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 0, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"}
    ],
    "blackout_dates": [],
    "market_close_behavior": "Continue",
    "timeout_settings": {
      "order_timeout": 1800,
      "connection_timeout": 20,
      "response_timeout": 5
    }
  },
  "market": {
    "volatility_threshold": "25",
    "min_liquidity_requirement": "5000",
    "price_impact_limit": "0.005",
    "correlation_limit": "60",
    "var_limit": "3000",
    "confidence_level": "0.95"
  },
  "advanced": {
    "dynamic_adjustment": {
      "enabled": true,
      "adjustment_frequency": 30,
      "volatility_factor": "0.15",
      "liquidity_factor": "0.1",
      "sentiment_factor": "0.05"
    },
    "stress_test": {
      "enabled": true,
      "test_frequency": 12,
      "extreme_price_move": "15",
      "liquidity_shock": "40",
      "correlation_shock": "70"
    },
    "risk_budget": {
      "total_risk_budget": "50000",
      "strategy_allocation": {"grid_trading": "100"},
      "asset_class_allocation": {},
      "geographic_allocation": {}
    },
    "emergency_measures": {
      "emergency_stop_threshold": "40",
      "auto_reduce_threshold": "25",
      "notification_threshold": "15",
      "emergency_contacts": [],
      "emergency_actions": []
    }
  }
}', now(), now(), 'system');

COMMIT;

-- ----------------------------
-- Indexes structure for table risk_config
-- ----------------------------
CREATE INDEX "idx_risk_config_name" ON "public"."risk_config" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_config_strategy_type" ON "public"."risk_config" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_config_enabled" ON "public"."risk_config" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_config_parameters_gin" ON "public"."risk_config" USING gin (
  "risk_parameters" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Primary Key structure for table risk_config
-- ----------------------------
ALTER TABLE "public"."risk_config" ADD CONSTRAINT "risk_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table risk_config
-- ----------------------------
ALTER TABLE "public"."risk_config" ADD CONSTRAINT "risk_config_name_strategy_type_key" UNIQUE ("name", "strategy_type");

-- ----------------------------
-- Comments for table risk_config
-- ----------------------------
COMMENT ON TABLE "public"."risk_config" IS '风险配置表 - 存储完整的风险管理配置';
COMMENT ON COLUMN "public"."risk_config"."id" IS '配置唯一标识';
COMMENT ON COLUMN "public"."risk_config"."name" IS '配置名称';
COMMENT ON COLUMN "public"."risk_config"."description" IS '配置描述';
COMMENT ON COLUMN "public"."risk_config"."strategy_type" IS '策略类型，NULL表示全局配置';
COMMENT ON COLUMN "public"."risk_config"."enabled" IS '是否启用';
COMMENT ON COLUMN "public"."risk_config"."risk_parameters" IS '完整的风险参数JSON配置';
COMMENT ON COLUMN "public"."risk_config"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."risk_config"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."risk_config"."created_by" IS '创建者';
