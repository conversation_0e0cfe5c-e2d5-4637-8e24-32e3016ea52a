/*
 Risk Budget Usage Table
 用于存储风险预算使用情况的历史记录，支持风险预算跟踪和分析
*/

-- ----------------------------
-- Table structure for risk_budget_usage
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_budget_usage";
CREATE TABLE "public"."risk_budget_usage" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "strategy_type" varchar(100) COLLATE "pg_catalog"."default",
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "total_risk_budget" numeric(20,8) NOT NULL,
  "used_risk_budget" numeric(20,8) NOT NULL DEFAULT 0,
  "available_risk_budget" numeric(20,8) NOT NULL DEFAULT 0,
  "usage_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "strategy_allocation" jsonb DEFAULT '{}'::jsonb,
  "asset_class_allocation" jsonb DEFAULT '{}'::jsonb,
  "geographic_allocation" jsonb DEFAULT '{}'::jsonb,
  "current_positions_risk" numeric(20,8) DEFAULT 0,
  "pending_orders_risk" numeric(20,8) DEFAULT 0,
  "var_estimate" numeric(20,8) DEFAULT 0,
  "stress_test_loss" numeric(20,8) DEFAULT 0,
  "risk_metrics" jsonb DEFAULT '{}'::jsonb,
  "breach_alerts" jsonb DEFAULT '[]'::jsonb,
  "created_at" timestamptz(6) DEFAULT now()
);
ALTER TABLE "public"."risk_budget_usage" OWNER TO "neondb_owner";

-- ----------------------------
-- Table structure for risk_budget_allocations
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_budget_allocations";
CREATE TABLE "public"."risk_budget_allocations" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "total_budget" numeric(20,8) NOT NULL,
  "allocation_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "allocation_key" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "allocated_amount" numeric(20,8) NOT NULL,
  "allocation_percentage" numeric(10,4) NOT NULL,
  "min_allocation" numeric(20,8) DEFAULT 0,
  "max_allocation" numeric(20,8),
  "current_usage" numeric(20,8) DEFAULT 0,
  "usage_percentage" numeric(10,4) DEFAULT 0,
  "is_active" bool NOT NULL DEFAULT true,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
);
ALTER TABLE "public"."risk_budget_allocations" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_budget_allocations
-- ----------------------------
BEGIN;
-- 示例风险预算分配
INSERT INTO "public"."risk_budget_allocations" ("name", "description", "total_budget", "allocation_type", "allocation_key", "allocated_amount", "allocation_percentage") 
VALUES ('Grid Trading Strategy', '网格交易策略风险预算', 100000.00000000, 'strategy', 'grid_trading', 50000.00000000, 50.0000);

INSERT INTO "public"."risk_budget_allocations" ("name", "description", "total_budget", "allocation_type", "allocation_key", "allocated_amount", "allocation_percentage") 
VALUES ('Crypto Assets', '加密货币资产类别风险预算', 100000.00000000, 'asset_class', 'crypto', 80000.00000000, 80.0000);

INSERT INTO "public"."risk_budget_allocations" ("name", "description", "total_budget", "allocation_type", "allocation_key", "allocated_amount", "allocation_percentage") 
VALUES ('Global Markets', '全球市场地理分配', 100000.00000000, 'geographic', 'global', 100000.00000000, 100.0000);
COMMIT;

-- ----------------------------
-- Indexes structure for table risk_budget_usage
-- ----------------------------
CREATE INDEX "idx_risk_budget_usage_timestamp" ON "public"."risk_budget_usage" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

CREATE INDEX "idx_risk_budget_usage_strategy_type" ON "public"."risk_budget_usage" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_risk_budget_usage_strategy_id" ON "public"."risk_budget_usage" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

CREATE INDEX "idx_risk_budget_usage_portfolio_id" ON "public"."risk_budget_usage" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Indexes structure for table risk_budget_allocations
-- ----------------------------
CREATE INDEX "idx_risk_budget_allocations_allocation_type" ON "public"."risk_budget_allocations" USING btree (
  "allocation_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_risk_budget_allocations_allocation_key" ON "public"."risk_budget_allocations" USING btree (
  "allocation_key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_risk_budget_allocations_active" ON "public"."risk_budget_allocations" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for tables
-- ----------------------------
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "risk_budget_usage_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "risk_budget_allocations_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table risk_budget_usage
-- ----------------------------
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "risk_budget_usage_strategy_id_fkey" 
  FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE SET NULL ON UPDATE NO ACTION;

ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "risk_budget_usage_portfolio_id_fkey" 
  FOREIGN KEY ("portfolio_id") REFERENCES "public"."portfolios" ("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------
-- Check constraints
-- ----------------------------
ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_risk_budget_usage_amounts_non_negative" 
  CHECK (total_risk_budget >= 0 AND used_risk_budget >= 0 AND available_risk_budget >= 0);

ALTER TABLE "public"."risk_budget_usage" ADD CONSTRAINT "check_risk_budget_usage_percentage_valid" 
  CHECK (usage_percentage >= 0 AND usage_percentage <= 100);

ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_risk_budget_allocations_amounts_valid" 
  CHECK (total_budget >= 0 AND allocated_amount >= 0 AND allocated_amount <= total_budget);

ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "check_risk_budget_allocations_percentage_valid" 
  CHECK (allocation_percentage >= 0 AND allocation_percentage <= 100 AND usage_percentage >= 0 AND usage_percentage <= 100);

-- ----------------------------
-- Unique constraints
-- ----------------------------
ALTER TABLE "public"."risk_budget_allocations" ADD CONSTRAINT "risk_budget_allocations_unique" 
  UNIQUE ("allocation_type", "allocation_key");

-- ----------------------------
-- Comments for tables
-- ----------------------------
COMMENT ON TABLE "public"."risk_budget_usage" IS '风险预算使用记录表，存储风险预算的历史使用情况';
COMMENT ON TABLE "public"."risk_budget_allocations" IS '风险预算分配表，存储风险预算的分配配置';

COMMENT ON COLUMN "public"."risk_budget_usage"."strategy_type" IS '策略类型';
COMMENT ON COLUMN "public"."risk_budget_usage"."total_risk_budget" IS '总风险预算';
COMMENT ON COLUMN "public"."risk_budget_usage"."used_risk_budget" IS '已使用风险预算';
COMMENT ON COLUMN "public"."risk_budget_usage"."available_risk_budget" IS '可用风险预算';
COMMENT ON COLUMN "public"."risk_budget_usage"."usage_percentage" IS '使用百分比';
COMMENT ON COLUMN "public"."risk_budget_usage"."current_positions_risk" IS '当前持仓风险';
COMMENT ON COLUMN "public"."risk_budget_usage"."pending_orders_risk" IS '挂单风险';
COMMENT ON COLUMN "public"."risk_budget_usage"."var_estimate" IS 'VaR估计值';
COMMENT ON COLUMN "public"."risk_budget_usage"."stress_test_loss" IS '压力测试损失';

COMMENT ON COLUMN "public"."risk_budget_allocations"."allocation_type" IS '分配类型（strategy, asset_class, geographic）';
COMMENT ON COLUMN "public"."risk_budget_allocations"."allocation_key" IS '分配键值';
COMMENT ON COLUMN "public"."risk_budget_allocations"."allocated_amount" IS '分配金额';
COMMENT ON COLUMN "public"."risk_budget_allocations"."allocation_percentage" IS '分配百分比';
COMMENT ON COLUMN "public"."risk_budget_allocations"."current_usage" IS '当前使用量';
COMMENT ON COLUMN "public"."risk_budget_allocations"."usage_percentage" IS '使用百分比';
