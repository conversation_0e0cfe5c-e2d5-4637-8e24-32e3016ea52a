/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:36
*/


-- ----------------------------
-- Table structure for risk_rules
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_rules";
CREATE TABLE "public"."risk_rules" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "rule_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "parameters" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "enabled" bool NOT NULL DEFAULT true,
  "priority" int4 NOT NULL DEFAULT 100,
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."risk_rules" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_rules
-- ----------------------------
BEGIN;
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('60751436-1b43-4773-8ee2-9d14e3fc8b35', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 08:58:53.638576+00', '2025-06-10 08:58:53.638576+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('a4d31e23-fd66-44b8-b591-00ab2769681b', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:01:41.706399+00', '2025-06-10 09:01:41.706399+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:03:42.115619+00', '2025-06-10 09:03:42.115619+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('f3701c22-d50d-437f-a481-cf294ef01d67', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:07:07.588823+00', '2025-06-10 09:07:07.588823+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('ba178e3e-32ed-43c8-b858-813ddfe95640', 'Test Max Order Size', 'max_order_size', '测试最大订单大小限制', '{"currency": "USDT", "max_size": "500.00"}', 't', 95, NULL, '2025-06-10 09:07:46.21853+00', '2025-06-10 09:07:46.21853+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('1a510ecf-0be9-4b73-a758-d5b9115a0e47', '最大订单金额限制', 'order_size_limit', NULL, '{"currency": "USDT", "max_order_amount": 10000}', 't', 1, NULL, '2025-06-13 18:06:05.364539+00', '2025-06-13 18:06:05.364539+00');
INSERT INTO "public"."risk_rules" ("id", "name", "rule_type", "description", "parameters", "enabled", "priority", "created_by", "created_at", "updated_at") VALUES ('48aca072-0122-4a54-9e2c-970c04080996', '最大订单金额限制', 'order_size_limit', NULL, '{"currency": "USDT", "max_order_amount": 10000}', 't', 1, NULL, '2025-06-13 18:08:28.770397+00', '2025-06-13 18:08:28.770397+00');
COMMIT;

-- ----------------------------
-- Primary Key structure for table risk_rules
-- ----------------------------
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "risk_rules_pkey" PRIMARY KEY ("id");
