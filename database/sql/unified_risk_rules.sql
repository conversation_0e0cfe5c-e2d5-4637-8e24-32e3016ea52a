/*
 统一风控规则引擎 - 数据库Schema设计
 
 设计理念：
 1. 一切皆规则：消除配置与规则的概念重复
 2. 参数化规则：规则包含自己的参数，无需外部配置
 3. 优先级驱动：通过优先级控制执行顺序
 4. 简单开关：通过enabled字段控制规则启用
*/

-- ----------------------------
-- 删除旧表（迁移时使用）
-- ----------------------------
-- DROP TABLE IF EXISTS "public"."risk_config";
-- DROP TABLE IF EXISTS "public"."risk_rules";

-- ----------------------------
-- 统一风控规则表
-- ----------------------------
DROP TABLE IF EXISTS "public"."unified_risk_rules";
CREATE TABLE "public"."unified_risk_rules" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  
  -- 规则分类和类型
  "category" VARCHAR(50) NOT NULL,           -- basic, position, trading, market, time_based, advanced
  "rule_type" VARCHAR(100) NOT NULL,         -- order_size_limit, trading_frequency, time_window等
  
  -- 规则配置
  "parameters" JSONB NOT NULL DEFAULT '{}',  -- 规则的所有参数和阈值
  "conditions" JSONB DEFAULT '{}',           -- 规则的执行条件
  
  -- 控制字段
  "enabled" BOOLEAN NOT NULL DEFAULT true,
  "priority" INTEGER NOT NULL DEFAULT 100,   -- 优先级，数值越大越先执行
  
  -- 适用范围
  "strategy_type" VARCHAR(100),              -- 策略类型，NULL表示全局规则
  "trading_pairs" JSONB DEFAULT '[]',        -- 适用的交易对，空数组表示所有
  
  -- 执行统计
  "execution_count" BIGINT DEFAULT 0,        -- 执行次数
  "success_count" BIGINT DEFAULT 0,          -- 成功次数
  "failure_count" BIGINT DEFAULT 0,          -- 失败次数
  "last_executed_at" TIMESTAMPTZ,           -- 最后执行时间
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  "updated_by" VARCHAR(255),
  
  -- 版本控制
  "version" INTEGER NOT NULL DEFAULT 1,
  "is_active" BOOLEAN NOT NULL DEFAULT true
);

-- ----------------------------
-- 索引设计
-- ----------------------------
CREATE INDEX "idx_unified_risk_rules_category" ON "public"."unified_risk_rules" ("category");
CREATE INDEX "idx_unified_risk_rules_type" ON "public"."unified_risk_rules" ("rule_type");
CREATE INDEX "idx_unified_risk_rules_enabled" ON "public"."unified_risk_rules" ("enabled");
CREATE INDEX "idx_unified_risk_rules_priority" ON "public"."unified_risk_rules" ("priority" DESC);
CREATE INDEX "idx_unified_risk_rules_strategy" ON "public"."unified_risk_rules" ("strategy_type");
CREATE INDEX "idx_unified_risk_rules_active" ON "public"."unified_risk_rules" ("is_active", "enabled");

-- 复合索引：查询性能优化
CREATE INDEX "idx_unified_risk_rules_execution" ON "public"."unified_risk_rules" 
  ("enabled", "is_active", "priority" DESC, "category");

-- ----------------------------
-- 规则执行历史表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_rule_executions";
CREATE TABLE "public"."risk_rule_executions" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "rule_id" UUID NOT NULL REFERENCES unified_risk_rules(id),
  "execution_context" JSONB NOT NULL,       -- 执行上下文（订单信息等）
  "result" VARCHAR(20) NOT NULL,            -- PASS, FAIL, ERROR
  "message" TEXT,                           -- 执行结果消息
  "execution_time_ms" INTEGER,              -- 执行耗时（毫秒）
  "executed_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- 分区字段（按日期分区）
  "date_partition" DATE NOT NULL DEFAULT CURRENT_DATE
);

-- 执行历史索引
CREATE INDEX "idx_risk_rule_executions_rule" ON "public"."risk_rule_executions" ("rule_id", "executed_at");
CREATE INDEX "idx_risk_rule_executions_result" ON "public"."risk_rule_executions" ("result", "executed_at");
CREATE INDEX "idx_risk_rule_executions_date" ON "public"."risk_rule_executions" ("date_partition");

-- ----------------------------
-- 规则开关历史表（审计用）
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_rule_switch_history";
CREATE TABLE "public"."risk_rule_switch_history" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "rule_id" UUID NOT NULL REFERENCES unified_risk_rules(id),
  "action" VARCHAR(20) NOT NULL,            -- ENABLE, DISABLE, UPDATE_PRIORITY
  "old_value" JSONB,                        -- 旧值
  "new_value" JSONB,                        -- 新值
  "reason" TEXT,                            -- 操作原因
  "operator" VARCHAR(255),                  -- 操作者
  "operated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "ip_address" INET,
  "user_agent" TEXT
);

-- 开关历史索引
CREATE INDEX "idx_risk_rule_switch_history_rule" ON "public"."risk_rule_switch_history" ("rule_id", "operated_at");
CREATE INDEX "idx_risk_rule_switch_history_operator" ON "public"."risk_rule_switch_history" ("operator", "operated_at");

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."unified_risk_rules" IS '统一风控规则表 - 包含所有风控规则的定义和参数';
COMMENT ON COLUMN "public"."unified_risk_rules"."category" IS '规则分类：basic, position, trading, market, time_based, advanced';
COMMENT ON COLUMN "public"."unified_risk_rules"."rule_type" IS '具体规则类型：order_size_limit, trading_frequency等';
COMMENT ON COLUMN "public"."unified_risk_rules"."parameters" IS '规则参数：包含所有阈值和配置';
COMMENT ON COLUMN "public"."unified_risk_rules"."conditions" IS '执行条件：规则何时生效的条件';
COMMENT ON COLUMN "public"."unified_risk_rules"."priority" IS '执行优先级：数值越大越先执行';
COMMENT ON COLUMN "public"."unified_risk_rules"."strategy_type" IS '适用策略类型：NULL表示全局规则';
COMMENT ON COLUMN "public"."unified_risk_rules"."trading_pairs" IS '适用交易对：空数组表示所有交易对';

COMMENT ON TABLE "public"."risk_rule_executions" IS '规则执行历史表 - 记录每次规则执行的结果';
COMMENT ON TABLE "public"."risk_rule_switch_history" IS '规则开关历史表 - 审计规则启用/禁用操作';





/*
 统一风控规则引擎 - 初始规则数据
 
 将原有的risk_config和risk_rules数据转换为统一的规则格式
*/

-- ----------------------------
-- 基础风控规则
-- ----------------------------

-- 订单大小限制规则
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '10000000-0000-0000-0000-000000000001',
  '全局订单金额限制',
  '限制单笔订单的最大金额，防止过大订单造成风险',
  'basic',
  'order_amount_limit',
  '{
    "max_amount": 10000,
    "currency": "USDT",
    "check_mode": "absolute",
    "include_fees": true
  }',
  true,
  100,
  NULL,
  'system'
);

-- 订单数量限制规则
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '10000000-0000-0000-0000-000000000002',
  '全局订单数量限制',
  '限制单笔订单的最大数量',
  'basic',
  'order_quantity_limit',
  '{
    "max_quantity": 100000,
    "min_quantity": 0.001,
    "precision_check": true
  }',
  true,
  99,
  NULL,
  'system'
);

-- 最大回撤限制规则
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '10000000-0000-0000-0000-000000000003',
  '最大回撤保护',
  '当回撤超过设定阈值时阻止新订单',
  'basic',
  'max_drawdown_protection',
  '{
    "max_drawdown_percent": 20.0,
    "calculation_period": "daily",
    "base_amount": "initial_capital",
    "emergency_threshold": 25.0
  }',
  true,
  95,
  NULL,
  'system'
);

-- ----------------------------
-- 交易频率控制规则
-- ----------------------------

-- 日交易频率限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '20000000-0000-0000-0000-000000000001',
  '日交易频率限制',
  '限制每日最大交易次数，防止过度交易',
  'trading',
  'daily_trading_frequency',
  '{
    "max_daily_trades": 100,
    "count_mode": "orders",
    "reset_time": "00:00:00",
    "timezone": "UTC",
    "exclude_cancelled": true
  }',
  true,
  90,
  NULL,
  'system'
);

-- 小时交易频率限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '20000000-0000-0000-0000-000000000002',
  '小时交易频率限制',
  '限制每小时最大交易次数',
  'trading',
  'hourly_trading_frequency',
  '{
    "max_hourly_trades": 10,
    "sliding_window": true,
    "burst_allowance": 3
  }',
  true,
  89,
  NULL,
  'system'
);

-- 订单间隔限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '20000000-0000-0000-0000-000000000003',
  '最小订单间隔',
  '确保订单之间有最小时间间隔',
  'trading',
  'min_order_interval',
  '{
    "min_interval_seconds": 60,
    "per_trading_pair": true,
    "same_side_only": false
  }',
  true,
  88,
  NULL,
  'system'
);

-- ----------------------------
-- 时间窗口控制规则
-- ----------------------------

-- 交易时间窗口
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '30000000-0000-0000-0000-000000000001',
  '全球交易时间窗口',
  '限制交易只能在指定时间段内进行',
  'time_based',
  'trading_time_window',
  '{
    "trading_hours": [
      {"day_of_week": 1, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 2, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 3, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 4, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 5, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 6, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"},
      {"day_of_week": 0, "start_time": "00:00", "end_time": "23:59", "timezone": "UTC"}
    ],
    "blackout_dates": [],
    "holiday_calendar": "global"
  }',
  true,
  85,
  NULL,
  'system'
);

-- ----------------------------
-- 持仓风险控制规则
-- ----------------------------

-- 单个持仓限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '40000000-0000-0000-0000-000000000001',
  '单个持仓大小限制',
  '限制单个交易对的最大持仓',
  'position',
  'single_position_limit',
  '{
    "max_position_percent": 25.0,
    "base_amount": "total_capital",
    "include_unrealized_pnl": true
  }',
  true,
  80,
  NULL,
  'system'
);

-- 杠杆限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '40000000-0000-0000-0000-000000000002',
  '最大杠杆限制',
  '限制最大使用杠杆倍数',
  'position',
  'max_leverage_limit',
  '{
    "max_leverage": 3.0,
    "calculation_mode": "portfolio_level",
    "margin_buffer": 0.1
  }',
  true,
  79,
  NULL,
  'system'
);

-- ----------------------------
-- 市场风险控制规则
-- ----------------------------

-- 波动率阈值
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '*************-0000-0000-000000000001',
  '市场波动率保护',
  '当市场波动率过高时限制交易',
  'market',
  'volatility_threshold',
  '{
    "max_volatility_percent": 30.0,
    "calculation_period": "24h",
    "volatility_type": "realized",
    "emergency_threshold": 50.0
  }',
  true,
  75,
  NULL,
  'system'
);

-- 流动性检查
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '*************-0000-0000-000000000002',
  '最小流动性要求',
  '确保交易对有足够的流动性',
  'market',
  'min_liquidity_requirement',
  '{
    "min_liquidity_amount": 10000,
    "currency": "USDT",
    "depth_levels": 5,
    "bid_ask_spread_max": 0.01
  }',
  true,
  74,
  NULL,
  'system'
);

-- ----------------------------
-- 网格策略专用规则
-- ----------------------------

-- 网格策略订单限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '*************-0000-0000-000000000001',
  '网格策略订单限制',
  '网格策略专用的订单大小限制',
  'basic',
  'order_amount_limit',
  '{
    "max_amount": 5000,
    "currency": "USDT",
    "check_mode": "absolute",
    "include_fees": true
  }',
  true,
  100,
  'grid_trading',
  'system'
);

-- 网格策略回撤限制
INSERT INTO "public"."unified_risk_rules" (
  "id", "name", "description", "category", "rule_type", 
  "parameters", "enabled", "priority", "strategy_type", "created_by"
) VALUES (
  '*************-0000-0000-000000000002',
  '网格策略回撤保护',
  '网格策略专用的回撤限制',
  'basic',
  'max_drawdown_protection',
  '{
    "max_drawdown_percent": 15.0,
    "calculation_period": "daily",
    "base_amount": "initial_capital",
    "emergency_threshold": 20.0
  }',
  true,
  95,
  'grid_trading',
  'system'
);
