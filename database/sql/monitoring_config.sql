-- ============================================================================
-- 监控配置数据库表结构
-- ============================================================================
-- 
-- 文件: monitoring_config.sql
-- 描述: 创建监控配置相关的数据库表结构
-- 版本: v1.0
-- 创建时间: 2025-06-30
-- 
-- 表结构:
-- 1. monitoring_config - 监控配置表
-- 2. monitoring_metrics - 监控指标数据表
-- 3. monitoring_alerts - 监控告警表
-- 
-- ============================================================================

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS "public"."monitoring_alerts" CASCADE;
DROP TABLE IF EXISTS "public"."monitoring_metrics" CASCADE;
DROP TABLE IF EXISTS "public"."monitoring_config" CASCADE;

-- ============================================================================
-- 1. 监控配置表
-- ============================================================================

CREATE TABLE "public"."monitoring_config" (
    -- 主键
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 配置基本信息
    "name" VARCHAR(100) NOT NULL DEFAULT 'default',
    "description" TEXT DEFAULT '默认监控配置',
    "enabled" BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 监控参数 (JSONB存储完整配置)
    "monitoring_parameters" JSONB NOT NULL,
    
    -- 元数据
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "created_by" VARCHAR(100) DEFAULT 'system'
);

-- ============================================================================
-- 2. 监控指标数据表
-- ============================================================================

CREATE TABLE "public"."monitoring_metrics" (
    -- 主键
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 指标信息
    "metric_type" VARCHAR(50) NOT NULL,        -- 指标类型: cpu, memory, disk, network, api, database
    "metric_name" VARCHAR(100) NOT NULL,       -- 指标名称: cpu_usage, memory_usage, response_time 等
    "metric_value" DOUBLE PRECISION NOT NULL,  -- 指标值
    "unit" VARCHAR(20),                        -- 单位: %, ms, bytes, count 等
    
    -- 标签和元数据 (JSONB存储)
    "labels" JSONB DEFAULT '{}',               -- 标签: {"host": "server1", "service": "api"}
    "metadata" JSONB DEFAULT '{}',             -- 元数据: 额外的上下文信息
    
    -- 时间和来源
    "timestamp" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "source" VARCHAR(50) NOT NULL DEFAULT 'system',
    
    -- 数据分区字段
    "date_partition" DATE NOT NULL DEFAULT CURRENT_DATE
);

-- ============================================================================
-- 3. 监控告警表
-- ============================================================================

CREATE TABLE "public"."monitoring_alerts" (
    -- 主键
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 告警基本信息
    "rule_id" VARCHAR(100) NOT NULL,           -- 告警规则ID
    "rule_name" VARCHAR(200) NOT NULL,         -- 告警规则名称
    "severity" VARCHAR(20) NOT NULL,           -- 告警级别: info, warning, critical, emergency
    "message" TEXT NOT NULL,                   -- 告警消息
    
    -- 告警数据
    "metric_name" VARCHAR(100) NOT NULL,       -- 触发的指标名称
    "triggered_value" DOUBLE PRECISION NOT NULL, -- 触发值
    "threshold_value" DOUBLE PRECISION NOT NULL, -- 阈值
    "condition" VARCHAR(20) NOT NULL,          -- 条件: >, <, =, >=, <=
    
    -- 告警状态
    "status" VARCHAR(20) NOT NULL DEFAULT 'triggered', -- triggered, acknowledged, resolved, suppressed
    
    -- 时间信息
    "triggered_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "acknowledged_at" TIMESTAMPTZ,
    "resolved_at" TIMESTAMPTZ,
    
    -- 处理信息
    "acknowledged_by" VARCHAR(100),
    "resolution_notes" TEXT,
    
    -- 额外数据 (JSONB存储)
    "alert_data" JSONB DEFAULT '{}',           -- 告警相关的额外数据
    "notification_sent" BOOLEAN DEFAULT FALSE, -- 是否已发送通知
    
    -- 数据分区字段
    "date_partition" DATE NOT NULL DEFAULT CURRENT_DATE
);

-- ============================================================================
-- 索引创建
-- ============================================================================

-- monitoring_config 表索引
CREATE INDEX idx_monitoring_config_name ON monitoring_config(name);
CREATE INDEX idx_monitoring_config_enabled ON monitoring_config(enabled);
CREATE INDEX idx_monitoring_config_created_at ON monitoring_config(created_at);

-- monitoring_metrics 表索引
CREATE INDEX idx_monitoring_metrics_type_name ON monitoring_metrics(metric_type, metric_name);
CREATE INDEX idx_monitoring_metrics_timestamp ON monitoring_metrics(timestamp DESC);
CREATE INDEX idx_monitoring_metrics_date_partition ON monitoring_metrics(date_partition);
CREATE INDEX idx_monitoring_metrics_source ON monitoring_metrics(source);

-- 复合索引用于常见查询
CREATE INDEX idx_monitoring_metrics_type_time ON monitoring_metrics(metric_type, timestamp DESC);
CREATE INDEX idx_monitoring_metrics_name_time ON monitoring_metrics(metric_name, timestamp DESC);

-- monitoring_alerts 表索引
CREATE INDEX idx_monitoring_alerts_rule_id ON monitoring_alerts(rule_id);
CREATE INDEX idx_monitoring_alerts_severity ON monitoring_alerts(severity);
CREATE INDEX idx_monitoring_alerts_status ON monitoring_alerts(status);
CREATE INDEX idx_monitoring_alerts_triggered_at ON monitoring_alerts(triggered_at DESC);
CREATE INDEX idx_monitoring_alerts_date_partition ON monitoring_alerts(date_partition);

-- 复合索引用于常见查询
CREATE INDEX idx_monitoring_alerts_status_time ON monitoring_alerts(status, triggered_at DESC);
CREATE INDEX idx_monitoring_alerts_severity_time ON monitoring_alerts(severity, triggered_at DESC);

-- ============================================================================
-- 约束添加
-- ============================================================================

-- monitoring_config 约束
ALTER TABLE monitoring_config ADD CONSTRAINT chk_monitoring_config_name_not_empty 
    CHECK (length(trim(name)) > 0);

-- monitoring_metrics 约束
ALTER TABLE monitoring_metrics ADD CONSTRAINT chk_monitoring_metrics_type_not_empty 
    CHECK (length(trim(metric_type)) > 0);
ALTER TABLE monitoring_metrics ADD CONSTRAINT chk_monitoring_metrics_name_not_empty 
    CHECK (length(trim(metric_name)) > 0);

-- monitoring_alerts 约束
ALTER TABLE monitoring_alerts ADD CONSTRAINT chk_monitoring_alerts_severity 
    CHECK (severity IN ('info', 'warning', 'critical', 'emergency'));
ALTER TABLE monitoring_alerts ADD CONSTRAINT chk_monitoring_alerts_status 
    CHECK (status IN ('triggered', 'acknowledged', 'resolved', 'suppressed'));
ALTER TABLE monitoring_alerts ADD CONSTRAINT chk_monitoring_alerts_condition 
    CHECK (condition IN ('>', '<', '=', '>=', '<=', '!='));

-- ============================================================================
-- 表注释
-- ============================================================================

COMMENT ON TABLE monitoring_config IS '监控配置表 - 存储系统监控相关配置参数';
COMMENT ON COLUMN monitoring_config.id IS '配置唯一标识符';
COMMENT ON COLUMN monitoring_config.name IS '配置名称';
COMMENT ON COLUMN monitoring_config.description IS '配置描述';
COMMENT ON COLUMN monitoring_config.enabled IS '是否启用此配置';
COMMENT ON COLUMN monitoring_config.monitoring_parameters IS '监控参数JSON配置';
COMMENT ON COLUMN monitoring_config.created_at IS '创建时间';
COMMENT ON COLUMN monitoring_config.updated_at IS '最后更新时间';
COMMENT ON COLUMN monitoring_config.created_by IS '创建者';

COMMENT ON TABLE monitoring_metrics IS '监控指标数据表 - 存储系统运行时的各种监控指标';
COMMENT ON COLUMN monitoring_metrics.id IS '指标记录唯一标识符';
COMMENT ON COLUMN monitoring_metrics.metric_type IS '指标类型';
COMMENT ON COLUMN monitoring_metrics.metric_name IS '指标名称';
COMMENT ON COLUMN monitoring_metrics.metric_value IS '指标值';
COMMENT ON COLUMN monitoring_metrics.unit IS '指标单位';
COMMENT ON COLUMN monitoring_metrics.labels IS '指标标签JSON';
COMMENT ON COLUMN monitoring_metrics.timestamp IS '指标记录时间';
COMMENT ON COLUMN monitoring_metrics.source IS '数据来源';

COMMENT ON TABLE monitoring_alerts IS '监控告警表 - 存储系统监控告警信息';
COMMENT ON COLUMN monitoring_alerts.id IS '告警唯一标识符';
COMMENT ON COLUMN monitoring_alerts.rule_id IS '告警规则ID';
COMMENT ON COLUMN monitoring_alerts.severity IS '告警级别';
COMMENT ON COLUMN monitoring_alerts.message IS '告警消息';
COMMENT ON COLUMN monitoring_alerts.status IS '告警状态';
COMMENT ON COLUMN monitoring_alerts.triggered_at IS '告警触发时间';

-- ============================================================================
-- 插入默认监控配置
-- ============================================================================

INSERT INTO monitoring_config (
    id,
    name,
    description,
    enabled,
    monitoring_parameters,
    created_by
) VALUES (
    gen_random_uuid(),
    'default',
    '系统默认监控配置',
    true,
    '{
        "name": "default",
        "description": "系统默认监控配置",
        "enabled": true,
        "metrics_enabled": true,
        "health_check_interval": 60,
        "data_collection_interval": 30,
        "data_retention_days": 30,
        "cpu_thresholds": {
            "warning": 70,
            "critical": 85,
            "check_interval": 60
        },
        "memory_thresholds": {
            "warning": 75,
            "critical": 90,
            "check_interval": 60
        },
        "disk_thresholds": {
            "warning": 80,
            "critical": 95,
            "check_interval": 300
        },
        "network_thresholds": {
            "latency_warning": 100.0,
            "latency_critical": 500.0,
            "bandwidth_warning": 80,
            "bandwidth_critical": 95,
            "check_interval": 60
        },
        "database_thresholds": {
            "connection_pool_warning": 70,
            "connection_pool_critical": 90,
            "query_time_warning": 1000.0,
            "query_time_critical": 5000.0,
            "slow_query_threshold": 1000.0,
            "check_interval": 60
        },
        "api_thresholds": {
            "response_time_warning": 500.0,
            "response_time_critical": 2000.0,
            "error_rate_warning": 5.0,
            "error_rate_critical": 10.0,
            "throughput_minimum": 10.0,
            "check_interval": 60
        },
        "alert_rules": [],
        "notification_channels": [],
        "alert_suppression": {
            "enabled": true,
            "default_duration": 300,
            "max_duration": 3600,
            "rules": []
        },
        "performance_monitoring": {
            "enabled": true,
            "monitoring_interval": 60,
            "data_retention_days": 30,
            "detailed_metrics": false,
            "sampling_rate": 1.0,
            "benchmarks": {
                "api_response_time_baseline": 200.0,
                "db_query_time_baseline": 100.0,
                "memory_usage_baseline": 50,
                "cpu_usage_baseline": 30
            }
        },
        "slow_query_monitoring": {
            "enabled": true,
            "threshold_ms": 1000,
            "log_query_details": true,
            "log_execution_plan": false,
            "log_retention_days": 7,
            "max_records": 1000
        },
        "health_check": {
            "enabled": true,
            "check_interval": 30,
            "timeout_seconds": 10,
            "retry_count": 3,
            "endpoints": []
        }
    }',
    'system'
);

-- ============================================================================
-- 数据验证查询
-- ============================================================================

-- 验证监控配置表
SELECT
    id,
    name,
    description,
    enabled,
    jsonb_pretty(monitoring_parameters) as config,
    created_at,
    created_by
FROM monitoring_config;

-- 验证表结构
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('monitoring_config', 'monitoring_metrics', 'monitoring_alerts')
ORDER BY table_name, ordinal_position;

-- 验证索引
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
    AND tablename IN ('monitoring_config', 'monitoring_metrics', 'monitoring_alerts')
ORDER BY tablename, indexname;

-- 验证约束
SELECT
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    cc.check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_schema = 'public'
    AND tc.table_name IN ('monitoring_config', 'monitoring_metrics', 'monitoring_alerts')
ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;
