/*
 优化后的风控违规表
 
 主要改进：
 1. 使用 ENUM 类型替代 VARCHAR
 2. 添加完整的索引策略
 3. 完善外键关系
 4. 添加数据完整性约束
 5. 优化查询性能
*/

-- ----------------------------
-- 创建 ENUM 类型
-- ----------------------------
DO $$ BEGIN
    CREATE TYPE violation_severity AS ENUM (
        'low',             -- 低风险
        'medium',          -- 中风险
        'high',            -- 高风险
        'critical'         -- 严重风险
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE violation_status AS ENUM (
        'active',          -- 活跃违规
        'resolved',        -- 已解决
        'ignored',         -- 已忽略
        'escalated'        -- 已升级
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ----------------------------
-- 删除旧表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_violations" CASCADE;

-- ----------------------------
-- 优化后的风控违规表
-- ----------------------------
CREATE TABLE "public"."risk_violations" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 关联信息
  "risk_check_id" UUID NOT NULL,
  "rule_id" UUID NOT NULL,
  "rule_name" VARCHAR(255) NOT NULL,
  "rule_version" INTEGER DEFAULT 1,
  
  -- 违规详情
  "severity" violation_severity NOT NULL DEFAULT 'medium',
  "status" violation_status NOT NULL DEFAULT 'active',
  "message" TEXT NOT NULL,
  "detailed_message" TEXT,
  
  -- 违规数据
  "current_value" VARCHAR(255),
  "limit_value" VARCHAR(255),
  "threshold_exceeded_by" DECIMAL(20,8),
  "threshold_exceeded_percent" DECIMAL(10,4),
  
  -- 上下文信息
  "trading_pair" VARCHAR(50),
  "strategy_id" UUID,
  "portfolio_id" UUID,
  "order_context" JSONB DEFAULT '{}',
  
  -- 影响评估
  "potential_loss" DECIMAL(20,8),
  "risk_contribution" DECIMAL(10,4),
  "impact_score" DECIMAL(5,2) DEFAULT 0,
  
  -- 处理信息
  "auto_resolved" BOOLEAN DEFAULT false,
  "resolution_action" VARCHAR(100),
  "resolution_notes" TEXT,
  "resolved_at" TIMESTAMPTZ,
  "resolved_by" VARCHAR(255),
  
  -- 通知状态
  "notification_sent" BOOLEAN DEFAULT false,
  "notification_sent_at" TIMESTAMPTZ,
  "escalation_level" INTEGER DEFAULT 0,
  "escalated_at" TIMESTAMPTZ,
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255)
);

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."risk_violations" IS '优化后的风控违规表 - 包含完整的违规信息、处理状态和影响评估';
COMMENT ON COLUMN "public"."risk_violations"."severity" IS '违规严重程度：low, medium, high, critical';
COMMENT ON COLUMN "public"."risk_violations"."status" IS '违规状态：active, resolved, ignored, escalated';
COMMENT ON COLUMN "public"."risk_violations"."rule_version" IS '触发违规的规则版本号';
COMMENT ON COLUMN "public"."risk_violations"."threshold_exceeded_by" IS '超出阈值的绝对数值';
COMMENT ON COLUMN "public"."risk_violations"."threshold_exceeded_percent" IS '超出阈值的百分比';
COMMENT ON COLUMN "public"."risk_violations"."potential_loss" IS '潜在损失金额';
COMMENT ON COLUMN "public"."risk_violations"."risk_contribution" IS '对总体风险的贡献度百分比';
COMMENT ON COLUMN "public"."risk_violations"."impact_score" IS '影响评分：0-100，数值越高影响越大';
COMMENT ON COLUMN "public"."risk_violations"."escalation_level" IS '升级级别：0=无升级，1=一级升级，2=二级升级等';

-- ----------------------------
-- 性能优化索引
-- ----------------------------

-- 基础查询索引
CREATE INDEX "idx_risk_violations_check_id" ON "public"."risk_violations" ("risk_check_id");
CREATE INDEX "idx_risk_violations_rule_id" ON "public"."risk_violations" ("rule_id");
CREATE INDEX "idx_risk_violations_severity" ON "public"."risk_violations" ("severity");
CREATE INDEX "idx_risk_violations_status" ON "public"."risk_violations" ("status");

-- 时间相关索引
CREATE INDEX "idx_risk_violations_created_at" ON "public"."risk_violations" ("created_at" DESC);
CREATE INDEX "idx_risk_violations_resolved_at" ON "public"."risk_violations" ("resolved_at" DESC);

-- 复合索引：查询性能优化
CREATE INDEX "idx_risk_violations_active" ON "public"."risk_violations" 
  ("status", "severity", "created_at" DESC) WHERE status = 'active';

CREATE INDEX "idx_risk_violations_critical" ON "public"."risk_violations" 
  ("severity", "created_at" DESC) WHERE severity = 'critical';

CREATE INDEX "idx_risk_violations_unresolved" ON "public"."risk_violations" 
  ("status", "created_at" DESC) WHERE status IN ('active', 'escalated');

CREATE INDEX "idx_risk_violations_strategy" ON "public"."risk_violations" 
  ("strategy_id", "status", "created_at" DESC) WHERE strategy_id IS NOT NULL;

CREATE INDEX "idx_risk_violations_trading_pair" ON "public"."risk_violations" 
  ("trading_pair", "severity", "created_at" DESC) WHERE trading_pair IS NOT NULL;

-- 规则分析索引
CREATE INDEX "idx_risk_violations_rule_analysis" ON "public"."risk_violations" 
  ("rule_id", "rule_name", "severity", "created_at" DESC);

CREATE INDEX "idx_risk_violations_rule_version" ON "public"."risk_violations" 
  ("rule_id", "rule_version", "created_at" DESC);

-- 影响评估索引
CREATE INDEX "idx_risk_violations_impact_score" ON "public"."risk_violations" ("impact_score" DESC);
CREATE INDEX "idx_risk_violations_potential_loss" ON "public"."risk_violations" ("potential_loss" DESC);

-- 通知和升级索引
CREATE INDEX "idx_risk_violations_notification_pending" ON "public"."risk_violations" 
  ("notification_sent", "severity", "created_at") WHERE notification_sent = false;

CREATE INDEX "idx_risk_violations_escalation" ON "public"."risk_violations" 
  ("escalation_level", "escalated_at" DESC) WHERE escalation_level > 0;

-- JSONB 字段索引
CREATE INDEX "idx_risk_violations_order_context_gin" ON "public"."risk_violations" USING gin("order_context");

-- ----------------------------
-- 数据完整性约束
-- ----------------------------

-- 数值范围约束
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_by_positive" 
  CHECK (threshold_exceeded_by IS NULL OR threshold_exceeded_by >= 0);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_percent_positive" 
  CHECK (threshold_exceeded_percent IS NULL OR threshold_exceeded_percent >= 0);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_potential_loss_positive" 
  CHECK (potential_loss IS NULL OR potential_loss >= 0);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_risk_contribution_range" 
  CHECK (risk_contribution IS NULL OR (risk_contribution >= 0 AND risk_contribution <= 100));

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_impact_score_range" 
  CHECK (impact_score >= 0 AND impact_score <= 100);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_level_positive" 
  CHECK (escalation_level >= 0);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_rule_version_positive" 
  CHECK (rule_version > 0);

-- 逻辑约束
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolution_consistency" 
  CHECK (
    (status = 'resolved' AND resolved_at IS NOT NULL AND resolved_by IS NOT NULL) OR
    (status != 'resolved' AND (resolved_at IS NULL OR resolved_by IS NULL))
  );

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_consistency" 
  CHECK (
    (escalation_level > 0 AND escalated_at IS NOT NULL) OR
    (escalation_level = 0 AND escalated_at IS NULL)
  );

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_consistency" 
  CHECK (
    (notification_sent = true AND notification_sent_at IS NOT NULL) OR
    (notification_sent = false AND notification_sent_at IS NULL)
  );

-- 时间逻辑约束
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolved_after_created" 
  CHECK (resolved_at IS NULL OR resolved_at >= created_at);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalated_after_created" 
  CHECK (escalated_at IS NULL OR escalated_at >= created_at);

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_after_created" 
  CHECK (notification_sent_at IS NULL OR notification_sent_at >= created_at);

-- ----------------------------
-- 外键约束
-- ----------------------------
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "fk_risk_violations_check" 
  FOREIGN KEY ("risk_check_id") REFERENCES "public"."risk_checks" ("id", "check_date") ON DELETE CASCADE;

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "fk_risk_violations_rule" 
  FOREIGN KEY ("rule_id") REFERENCES "public"."risk_rules" ("id") ON DELETE RESTRICT;

ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "fk_risk_violations_strategy" 
  FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE SET NULL;

-- 注意：如果 portfolios 表存在，取消注释下面的约束
-- ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "fk_risk_violations_portfolio" 
--   FOREIGN KEY ("portfolio_id") REFERENCES "public"."portfolios" ("id") ON DELETE SET NULL;

-- ----------------------------
-- 触发器：自动更新 updated_at
-- ----------------------------
CREATE OR REPLACE FUNCTION update_risk_violations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_violations_updated_at
    BEFORE UPDATE ON "public"."risk_violations"
    FOR EACH ROW
    EXECUTE FUNCTION update_risk_violations_updated_at();

-- ----------------------------
-- 触发器：自动处理状态变更
-- ----------------------------
CREATE OR REPLACE FUNCTION handle_risk_violations_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态变为 resolved 时，自动设置解决时间
    IF NEW.status = 'resolved' AND OLD.status != 'resolved' THEN
        NEW.resolved_at = NOW();
        IF NEW.resolved_by IS NULL THEN
            NEW.resolved_by = NEW.updated_by;
        END IF;
    END IF;
    
    -- 当升级级别增加时，设置升级时间
    IF NEW.escalation_level > OLD.escalation_level THEN
        NEW.escalated_at = NOW();
        NEW.status = 'escalated';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_violations_status_change
    BEFORE UPDATE ON "public"."risk_violations"
    FOR EACH ROW
    EXECUTE FUNCTION handle_risk_violations_status_change();

-- ----------------------------
-- 权限设置
-- ----------------------------
ALTER TABLE "public"."risk_violations" OWNER TO "neondb_owner";
