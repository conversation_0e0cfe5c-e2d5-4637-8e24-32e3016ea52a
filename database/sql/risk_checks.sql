-- risk_checks 表结构（从线上数据库导出）
-- 导出时间: 2025-08-05 18:15:48

DROP TABLE IF EXISTS "public"."risk_checks" CASCADE;

CREATE TABLE "public"."risk_checks" (
  "id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "trading_pair" VARCHAR(50) NOT NULL,
  "side" USER-DEFINED NOT NULL,
  "quantity" DECIMAL(20,8) NOT NULL,
  "price" DECIMAL(20,8),
  "order_type" USER-DEFINED NOT NULL,
  "order_value" DECIMAL(20,8),
  "result" USER-DEFINED NOT NULL,
  "risk_score" DECIMAL(5,2) NOT NULL DEFAULT 0,
  "confidence_level" DECIMAL(5,2) DEFAULT 95.0,
  "violations" JSONB NOT NULL DEFAULT '[]'::jsonb,
  "warnings" JSONB NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" JSONB NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" JSONB NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" DECIMAL(20,8),
  "suggested_price_min" DECIMAL(20,8),
  "suggested_price_max" DECIMAL(20,8),
  "alternative_suggestions" JSONB DEFAULT '[]'::jsonb,
  "processing_time_ms" INTEGER,
  "rules_evaluated" INTEGER DEFAULT 0,
  "cache_hit_rate" DECIMAL(5,2) DEFAULT 0,
  "strategy_id" UUID,
  "portfolio_id" UUID,
  "engine_id" UUID,
  "session_id" VARCHAR(100),
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  "check_date" DATE NOT NULL DEFAULT CURRENT_DATE,
  PRIMARY KEY ("id", "check_date")
);

-- 索引

CREATE INDEX idx_risk_checks_timestamp ON ONLY public.risk_checks USING btree ("timestamp");
CREATE INDEX idx_risk_checks_date ON ONLY public.risk_checks USING btree (check_date);
CREATE INDEX idx_risk_checks_trading_pair ON ONLY public.risk_checks USING btree (trading_pair);
CREATE INDEX idx_risk_checks_result ON ONLY public.risk_checks USING btree (result);
CREATE INDEX idx_risk_checks_strategy ON ONLY public.risk_checks USING btree (strategy_id);
CREATE INDEX idx_risk_checks_pair_result_time ON ONLY public.risk_checks USING btree (trading_pair, result, "timestamp" DESC);
CREATE INDEX idx_risk_checks_strategy_time ON ONLY public.risk_checks USING btree (strategy_id, "timestamp" DESC) WHERE (strategy_id IS NOT NULL);
CREATE INDEX idx_risk_checks_failed_recent ON ONLY public.risk_checks USING btree (result, "timestamp" DESC) WHERE (result = 'failed'::check_result);
CREATE INDEX idx_risk_checks_risk_score ON ONLY public.risk_checks USING btree (risk_score DESC);
CREATE INDEX idx_risk_checks_high_risk ON ONLY public.risk_checks USING btree (risk_score, "timestamp" DESC) WHERE (risk_score >= (70)::numeric);
CREATE INDEX idx_risk_checks_violations_gin ON ONLY public.risk_checks USING gin (violations);
CREATE INDEX idx_risk_checks_warnings_gin ON ONLY public.risk_checks USING gin (warnings);
CREATE INDEX idx_risk_checks_applied_rules_gin ON ONLY public.risk_checks USING gin (applied_rules);
CREATE INDEX idx_risk_checks_processing_time ON ONLY public.risk_checks USING btree (processing_time_ms);
CREATE INDEX idx_risk_checks_slow_queries ON ONLY public.risk_checks USING btree (processing_time_ms, "timestamp" DESC) WHERE (processing_time_ms > 1000);

-- 约束

ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > (0)::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_price_positive" CHECK ((price IS NULL) OR (price > (0)::numeric));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK ((cache_hit_rate >= (0)::numeric) AND (cache_hit_rate <= (100)::numeric));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_risk_score_range" CHECK ((risk_score >= (0)::numeric) AND (risk_score <= (100)::numeric));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_confidence_level_range" CHECK ((confidence_level >= (0)::numeric) AND (confidence_level <= (100)::numeric));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK ((max_allowed_quantity IS NULL) OR (max_allowed_quantity > (0)::numeric));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_suggested_prices_positive" CHECK (((suggested_price_min IS NULL) OR (suggested_price_min > (0)::numeric)) AND ((suggested_price_max IS NULL) OR (suggested_price_max > (0)::numeric)) AND ((suggested_price_min IS NULL) OR (suggested_price_max IS NULL) OR (suggested_price_min <= suggested_price_max)));
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_1_not_null" CHECK id IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_2_not_null" CHECK timestamp IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_3_not_null" CHECK trading_pair IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_4_not_null" CHECK side IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_5_not_null" CHECK quantity IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_7_not_null" CHECK order_type IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_9_not_null" CHECK result IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_10_not_null" CHECK risk_score IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_12_not_null" CHECK violations IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_13_not_null" CHECK warnings IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_14_not_null" CHECK recommendations IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_15_not_null" CHECK applied_rules IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_27_not_null" CHECK created_at IS NOT NULL;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "2200_18021_29_not_null" CHECK check_date IS NOT NULL;

ALTER TABLE "public"."risk_checks" OWNER TO "neondb_owner";