/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:10:27
*/


-- ----------------------------
-- Table structure for risk_checks
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks";
CREATE TABLE "public"."risk_checks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "passed" bool NOT NULL,
  "risk_score" numeric(10,4) NOT NULL DEFAULT 0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "processing_time_ms" int4,
  "strategy_id" uuid,
  "engine_id" uuid,
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."risk_checks" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of risk_checks
-- ----------------------------
BEGIN;
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('8b0d4963-ff1d-4116-9704-2ddc343cf68d', '2025-06-10 08:58:53.850393+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 0.4000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '[]', 500.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 08:58:53.850393+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('ba4227b7-d45b-4fc0-92ef-74143843b02c', '2025-06-10 08:58:54.407981+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 08:58:54.407981+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('e9a8d55b-a786-4918-ae92-c636c6d5e7ec', '2025-06-10 09:01:41.935278+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 0.8000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 185, NULL, NULL, '2025-06-10 09:01:41.935278+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('de91c05c-31d8-4eb1-b238-593815a59ad7', '2025-06-10 09:01:42.706432+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 124, NULL, NULL, '2025-06-10 09:01:42.706432+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('489feadc-9a63-43d9-9bd3-d1ea9295cf6e', '2025-06-10 09:03:42.325633+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 1.2000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 175, NULL, NULL, '2025-06-10 09:03:42.325633+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('e729c2d1-0a1a-4ee3-9643-0ffbaecd9eeb', '2025-06-10 09:03:43.202021+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 171, NULL, NULL, '2025-06-10 09:03:43.202021+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('22cbd19f-3937-486c-8a86-60b949d4ac17', '2025-06-10 09:07:07.781793+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 1.6000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "f3701c22-d50d-437f-a481-cf294ef01d67", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 192, NULL, NULL, '2025-06-10 09:07:07.781793+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('4ead36e2-a116-4784-a8e1-e0ad4d52000e', '2025-06-10 09:07:09.081252+00', 'BTC_USDT', 'Buy', 400.00000000, 43000.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 115, NULL, NULL, '2025-06-10 09:07:09.081252+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('317f0432-30bf-4c5e-9aa6-48a67406c35c', '2025-06-10 09:07:46.430018+00', 'BTC_USDT', 'Buy', 600.00000000, 43000.00000000, 'Limit', 'f', 2.0000, '[{"message": "Order size exceeds maximum allowed limit", "rule_id": "60751436-1b43-4773-8ee2-9d14e3fc8b35", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "a4d31e23-fd66-44b8-b591-00ab2769681b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "490cfdb7-ca0d-46d3-930f-7e4abf5c3c4b", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "f3701c22-d50d-437f-a481-cf294ef01d67", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}, {"message": "Order size exceeds maximum allowed limit", "rule_id": "ba178e3e-32ed-43c8-b858-813ddfe95640", "severity": "High", "rule_name": "Test Max Order Size", "limit_value": "500", "current_value": "600"}]', '[]', '["Consider reducing order size to lower risk"]', 500.00000000, NULL, NULL, 177, NULL, NULL, '2025-06-10 09:07:46.430018+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('ca3f5868-5457-477f-82bf-b70ef6342ad7', '2025-06-10 09:52:16.696479+00', 'BNB_USDT', 'Buy', 50.00000000, 350.00000000, 'Limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 2000.00000000, NULL, NULL, 172, NULL, NULL, '2025-06-10 09:52:16.696479+00');
INSERT INTO "public"."risk_checks" ("id", "timestamp", "trading_pair", "side", "quantity", "price", "order_type", "passed", "risk_score", "violations", "warnings", "recommendations", "max_allowed_quantity", "suggested_price_min", "suggested_price_max", "processing_time_ms", "strategy_id", "engine_id", "created_at") VALUES ('70041fbe-76b7-4866-99cd-ac35e6b898cf', '2025-06-13 18:16:56.774183+00', 'BTCUSDT', 'buy', 0.10000000, 50000.00000000, 'limit', 't', 0.0000, '[]', '[]', '["Order passes all risk checks"]', 500.00000000, NULL, NULL, 644, NULL, NULL, '2025-06-13 18:16:56.774183+00');
COMMIT;

-- ----------------------------
-- Primary Key structure for table risk_checks
-- ----------------------------
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table risk_checks
-- ----------------------------
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_engine_id_fkey" FOREIGN KEY ("engine_id") REFERENCES "public"."engines" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
