-- ============================================================================
-- SigmaX 策略配置表
-- ============================================================================
-- 
-- 专门存储策略相关配置，使用JSONB格式存储完整的策略参数
-- 支持高效查询和索引优化，与RiskConfig和MonitoringConfig保持一致的架构
--
-- 创建时间: 2025-06-30
-- 作者: Claude 4.0 sonnet
-- 版本: v1.0
-- ============================================================================

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS "public"."strategy_config";

-- 删除序列（如果存在）
DROP SEQUENCE IF EXISTS strategy_config_id_seq;
CREATE SEQUENCE strategy_config_id_seq START 1;

-- 创建策略配置表
CREATE TABLE "public"."strategy_config" (
    -- 主键
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 配置基本信息
    "name" VARCHAR(100) NOT NULL DEFAULT 'default',
    "description" TEXT DEFAULT '默认策略配置',
    "enabled" BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 策略参数 (JSONB存储完整配置)
    "strategy_parameters" JSONB NOT NULL,
    
    -- 元数据
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "created_by" VARCHAR(100) DEFAULT 'system'
);

-- 创建索引
CREATE INDEX idx_strategy_config_enabled ON strategy_config(enabled);
CREATE INDEX idx_strategy_config_name ON strategy_config(name);
CREATE INDEX idx_strategy_config_parameters_gin ON strategy_config USING gin(strategy_parameters);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_strategy_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_strategy_config_updated_at
    BEFORE UPDATE ON strategy_config
    FOR EACH ROW
    EXECUTE FUNCTION update_strategy_config_updated_at();

-- 插入默认配置
INSERT INTO "public"."strategy_config" (
    "name",
    "description", 
    "enabled",
    "strategy_parameters",
    "created_by"
) VALUES (
    'default',
    '系统默认策略配置',
    TRUE,
    '{
        "default_grid_levels": 10,
        "default_grid_spacing": 1.0,
        "max_active_orders": 20,
        "rebalance_interval": 300
    }'::jsonb,
    'system'
);

-- 添加注释
COMMENT ON TABLE strategy_config IS '策略配置表 - 存储系统策略相关配置参数';
COMMENT ON COLUMN strategy_config.id IS '配置唯一标识符';
COMMENT ON COLUMN strategy_config.name IS '配置名称';
COMMENT ON COLUMN strategy_config.description IS '配置描述';
COMMENT ON COLUMN strategy_config.enabled IS '是否启用此配置';
COMMENT ON COLUMN strategy_config.strategy_parameters IS '策略参数JSON配置';
COMMENT ON COLUMN strategy_config.created_at IS '创建时间';
COMMENT ON COLUMN strategy_config.updated_at IS '最后更新时间';
COMMENT ON COLUMN strategy_config.created_by IS '创建者';

-- 验证数据
SELECT 
    id,
    name,
    description,
    enabled,
    jsonb_pretty(strategy_parameters) as config,
    created_at,
    created_by
FROM strategy_config;
