/*
 优化后的风控统计表
 
 主要改进：
 1. 添加更详细的统计维度
 2. 优化索引策略
 3. 添加数据完整性约束
 4. 支持多维度分析
 5. 添加自动聚合功能
*/

-- ----------------------------
-- 删除旧表
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_statistics" CASCADE;

-- ----------------------------
-- 优化后的风控统计表
-- ----------------------------
CREATE TABLE "public"."risk_statistics" (
  -- 基础字段
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "date" DATE NOT NULL,
  "hour" INTEGER, -- 小时级别统计（0-23），NULL表示日级别统计
  
  -- 维度字段
  "strategy_type" VARCHAR(100), -- 策略类型，NULL表示全局统计
  "trading_pair" VARCHAR(50),   -- 交易对，NULL表示所有交易对
  "rule_category" VARCHAR(50),  -- 规则分类，NULL表示所有分类
  
  -- 基础统计
  "total_checks" INTEGER NOT NULL DEFAULT 0,
  "passed_checks" INTEGER NOT NULL DEFAULT 0,
  "failed_checks" INTEGER NOT NULL DEFAULT 0,
  "warning_checks" INTEGER NOT NULL DEFAULT 0,
  "error_checks" INTEGER NOT NULL DEFAULT 0,
  
  -- 计算字段
  "pass_rate" DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN total_checks > 0 
    THEN (passed_checks::DECIMAL / total_checks * 100) 
    ELSE 0 END
  ) STORED,
  "fail_rate" DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN total_checks > 0 
    THEN (failed_checks::DECIMAL / total_checks * 100) 
    ELSE 0 END
  ) STORED,
  
  -- 风险评分统计
  "average_risk_score" DECIMAL(5,2) NOT NULL DEFAULT 0,
  "max_risk_score" DECIMAL(5,2) NOT NULL DEFAULT 0,
  "min_risk_score" DECIMAL(5,2) NOT NULL DEFAULT 0,
  "risk_score_std_dev" DECIMAL(5,2) DEFAULT 0,
  
  -- 性能统计
  "average_processing_time_ms" DECIMAL(8,3) NOT NULL DEFAULT 0,
  "max_processing_time_ms" INTEGER DEFAULT 0,
  "min_processing_time_ms" INTEGER DEFAULT 0,
  "total_processing_time_ms" BIGINT DEFAULT 0,
  
  -- 违规统计
  "total_violations" INTEGER NOT NULL DEFAULT 0,
  "critical_violations" INTEGER NOT NULL DEFAULT 0,
  "high_violations" INTEGER NOT NULL DEFAULT 0,
  "medium_violations" INTEGER NOT NULL DEFAULT 0,
  "low_violations" INTEGER NOT NULL DEFAULT 0,
  
  -- 详细分析数据
  "top_violations" JSONB NOT NULL DEFAULT '[]',
  "risk_score_distribution" JSONB NOT NULL DEFAULT '{}',
  "processing_time_distribution" JSONB NOT NULL DEFAULT '{}',
  "rule_performance" JSONB NOT NULL DEFAULT '{}',
  "trend_indicators" JSONB NOT NULL DEFAULT '{}',
  
  -- 业务指标
  "total_order_value" DECIMAL(20,8) DEFAULT 0,
  "blocked_order_value" DECIMAL(20,8) DEFAULT 0,
  "potential_loss_prevented" DECIMAL(20,8) DEFAULT 0,
  "false_positive_rate" DECIMAL(5,2) DEFAULT 0,
  
  -- 系统指标
  "cache_hit_rate" DECIMAL(5,2) DEFAULT 0,
  "rule_execution_efficiency" DECIMAL(5,2) DEFAULT 0,
  "system_load_impact" DECIMAL(5,2) DEFAULT 0,
  
  -- 审计字段
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "last_aggregated_at" TIMESTAMPTZ,
  "data_quality_score" DECIMAL(5,2) DEFAULT 100
);

-- ----------------------------
-- 表注释
-- ----------------------------
COMMENT ON TABLE "public"."risk_statistics" IS '优化后的风控统计表 - 支持多维度分析和自动聚合';
COMMENT ON COLUMN "public"."risk_statistics"."hour" IS '小时级别统计（0-23），NULL表示日级别统计';
COMMENT ON COLUMN "public"."risk_statistics"."strategy_type" IS '策略类型维度，NULL表示全局统计';
COMMENT ON COLUMN "public"."risk_statistics"."trading_pair" IS '交易对维度，NULL表示所有交易对';
COMMENT ON COLUMN "public"."risk_statistics"."rule_category" IS '规则分类维度，NULL表示所有分类';
COMMENT ON COLUMN "public"."risk_statistics"."pass_rate" IS '通过率：自动计算的百分比';
COMMENT ON COLUMN "public"."risk_statistics"."fail_rate" IS '失败率：自动计算的百分比';
COMMENT ON COLUMN "public"."risk_statistics"."risk_score_std_dev" IS '风险评分标准差';
COMMENT ON COLUMN "public"."risk_statistics"."potential_loss_prevented" IS '预防的潜在损失金额';
COMMENT ON COLUMN "public"."risk_statistics"."false_positive_rate" IS '误报率：错误阻止正常交易的比例';
COMMENT ON COLUMN "public"."risk_statistics"."rule_execution_efficiency" IS '规则执行效率：规则执行的效率评分';
COMMENT ON COLUMN "public"."risk_statistics"."data_quality_score" IS '数据质量评分：统计数据的可信度';

-- ----------------------------
-- 性能优化索引
-- ----------------------------

-- 基础查询索引
CREATE INDEX "idx_risk_statistics_date" ON "public"."risk_statistics" ("date" DESC);
CREATE INDEX "idx_risk_statistics_hour" ON "public"."risk_statistics" ("date", "hour");

-- 维度查询索引
CREATE INDEX "idx_risk_statistics_strategy" ON "public"."risk_statistics" ("strategy_type", "date" DESC);
CREATE INDEX "idx_risk_statistics_trading_pair" ON "public"."risk_statistics" ("trading_pair", "date" DESC);
CREATE INDEX "idx_risk_statistics_rule_category" ON "public"."risk_statistics" ("rule_category", "date" DESC);

-- 复合维度索引
CREATE INDEX "idx_risk_statistics_multi_dimension" ON "public"."risk_statistics" 
  ("date", "strategy_type", "trading_pair", "rule_category");

-- 全局统计索引（最常用的查询）
CREATE INDEX "idx_risk_statistics_global" ON "public"."risk_statistics" 
  ("date" DESC) WHERE strategy_type IS NULL AND trading_pair IS NULL AND rule_category IS NULL;

-- 日级别统计索引
CREATE INDEX "idx_risk_statistics_daily" ON "public"."risk_statistics" 
  ("date" DESC, "strategy_type") WHERE hour IS NULL;

-- 小时级别统计索引
CREATE INDEX "idx_risk_statistics_hourly" ON "public"."risk_statistics" 
  ("date" DESC, "hour", "strategy_type") WHERE hour IS NOT NULL;

-- 性能分析索引
CREATE INDEX "idx_risk_statistics_performance" ON "public"."risk_statistics" 
  ("average_processing_time_ms", "date" DESC);

CREATE INDEX "idx_risk_statistics_efficiency" ON "public"."risk_statistics" 
  ("rule_execution_efficiency" DESC, "date" DESC);

-- 风险分析索引
CREATE INDEX "idx_risk_statistics_risk_analysis" ON "public"."risk_statistics" 
  ("average_risk_score" DESC, "total_violations" DESC, "date" DESC);

CREATE INDEX "idx_risk_statistics_critical_violations" ON "public"."risk_statistics" 
  ("critical_violations" DESC, "date" DESC) WHERE critical_violations > 0;

-- 业务价值索引
CREATE INDEX "idx_risk_statistics_business_value" ON "public"."risk_statistics" 
  ("potential_loss_prevented" DESC, "blocked_order_value" DESC, "date" DESC);

-- JSONB 字段索引
CREATE INDEX "idx_risk_statistics_top_violations_gin" ON "public"."risk_statistics" USING gin("top_violations");
CREATE INDEX "idx_risk_statistics_rule_performance_gin" ON "public"."risk_statistics" USING gin("rule_performance");
CREATE INDEX "idx_risk_statistics_trend_indicators_gin" ON "public"."risk_statistics" USING gin("trend_indicators");

-- ----------------------------
-- 唯一约束
-- ----------------------------

-- 确保每个维度组合在每个时间点只有一条记录
CREATE UNIQUE INDEX "idx_risk_statistics_unique_daily" ON "public"."risk_statistics" 
  ("date", COALESCE("strategy_type", ''), COALESCE("trading_pair", ''), COALESCE("rule_category", ''))
  WHERE hour IS NULL;

CREATE UNIQUE INDEX "idx_risk_statistics_unique_hourly" ON "public"."risk_statistics" 
  ("date", "hour", COALESCE("strategy_type", ''), COALESCE("trading_pair", ''), COALESCE("rule_category", ''))
  WHERE hour IS NOT NULL;

-- ----------------------------
-- 数据完整性约束
-- ----------------------------

-- 基础数值约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_counts_non_negative" 
  CHECK (
    total_checks >= 0 AND passed_checks >= 0 AND failed_checks >= 0 AND 
    warning_checks >= 0 AND error_checks >= 0 AND total_violations >= 0
  );

ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_violation_counts_non_negative" 
  CHECK (
    critical_violations >= 0 AND high_violations >= 0 AND 
    medium_violations >= 0 AND low_violations >= 0
  );

-- 逻辑约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_checks_sum_consistency" 
  CHECK (passed_checks + failed_checks + warning_checks + error_checks <= total_checks);

ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_violations_sum_consistency" 
  CHECK (critical_violations + high_violations + medium_violations + low_violations <= total_violations);

-- 百分比范围约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_percentage_ranges" 
  CHECK (
    false_positive_rate >= 0 AND false_positive_rate <= 100 AND
    cache_hit_rate >= 0 AND cache_hit_rate <= 100 AND
    rule_execution_efficiency >= 0 AND rule_execution_efficiency <= 100 AND
    system_load_impact >= 0 AND system_load_impact <= 100 AND
    data_quality_score >= 0 AND data_quality_score <= 100
  );

-- 风险评分约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_risk_score_ranges" 
  CHECK (
    average_risk_score >= 0 AND average_risk_score <= 100 AND
    max_risk_score >= 0 AND max_risk_score <= 100 AND
    min_risk_score >= 0 AND min_risk_score <= 100 AND
    risk_score_std_dev >= 0
  );

ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_risk_score_logic" 
  CHECK (min_risk_score <= average_risk_score AND average_risk_score <= max_risk_score);

-- 时间约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_hour_range" 
  CHECK (hour IS NULL OR (hour >= 0 AND hour <= 23));

ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_processing_time_positive" 
  CHECK (
    average_processing_time_ms >= 0 AND max_processing_time_ms >= 0 AND 
    min_processing_time_ms >= 0 AND total_processing_time_ms >= 0
  );

-- 金额约束
ALTER TABLE "public"."risk_statistics" ADD CONSTRAINT "check_amounts_non_negative" 
  CHECK (
    total_order_value >= 0 AND blocked_order_value >= 0 AND potential_loss_prevented >= 0
  );

-- ----------------------------
-- 触发器：自动更新 updated_at
-- ----------------------------
CREATE OR REPLACE FUNCTION update_risk_statistics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_risk_statistics_updated_at
    BEFORE UPDATE ON "public"."risk_statistics"
    FOR EACH ROW
    EXECUTE FUNCTION update_risk_statistics_updated_at();

-- ----------------------------
-- 聚合函数：自动生成统计数据
-- ----------------------------
CREATE OR REPLACE FUNCTION aggregate_daily_risk_statistics(target_date DATE DEFAULT CURRENT_DATE)
RETURNS INTEGER AS $$
DECLARE
    records_processed INTEGER := 0;
BEGIN
    -- 删除当天已有的统计数据
    DELETE FROM risk_statistics WHERE date = target_date AND hour IS NULL;
    
    -- 生成全局日统计
    INSERT INTO risk_statistics (
        date, strategy_type, trading_pair, rule_category,
        total_checks, passed_checks, failed_checks, warning_checks, error_checks,
        average_risk_score, max_risk_score, min_risk_score,
        average_processing_time_ms, max_processing_time_ms, min_processing_time_ms,
        total_violations, critical_violations, high_violations, medium_violations, low_violations,
        last_aggregated_at
    )
    SELECT 
        target_date,
        NULL, NULL, NULL,
        COUNT(*),
        COUNT(*) FILTER (WHERE result = 'passed'),
        COUNT(*) FILTER (WHERE result = 'failed'),
        COUNT(*) FILTER (WHERE result = 'warning'),
        COUNT(*) FILTER (WHERE result = 'error'),
        AVG(risk_score),
        MAX(risk_score),
        MIN(risk_score),
        AVG(processing_time_ms),
        MAX(processing_time_ms),
        MIN(processing_time_ms),
        (SELECT COUNT(*) FROM risk_violations rv 
         JOIN risk_checks rc ON rv.risk_check_id = rc.id 
         WHERE DATE(rc.timestamp) = target_date),
        (SELECT COUNT(*) FROM risk_violations rv 
         JOIN risk_checks rc ON rv.risk_check_id = rc.id 
         WHERE DATE(rc.timestamp) = target_date AND rv.severity = 'critical'),
        (SELECT COUNT(*) FROM risk_violations rv 
         JOIN risk_checks rc ON rv.risk_check_id = rc.id 
         WHERE DATE(rc.timestamp) = target_date AND rv.severity = 'high'),
        (SELECT COUNT(*) FROM risk_violations rv 
         JOIN risk_checks rc ON rv.risk_check_id = rc.id 
         WHERE DATE(rc.timestamp) = target_date AND rv.severity = 'medium'),
        (SELECT COUNT(*) FROM risk_violations rv 
         JOIN risk_checks rc ON rv.risk_check_id = rc.id 
         WHERE DATE(rc.timestamp) = target_date AND rv.severity = 'low'),
        NOW()
    FROM risk_checks 
    WHERE DATE(timestamp) = target_date;
    
    GET DIAGNOSTICS records_processed = ROW_COUNT;
    
    RETURN records_processed;
END;
$$ LANGUAGE plpgsql;

-- ----------------------------
-- 权限设置
-- ----------------------------
ALTER TABLE "public"."risk_statistics" OWNER TO "neondb_owner";
