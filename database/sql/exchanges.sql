/*
 Navicat Premium Data Transfer

 Source Server         : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : ep-crimson-field-a15cqruu-pooler.ap-southeast-1.aws.neon.tech:5432
 Source Catalog        : neondb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 27/06/2025 11:09:01
*/


-- ----------------------------
-- Table structure for exchanges
-- ----------------------------
DROP TABLE IF EXISTS "public"."exchanges";
CREATE TABLE "public"."exchanges" (
  "id" int4 NOT NULL DEFAULT nextval('exchanges_id_seq'::regclass),
  "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "display_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "status" "public"."exchange_status" NOT NULL DEFAULT 'Active'::exchange_status,
  "api_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "websocket_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "withdrawal_fee_rate" numeric(10,6) DEFAULT 0.0005,
  "min_order_amount" numeric(20,8),
  "max_order_amount" numeric(20,8),
  "supported_order_types" "public"."order_type"[] DEFAULT ARRAY['Market'::order_type, 'Limit'::order_type],
  "config" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."exchanges" OWNER TO "neondb_owner";

-- ----------------------------
-- Records of exchanges
-- ----------------------------
BEGIN;
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (1, 'binance', 'Binance', 'Active', 'https://api.binance.com', 'wss://stream.binance.com:9443', 0.001000, 0.000500, NULL, NULL, '{Market,Limit,StopLoss,StopLimit}', '{"rate_limit": 1200, "weight_limit": 6000}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (2, 'coinbase', 'Coinbase Pro', 'Active', 'https://api.pro.coinbase.com', 'wss://ws-feed.pro.coinbase.com', 0.005000, 0.002500, NULL, NULL, '{Market,Limit}', '{"sandbox": "https://api-public.sandbox.pro.coinbase.com", "rate_limit": 10}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (3, 'kraken', 'Kraken', 'Active', 'https://api.kraken.com', 'wss://ws.kraken.com', 0.002600, 0.000500, NULL, NULL, '{Market,Limit,StopLoss}', '{"tier": "starter", "rate_limit": 1}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
INSERT INTO "public"."exchanges" ("id", "name", "display_name", "status", "api_endpoint", "websocket_endpoint", "trading_fee_rate", "withdrawal_fee_rate", "min_order_amount", "max_order_amount", "supported_order_types", "config", "created_at", "updated_at") VALUES (4, 'simulator', 'Simulator', 'Active', 'http://localhost:8080/api/simulator', 'ws://localhost:8080/ws/simulator', 0.000000, 0.000000, NULL, NULL, '{Market,Limit,StopLoss,StopLimit}', '{"simulated": true, "initial_balance": 100000}', '2025-06-10 02:51:41.071066+00', '2025-06-10 02:51:41.071066+00');
COMMIT;

-- ----------------------------
-- Indexes structure for table exchanges
-- ----------------------------
CREATE INDEX "idx_exchanges_name" ON "public"."exchanges" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_exchanges_status" ON "public"."exchanges" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table exchanges
-- ----------------------------
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table exchanges
-- ----------------------------
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_pkey" PRIMARY KEY ("id");
