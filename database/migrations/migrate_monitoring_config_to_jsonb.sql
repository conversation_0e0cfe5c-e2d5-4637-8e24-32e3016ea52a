-- ============================================================================
-- 监控配置数据迁移脚本
-- ============================================================================
-- 
-- 文件: migrate_monitoring_config_to_jsonb.sql
-- 描述: 将监控配置从 system_config 表迁移到专用的 monitoring_config 表
-- 版本: v1.0
-- 创建时间: 2025-06-30
-- 
-- 迁移步骤:
-- 1. 备份现有监控配置数据
-- 2. 创建新的监控配置表结构
-- 3. 迁移数据到新表
-- 4. 验证数据完整性
-- 5. 清理旧数据（可选）
-- 
-- ============================================================================

-- 开始事务
BEGIN;

-- ============================================================================
-- 第一步：备份现有监控配置数据
-- ============================================================================

-- 创建备份表
CREATE TABLE IF NOT EXISTS monitoring_config_backup AS
SELECT 
    key,
    value,
    description,
    created_at,
    updated_at
FROM system_config 
WHERE key LIKE 'monitoring.%';

-- 记录备份信息
INSERT INTO audit_logs (
    table_name,
    operation,
    old_values,
    new_values,
    changed_by,
    changed_at
) VALUES (
    'monitoring_config_backup',
    'BACKUP',
    '{}',
    jsonb_build_object(
        'backup_count', (SELECT COUNT(*) FROM monitoring_config_backup),
        'backup_time', NOW()
    ),
    'migration_script',
    NOW()
);

-- ============================================================================
-- 第二步：创建新的监控配置表结构（如果不存在）
-- ============================================================================

-- 执行监控配置表创建脚本
-- 注意：这里假设 monitoring_config.sql 已经执行过
-- 如果没有，请先执行该脚本

-- 验证表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'monitoring_config') THEN
        RAISE EXCEPTION '监控配置表不存在，请先执行 monitoring_config.sql 脚本';
    END IF;
END $$;

-- ============================================================================
-- 第三步：迁移数据到新表
-- ============================================================================

-- 构建完整的监控配置 JSON
WITH monitoring_data AS (
    SELECT 
        jsonb_object_agg(
            REPLACE(key, 'monitoring.', ''),
            CASE 
                WHEN value::text ~ '^[0-9]+$' THEN value::text::int::jsonb
                WHEN value::text ~ '^[0-9]+\.[0-9]+$' THEN value::text::numeric::jsonb
                WHEN value::text IN ('true', 'false') THEN value::text::boolean::jsonb
                ELSE value
            END
        ) as config_data
    FROM system_config 
    WHERE key LIKE 'monitoring.%'
),
enhanced_config AS (
    SELECT 
        jsonb_build_object(
            'name', 'migrated_from_system_config',
            'description', '从 system_config 表迁移的监控配置',
            'enabled', true,
            'metrics_enabled', COALESCE((config_data->>'metrics_enabled')::boolean, true),
            'health_check_interval', COALESCE((config_data->>'health_check_interval')::int, 60),
            'data_collection_interval', 30,
            'data_retention_days', 30,
            'cpu_thresholds', jsonb_build_object(
                'warning', COALESCE((config_data->>'alert_threshold_cpu')::int, 70),
                'critical', COALESCE((config_data->>'alert_threshold_cpu')::int + 15, 85),
                'check_interval', 60
            ),
            'memory_thresholds', jsonb_build_object(
                'warning', COALESCE((config_data->>'alert_threshold_memory')::int, 75),
                'critical', COALESCE((config_data->>'alert_threshold_memory')::int + 15, 90),
                'check_interval', 60
            ),
            'disk_thresholds', jsonb_build_object(
                'warning', 80,
                'critical', 95,
                'check_interval', 300
            ),
            'network_thresholds', jsonb_build_object(
                'latency_warning', 100.0,
                'latency_critical', 500.0,
                'bandwidth_warning', 80,
                'bandwidth_critical', 95,
                'check_interval', 60
            ),
            'database_thresholds', jsonb_build_object(
                'connection_pool_warning', 70,
                'connection_pool_critical', 90,
                'query_time_warning', 1000.0,
                'query_time_critical', 5000.0,
                'slow_query_threshold', 1000.0,
                'check_interval', 60
            ),
            'api_thresholds', jsonb_build_object(
                'response_time_warning', 500.0,
                'response_time_critical', 2000.0,
                'error_rate_warning', 5.0,
                'error_rate_critical', 10.0,
                'throughput_minimum', 10.0,
                'check_interval', 60
            ),
            'alert_rules', '[]'::jsonb,
            'notification_channels', '[]'::jsonb,
            'alert_suppression', jsonb_build_object(
                'enabled', true,
                'default_duration', 300,
                'max_duration', 3600,
                'rules', '[]'::jsonb
            ),
            'performance_monitoring', jsonb_build_object(
                'enabled', true,
                'monitoring_interval', 60,
                'data_retention_days', 30,
                'detailed_metrics', false,
                'sampling_rate', 1.0,
                'benchmarks', jsonb_build_object(
                    'api_response_time_baseline', 200.0,
                    'db_query_time_baseline', 100.0,
                    'memory_usage_baseline', 50,
                    'cpu_usage_baseline', 30
                )
            ),
            'slow_query_monitoring', jsonb_build_object(
                'enabled', true,
                'threshold_ms', 1000,
                'log_query_details', true,
                'log_execution_plan', false,
                'log_retention_days', 7,
                'max_records', 1000
            ),
            'health_check', jsonb_build_object(
                'enabled', true,
                'check_interval', 30,
                'timeout_seconds', 10,
                'retry_count', 3,
                'endpoints', '[]'::jsonb
            )
        ) as monitoring_parameters
    FROM monitoring_data
)
-- 插入迁移的配置
INSERT INTO monitoring_config (
    name,
    description,
    enabled,
    monitoring_parameters,
    created_by
)
SELECT 
    'migrated_from_system_config',
    '从 system_config 表迁移的监控配置',
    true,
    monitoring_parameters,
    'migration_script'
FROM enhanced_config
WHERE NOT EXISTS (
    SELECT 1 FROM monitoring_config 
    WHERE name = 'migrated_from_system_config'
);

-- ============================================================================
-- 第四步：验证数据完整性
-- ============================================================================

-- 验证迁移结果
DO $$
DECLARE
    old_count INTEGER;
    new_count INTEGER;
    migrated_config JSONB;
BEGIN
    -- 检查原始数据数量
    SELECT COUNT(*) INTO old_count 
    FROM system_config 
    WHERE key LIKE 'monitoring.%';
    
    -- 检查新表数据数量
    SELECT COUNT(*) INTO new_count 
    FROM monitoring_config 
    WHERE name = 'migrated_from_system_config';
    
    -- 获取迁移的配置
    SELECT monitoring_parameters INTO migrated_config
    FROM monitoring_config 
    WHERE name = 'migrated_from_system_config'
    LIMIT 1;
    
    -- 记录迁移结果
    RAISE NOTICE '迁移完成统计:';
    RAISE NOTICE '- 原始监控配置项数量: %', old_count;
    RAISE NOTICE '- 迁移后配置记录数量: %', new_count;
    RAISE NOTICE '- 迁移配置大小: % bytes', LENGTH(migrated_config::text);
    
    -- 验证关键配置项
    IF migrated_config ? 'cpu_thresholds' AND migrated_config ? 'memory_thresholds' THEN
        RAISE NOTICE '✅ 关键配置项验证通过';
    ELSE
        RAISE EXCEPTION '❌ 关键配置项验证失败';
    END IF;
    
    -- 记录到审计日志
    INSERT INTO audit_logs (
        table_name,
        operation,
        old_values,
        new_values,
        changed_by,
        changed_at
    ) VALUES (
        'monitoring_config',
        'MIGRATION',
        jsonb_build_object('old_count', old_count),
        jsonb_build_object(
            'new_count', new_count,
            'config_size', LENGTH(migrated_config::text)
        ),
        'migration_script',
        NOW()
    );
END $$;

-- ============================================================================
-- 第五步：清理旧数据（可选 - 默认注释掉）
-- ============================================================================

-- 注意：以下清理操作默认被注释掉，需要手动确认后执行

/*
-- 删除 system_config 表中的监控配置项
DELETE FROM system_config WHERE key LIKE 'monitoring.%';

-- 记录清理操作
INSERT INTO audit_logs (
    table_name,
    operation,
    old_values,
    new_values,
    changed_by,
    changed_at
) VALUES (
    'system_config',
    'CLEANUP',
    jsonb_build_object('deleted_keys', 'monitoring.*'),
    '{}',
    'migration_script',
    NOW()
);

RAISE NOTICE '✅ 旧监控配置数据已清理';
*/

-- 提交事务
COMMIT;

-- ============================================================================
-- 迁移完成提示
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 监控配置迁移完成！';
    RAISE NOTICE '📋 迁移摘要:';
    RAISE NOTICE '   - 备份表: monitoring_config_backup';
    RAISE NOTICE '   - 新配置表: monitoring_config';
    RAISE NOTICE '   - 迁移配置名称: migrated_from_system_config';
    RAISE NOTICE '⚠️  注意: 旧数据仍保留在 system_config 表中';
    RAISE NOTICE '   如需清理，请手动执行清理脚本';
END $$;
