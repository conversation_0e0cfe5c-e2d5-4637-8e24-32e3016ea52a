-- ============================================================================
-- 交易配置迁移脚本：从分散存储迁移到 JSONB 存储
-- ============================================================================
--
-- 此脚本将现有的交易配置从 system_config 表的分散键值对存储
-- 迁移到新的 trading_config 表的 JSONB 存储格式
--
-- 创建时间: 2025-06-30
-- 作者: Claude 4.0 sonnet
-- 版本: v1.0
-- ============================================================================

-- 开始事务
BEGIN;

-- 1. 创建备份表
CREATE TABLE IF NOT EXISTS trading_config_backup AS 
SELECT * FROM system_config WHERE key LIKE 'trading.%';

-- 2. 验证备份数据
DO $$
DECLARE
    backup_count INTEGER;
    original_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO backup_count FROM trading_config_backup;
    SELECT COUNT(*) INTO original_count FROM system_config WHERE key LIKE 'trading.%';
    
    IF backup_count != original_count THEN
        RAISE EXCEPTION 'Backup verification failed: backup_count=%, original_count=%', backup_count, original_count;
    END IF;
    
    RAISE NOTICE 'Backup created successfully with % records', backup_count;
END $$;

-- 3. 确保 trading_config 表存在
-- (假设已经通过 trading_config.sql 创建)

-- 4. 提取现有交易配置数据
DO $$
DECLARE
    max_orders_per_strategy INTEGER;
    max_position_size TEXT;
    default_order_timeout INTEGER;
    min_order_interval INTEGER;
    max_slippage_percent NUMERIC;
    config_json JSONB;
    existing_count INTEGER;
BEGIN
    -- 检查是否已有数据
    SELECT COUNT(*) INTO existing_count FROM trading_config;
    
    IF existing_count > 0 THEN
        RAISE NOTICE 'Trading config table already has % records, skipping migration', existing_count;
        RETURN;
    END IF;
    
    -- 提取各个配置值
    SELECT value::text::integer INTO max_orders_per_strategy 
    FROM system_config WHERE key = 'trading.max_orders_per_strategy';
    
    SELECT value::text INTO max_position_size 
    FROM system_config WHERE key = 'trading.max_position_size';
    
    SELECT value::text::integer INTO default_order_timeout 
    FROM system_config WHERE key = 'trading.default_order_timeout';
    
    SELECT value::text::integer INTO min_order_interval 
    FROM system_config WHERE key = 'trading.min_order_interval';
    
    SELECT value::text::numeric INTO max_slippage_percent 
    FROM system_config WHERE key = 'trading.max_slippage_percent';
    
    -- 使用默认值如果某些配置不存在
    max_orders_per_strategy := COALESCE(max_orders_per_strategy, 100);
    max_position_size := COALESCE(max_position_size, '1000000');
    default_order_timeout := COALESCE(default_order_timeout, 3600);
    min_order_interval := COALESCE(min_order_interval, 1);
    max_slippage_percent := COALESCE(max_slippage_percent, 0.5);
    
    -- 构建 JSONB 配置
    config_json := jsonb_build_object(
        'max_orders_per_strategy', max_orders_per_strategy,
        'max_position_size', max_position_size,
        'default_order_timeout', default_order_timeout,
        'min_order_interval', min_order_interval,
        'max_slippage_percent', max_slippage_percent
    );
    
    -- 插入迁移后的配置
    INSERT INTO trading_config (
        name,
        description,
        enabled,
        trading_parameters,
        created_by
    ) VALUES (
        'migrated_default',
        '从 system_config 迁移的默认交易配置',
        true,
        config_json,
        'migration_script'
    );
    
    RAISE NOTICE 'Successfully migrated trading config: %', config_json;
END $$;

-- 5. 验证迁移结果
DO $$
DECLARE
    migrated_config JSONB;
    original_max_orders INTEGER;
    migrated_max_orders INTEGER;
BEGIN
    -- 获取迁移后的配置
    SELECT trading_parameters INTO migrated_config 
    FROM trading_config 
    WHERE name = 'migrated_default';
    
    -- 获取原始配置
    SELECT value::text::integer INTO original_max_orders 
    FROM system_config 
    WHERE key = 'trading.max_orders_per_strategy';
    
    -- 获取迁移后的配置值
    migrated_max_orders := (migrated_config->>'max_orders_per_strategy')::integer;
    
    -- 验证关键字段
    IF original_max_orders IS NOT NULL AND original_max_orders != migrated_max_orders THEN
        RAISE EXCEPTION 'Migration validation failed: max_orders_per_strategy mismatch (original=%, migrated=%)', 
                       original_max_orders, migrated_max_orders;
    END IF;
    
    RAISE NOTICE 'Migration validation passed';
    RAISE NOTICE 'Migrated config: %', jsonb_pretty(migrated_config);
END $$;

-- 6. 创建清理脚本（注释掉，需要手动执行）
/*
-- 清理旧的交易配置数据（谨慎执行！）
-- DELETE FROM system_config WHERE key LIKE 'trading.%';

-- 删除备份表（在确认迁移成功后执行）
-- DROP TABLE IF EXISTS trading_config_backup;
*/

-- 7. 显示迁移摘要
SELECT 
    'Migration Summary' as status,
    (SELECT COUNT(*) FROM trading_config_backup) as backed_up_records,
    (SELECT COUNT(*) FROM trading_config WHERE created_by = 'migration_script') as migrated_records,
    (SELECT COUNT(*) FROM system_config WHERE key LIKE 'trading.%') as remaining_old_records;

-- 提交事务
COMMIT;

-- 显示最终结果
SELECT 
    id,
    name,
    description,
    enabled,
    jsonb_pretty(trading_parameters) as config,
    created_at,
    created_by
FROM trading_config 
WHERE created_by = 'migration_script';

RAISE NOTICE '=== 迁移完成 ===';
RAISE NOTICE '1. 请验证迁移结果';
RAISE NOTICE '2. 测试新的交易配置服务';
RAISE NOTICE '3. 确认无误后，可以清理旧数据：';
RAISE NOTICE '   DELETE FROM system_config WHERE key LIKE ''trading.%'';';
RAISE NOTICE '4. 删除备份表：';
RAISE NOTICE '   DROP TABLE trading_config_backup;';
