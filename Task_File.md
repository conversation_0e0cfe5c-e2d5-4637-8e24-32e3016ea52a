# Context
Filename: Task_File.md
Created On: 2025/7/3 下午4:38:14 (Asia/Shanghai, UTC+8:00)
Created By: AI
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
逐一所有`TODO`：对接真实数据库，并确认数据结构是否可以支撑功能，我怀疑数据库也有字段不全，表不完整的问题

# Project Overview
SigmaX 是一个交易平台，涉及风险管理、交易执行、数据分析等多个模块。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 🎯 项目架构深度分析

### 系统概述
SigmaX是一个高性能Rust量化交易系统，采用模块化微服务架构。系统专注于网格交易策略和多交易所支持，完整支持回测、实盘交易和策略管理。

### 核心技术栈
- **语言与运行时**: Rust 1.70+ + Tokio异步运行时
- **数据库**: PostgreSQL 13+ (主数据库) + Redis (缓存)
- **Web框架**: Axum (REST API) + WebSocket (实时推送)
- **ORM**: SQLx + Diesel (双ORM支持)
- **序列化**: serde + serde_json
- **构建系统**: Cargo workspace (18个模块)

### 🏗️ 模块架构分析

#### 核心层 (Foundation Layer)
- **core**: 基础类型、枚举、错误处理、配置管理、事件系统
- **database**: 数据持久化、连接池、迁移管理、ORM模型

#### 数据层 (Data Layer)  
- **data**: 市场数据处理、数据提供者、存储管理
- **exchange**: 多交易所接口、模拟器、工厂模式

#### 业务层 (Business Layer)
- **strategies**: 交易策略实现（网格、DCA、套利等）
- **engines**: 交易引擎（回测引擎、实盘引擎）
- **execution**: 订单执行管理、路由器
- **risk**: 风险控制系统、规则引擎
- **portfolio**: 投资组合管理、资产跟踪

#### 应用层 (Application Layer)
- **web**: REST API、WebSocket、Web管理界面
- **reporting**: 性能分析、报告生成
- **performance_monitor**: 系统监控、指标收集

### 📊 数据库架构分析

#### 核心数据表
```sql
-- 核心交易数据
trading_pairs: 交易对配置 (symbol, base_asset, quote_asset)
orders: 订单管理 (UUID主键, 状态, 价格, 数量, 交易所ID)
trades: 交易记录 (order_id关联, 执行价格, 手续费)

-- 投资组合数据  
portfolios: 投资组合 (initial_capital, current_capital, pnl)
positions: 持仓管理 (数量, 平均价格, 未实现盈亏)
balances: 资产余额 (free, locked按交易所和资产)

-- 策略管理
strategies: 策略配置 (name, type, config JSONB, status)
engines: 引擎实例 (backtest/live配置)

-- 风险控制
risk_rules: 风险规则定义
risk_violations: 风险违规记录
audit_logs: 审计日志
```

#### 数据类型设计
- **精度控制**: 使用 `numeric(20,8)` 确保交易精度
- **UUID主键**: 分布式系统友好的唯一标识
- **JSONB配置**: 灵活的配置存储和查询
- **枚举类型**: 强类型的状态和类型约束
- **时间戳**: 完整的时间跟踪和审计

### ⚡ 系统架构特点

#### 1. 高性能设计
- **异步架构**: 全系统基于Tokio异步运行时
- **分层缓存**: L1内存缓存 + L2 Redis缓存  
- **连接池**: 数据库连接池优化
- **批量处理**: 支持高效的批量操作

#### 2. 模块化设计
- **清晰边界**: 每个模块职责明确，接口清晰
- **松耦合**: 模块间通过trait交互，降低耦合度
- **依赖注入**: 工厂模式和IoC容器支持
- **可扩展**: 支持新模块和新功能的轻松添加

#### 3. 风险控制系统
- **统一门面**: RiskServiceFacade作为唯一入口
- **适配器模式**: 回测/实盘/WebAPI/策略专用适配器
- **规则引擎**: 灵活的风险规则配置和执行
- **实时监控**: 持仓、订单、策略的实时风险检查

### 🔍 当前项目状态

#### 开发完成度
- ✅ **阶段1-5**: 核心功能100%完成
- ✅ **基础设施**: 项目结构、核心模块
- ✅ **核心服务**: 数据存储、缓存、事件系统  
- ✅ **Web界面**: REST API、Web服务器
- ✅ **交易引擎**: 回测引擎、实盘引擎
- ✅ **策略系统**: 网格策略、策略管理

#### 测试与质量
- **单元测试**: 61/61 测试通过 (100%)
- **编译状态**: 整个workspace编译成功
- **代码质量**: 模块化设计，清晰的架构层次

## 🚧 待完成工作分析 (基于现有TODO)

### 核心问题识别
当前主要挑战是**真实数据库对接**和**数据结构完整性验证**。系统架构完整，但需要确保数据库模型能够支撑所有业务功能。

### TODO分类汇总

### engines/src/paper.rs

*   **TODO: 重新设计PortfolioManager接口以支持内部可变性** (line 99)
    *   需要重新设计 `PortfolioManager` 接口，以便支持内部可变性。
    *   数据结构：需要考虑如何修改 `PortfolioManager` 的数据结构。

*   **TODO: 使用 strategy_manager 获取当前策略** (line 247, 274)
    *   需要使用 `strategy_manager` 获取当前策略。
    *   数据结构：需要确认 `strategy_manager` 中存储策略的表和字段。

*   **TODO: 实现策略设置逻辑** (line 336)
    *   需要实现策略设置逻辑。
    *   数据结构：需要确认如何存储策略设置信息。

*   **TODO: 实现策略移除逻辑** (line 343)
    *   需要实现策略移除逻辑。
    *   数据结构：需要确认如何删除策略设置信息。

*   **TODO: 实现获取当前策略逻辑** (line 349)
    *   需要实现获取当前策略逻辑。
    *   数据结构：需要确认如何获取当前策略信息。

*   **TODO: 实现检查策略逻辑** (line 355)
    *   需要实现检查策略逻辑。
    *   数据结构：需要确认如何检查策略信息。

# Proposed Solution (Populated by INNOVATE mode)

### engines/src/live.rs

*   **TODO: 实现获取特定订单的交易记录** (line 199)
    *   需要实现获取特定订单的交易记录。
    *   数据结构：需要确认 `Exchange` trait 中是否包含获取订单交易记录的方法，以及如何存储交易记录。

*   **TODO: 处理策略信号** (line 224)
    *   需要处理策略信号，获取策略生成的订单。
    *   数据结构：需要确认策略信号的数据结构，以及如何将信号转换为订单。

*   **TODO: 获取实时市场数据, 将市场数据传递给策略** (line 243, 246)
    *   需要获取实时市场数据，并将数据传递给策略。
    *   数据结构：需要确认市场数据的数据结构，以及策略如何接收数据。

*   **TODO: 更新投资组合管理器的余额信息** (line 263)
    *   需要更新投资组合管理器的余额信息。
    *   数据结构：需要确认 `PortfolioManager` 中余额信息的数据结构。

*   **TODO: 初始化策略** (line 372)
    *   需要初始化策略。
    *   数据结构：需要确认策略初始化所需的数据结构。

*   **TODO: 停止策略** (line 394)
    *   需要停止策略。
    *   数据结构：需要确认停止策略所需的数据结构。

### execution/src/router.rs

*   **TODO: 实现更复杂的选择逻辑** (line 39)
    *   需要实现更复杂的交易所选择逻辑。
    *   数据结构：需要确认如何存储和评估交易所的选择条件。

### web/src/migration_api.rs

*   **TODO: 实现迁移管理模块** (line 14)
    *   需要实现数据库迁移管理模块。
    *   数据结构：需要确认数据库迁移的数据结构和管理方式。

### web/src/services/config_service.rs

*   **TODO: 后续改造 - 只保留系统相关配置，其他配置由专门模块负责** (line 54)
    *   需要将配置拆分到不同的模块中，只保留系统相关配置。
    *   数据结构：需要确认如何组织和存储不同类型的配置。

*   **TODO: 后续改造 - 这里需要拆分配置类型** (line 58)
    *   需要拆分配置类型，预加载所有强类型配置。
    *   数据结构：需要确认如何定义和存储不同类型的配置。

*   **TODO: 后续改造 - 复杂配置应该由各自模块负责预加载** (line 93)
    *   复杂配置应该由各自模块负责预加载，例如策略模板和交易所配置。
    *   数据结构：需要确认如何组织和存储复杂配置。

*   **TODO: 后续改造 - 这个方法需要重构，只处理系统相关配置** (line 522)
    *   需要重构该方法，只处理系统相关配置。
    *   数据结构：需要确认如何区分系统配置和其他配置。

*   **TODO: 后续改造 - 这些复杂配置应该由各自模块负责** (line 582)
    *   这些复杂配置应该由各自模块负责，例如策略模板应该由策略模块负责。
    *   数据结构：需要确认如何将配置与模块关联。

### web/src/handlers/market_data_handlers.rs

*   **TODO: 从实际市场数据源获取价格** (line 232)
    *   需要从实际市场数据源获取价格数据。
    *   数据结构：需要确认市场数据源返回的价格数据结构，并将其映射到 `TickerData` 结构体。

*   **TODO: 从实际市场数据源获取订单簿** (line 265)
    *   需要从实际市场数据源获取订单簿数据。
    *   数据结构：需要确认市场数据源返回的订单簿数据结构，并将其映射到 `OrderBookData` 结构体。

*   **TODO: 从实际市场数据源获取交易数据** (line 308)
    *   需要从实际市场数据源获取交易数据。
    *   数据结构：需要确认市场数据源返回的交易数据结构，并将其映射到 `TradeData` 结构体。

*   **TODO: 从实际市场数据源获取K线数据** (line 337)
    *   需要从实际市场数据源获取K线数据。
    *   数据结构：需要确认市场数据源返回的K线数据结构，并将其映射到 `CandleData` 结构体。

*   **TODO: 从实际市场数据源获取成交量数据** (line 391)
    *   需要从实际市场数据源获取成交量数据。
    *   数据结构：需要确认市场数据源返回的成交量数据结构，并将其映射到 `VolumeData` 结构体。

*   **TODO: 从实际市场数据源计算波动率** (line 433)
    *   需要从实际市场数据源计算波动率。
    *   数据结构：需要确认市场数据源返回的数据结构，以及如何计算波动率。

*   **TODO: 从实际市场数据计算相关性** (line 457)
    *   需要从实际市场数据计算相关性。
    *   数据结构：需要确认市场数据源返回的数据结构，以及如何计算相关性。

*   **TODO: 从实际数据源获取市场情绪** (line 503)
    *   需要从实际数据源获取市场情绪。
    *   数据结构：需要确认市场数据源返回的市场情绪数据结构，并将其映射到 `SocialSentiment` 结构体。

### web/src/handlers/portfolio_handlers.rs

*   **TODO: 从数据库获取实际的投资组合数据** (line 120)
    *   需要从数据库获取实际的投资组合数据。
    *   数据结构：需要确认数据库中存储投资组合数据的表和字段，以及如何映射到 `Portfolio` 结构体。

*   **TODO: 保存到数据库** (line 201, 251)
    *   需要将投资组合数据保存到数据库。
    *   数据结构：需要确认数据库中存储投资组合数据的表和字段。

*   **TODO: 从数据库获取实际数据** (line 213)
    *   需要从数据库获取实际的投资组合数据。
    *   数据结构：需要确认数据库中存储投资组合数据的表和字段，以及如何映射到 `Portfolio` 结构体。

*   **TODO: 从数据库获取并更新实际数据** (line 230)
    *   需要从数据库获取并更新实际的投资组合数据。
    *   数据结构：需要确认数据库中存储投资组合数据的表和字段，以及如何映射到 `Portfolio` 结构体。

*   **TODO: 检查投资组合是否存在, 检查是否有未平仓的持仓, 从数据库删除** (line 263, 264, 265)
    *   需要检查投资组合是否存在，检查是否有未平仓的持仓，并从数据库删除。
    *   数据结构：需要确认数据库中存储投资组合和持仓数据的表和字段。

*   **TODO: 从数据库获取实际余额数据** (line 277)
    *   需要从数据库获取实际的余额数据。
    *   数据结构：需要确认数据库中存储余额数据的表和字段，以及如何映射到 `AssetBalance` 结构体。

*   **TODO: 计算实际的性能指标** (line 289)
    *   需要计算实际的性能指标。
    *   数据结构：需要确认数据库中存储交易记录和持仓数据的表和字段，以及如何计算性能指标。

*   **TODO: 实现实际的重新平衡逻辑** (line 308)
    *   需要实现实际的重新平衡逻辑。
    *   数据结构：需要确认数据库中存储投资组合和目标配置数据的表和字段。

### web/src/handlers/risk_handlers.rs

*   **TODO: 调用服务层方法来实现创建规则的完整数据库逻辑** (line 133)
    *   需要调用服务层方法来实现创建规则的完整数据库逻辑。
    *   数据结构：需要确认服务层方法需要的数据结构，以及数据库中存储规则数据的表和字段。

*   **TODO: 实现删除规则的完整数据库逻辑** (line 181)
    *   需要实现删除规则的完整数据库逻辑。
    *   数据结构：需要确认数据库中存储规则数据的表和字段。
[此处将列出不同的解决方案，并进行评估]

# Implementation Plan (Generated by PLAN mode)
[此处将生成详细的实施计划]

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "[Step number and name]"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   [DateTime]
    *   Step: [Checklist item number and description]
    *   Modifications: [List of file and code changes, including reported minor deviation corrections]
    *   Reason: [Executing plan step [X]]
    *   Blockers: [Any issues encountered, or None]
    *   User Confirmation Status: [Success / Success with minor issues / Failure]
*   [DateTime]
    *   Step: ...

# Final Review (Populated by REVIEW mode)
[Summary of implementation compliance assessment against the final plan, whether unreported deviations were found]