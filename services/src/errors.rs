//! 服务模块错误定义

use thiserror::Error;

/// 服务错误类型
#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("缓存错误: {0}")]
    Cache(String),
    
    #[error("指标收集错误: {0}")]
    Metrics(String),
    
    #[error("配置错误: {0}")]
    Config(String),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Redis错误: {0}")]
    Redis(#[from] redis::RedisError),
    
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("配置解析错误: {0}")]
    ConfigParse(#[from] config::ConfigError),
    
    #[error("通用错误: {0}")]
    Generic(String),
}

/// 服务结果类型
pub type ServiceResult<T> = Result<T, ServiceError>;

impl From<String> for ServiceError {
    fn from(msg: String) -> Self {
        ServiceError::Generic(msg)
    }
}

impl From<&str> for ServiceError {
    fn from(msg: &str) -> Self {
        ServiceError::Generic(msg.to_string())
    }
}
