//! 内存缓存服务实现

use async_trait::async_trait;
use core::traits::CacheService;
use moka::future::Cache;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error};

use crate::errors::{ServiceError, ServiceResult};
use super::{CacheStats, ExtendedCacheService};

/// 内存缓存项
#[derive(Debug, Clone)]
struct CacheItem {
    data: String,
    created_at: Instant,
    ttl: Option<Duration>,
}

impl CacheItem {
    fn new(data: String, ttl: Option<Duration>) -> Self {
        Self {
            data,
            created_at: Instant::now(),
            ttl,
        }
    }
    
    fn is_expired(&self) -> bool {
        if let Some(ttl) = self.ttl {
            self.created_at.elapsed() > ttl
        } else {
            false
        }
    }
}

/// 内存缓存服务
pub struct MemoryCacheService {
    cache: Cache<String, CacheItem>,
    stats: Arc<RwLock<CacheStats>>,
    config: MemoryCacheConfig,
}

/// 内存缓存配置
#[derive(Debug, Clone)]
pub struct MemoryCacheConfig {
    pub max_capacity: u64,
    pub time_to_live: Option<Duration>,
    pub time_to_idle: Option<Duration>,
    pub cleanup_interval: Duration,
}

impl Default for MemoryCacheConfig {
    fn default() -> Self {
        Self {
            max_capacity: 10_000,
            time_to_live: Some(Duration::from_secs(3600)), // 1小时
            time_to_idle: Some(Duration::from_secs(1800)), // 30分钟
            cleanup_interval: Duration::from_secs(300),    // 5分钟
        }
    }
}

impl MemoryCacheService {
    /// 创建新的内存缓存服务
    pub fn new(config: MemoryCacheConfig) -> Self {
        let mut cache_builder = Cache::builder()
            .max_capacity(config.max_capacity);
        
        if let Some(ttl) = config.time_to_live {
            cache_builder = cache_builder.time_to_live(ttl);
        }
        
        if let Some(tti) = config.time_to_idle {
            cache_builder = cache_builder.time_to_idle(tti);
        }
        
        let cache = cache_builder.build();
        
        let service = Self {
            cache,
            stats: Arc::new(RwLock::new(CacheStats::new())),
            config,
        };
        
        // 启动清理任务
        service.start_cleanup_task();
        
        service
    }
    
    /// 启动定期清理任务
    fn start_cleanup_task(&self) {
        let cache = self.cache.clone();
        let interval = self.config.cleanup_interval;
        
        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            loop {
                interval_timer.tick().await;
                
                // 触发过期项清理
                cache.run_pending_tasks().await;
                
                debug!("Memory cache cleanup completed");
            }
        });
    }
    
    /// 检查并移除过期项
    async fn check_and_remove_expired(&self, key: &str) -> bool {
        if let Some(item) = self.cache.get(key).await {
            if item.is_expired() {
                self.cache.invalidate(key).await;
                debug!("Removed expired cache item for key: {}", key);
                return true;
            }
        }
        false
    }
}

#[async_trait]
impl CacheService for MemoryCacheService {
    async fn get<T>(&self, key: &str) -> core::errors::SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send,
    {
        let start = Instant::now();
        
        // 检查并移除过期项
        if self.check_and_remove_expired(key).await {
            let elapsed = start.elapsed().as_millis() as f64;
            self.stats.write().await.record_miss(elapsed);
            return Ok(None);
        }
        
        match self.cache.get(key).await {
            Some(item) => {
                if item.is_expired() {
                    self.cache.invalidate(key).await;
                    let elapsed = start.elapsed().as_millis() as f64;
                    self.stats.write().await.record_miss(elapsed);
                    debug!("Cache miss (expired) for key: {}", key);
                    Ok(None)
                } else {
                    match serde_json::from_str::<T>(&item.data) {
                        Ok(deserialized) => {
                            let elapsed = start.elapsed().as_millis() as f64;
                            self.stats.write().await.record_hit(elapsed);
                            debug!("Cache hit for key: {}", key);
                            Ok(Some(deserialized))
                        }
                        Err(e) => {
                            error!("Failed to deserialize cached value for key {}: {}", key, e);
                            self.cache.invalidate(key).await;
                            Err(core::errors::SigmaXError::Serialization(e.to_string()))
                        }
                    }
                }
            }
            None => {
                let elapsed = start.elapsed().as_millis() as f64;
                self.stats.write().await.record_miss(elapsed);
                debug!("Cache miss for key: {}", key);
                Ok(None)
            }
        }
    }
    
    async fn set<T>(&self, key: &str, value: T, ttl: Duration) -> core::errors::SigmaXResult<()>
    where
        T: serde::Serialize + Send,
    {
        let serialized = serde_json::to_string(&value)
            .map_err(|e| core::errors::SigmaXError::Serialization(e.to_string()))?;
        
        let item = CacheItem::new(serialized, Some(ttl));
        self.cache.insert(key.to_string(), item).await;
        
        debug!("Successfully cached value for key: {} with TTL: {:?}", key, ttl);
        Ok(())
    }
    
    async fn invalidate(&self, pattern: &str) -> core::errors::SigmaXResult<()> {
        // 对于内存缓存，我们需要遍历所有键来匹配模式
        // 这是一个简化的实现，实际生产环境可能需要更高效的方法
        
        if pattern == "*" {
            self.cache.invalidate_all();
            debug!("Invalidated all cache entries");
        } else {
            // 简单的前缀匹配
            let prefix = pattern.trim_end_matches('*');
            let keys_to_remove: Vec<String> = self.cache
                .iter()
                .filter(|(key, _)| key.starts_with(prefix))
                .map(|(key, _)| key.clone())
                .collect();
            
            for key in keys_to_remove {
                self.cache.invalidate(&key).await;
            }
            
            debug!("Invalidated {} cache entries matching pattern: {}", 
                   keys_to_remove.len(), pattern);
        }
        
        Ok(())
    }
    
    async fn clear(&self) -> core::errors::SigmaXResult<()> {
        self.cache.invalidate_all();
        debug!("Cleared all cache entries");
        Ok(())
    }
}

#[async_trait]
impl ExtendedCacheService for MemoryCacheService {
    async fn get_stats(&self) -> ServiceResult<CacheStats> {
        Ok(self.stats.read().await.clone())
    }
    
    async fn reset_stats(&self) -> ServiceResult<()> {
        *self.stats.write().await = CacheStats::new();
        Ok(())
    }
    
    async fn size(&self) -> ServiceResult<usize> {
        Ok(self.cache.entry_count() as usize)
    }
    
    async fn exists(&self, key: &str) -> ServiceResult<bool> {
        if self.check_and_remove_expired(key).await {
            return Ok(false);
        }
        
        Ok(self.cache.contains_key(key))
    }
    
    async fn expire(&self, key: &str, ttl: Duration) -> ServiceResult<()> {
        if let Some(item) = self.cache.get(key).await {
            let new_item = CacheItem::new(item.data, Some(ttl));
            self.cache.insert(key.to_string(), new_item).await;
            debug!("Updated TTL for key: {} to {:?}", key, ttl);
        }
        Ok(())
    }
    
    async fn ttl(&self, key: &str) -> ServiceResult<Option<Duration>> {
        if let Some(item) = self.cache.get(key).await {
            if let Some(ttl) = item.ttl {
                let elapsed = item.created_at.elapsed();
                if elapsed < ttl {
                    return Ok(Some(ttl - elapsed));
                }
            }
        }
        Ok(None)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_memory_cache_basic_operations() {
        let config = MemoryCacheConfig::default();
        let cache = MemoryCacheService::new(config);
        
        // 测试设置和获取
        let key = "test_key";
        let value = "test_value";
        let ttl = Duration::from_secs(60);
        
        cache.set(key, value, ttl).await.unwrap();
        let result: Option<String> = cache.get(key).await.unwrap();
        assert_eq!(result, Some(value.to_string()));
        
        // 测试不存在的键
        let result: Option<String> = cache.get("nonexistent").await.unwrap();
        assert_eq!(result, None);
    }
    
    #[tokio::test]
    async fn test_memory_cache_expiration() {
        let config = MemoryCacheConfig::default();
        let cache = MemoryCacheService::new(config);
        
        let key = "expire_test";
        let value = "expire_value";
        let ttl = Duration::from_millis(100);
        
        cache.set(key, value, ttl).await.unwrap();
        
        // 立即获取应该成功
        let result: Option<String> = cache.get(key).await.unwrap();
        assert_eq!(result, Some(value.to_string()));
        
        // 等待过期
        sleep(Duration::from_millis(150)).await;
        
        // 过期后应该返回None
        let result: Option<String> = cache.get(key).await.unwrap();
        assert_eq!(result, None);
    }
    
    #[tokio::test]
    async fn test_memory_cache_stats() {
        let config = MemoryCacheConfig::default();
        let cache = MemoryCacheService::new(config);
        
        let key = "stats_test";
        let value = "stats_value";
        let ttl = Duration::from_secs(60);
        
        // 初始统计
        let stats = cache.get_stats().await.unwrap();
        assert_eq!(stats.total_requests, 0);
        
        // 缓存未命中
        let _: Option<String> = cache.get(key).await.unwrap();
        let stats = cache.get_stats().await.unwrap();
        assert_eq!(stats.misses, 1);
        assert_eq!(stats.total_requests, 1);
        
        // 设置值
        cache.set(key, value, ttl).await.unwrap();
        
        // 缓存命中
        let _: Option<String> = cache.get(key).await.unwrap();
        let stats = cache.get_stats().await.unwrap();
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.misses, 1);
        assert_eq!(stats.total_requests, 2);
        assert_eq!(stats.hit_rate, 0.5);
    }
}
