//! Redis缓存服务实现

use async_trait::async_trait;
use core::traits::CacheService;
use redis::{AsyncCommands, Client, Connection};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, warn};

use crate::errors::{ServiceError, ServiceResult};
use super::{CacheStats, ExtendedCacheService};

/// Redis缓存服务
pub struct RedisCacheService {
    client: Client,
    connection_pool: Arc<RwLock<Option<redis::aio::ConnectionManager>>>,
    stats: Arc<RwLock<CacheStats>>,
    config: RedisCacheConfig,
}

/// Redis缓存配置
#[derive(Debug, Clone)]
pub struct RedisCacheConfig {
    pub url: String,
    pub max_connections: u32,
    pub connection_timeout: Duration,
    pub command_timeout: Duration,
    pub retry_attempts: u32,
    pub key_prefix: String,
}

impl Default for RedisCacheConfig {
    fn default() -> Self {
        Self {
            url: "redis://localhost:6379".to_string(),
            max_connections: 10,
            connection_timeout: Duration::from_secs(5),
            command_timeout: Duration::from_secs(3),
            retry_attempts: 3,
            key_prefix: "sigmax:".to_string(),
        }
    }
}

impl RedisCacheService {
    /// 创建新的Redis缓存服务
    pub async fn new(config: RedisCacheConfig) -> ServiceResult<Self> {
        let client = Client::open(config.url.clone())
            .map_err(|e| ServiceError::Redis(e))?;
        
        let service = Self {
            client,
            connection_pool: Arc::new(RwLock::new(None)),
            stats: Arc::new(RwLock::new(CacheStats::new())),
            config,
        };
        
        // 初始化连接池
        service.init_connection_pool().await?;
        
        Ok(service)
    }
    
    /// 初始化连接池
    async fn init_connection_pool(&self) -> ServiceResult<()> {
        let manager = redis::aio::ConnectionManager::new(self.client.clone()).await
            .map_err(|e| ServiceError::Redis(e))?;
        
        *self.connection_pool.write().await = Some(manager);
        debug!("Redis connection pool initialized");
        Ok(())
    }
    
    /// 获取连接
    async fn get_connection(&self) -> ServiceResult<redis::aio::ConnectionManager> {
        let pool = self.connection_pool.read().await;
        match pool.as_ref() {
            Some(manager) => Ok(manager.clone()),
            None => {
                drop(pool);
                self.init_connection_pool().await?;
                let pool = self.connection_pool.read().await;
                pool.as_ref()
                    .ok_or_else(|| ServiceError::Cache("Failed to initialize Redis connection".to_string()))
                    .map(|m| m.clone())
            }
        }
    }
    
    /// 生成带前缀的键
    fn prefixed_key(&self, key: &str) -> String {
        format!("{}{}", self.config.key_prefix, key)
    }
    
    /// 执行带重试的Redis命令
    async fn execute_with_retry<F, T>(&self, operation: F) -> ServiceResult<T>
    where
        F: Fn(redis::aio::ConnectionManager) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, redis::RedisError>> + Send>>,
        T: Send,
    {
        let mut last_error = None;
        
        for attempt in 0..self.config.retry_attempts {
            match self.get_connection().await {
                Ok(mut conn) => {
                    match operation(conn).await {
                        Ok(result) => return Ok(result),
                        Err(e) => {
                            warn!("Redis operation failed on attempt {}: {}", attempt + 1, e);
                            last_error = Some(e);
                            
                            if attempt < self.config.retry_attempts - 1 {
                                tokio::time::sleep(Duration::from_millis(100 * (attempt + 1) as u64)).await;
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to get Redis connection: {}", e);
                    return Err(e);
                }
            }
        }
        
        Err(ServiceError::Redis(last_error.unwrap_or_else(|| {
            redis::RedisError::from((redis::ErrorKind::IoError, "Max retry attempts exceeded"))
        })))
    }
}

#[async_trait]
impl CacheService for RedisCacheService {
    async fn get<T>(&self, key: &str) -> core::errors::SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send,
    {
        let start = Instant::now();
        let prefixed_key = self.prefixed_key(key);
        
        let result = self.execute_with_retry(|mut conn| {
            let key = prefixed_key.clone();
            Box::pin(async move {
                conn.get::<_, Option<String>>(key).await
            })
        }).await;
        
        let elapsed = start.elapsed().as_millis() as f64;
        
        match result {
            Ok(Some(value)) => {
                match serde_json::from_str::<T>(&value) {
                    Ok(deserialized) => {
                        self.stats.write().await.record_hit(elapsed);
                        debug!("Cache hit for key: {}", key);
                        Ok(Some(deserialized))
                    }
                    Err(e) => {
                        error!("Failed to deserialize cached value for key {}: {}", key, e);
                        Err(core::errors::SigmaXError::Serialization(e.to_string()))
                    }
                }
            }
            Ok(None) => {
                self.stats.write().await.record_miss(elapsed);
                debug!("Cache miss for key: {}", key);
                Ok(None)
            }
            Err(e) => {
                self.stats.write().await.record_miss(elapsed);
                error!("Redis get operation failed for key {}: {}", key, e);
                Err(core::errors::SigmaXError::Cache(e.to_string()))
            }
        }
    }
    
    async fn set<T>(&self, key: &str, value: T, ttl: Duration) -> core::errors::SigmaXResult<()>
    where
        T: serde::Serialize + Send,
    {
        let prefixed_key = self.prefixed_key(key);
        let serialized = serde_json::to_string(&value)
            .map_err(|e| core::errors::SigmaXError::Serialization(e.to_string()))?;
        
        let ttl_seconds = ttl.as_secs() as usize;
        
        let result = self.execute_with_retry(|mut conn| {
            let key = prefixed_key.clone();
            let value = serialized.clone();
            Box::pin(async move {
                conn.set_ex::<_, _, ()>(key, value, ttl_seconds).await
            })
        }).await;
        
        match result {
            Ok(_) => {
                debug!("Successfully cached value for key: {} with TTL: {:?}", key, ttl);
                Ok(())
            }
            Err(e) => {
                error!("Redis set operation failed for key {}: {}", key, e);
                Err(core::errors::SigmaXError::Cache(e.to_string()))
            }
        }
    }
    
    async fn invalidate(&self, pattern: &str) -> core::errors::SigmaXResult<()> {
        let prefixed_pattern = self.prefixed_key(pattern);
        
        let result = self.execute_with_retry(|mut conn| {
            let pattern = prefixed_pattern.clone();
            Box::pin(async move {
                let keys: Vec<String> = conn.keys(pattern).await?;
                if !keys.is_empty() {
                    conn.del::<_, ()>(keys).await?;
                }
                Ok::<(), redis::RedisError>(())
            })
        }).await;
        
        match result {
            Ok(_) => {
                debug!("Successfully invalidated keys matching pattern: {}", pattern);
                Ok(())
            }
            Err(e) => {
                error!("Redis invalidate operation failed for pattern {}: {}", pattern, e);
                Err(core::errors::SigmaXError::Cache(e.to_string()))
            }
        }
    }
    
    async fn clear(&self) -> core::errors::SigmaXResult<()> {
        self.invalidate("*").await
    }
}

#[async_trait]
impl ExtendedCacheService for RedisCacheService {
    async fn get_stats(&self) -> ServiceResult<CacheStats> {
        Ok(self.stats.read().await.clone())
    }
    
    async fn reset_stats(&self) -> ServiceResult<()> {
        *self.stats.write().await = CacheStats::new();
        Ok(())
    }
    
    async fn size(&self) -> ServiceResult<usize> {
        let result = self.execute_with_retry(|mut conn| {
            let pattern = format!("{}*", self.config.key_prefix);
            Box::pin(async move {
                let keys: Vec<String> = conn.keys(pattern).await?;
                Ok::<usize, redis::RedisError>(keys.len())
            })
        }).await;
        
        result.map_err(|e| ServiceError::Redis(e))
    }
    
    async fn exists(&self, key: &str) -> ServiceResult<bool> {
        let prefixed_key = self.prefixed_key(key);
        
        let result = self.execute_with_retry(|mut conn| {
            let key = prefixed_key.clone();
            Box::pin(async move {
                conn.exists::<_, bool>(key).await
            })
        }).await;
        
        result.map_err(|e| ServiceError::Redis(e))
    }
    
    async fn expire(&self, key: &str, ttl: Duration) -> ServiceResult<()> {
        let prefixed_key = self.prefixed_key(key);
        let ttl_seconds = ttl.as_secs() as usize;
        
        let result = self.execute_with_retry(|mut conn| {
            let key = prefixed_key.clone();
            Box::pin(async move {
                conn.expire::<_, ()>(key, ttl_seconds).await
            })
        }).await;
        
        result.map_err(|e| ServiceError::Redis(e))
    }
    
    async fn ttl(&self, key: &str) -> ServiceResult<Option<Duration>> {
        let prefixed_key = self.prefixed_key(key);
        
        let result = self.execute_with_retry(|mut conn| {
            let key = prefixed_key.clone();
            Box::pin(async move {
                conn.ttl::<_, i64>(key).await
            })
        }).await;
        
        match result {
            Ok(ttl_seconds) => {
                if ttl_seconds > 0 {
                    Ok(Some(Duration::from_secs(ttl_seconds as u64)))
                } else {
                    Ok(None)
                }
            }
            Err(e) => Err(ServiceError::Redis(e))
        }
    }
}
