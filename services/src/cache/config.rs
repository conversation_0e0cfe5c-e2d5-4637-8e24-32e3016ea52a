//! 缓存配置

use serde::{Deserialize, Serialize};
use std::time::Duration;

use super::{CacheType, CacheStrategyType};

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// 缓存类型
    pub cache_type: CacheType,
    /// 缓存策略类型
    pub strategy_type: CacheStrategyType,
    /// Redis配置
    pub redis: Option<RedisCacheConfig>,
    /// 内存缓存配置
    pub memory: Option<MemoryCacheConfig>,
    /// 全局配置
    pub global: GlobalCacheConfig,
}

/// Redis缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisCacheConfig {
    /// Redis连接URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 命令超时时间（秒）
    pub command_timeout_secs: u64,
    /// 重试次数
    pub retry_attempts: u32,
    /// 键前缀
    pub key_prefix: String,
}

/// 内存缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryCacheConfig {
    /// 最大容量
    pub max_capacity: u64,
    /// 生存时间（秒）
    pub time_to_live_secs: Option<u64>,
    /// 空闲时间（秒）
    pub time_to_idle_secs: Option<u64>,
    /// 清理间隔（秒）
    pub cleanup_interval_secs: u64,
}

/// 全局缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalCacheConfig {
    /// 默认TTL（秒）
    pub default_ttl_secs: u64,
    /// 启用统计
    pub enable_stats: bool,
    /// 启用压缩
    pub enable_compression: bool,
    /// 压缩阈值（字节）
    pub compression_threshold_bytes: usize,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            cache_type: CacheType::Memory,
            strategy_type: CacheStrategyType::Primary,
            redis: Some(RedisCacheConfig::default()),
            memory: Some(MemoryCacheConfig::default()),
            global: GlobalCacheConfig::default(),
        }
    }
}

impl Default for RedisCacheConfig {
    fn default() -> Self {
        Self {
            url: "redis://localhost:6379".to_string(),
            max_connections: 10,
            connection_timeout_secs: 5,
            command_timeout_secs: 3,
            retry_attempts: 3,
            key_prefix: "sigmax:".to_string(),
        }
    }
}

impl Default for MemoryCacheConfig {
    fn default() -> Self {
        Self {
            max_capacity: 10_000,
            time_to_live_secs: Some(3600), // 1小时
            time_to_idle_secs: Some(1800), // 30分钟
            cleanup_interval_secs: 300,    // 5分钟
        }
    }
}

impl Default for GlobalCacheConfig {
    fn default() -> Self {
        Self {
            default_ttl_secs: 3600, // 1小时
            enable_stats: true,
            enable_compression: false,
            compression_threshold_bytes: 1024, // 1KB
        }
    }
}

impl RedisCacheConfig {
    /// 转换为Redis缓存配置
    pub fn to_redis_config(&self) -> crate::cache::redis::RedisCacheConfig {
        crate::cache::redis::RedisCacheConfig {
            url: self.url.clone(),
            max_connections: self.max_connections,
            connection_timeout: Duration::from_secs(self.connection_timeout_secs),
            command_timeout: Duration::from_secs(self.command_timeout_secs),
            retry_attempts: self.retry_attempts,
            key_prefix: self.key_prefix.clone(),
        }
    }
}

impl MemoryCacheConfig {
    /// 转换为内存缓存配置
    pub fn to_memory_config(&self) -> crate::cache::memory::MemoryCacheConfig {
        crate::cache::memory::MemoryCacheConfig {
            max_capacity: self.max_capacity,
            time_to_live: self.time_to_live_secs.map(Duration::from_secs),
            time_to_idle: self.time_to_idle_secs.map(Duration::from_secs),
            cleanup_interval: Duration::from_secs(self.cleanup_interval_secs),
        }
    }
}

impl GlobalCacheConfig {
    /// 获取默认TTL
    pub fn default_ttl(&self) -> Duration {
        Duration::from_secs(self.default_ttl_secs)
    }
}

/// 缓存配置构建器
pub struct CacheConfigBuilder {
    config: CacheConfig,
}

impl CacheConfigBuilder {
    /// 创建新的配置构建器
    pub fn new() -> Self {
        Self {
            config: CacheConfig::default(),
        }
    }
    
    /// 设置缓存类型
    pub fn cache_type(mut self, cache_type: CacheType) -> Self {
        self.config.cache_type = cache_type;
        self
    }
    
    /// 设置策略类型
    pub fn strategy_type(mut self, strategy_type: CacheStrategyType) -> Self {
        self.config.strategy_type = strategy_type;
        self
    }
    
    /// 设置Redis配置
    pub fn redis_config(mut self, redis_config: RedisCacheConfig) -> Self {
        self.config.redis = Some(redis_config);
        self
    }
    
    /// 设置内存缓存配置
    pub fn memory_config(mut self, memory_config: MemoryCacheConfig) -> Self {
        self.config.memory = Some(memory_config);
        self
    }
    
    /// 设置全局配置
    pub fn global_config(mut self, global_config: GlobalCacheConfig) -> Self {
        self.config.global = global_config;
        self
    }
    
    /// 构建配置
    pub fn build(self) -> CacheConfig {
        self.config
    }
}

impl Default for CacheConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 从环境变量加载缓存配置
pub fn load_cache_config_from_env() -> Result<CacheConfig, config::ConfigError> {
    let mut settings = config::Config::default();
    
    // 添加默认配置
    settings.merge(config::Config::try_from(&CacheConfig::default())?)?;
    
    // 从环境变量覆盖
    settings.merge(config::Environment::with_prefix("SIGMAX_CACHE"))?;
    
    settings.try_into()
}

/// 从文件加载缓存配置
pub fn load_cache_config_from_file(file_path: &str) -> Result<CacheConfig, config::ConfigError> {
    let mut settings = config::Config::default();
    
    // 添加默认配置
    settings.merge(config::Config::try_from(&CacheConfig::default())?)?;
    
    // 从文件加载
    settings.merge(config::File::with_name(file_path))?;
    
    // 从环境变量覆盖
    settings.merge(config::Environment::with_prefix("SIGMAX_CACHE"))?;
    
    settings.try_into()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_config_default() {
        let config = CacheConfig::default();
        assert_eq!(config.cache_type, CacheType::Memory);
        assert_eq!(config.strategy_type, CacheStrategyType::Primary);
        assert!(config.redis.is_some());
        assert!(config.memory.is_some());
        assert_eq!(config.global.default_ttl_secs, 3600);
    }
    
    #[test]
    fn test_cache_config_builder() {
        let config = CacheConfigBuilder::new()
            .cache_type(CacheType::Redis)
            .strategy_type(CacheStrategyType::Fallback)
            .build();
        
        assert_eq!(config.cache_type, CacheType::Redis);
        assert_eq!(config.strategy_type, CacheStrategyType::Fallback);
    }
    
    #[test]
    fn test_redis_config_conversion() {
        let config = RedisCacheConfig::default();
        let redis_config = config.to_redis_config();
        
        assert_eq!(redis_config.url, "redis://localhost:6379");
        assert_eq!(redis_config.max_connections, 10);
        assert_eq!(redis_config.connection_timeout, Duration::from_secs(5));
    }
    
    #[test]
    fn test_memory_config_conversion() {
        let config = MemoryCacheConfig::default();
        let memory_config = config.to_memory_config();
        
        assert_eq!(memory_config.max_capacity, 10_000);
        assert_eq!(memory_config.time_to_live, Some(Duration::from_secs(3600)));
        assert_eq!(memory_config.cleanup_interval, Duration::from_secs(300));
    }
}
