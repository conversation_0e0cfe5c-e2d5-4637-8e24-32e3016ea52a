//! 缓存策略实现 - 智能路由

use async_trait::async_trait;
use core::traits::CacheService;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, warn};

use crate::errors::{ServiceError, ServiceResult};
use super::{CacheStats, ExtendedCacheService, RedisCacheService, MemoryCacheService, CacheType};

/// 缓存策略
pub struct CacheStrategy {
    primary: Arc<dyn ExtendedCacheService + Send + Sync>,
    fallback: Option<Arc<dyn ExtendedCacheService + Send + Sync>>,
    strategy_type: CacheStrategyType,
}

/// 缓存策略类型
#[derive(Debug, Clone, PartialEq)]
pub enum CacheStrategyType {
    /// 仅使用主缓存
    Primary,
    /// 主缓存失败时使用备用缓存
    Fallback,
    /// 写入两个缓存，读取时优先主缓存
    WriteThrough,
    /// 基于键的路由策略
    KeyBased,
}

impl CacheStrategy {
    /// 创建仅使用主缓存的策略
    pub fn primary_only(cache: Arc<dyn ExtendedCacheService + Send + Sync>) -> Self {
        Self {
            primary: cache,
            fallback: None,
            strategy_type: CacheStrategyType::Primary,
        }
    }
    
    /// 创建带备用缓存的策略
    pub fn with_fallback(
        primary: Arc<dyn ExtendedCacheService + Send + Sync>,
        fallback: Arc<dyn ExtendedCacheService + Send + Sync>,
    ) -> Self {
        Self {
            primary,
            fallback: Some(fallback),
            strategy_type: CacheStrategyType::Fallback,
        }
    }
    
    /// 创建写穿透策略
    pub fn write_through(
        primary: Arc<dyn ExtendedCacheService + Send + Sync>,
        secondary: Arc<dyn ExtendedCacheService + Send + Sync>,
    ) -> Self {
        Self {
            primary,
            fallback: Some(secondary),
            strategy_type: CacheStrategyType::WriteThrough,
        }
    }
    
    /// 创建基于键的路由策略
    pub fn key_based(
        memory_cache: Arc<dyn ExtendedCacheService + Send + Sync>,
        redis_cache: Arc<dyn ExtendedCacheService + Send + Sync>,
    ) -> Self {
        Self {
            primary: memory_cache,
            fallback: Some(redis_cache),
            strategy_type: CacheStrategyType::KeyBased,
        }
    }
    
    /// 根据键决定使用哪个缓存
    fn select_cache_for_key(&self, key: &str) -> &Arc<dyn ExtendedCacheService + Send + Sync> {
        match self.strategy_type {
            CacheStrategyType::KeyBased => {
                // 简单的路由策略：
                // - 以"hot:"开头的键使用内存缓存
                // - 其他键使用Redis缓存
                if key.starts_with("hot:") || key.starts_with("fast:") {
                    &self.primary // 内存缓存
                } else {
                    self.fallback.as_ref().unwrap_or(&self.primary) // Redis缓存
                }
            }
            _ => &self.primary,
        }
    }
    
    /// 获取组合统计信息
    pub async fn get_combined_stats(&self) -> ServiceResult<CombinedCacheStats> {
        let primary_stats = self.primary.get_stats().await?;
        
        let fallback_stats = if let Some(fallback) = &self.fallback {
            Some(fallback.get_stats().await?)
        } else {
            None
        };
        
        Ok(CombinedCacheStats {
            primary: primary_stats,
            fallback: fallback_stats,
            strategy_type: self.strategy_type.clone(),
        })
    }
}

/// 组合缓存统计信息
#[derive(Debug, Clone)]
pub struct CombinedCacheStats {
    pub primary: CacheStats,
    pub fallback: Option<CacheStats>,
    pub strategy_type: CacheStrategyType,
}

impl CombinedCacheStats {
    /// 计算总体命中率
    pub fn overall_hit_rate(&self) -> f64 {
        let total_hits = self.primary.hits + self.fallback.as_ref().map(|f| f.hits).unwrap_or(0);
        let total_requests = self.primary.total_requests + self.fallback.as_ref().map(|f| f.total_requests).unwrap_or(0);
        
        if total_requests > 0 {
            total_hits as f64 / total_requests as f64
        } else {
            0.0
        }
    }
    
    /// 计算平均响应时间
    pub fn avg_response_time(&self) -> f64 {
        let primary_weight = self.primary.total_requests as f64;
        let fallback_weight = self.fallback.as_ref().map(|f| f.total_requests as f64).unwrap_or(0.0);
        let total_weight = primary_weight + fallback_weight;
        
        if total_weight > 0.0 {
            let primary_weighted = self.primary.avg_response_time_ms * primary_weight;
            let fallback_weighted = self.fallback.as_ref()
                .map(|f| f.avg_response_time_ms * fallback_weight)
                .unwrap_or(0.0);
            
            (primary_weighted + fallback_weighted) / total_weight
        } else {
            0.0
        }
    }
}

#[async_trait]
impl CacheService for CacheStrategy {
    async fn get<T>(&self, key: &str) -> core::errors::SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send,
    {
        match self.strategy_type {
            CacheStrategyType::Primary => {
                self.primary.get(key).await
            }
            CacheStrategyType::Fallback => {
                match self.primary.get(key).await {
                    Ok(result) => Ok(result),
                    Err(e) => {
                        warn!("Primary cache failed for key {}: {}, trying fallback", key, e);
                        if let Some(fallback) = &self.fallback {
                            fallback.get(key).await
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            CacheStrategyType::WriteThrough => {
                // 优先从主缓存读取
                match self.primary.get(key).await {
                    Ok(Some(value)) => Ok(Some(value)),
                    Ok(None) => {
                        // 主缓存没有，尝试备用缓存
                        if let Some(fallback) = &self.fallback {
                            fallback.get(key).await
                        } else {
                            Ok(None)
                        }
                    }
                    Err(e) => {
                        warn!("Primary cache error for key {}: {}, trying fallback", key, e);
                        if let Some(fallback) = &self.fallback {
                            fallback.get(key).await
                        } else {
                            Err(e)
                        }
                    }
                }
            }
            CacheStrategyType::KeyBased => {
                let selected_cache = self.select_cache_for_key(key);
                selected_cache.get(key).await
            }
        }
    }
    
    async fn set<T>(&self, key: &str, value: T, ttl: Duration) -> core::errors::SigmaXResult<()>
    where
        T: serde::Serialize + Send + Clone,
    {
        match self.strategy_type {
            CacheStrategyType::Primary => {
                self.primary.set(key, value, ttl).await
            }
            CacheStrategyType::Fallback => {
                // 只写入主缓存
                self.primary.set(key, value, ttl).await
            }
            CacheStrategyType::WriteThrough => {
                // 写入两个缓存
                let primary_result = self.primary.set(key, value.clone(), ttl).await;
                
                if let Some(fallback) = &self.fallback {
                    let fallback_result = fallback.set(key, value, ttl).await;
                    
                    // 如果主缓存成功，返回成功；否则返回主缓存的错误
                    match (primary_result, fallback_result) {
                        (Ok(_), _) => {
                            debug!("Successfully wrote to both caches for key: {}", key);
                            Ok(())
                        }
                        (Err(e), Ok(_)) => {
                            warn!("Primary cache write failed for key {}: {}, but fallback succeeded", key, e);
                            Err(e)
                        }
                        (Err(e1), Err(e2)) => {
                            warn!("Both caches failed for key {}: primary={}, fallback={}", key, e1, e2);
                            Err(e1)
                        }
                    }
                } else {
                    primary_result
                }
            }
            CacheStrategyType::KeyBased => {
                let selected_cache = self.select_cache_for_key(key);
                selected_cache.set(key, value, ttl).await
            }
        }
    }
    
    async fn invalidate(&self, pattern: &str) -> core::errors::SigmaXResult<()> {
        let primary_result = self.primary.invalidate(pattern).await;
        
        if let Some(fallback) = &self.fallback {
            let fallback_result = fallback.invalidate(pattern).await;
            
            // 记录错误但不阻止操作
            if let Err(e) = &fallback_result {
                warn!("Fallback cache invalidation failed for pattern {}: {}", pattern, e);
            }
        }
        
        primary_result
    }
    
    async fn clear(&self) -> core::errors::SigmaXResult<()> {
        let primary_result = self.primary.clear().await;
        
        if let Some(fallback) = &self.fallback {
            let fallback_result = fallback.clear().await;
            
            // 记录错误但不阻止操作
            if let Err(e) = &fallback_result {
                warn!("Fallback cache clear failed: {}", e);
            }
        }
        
        primary_result
    }
}

#[async_trait]
impl ExtendedCacheService for CacheStrategy {
    async fn get_stats(&self) -> ServiceResult<CacheStats> {
        // 返回主缓存的统计信息
        self.primary.get_stats().await
    }
    
    async fn reset_stats(&self) -> ServiceResult<()> {
        let primary_result = self.primary.reset_stats().await;
        
        if let Some(fallback) = &self.fallback {
            let _ = fallback.reset_stats().await; // 忽略备用缓存的错误
        }
        
        primary_result
    }
    
    async fn size(&self) -> ServiceResult<usize> {
        let primary_size = self.primary.size().await?;
        
        if let Some(fallback) = &self.fallback {
            let fallback_size = fallback.size().await.unwrap_or(0);
            Ok(primary_size + fallback_size)
        } else {
            Ok(primary_size)
        }
    }
    
    async fn exists(&self, key: &str) -> ServiceResult<bool> {
        match self.strategy_type {
            CacheStrategyType::KeyBased => {
                let selected_cache = self.select_cache_for_key(key);
                selected_cache.exists(key).await
            }
            _ => {
                // 检查主缓存，如果不存在且有备用缓存，则检查备用缓存
                if self.primary.exists(key).await? {
                    Ok(true)
                } else if let Some(fallback) = &self.fallback {
                    fallback.exists(key).await
                } else {
                    Ok(false)
                }
            }
        }
    }
    
    async fn expire(&self, key: &str, ttl: Duration) -> ServiceResult<()> {
        match self.strategy_type {
            CacheStrategyType::KeyBased => {
                let selected_cache = self.select_cache_for_key(key);
                selected_cache.expire(key, ttl).await
            }
            _ => {
                let primary_result = self.primary.expire(key, ttl).await;
                
                if let Some(fallback) = &self.fallback {
                    let _ = fallback.expire(key, ttl).await; // 忽略备用缓存的错误
                }
                
                primary_result
            }
        }
    }
    
    async fn ttl(&self, key: &str) -> ServiceResult<Option<Duration>> {
        match self.strategy_type {
            CacheStrategyType::KeyBased => {
                let selected_cache = self.select_cache_for_key(key);
                selected_cache.ttl(key).await
            }
            _ => {
                // 返回主缓存的TTL
                self.primary.ttl(key).await
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::cache::{MemoryCacheService, MemoryCacheConfig};

    #[tokio::test]
    async fn test_cache_strategy_primary_only() {
        let memory_config = MemoryCacheConfig::default();
        let memory_cache = Arc::new(MemoryCacheService::new(memory_config));
        
        let strategy = CacheStrategy::primary_only(memory_cache);
        
        let key = "test_key";
        let value = "test_value";
        let ttl = Duration::from_secs(60);
        
        strategy.set(key, value, ttl).await.unwrap();
        let result: Option<String> = strategy.get(key).await.unwrap();
        assert_eq!(result, Some(value.to_string()));
    }
    
    #[tokio::test]
    async fn test_cache_strategy_key_based() {
        let memory_config = MemoryCacheConfig::default();
        let memory_cache = Arc::new(MemoryCacheService::new(memory_config.clone()));
        let redis_cache = Arc::new(MemoryCacheService::new(memory_config)); // 用内存缓存模拟Redis
        
        let strategy = CacheStrategy::key_based(memory_cache, redis_cache);
        
        // 热数据应该使用内存缓存
        let hot_key = "hot:user_session";
        let hot_value = "session_data";
        let ttl = Duration::from_secs(60);
        
        strategy.set(hot_key, hot_value, ttl).await.unwrap();
        let result: Option<String> = strategy.get(hot_key).await.unwrap();
        assert_eq!(result, Some(hot_value.to_string()));
        
        // 普通数据应该使用Redis缓存
        let normal_key = "user:profile";
        let normal_value = "profile_data";
        
        strategy.set(normal_key, normal_value, ttl).await.unwrap();
        let result: Option<String> = strategy.get(normal_key).await.unwrap();
        assert_eq!(result, Some(normal_value.to_string()));
    }
}
