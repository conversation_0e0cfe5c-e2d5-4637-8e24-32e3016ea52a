//! 模拟交易所实现

use async_trait::async_trait;
use sigmax_core::{
    Balance, Candle, Exchange, ExchangeId, Order, OrderBook, OrderId, SigmaXResult, TimeFrame,
    TradingPair, Trade,
};

/// 模拟交易所
pub struct SimulatorExchange {
    // 模拟数据等
}

impl SimulatorExchange {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl Exchange for SimulatorExchange {
    fn id(&self) -> ExchangeId {
        ExchangeId::Simulator
    }

    async fn get_balances(&self) -> SigmaXResult<Vec<Balance>> {
        todo!("实现模拟余额查询")
    }

    async fn place_order(&self, _order: &Order) -> SigmaXResult<OrderId> {
        todo!("实现模拟下单")
    }

    async fn cancel_order(&self, _order_id: OrderId) -> SigmaXResult<()> {
        todo!("实现模拟取消订单")
    }

    async fn get_order(&self, _order_id: OrderId) -> SigmaXResult<Order> {
        todo!("实现模拟订单查询")
    }

    async fn get_order_book(&self, _trading_pair: &TradingPair) -> SigmaXResult<OrderBook> {
        todo!("实现模拟订单簿查询")
    }

    async fn get_candles(
        &self,
        _trading_pair: &TradingPair,
        _timeframe: TimeFrame,
        _limit: Option<u32>,
    ) -> SigmaXResult<Vec<Candle>> {
        todo!("实现模拟K线查询")
    }

    async fn get_trades(&self, _trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        todo!("实现模拟交易历史查询")
    }
}
