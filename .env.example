# SigmaX Trading System Environment Configuration
# 复制此文件为 .env 并修改相应配置

# =============================================================================
# 数据库配置 (Database Configuration)
# =============================================================================

# 数据库连接URL - 请设置你的数据库连接
DATABASE_URL=

# 数据库连接池配置
DATABASE_MAX_CONNECTIONS=10
DATABASE_CONNECTION_TIMEOUT=30
DATABASE_AUTO_MIGRATE=true

# =============================================================================
# Web服务器配置 (Web Server Configuration)
# =============================================================================

# Web服务器主机和端口
WEB_HOST=127.0.0.1
WEB_PORT=8080

# JWT密钥 (生产环境请更改)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# =============================================================================
# 日志配置 (Logging Configuration)
# =============================================================================

# 日志级别: trace, debug, info, warn, error
LOG_LEVEL=info
RUST_LOG=info

# =============================================================================
# 开发模式配置 (Development Configuration)
# =============================================================================

# 是否启用开发模式
DEVELOPMENT_MODE=true

# =============================================================================
# 其他数据库选项 (Alternative Database Options)
# =============================================================================

# 本地PostgreSQL (如果你有本地数据库)
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/sigmax_trading

# Docker PostgreSQL
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/sigmax_trading

# Supabase (如果你使用Supabase)
# DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

# Railway (如果你使用Railway)
# DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/railway

# =============================================================================
# 交易所API配置 (Exchange API Configuration)
# =============================================================================

# Binance API (可选)
# BINANCE_API_KEY=your_binance_api_key
# BINANCE_SECRET_KEY=your_binance_secret_key
# BINANCE_TESTNET=true

# Coinbase API (可选)
# COINBASE_API_KEY=your_coinbase_api_key
# COINBASE_SECRET_KEY=your_coinbase_secret_key
# COINBASE_PASSPHRASE=your_coinbase_passphrase
# COINBASE_SANDBOX=true

# Kraken API (可选)
# KRAKEN_API_KEY=your_kraken_api_key
# KRAKEN_SECRET_KEY=your_kraken_secret_key

# =============================================================================
# 风险管理配置 (Risk Management Configuration)
# =============================================================================

# 最大单笔订单金额 (USDT)
MAX_ORDER_AMOUNT=1000.0

# 最大日损失限制 (USDT)
MAX_DAILY_LOSS=500.0

# 最大持仓比例 (0.0-1.0)
MAX_POSITION_RATIO=0.8

# 风险检查间隔 (秒)
RISK_CHECK_INTERVAL=60

# =============================================================================
# 回测配置 (Backtest Configuration)
# =============================================================================

# 回测默认初始资金
BACKTEST_INITIAL_CAPITAL=10000.0

# 手续费率
BACKTEST_COMMISSION_RATE=0.001

# 滑点 (百分比)
BACKTEST_SLIPPAGE=0.0005

# =============================================================================
# 性能监控配置 (Performance Monitoring Configuration)
# =============================================================================

# 监控间隔 (秒)
MONITOR_INTERVAL=30

# 保留历史数据天数
HISTORY_DAYS=30

# 是否启用详细监控
DETAILED_MONITORING=true

# =============================================================================
# 使用说明 (Usage Instructions)
# =============================================================================

# 1. 复制此文件为 .env:
#    cp .env.example .env
#
# 2. 编辑 .env 文件，修改相应配置
#
# 3. 启动应用程序:
#    cargo run --bin server
#
# 4. 或者直接设置环境变量:
#    export DATABASE_URL="your-database-url"
#    cargo run --bin server

# =============================================================================
# 数据库连接测试 (Database Connection Testing)
# =============================================================================

# 测试数据库连接:
# psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# 或使用项目提供的测试脚本:
# python3 setup_db.py

# =============================================================================
# 注意事项 (Important Notes)
# =============================================================================

# 1. 生产环境请务必更改JWT_SECRET
# 2. 数据库密码包含特殊字符，请确保URL编码正确
# 3. Neon数据库有连接限制，建议设置合理的连接池大小
# 4. 如果使用其他云数据库，请相应修改DATABASE_URL
# 5. 本地开发可以使用Docker PostgreSQL或本地安装的PostgreSQL
