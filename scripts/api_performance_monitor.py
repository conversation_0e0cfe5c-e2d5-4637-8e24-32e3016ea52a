#!/usr/bin/env python3
"""
SigmaX-R API性能监控脚本
监控API响应时间、成功率、错误率等关键指标
"""

import requests
import time
import json
import statistics
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

BASE_URL = "http://127.0.0.1:8080"

class APIPerformanceMonitor:
    def __init__(self):
        self.results = []
        self.start_time = None
        self.end_time = None
    
    def test_endpoint(self, method, endpoint, data=None, timeout=10):
        """测试单个API端点的性能"""
        url = f"{BASE_URL}{endpoint}"
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=timeout)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=timeout)
            elif method.upper() == "PUT":
                response = requests.put(url, json=data, timeout=timeout)
            elif method.upper() == "DELETE":
                response = requests.delete(url, timeout=timeout)
            else:
                return None
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            return {
                'endpoint': f"{method} {endpoint}",
                'status_code': response.status_code,
                'response_time_ms': response_time,
                'success': response.status_code == 200,
                'content_length': len(response.content) if response.content else 0,
                'timestamp': datetime.now().isoformat()
            }
            
        except requests.exceptions.Timeout:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            return {
                'endpoint': f"{method} {endpoint}",
                'status_code': 408,  # Request Timeout
                'response_time_ms': response_time,
                'success': False,
                'content_length': 0,
                'timestamp': datetime.now().isoformat(),
                'error': 'Timeout'
            }
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            return {
                'endpoint': f"{method} {endpoint}",
                'status_code': 0,
                'response_time_ms': response_time,
                'success': False,
                'content_length': 0,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def run_performance_test(self, iterations=3, concurrent_requests=5):
        """运行性能测试"""
        print(f"🚀 开始API性能监控测试")
        print(f"📊 测试参数: {iterations}次迭代, {concurrent_requests}个并发请求")
        print("=" * 80)
        
        # 核心API端点列表
        core_endpoints = [
            ('GET', '/api/health'),
            ('GET', '/api/status'),
            ('GET', '/api/strategies'),
            ('GET', '/api/orders'),
            ('GET', '/api/trades'),
            ('GET', '/api/backtest/files'),
            ('GET', '/api/v2/exchanges'),
            ('GET', '/api/v2/market/ticker/BTCUSDT'),
            ('GET', '/api/v2/market/orderbook/BTCUSDT'),
            ('GET', '/api/v2/reports/performance'),
            ('GET', '/api/v2/config'),
            ('GET', '/api/v2/risk/parameters'),
            ('GET', '/api/v2/portfolios'),
            ('GET', '/api/v2/monitoring/health'),
            ('GET', '/api/v2/cache/stats'),
            ('GET', '/api/v2/database/status'),
        ]
        
        self.start_time = datetime.now()
        
        for iteration in range(iterations):
            print(f"\n🔄 第 {iteration + 1} 轮测试")
            print("-" * 40)
            
            # 使用线程池进行并发测试
            with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
                futures = []
                
                for method, endpoint in core_endpoints:
                    future = executor.submit(self.test_endpoint, method, endpoint)
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    result = future.result()
                    if result:
                        self.results.append(result)
                        status = "✅" if result['success'] else "❌"
                        print(f"{status} {result['endpoint']} - {result['response_time_ms']:.1f}ms")
            
            # 迭代间隔
            if iteration < iterations - 1:
                time.sleep(1)
        
        self.end_time = datetime.now()
        
        # 生成性能报告
        self.generate_performance_report()
    
    def generate_performance_report(self):
        """生成性能报告"""
        if not self.results:
            print("❌ 没有测试结果可用于生成报告")
            return
        
        print("\n" + "=" * 80)
        print("📊 API性能监控报告")
        print("=" * 80)
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r['success'])
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        print(f"📈 基本统计:")
        print(f"  总请求数: {total_requests}")
        print(f"  成功请求: {successful_requests}")
        print(f"  失败请求: {failed_requests}")
        print(f"  成功率: {success_rate:.1f}%")
        print(f"  测试时长: {(self.end_time - self.start_time).total_seconds():.1f}秒")
        
        # 响应时间统计
        response_times = [r['response_time_ms'] for r in self.results if r['success']]
        if response_times:
            print(f"\n⏱️ 响应时间统计 (毫秒):")
            print(f"  平均响应时间: {statistics.mean(response_times):.1f}ms")
            print(f"  中位数响应时间: {statistics.median(response_times):.1f}ms")
            print(f"  最快响应时间: {min(response_times):.1f}ms")
            print(f"  最慢响应时间: {max(response_times):.1f}ms")
            if len(response_times) > 1:
                print(f"  响应时间标准差: {statistics.stdev(response_times):.1f}ms")
        
        # 按端点分组统计
        endpoint_stats = {}
        for result in self.results:
            endpoint = result['endpoint']
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    'total': 0,
                    'success': 0,
                    'response_times': []
                }
            
            endpoint_stats[endpoint]['total'] += 1
            if result['success']:
                endpoint_stats[endpoint]['success'] += 1
                endpoint_stats[endpoint]['response_times'].append(result['response_time_ms'])
        
        print(f"\n📋 端点性能详情:")
        print("-" * 80)
        for endpoint, stats in sorted(endpoint_stats.items()):
            success_rate = (stats['success'] / stats['total']) * 100 if stats['total'] > 0 else 0
            avg_time = statistics.mean(stats['response_times']) if stats['response_times'] else 0
            
            status_icon = "✅" if success_rate == 100 else "⚠️" if success_rate >= 80 else "❌"
            print(f"{status_icon} {endpoint}")
            print(f"    成功率: {success_rate:.1f}% ({stats['success']}/{stats['total']})")
            if stats['response_times']:
                print(f"    平均响应时间: {avg_time:.1f}ms")
        
        # 性能等级评估
        print(f"\n🏆 性能等级评估:")
        if success_rate >= 99 and statistics.mean(response_times) <= 100:
            grade = "优秀 🌟"
        elif success_rate >= 95 and statistics.mean(response_times) <= 200:
            grade = "良好 👍"
        elif success_rate >= 90 and statistics.mean(response_times) <= 500:
            grade = "一般 ⚠️"
        else:
            grade = "需要改进 ❌"
        
        print(f"  系统性能等级: {grade}")
        
        # 保存详细报告
        self.save_detailed_report()
    
    def save_detailed_report(self):
        """保存详细的JSON报告"""
        reports_dir = Path("reports/performance")
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"api_performance_{timestamp}.json"
        
        report_data = {
            'test_info': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat(),
                'duration_seconds': (self.end_time - self.start_time).total_seconds(),
                'total_requests': len(self.results)
            },
            'summary': {
                'total_requests': len(self.results),
                'successful_requests': sum(1 for r in self.results if r['success']),
                'failed_requests': sum(1 for r in self.results if not r['success']),
                'success_rate': (sum(1 for r in self.results if r['success']) / len(self.results)) * 100 if self.results else 0
            },
            'detailed_results': self.results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")

def main():
    monitor = APIPerformanceMonitor()
    
    # 运行性能测试
    # 参数: iterations=测试轮数, concurrent_requests=并发请求数
    monitor.run_performance_test(iterations=3, concurrent_requests=5)
    
    print(f"\n🎉 API性能监控测试完成！")
    print(f"📊 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
