# SigmaX 完整回测流程集成测试

基于Context7 MCP最佳实践的完整API集成测试系统，提供全面的回测流程验证。

## 📋 测试套件概述

### 🎯 测试目标
- **完整API流程验证**：从数据准备到结果分析的完整回测流程
- **数据库集成测试**：验证数据持久化和查询功能
- **WebSocket实时监控**：测试实时进度推送和事件处理
- **性能基准测试**：验证API响应时间和系统性能
- **错误处理验证**：测试各种错误场景和边界条件
- **并发测试**：验证多引擎并发执行能力

### 🛠️ 测试工具

#### 1. 主要测试脚本

| 脚本名称 | 功能描述 | 执行时间 | 重要性 |
|---------|---------|---------|--------|
| `comprehensive_backtest_integration_test.py` | 完整回测流程集成测试 | ~5分钟 | 🔴 关键 |
| `quick_backtest_api_test.py` | 快速API功能验证 | ~1分钟 | 🔴 关键 |
| `websocket_backtest_monitor.py` | WebSocket实时监控测试 | ~2分钟 | 🟡 重要 |
| `run_integration_tests.py` | 统一测试运行器 | ~8分钟 | 🔴 关键 |

#### 2. 传统测试脚本（兼容性）

| 脚本名称 | 功能描述 | 状态 |
|---------|---------|------|
| `test_complete_backtest_flow.sh` | Bash版完整流程测试 | ✅ 兼容 |
| `comprehensive_api_test.py` | 原有API测试脚本 | ✅ 兼容 |

## 🚀 快速开始

### 环境准备

1. **启动SigmaX服务器**
```bash
# 在项目根目录
cargo run --bin web_server
```

2. **安装Python依赖**
```bash
pip install requests websockets psycopg2-binary
```

3. **准备测试数据**
确保 `bt_klines/` 目录中有测试数据文件（如 `BNB_USDT_1d.json`）

### 运行测试

#### 方式1：统一测试运行器（推荐）
```bash
# 运行所有测试（并行）
python scripts/run_integration_tests.py

# 运行所有测试（顺序执行）
python scripts/run_integration_tests.py --sequential

# 自定义API地址
python scripts/run_integration_tests.py --api-url http://localhost:8080
```

#### 方式2：单独运行测试
```bash
# 快速API验证
python scripts/quick_backtest_api_test.py

# 完整集成测试
python scripts/comprehensive_backtest_integration_test.py

# WebSocket监控测试
python scripts/websocket_backtest_monitor.py

# 传统Bash测试
bash scripts/test_complete_backtest_flow.sh
```

## 📊 测试覆盖范围

### API端点测试覆盖

#### 回测数据管理 (4个端点)
- ✅ `GET /api/backtest/files` - 获取数据文件列表
- ✅ `GET /api/backtest/files/{filename}` - 获取K线数据
- ✅ `GET /api/backtest/files/{filename}/validate` - 验证数据文件
- ✅ `GET /api/backtest/stats` - 获取数据统计

#### 引擎管理 (6个端点)
- ✅ `GET /api/engines` - 获取所有引擎
- ✅ `POST /api/engines` - 创建引擎
- ✅ `GET /api/engines/{id}` - 获取指定引擎
- ✅ `DELETE /api/engines/{id}` - 删除引擎
- ✅ `POST /api/engines/{id}/start` - 启动引擎
- ✅ `GET /api/engines/{id}/status` - 获取引擎状态

#### 回测配置和结果 (6个端点)
- ✅ `POST /api/engines/{id}/backtest/config` - 设置回测配置
- ✅ `GET /api/engines/{id}/backtest/config` - 获取回测配置
- ✅ `GET /api/engines/{id}/backtest/progress` - 获取回测进度
- ✅ `GET /api/engines/{id}/backtest/result` - 获取回测结果
- ✅ `GET /api/engines/{id}/backtest/trades` - 获取交易记录
- ✅ `GET /api/engines/{id}/backtest/portfolio` - 获取投资组合

#### 策略管理 (7个端点)
- ✅ `GET /api/strategies` - 获取策略列表
- ✅ `POST /api/strategies` - 创建策略
- ✅ `GET /api/strategies/{id}` - 获取策略详情
- ✅ `PUT /api/strategies/{id}` - 更新策略
- ✅ `DELETE /api/strategies/{id}` - 删除策略
- ✅ `POST /api/strategies/{id}/start` - 启动策略
- ✅ `GET /api/strategies/{id}/status` - 获取策略状态

#### WebSocket通信
- ✅ `WS /ws` - WebSocket连接
- ✅ 回测进度订阅
- ✅ 系统状态监控
- ✅ 实时事件推送

### 功能测试覆盖

#### 核心功能
- ✅ 完整回测流程执行
- ✅ 数据文件管理和验证
- ✅ 引擎生命周期管理
- ✅ 策略配置和执行
- ✅ 实时进度监控
- ✅ 结果数据获取

#### 高级功能
- ✅ 并发回测执行
- ✅ WebSocket实时通信
- ✅ 数据库持久化验证
- ✅ 错误处理和恢复
- ✅ 性能基准测试
- ✅ 连接稳定性测试

## 📈 测试结果解读

### 成功标准
- **关键测试通过率**: ≥ 95%
- **API响应时间**: < 2秒
- **WebSocket连接稳定性**: ≥ 90%
- **并发测试成功率**: ≥ 80%
- **数据库集成**: 100%正确性

### 测试报告
测试完成后会生成详细报告：
- `integration_test_report_YYYYMMDD_HHMMSS.json` - 详细JSON报告
- `backtest_integration_test.log` - 测试执行日志

### 报告内容
```json
{
  "summary": {
    "total_tests": 25,
    "passed": 23,
    "failed": 2,
    "success_rate": 92.0,
    "total_duration": 287.5
  },
  "test_results": [...],
  "config": {...}
}
```

## 🔧 故障排除

### 常见问题

#### 1. 服务器连接失败
```
❌ Cannot connect to server: Connection refused
```
**解决方案**：
- 确保SigmaX服务器正在运行：`cargo run --bin web_server`
- 检查端口是否被占用：`lsof -i :8080`
- 验证防火墙设置

#### 2. 数据库连接失败
```
❌ Database connection failed: could not connect to server
```
**解决方案**：
- 检查数据库连接字符串配置
- 验证网络连接到Neon数据库
- 确认数据库凭据正确

#### 3. 测试数据缺失
```
⚠️ No data files found
```
**解决方案**：
- 确保 `bt_klines/` 目录存在
- 添加测试数据文件（如 `BNB_USDT_1d.json`）
- 检查文件格式和权限

#### 4. WebSocket连接超时
```
⚠️ WebSocket response timeout
```
**解决方案**：
- 检查WebSocket服务是否启用
- 验证防火墙WebSocket端口设置
- 增加超时时间配置

### 调试模式
```bash
# 启用详细日志
export RUST_LOG=debug
cargo run --bin web_server

# Python测试调试
python -u scripts/quick_backtest_api_test.py
```

## 🔄 持续集成

### CI/CD集成
```yaml
# .github/workflows/integration-tests.yml
name: Integration Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
      - name: Setup Python
        uses: actions/setup-python@v2
      - name: Install dependencies
        run: pip install requests websockets psycopg2-binary
      - name: Start SigmaX server
        run: cargo run --bin web_server &
      - name: Run integration tests
        run: python scripts/run_integration_tests.py
```

### 本地开发工作流
```bash
# 开发前测试
python scripts/quick_backtest_api_test.py

# 功能开发后完整测试
python scripts/run_integration_tests.py

# 发布前全面验证
bash scripts/test_complete_backtest_flow.sh
```

## 📚 扩展和定制

### 添加新测试
1. 创建新的测试脚本
2. 在 `run_integration_tests.py` 中注册
3. 更新测试覆盖文档

### 自定义配置
```python
# 自定义测试配置
config = TestConfig(
    api_base_url="http://custom-host:8080",
    initial_bnb=5.0,
    initial_usdt=500.0,
    concurrent_engines=5
)
```

### 性能调优
- 调整并发数量
- 优化超时设置
- 配置重试策略
- 启用连接池

---

## 📞 支持

如有问题或建议，请：
1. 查看测试日志文件
2. 检查API文档：`docs/api/`
3. 提交Issue到项目仓库
4. 联系开发团队

**测试愉快！** 🎉
