#!/usr/bin/env python3
"""
检查strategy_status枚举类型
"""

import psycopg2
import sys

# Neon数据库连接参数
NEON_DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

def main():
    """主函数"""
    print("🔍 检查strategy_status枚举类型")
    print("=" * 50)
    
    try:
        print("📡 连接到Neon数据库...")
        conn = psycopg2.connect(NEON_DATABASE_URL)
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        print()
        
        # 检查strategy_status枚举类型
        print("📋 strategy_status 枚举值:")
        print("-" * 30)
        
        cursor.execute("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'strategy_status'
            )
            ORDER BY enumsortorder;
        """)
        
        enum_values = cursor.fetchall()
        if enum_values:
            for value in enum_values:
                print(f"  - {value[0]}")
        else:
            print("  ❌ 没有找到strategy_status枚举类型")
        
        print()
        print("=" * 50)
        print("✅ 检查完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
