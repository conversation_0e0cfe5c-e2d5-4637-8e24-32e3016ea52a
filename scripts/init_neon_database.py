#!/usr/bin/env python3
"""
SigmaX Neon数据库初始化脚本
使用Python连接Neon数据库并执行初始化SQL脚本
"""

import psycopg2
import os
import sys
from pathlib import Path

# Neon数据库连接参数
NEON_DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

def read_sql_file(filename):
    """读取SQL文件内容"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return None

def execute_sql_file(cursor, filename):
    """执行SQL文件"""
    print(f"执行 {filename}...")
    sql_content = read_sql_file(filename)
    if sql_content is None:
        return False
    
    try:
        # 执行整个SQL文件内容
        try:
            cursor.execute(sql_content)
        except Exception as e:
            # 如果整体执行失败，尝试逐句执行
            print(f"⚠️  整体执行失败，尝试逐句执行: {str(e)}")

            # 更智能的SQL分割
            statements = []
            current_statement = ""
            in_function = False

            for line in sql_content.split('\n'):
                line = line.strip()
                if not line or line.startswith('--'):
                    continue

                current_statement += line + '\n'

                # 检测函数开始和结束
                if 'CREATE OR REPLACE FUNCTION' in line.upper():
                    in_function = True
                elif in_function and line.endswith('$$;'):
                    in_function = False
                    statements.append(current_statement.strip())
                    current_statement = ""
                elif not in_function and line.endswith(';'):
                    statements.append(current_statement.strip())
                    current_statement = ""

            # 执行每个语句
            for statement in statements:
                if statement:
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        # 忽略已存在的对象错误
                        if "already exists" in str(e) or "duplicate" in str(e):
                            print(f"⚠️  跳过已存在的对象: {str(e).split(':')[0]}")
                            continue
                        else:
                            raise e
        
        print(f"✅ {filename} 执行成功")
        return True
    except Exception as e:
        print(f"❌ 执行 {filename} 时出错: {e}")
        return False

def create_basic_tables(cursor):
    """创建基础表结构"""
    print("创建基础表结构...")
    
    # 创建回测结果表
    backtest_results_sql = """
    CREATE TABLE IF NOT EXISTS backtest_results (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        engine_id UUID NOT NULL,
        config JSONB NOT NULL,
        start_time TIMESTAMP WITH TIME ZONE NOT NULL,
        end_time TIMESTAMP WITH TIME ZONE NOT NULL,
        initial_capital DECIMAL(20, 8) NOT NULL,
        final_capital DECIMAL(20, 8) NOT NULL,
        total_return DECIMAL(20, 8) NOT NULL,
        total_return_percentage DECIMAL(10, 4) NOT NULL,
        max_drawdown DECIMAL(20, 8) NOT NULL,
        max_drawdown_percentage DECIMAL(10, 4) NOT NULL,
        sharpe_ratio DECIMAL(10, 4),
        profit_factor DECIMAL(10, 4),
        win_rate DECIMAL(10, 4),
        total_trades INTEGER NOT NULL DEFAULT 0,
        winning_trades INTEGER NOT NULL DEFAULT 0,
        losing_trades INTEGER NOT NULL DEFAULT 0,
        average_win DECIMAL(20, 8),
        average_loss DECIMAL(20, 8),
        largest_win DECIMAL(20, 8),
        largest_loss DECIMAL(20, 8),
        status VARCHAR(50) NOT NULL DEFAULT 'Created',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE
    );
    """
    
    # 创建回测交易记录表
    backtest_trades_sql = """
    CREATE TABLE IF NOT EXISTS backtest_trades (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        backtest_id UUID NOT NULL REFERENCES backtest_results(id) ON DELETE CASCADE,
        trade_id VARCHAR(100) NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
        trading_pair VARCHAR(20) NOT NULL,
        side VARCHAR(10) NOT NULL,
        quantity DECIMAL(20, 8) NOT NULL,
        price DECIMAL(20, 8) NOT NULL,
        value DECIMAL(20, 8) NOT NULL,
        fee DECIMAL(20, 8) NOT NULL DEFAULT 0,
        strategy_id VARCHAR(100),
        pnl DECIMAL(20, 8) NOT NULL DEFAULT 0,
        cumulative_pnl DECIMAL(20, 8) NOT NULL DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 创建回测投资组合快照表
    backtest_portfolio_snapshots_sql = """
    CREATE TABLE IF NOT EXISTS backtest_portfolio_snapshots (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        backtest_id UUID NOT NULL REFERENCES backtest_results(id) ON DELETE CASCADE,
        timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
        total_value DECIMAL(20, 8) NOT NULL,
        cash_balance DECIMAL(20, 8) NOT NULL,
        positions JSONB NOT NULL DEFAULT '[]',
        unrealized_pnl DECIMAL(20, 8) NOT NULL DEFAULT 0,
        realized_pnl DECIMAL(20, 8) NOT NULL DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 创建引擎状态表
    engines_sql = """
    CREATE TABLE IF NOT EXISTS engines (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        engine_type VARCHAR(50) NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'Created',
        config JSONB NOT NULL DEFAULT '{}',
        trading_pairs JSONB NOT NULL DEFAULT '[]',
        initial_capital DECIMAL(20, 8),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    # 创建回测数据文件表
    backtest_files_sql = """
    CREATE TABLE IF NOT EXISTS backtest_files (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        trading_pair VARCHAR(20) NOT NULL,
        timeframe VARCHAR(10) NOT NULL,
        candle_count INTEGER NOT NULL DEFAULT 0,
        start_time TIMESTAMP WITH TIME ZONE,
        end_time TIMESTAMP WITH TIME ZONE,
        file_size BIGINT,
        file_hash VARCHAR(64),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    tables = [
        ("backtest_results", backtest_results_sql),
        ("backtest_trades", backtest_trades_sql),
        ("backtest_portfolio_snapshots", backtest_portfolio_snapshots_sql),
        ("engines", engines_sql),
        ("backtest_files", backtest_files_sql),
    ]
    
    for table_name, sql in tables:
        try:
            cursor.execute(sql)
            print(f"✅ 表 {table_name} 创建成功")
        except Exception as e:
            if "already exists" in str(e):
                print(f"⚠️  表 {table_name} 已存在")
            else:
                print(f"❌ 创建表 {table_name} 失败: {e}")
                return False
    
    return True

def create_indexes(cursor):
    """创建索引"""
    print("创建索引...")
    
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_backtest_results_engine_id ON backtest_results(engine_id);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_results_status ON backtest_results(status);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_results_created_at ON backtest_results(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_trades_backtest_id ON backtest_trades(backtest_id);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_trades_timestamp ON backtest_trades(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_trades_trading_pair ON backtest_trades(trading_pair);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_portfolio_snapshots_backtest_id ON backtest_portfolio_snapshots(backtest_id);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_portfolio_snapshots_timestamp ON backtest_portfolio_snapshots(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_engines_status ON engines(status);",
        "CREATE INDEX IF NOT EXISTS idx_engines_engine_type ON engines(engine_type);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_files_trading_pair ON backtest_files(trading_pair);",
        "CREATE INDEX IF NOT EXISTS idx_backtest_files_timeframe ON backtest_files(timeframe);",
    ]
    
    for index_sql in indexes:
        try:
            cursor.execute(index_sql)
        except Exception as e:
            if "already exists" in str(e):
                continue
            else:
                print(f"❌ 创建索引失败: {e}")
                return False
    
    print("✅ 索引创建完成")
    return True

def test_database_connection(cursor):
    """测试数据库连接和基本功能"""
    print("测试数据库连接...")
    
    try:
        # 测试基本查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL版本: {version}")
        
        # 测试表是否存在
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        print(f"✅ 数据库表数量: {len(tables)}")
        
        # 显示表列表
        if tables:
            print("📋 数据库表:")
            for table in tables:
                print(f"  - {table[0]}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SigmaX Neon数据库初始化")
    print("=" * 50)
    
    try:
        print("📡 连接到Neon数据库...")
        conn = psycopg2.connect(NEON_DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        print()
        
        # 测试连接
        if not test_database_connection(cursor):
            return False
        print()
        
        # 创建基础表
        if not create_basic_tables(cursor):
            return False
        print()
        
        # 创建索引
        if not create_indexes(cursor):
            return False
        print()
        
        # 最终测试
        print("🔍 最终验证...")
        if test_database_connection(cursor):
            print("✅ Neon数据库初始化完成!")
            print()
            print("🎉 现在可以启动SigmaX系统:")
            print("   cargo run --bin web_server")
            print()
            print("🌐 或者测试API:")
            print("   curl http://localhost:8080/api/health")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        print()
        print("💡 请检查:")
        print("1. 网络连接是否正常")
        print("2. Neon数据库是否可访问")
        print("3. 连接字符串是否正确")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
