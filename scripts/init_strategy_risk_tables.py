#!/usr/bin/env python3
"""
SigmaX 策略和风控表初始化脚本
"""

import psycopg2
import sys
from pathlib import Path

# Neon数据库连接参数
NEON_DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

def read_sql_file(filename):
    """读取SQL文件内容"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return None

def execute_sql_script(cursor, sql_content):
    """执行SQL脚本"""
    try:
        # 分割SQL语句
        statements = []
        current_statement = ""
        in_function = False

        for line in sql_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue

            current_statement += line + '\n'

            # 检测函数开始和结束
            if 'CREATE OR REPLACE FUNCTION' in line.upper():
                in_function = True
            elif in_function and line.endswith('$$;'):
                in_function = False
                statements.append(current_statement.strip())
                current_statement = ""
            elif not in_function and line.endswith(';'):
                statements.append(current_statement.strip())
                current_statement = ""

        # 执行每个语句
        for i, statement in enumerate(statements):
            if statement:
                try:
                    cursor.execute(statement)
                    print(f"✅ 执行语句 {i+1}/{len(statements)} 成功")
                except Exception as e:
                    # 忽略已存在的对象错误
                    if "already exists" in str(e) or "duplicate" in str(e):
                        print(f"⚠️  跳过已存在的对象: {str(e).split(':')[0]}")
                        continue
                    else:
                        print(f"❌ 执行语句 {i+1} 失败: {e}")
                        print(f"语句内容: {statement[:100]}...")
                        raise e
        
        return True
    except Exception as e:
        print(f"❌ 执行SQL脚本时出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SigmaX 策略和风控表初始化")
    print("=" * 50)
    
    try:
        print("📡 连接到Neon数据库...")
        conn = psycopg2.connect(NEON_DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        print()
        
        # 读取SQL文件
        sql_file = "database/init-scripts/03-create-strategy-risk-tables.sql"
        print(f"📖 读取SQL文件: {sql_file}")
        
        sql_content = read_sql_file(sql_file)
        if sql_content is None:
            return False
        
        print(f"📄 SQL文件大小: {len(sql_content)} 字符")
        print()
        
        # 执行SQL脚本
        print("🔧 执行SQL脚本...")
        if execute_sql_script(cursor, sql_content):
            print()
            print("✅ 策略和风控表初始化完成!")
            
            # 验证表是否创建成功
            print("🔍 验证表创建...")
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('strategies', 'strategy_performance', 'strategy_trades', 
                                  'strategy_positions', 'risk_rules', 'risk_checks', 
                                  'risk_violations', 'risk_statistics')
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            if tables:
                print(f"✅ 成功创建 {len(tables)} 个表:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("⚠️  没有找到新创建的表")
            
            # 检查默认风控规则
            print()
            print("🔍 检查默认风控规则...")
            cursor.execute("SELECT COUNT(*) FROM risk_rules;")
            rule_count = cursor.fetchone()[0]
            print(f"✅ 风控规则数量: {rule_count}")
            
            if rule_count > 0:
                cursor.execute("SELECT name, rule_type, enabled FROM risk_rules ORDER BY priority DESC;")
                rules = cursor.fetchall()
                print("📋 风控规则列表:")
                for rule in rules:
                    status = "启用" if rule[2] else "禁用"
                    print(f"  - {rule[0]} ({rule[1]}) - {status}")
            
            print()
            print("🎉 数据库初始化完成!")
            print()
            print("🚀 现在可以启动SigmaX系统:")
            print("   cargo run --bin web_server")
            print()
            print("🌐 然后测试策略和风控API:")
            print("   ./scripts/test_complete_backtest_flow.sh")
            
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        print()
        print("💡 请检查:")
        print("1. 网络连接是否正常")
        print("2. Neon数据库是否可访问")
        print("3. 连接字符串是否正确")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
