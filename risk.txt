graph TB
    subgraph "🏗️ SigmaX风控系统最佳架构设计"
        subgraph "API层 - 统一入口"
            A1[RiskManager]
            A2["check_order_risk()"]
            A3["check_position_risk()"]
            A4["get_risk_metrics()"]
        end
        
        subgraph "核心层 - 微内核"
            B1[RiskCore]
            B2["规则调度器"]
            B3["结果聚合器"]
            B4["性能监控器"]
            B5["缓存管理器"]
        end
        
        subgraph "规则层 - 可插拔"
            C1[OrderAmountRule]
            C2[DrawdownRule]
            C3[LeverageRule]
            C4[VolatilityRule]
            C5[CustomRule]
        end
        
        subgraph "数据层 - 统一访问"
            D1[RiskRepository]
            D2["批量查询优化"]
            D3["连接池管理"]
            D4["SQL缓存"]
        end
        
        subgraph "事件层 - 异步通知"
            E1[RiskEventBus]
            E2["风险预警事件"]
            E3["违规通知事件"]
            E4["性能指标事件"]
        end
        
        subgraph "存储层"
            F1[(PostgreSQL)]
            F2[(Redis Cache)]
            F3[(Metrics Store)]
        end
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    
    B3 --> D1
    B4 --> D2
    B5 --> D3
    
    B1 --> E1
    E1 --> E2
    E1 --> E3
    E1 --> E4
    
    D1 --> F1
    D2 --> F2
    D3 --> F3
    
    style B1 fill:#e1f5fe
    style C1 fill:#f3e5f5
    style D1 fill:#e8f5e8
    style E1 fill:#fff3e0