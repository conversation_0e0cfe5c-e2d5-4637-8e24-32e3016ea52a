# Cargo配置文件 - 测试和构建配置

[build]
# 启用增量编译以加快构建速度
incremental = true

[target.x86_64-pc-windows-msvc]
# Windows特定配置
rustflags = ["-C", "target-cpu=native"]

[env]
# 测试环境变量
RUST_TEST_THREADS = "1"  # 串行运行测试以避免并发问题
RUST_BACKTRACE = "1"     # 启用错误回溯
RUST_LOG = "debug"       # 设置日志级别

[alias]
# 常用命令别名
test-all = "test --workspace --all-features"
test-unit = "test --lib --workspace"
test-integration = "test --test integration_tests"
test-doc = "test --doc --workspace"
check-all = "check --workspace --all-targets --all-features"
fmt-check = "fmt --all -- --check"
clippy-all = "clippy --workspace --all-targets --all-features -- -D warnings"
build-release = "build --release --workspace"
clean-all = "clean"

# 测试相关别名
test-core = "test --package sigmax-core"
test-data = "test --package sigmax-data"
test-engines = "test --package sigmax-engines"
test-exchange = "test --package sigmax-exchange"
test-strategies = "test --package sigmax-strategies"
test-web = "test --package sigmax-web"

# 开发工具别名
dev-check = ["check-all", "fmt-check", "clippy-all"]
dev-test = ["test-unit", "test-integration", "test-doc"]
dev-all = ["dev-check", "dev-test"]
