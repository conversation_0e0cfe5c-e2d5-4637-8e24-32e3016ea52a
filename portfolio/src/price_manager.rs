//! 价格数据管理器
//!
//! 负责管理实时价格数据，支持价格缓存、价格更新和价格查询

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rust_decimal::Decimal;
use chrono::{DateTime, Utc};
use sigmax_core::{SigmaXResult, TradingPair, Amount};
use tracing::{info, debug, warn};

/// 价格数据项
#[derive(Debug, Clone)]
pub struct PriceData {
    pub asset: String,
    pub price: Decimal,
    pub timestamp: DateTime<Utc>,
    pub source: PriceSource,
}

/// 价格数据来源
#[derive(Debug, Clone)]
pub enum PriceSource {
    /// 交易数据
    Trade,
    /// 市场数据
    Market,
    /// 手动设置
    Manual,
    /// 默认价格
    Default,
}

/// 价格数据管理器
pub struct PriceManager {
    /// 价格缓存 (资产 -> 价格数据)
    prices: Arc<RwLock<HashMap<String, PriceData>>>,
    /// 默认价格配置
    default_prices: Arc<RwLock<HashMap<String, Decimal>>>,
    /// 价格更新回调
    update_callbacks: Arc<RwLock<Vec<Box<dyn Fn(&PriceData) + Send + Sync>>>>,
}

impl PriceManager {
    pub fn new() -> Self {
        Self {
            prices: Arc::new(RwLock::new(HashMap::new())),
            default_prices: Arc::new(RwLock::new(HashMap::new())),
            update_callbacks: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 设置默认价格
    pub async fn set_default_price(&self, asset: &str, price: Decimal) -> SigmaXResult<()> {
        let mut defaults = self.default_prices.write().await;
        defaults.insert(asset.to_string(), price);
        debug!("设置默认价格: {} = {}", asset, price);
        Ok(())
    }

    /// 更新价格数据
    pub async fn update_price(&self, asset: &str, price: Decimal, source: PriceSource) -> SigmaXResult<()> {
        let price_data = PriceData {
            asset: asset.to_string(),
            price,
            timestamp: Utc::now(),
            source,
        };

        // 更新价格缓存
        {
            let mut prices = self.prices.write().await;
            prices.insert(asset.to_string(), price_data.clone());
        }

        // 触发回调
        self.trigger_update_callbacks(&price_data).await;

        debug!("更新价格: {} = {} (来源: {:?})", asset, price, price_data.source);
        Ok(())
    }

    /// 从交易数据更新价格
    pub async fn update_price_from_trade(&self, trading_pair: &TradingPair, price: Decimal) -> SigmaXResult<()> {
        // 更新基础资产价格（以计价资产为单位）
        self.update_price(&trading_pair.base, price, PriceSource::Trade).await?;

        // 如果计价资产不是USDT，也需要更新其价格
        if trading_pair.quote != "USDT" {
            // 这里可以添加更复杂的价格转换逻辑
            // 暂时假设所有非USDT资产都需要转换为USDT价格
        }

        Ok(())
    }

    /// 获取价格
    pub async fn get_price(&self, asset: &str) -> SigmaXResult<Option<Decimal>> {
        let prices = self.prices.read().await;

        if let Some(price_data) = prices.get(asset) {
            Ok(Some(price_data.price))
        } else {
            // 尝试获取默认价格
            let defaults = self.default_prices.read().await;
            Ok(defaults.get(asset).copied())
        }
    }

    /// 获取价格数据（包含时间戳和来源）
    pub async fn get_price_data(&self, asset: &str) -> SigmaXResult<Option<PriceData>> {
        let prices = self.prices.read().await;
        Ok(prices.get(asset).cloned())
    }

    /// 获取所有价格
    pub async fn get_all_prices(&self) -> SigmaXResult<HashMap<String, Decimal>> {
        let prices = self.prices.read().await;
        let result = prices.iter()
            .map(|(asset, data)| (asset.clone(), data.price))
            .collect();
        Ok(result)
    }

    /// 计算资产的USDT价值
    pub async fn calculate_usdt_value(&self, asset: &str, amount: Decimal) -> SigmaXResult<Decimal> {
        if asset == "USDT" || asset == "USD" {
            // 稳定币直接返回
            return Ok(amount);
        }

        if let Some(price) = self.get_price(asset).await? {
            Ok(amount * price)
        } else {
            warn!("缺少 {} 的价格数据，无法计算USDT价值", asset);
            Ok(Decimal::ZERO)
        }
    }

    /// 批量计算投资组合的总USDT价值
    pub async fn calculate_portfolio_value(&self, balances: &HashMap<String, Decimal>) -> SigmaXResult<Decimal> {
        let mut total_value = Decimal::ZERO;

        for (asset, amount) in balances {
            let usdt_value = self.calculate_usdt_value(asset, *amount).await?;
            total_value += usdt_value;
        }

        Ok(total_value)
    }

    /// 检查价格数据是否过期
    pub async fn is_price_stale(&self, asset: &str, max_age_seconds: i64) -> SigmaXResult<bool> {
        let prices = self.prices.read().await;

        if let Some(price_data) = prices.get(asset) {
            let age = Utc::now().signed_duration_since(price_data.timestamp);
            Ok(age.num_seconds() > max_age_seconds)
        } else {
            Ok(true) // 没有价格数据视为过期
        }
    }

    /// 清理过期价格数据
    pub async fn cleanup_stale_prices(&self, max_age_seconds: i64) -> SigmaXResult<usize> {
        let mut prices = self.prices.write().await;
        let now = Utc::now();
        let mut removed_count = 0;

        prices.retain(|asset, price_data| {
            let age = now.signed_duration_since(price_data.timestamp);
            let is_stale = age.num_seconds() > max_age_seconds;

            if is_stale {
                debug!("清理过期价格数据: {} (年龄: {}秒)", asset, age.num_seconds());
                removed_count += 1;
            }

            !is_stale
        });

        if removed_count > 0 {
            info!("清理了 {} 个过期价格数据", removed_count);
        }

        Ok(removed_count)
    }

    /// 添加价格更新回调
    pub async fn add_update_callback<F>(&self, callback: F) -> SigmaXResult<()>
    where
        F: Fn(&PriceData) + Send + Sync + 'static,
    {
        let mut callbacks = self.update_callbacks.write().await;
        callbacks.push(Box::new(callback));
        Ok(())
    }

    /// 触发价格更新回调
    async fn trigger_update_callbacks(&self, price_data: &PriceData) {
        let callbacks = self.update_callbacks.read().await;
        for callback in callbacks.iter() {
            callback(price_data);
        }
    }

    /// 获取价格统计信息
    pub async fn get_price_stats(&self) -> SigmaXResult<PriceStats> {
        let prices = self.prices.read().await;
        let defaults = self.default_prices.read().await;

        let total_assets = prices.len();
        let default_assets = defaults.len();

        let mut source_counts = HashMap::new();
        for price_data in prices.values() {
            let count = source_counts.entry(format!("{:?}", price_data.source)).or_insert(0);
            *count += 1;
        }

        let oldest_timestamp = prices.values()
            .map(|data| data.timestamp)
            .min();

        let newest_timestamp = prices.values()
            .map(|data| data.timestamp)
            .max();

        Ok(PriceStats {
            total_assets,
            default_assets,
            source_counts,
            oldest_timestamp,
            newest_timestamp,
        })
    }

    /// 打印价格状态报告
    pub async fn print_price_report(&self) -> SigmaXResult<()> {
        println!("\n💰 价格数据管理报告");
        println!("{}", "=".repeat(50));

        let stats = self.get_price_stats().await?;
        let prices = self.get_all_prices().await?;

        println!("📊 统计信息:");
        println!("   总资产数: {}", stats.total_assets);
        println!("   默认价格数: {}", stats.default_assets);

        if let (Some(oldest), Some(newest)) = (stats.oldest_timestamp, stats.newest_timestamp) {
            println!("   最旧数据: {}", oldest.format("%Y-%m-%d %H:%M:%S"));
            println!("   最新数据: {}", newest.format("%Y-%m-%d %H:%M:%S"));
        }

        println!("\n📈 价格来源分布:");
        for (source, count) in &stats.source_counts {
            println!("   {}: {}", source, count);
        }

        println!("\n💵 当前价格:");
        for (asset, price) in &prices {
            println!("   {}: {}", asset, price);
        }

        println!("{}", "=".repeat(50));
        Ok(())
    }
}

/// 价格统计信息
#[derive(Debug, Clone)]
pub struct PriceStats {
    pub total_assets: usize,
    pub default_assets: usize,
    pub source_counts: HashMap<String, usize>,
    pub oldest_timestamp: Option<DateTime<Utc>>,
    pub newest_timestamp: Option<DateTime<Utc>>,
}

impl Default for PriceManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_price_manager_basic_operations() {
        let manager = PriceManager::new();

        // 设置默认价格
        manager.set_default_price("BTC", Decimal::new(50000, 0)).await.unwrap();

        // 更新价格
        manager.update_price("BTC", Decimal::new(51000, 0), PriceSource::Market).await.unwrap();

        // 获取价格
        let price = manager.get_price("BTC").await.unwrap();
        assert_eq!(price, Some(Decimal::new(51000, 0)));

        // 计算USDT价值
        let value = manager.calculate_usdt_value("BTC", Decimal::new(1, 1)).await.unwrap(); // 0.1 BTC
        assert_eq!(value, Decimal::new(5100, 0)); // 0.1 * 51000 = 5100
    }

    #[tokio::test]
    async fn test_portfolio_value_calculation() {
        let manager = PriceManager::new();

        // 设置价格
        manager.update_price("BTC", Decimal::new(50000, 0), PriceSource::Market).await.unwrap();
        manager.update_price("ETH", Decimal::new(3000, 0), PriceSource::Market).await.unwrap();

        // 创建投资组合
        let mut balances = HashMap::new();
        balances.insert("USDT".to_string(), Decimal::new(1000, 0)); // 1000 USDT
        balances.insert("BTC".to_string(), Decimal::new(1, 1)); // 0.1 BTC
        balances.insert("ETH".to_string(), Decimal::new(5, 1)); // 0.5 ETH

        // 计算总价值
        let total_value = manager.calculate_portfolio_value(&balances).await.unwrap();
        // 1000 + 0.1*50000 + 0.5*3000 = 1000 + 5000 + 1500 = 7500
        assert_eq!(total_value, Decimal::new(7500, 0));
    }
}
