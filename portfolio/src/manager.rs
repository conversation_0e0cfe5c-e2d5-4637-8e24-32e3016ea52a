//! 投资组合管理器实现

use async_trait::async_trait;
use sigmax_core::{Amount, Balance, PortfolioManager, EnhancedPortfolioManager, SigmaXResult, Trade, OrderSide, ExchangeId};
use crate::price_manager::{PriceManager, PriceSource};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rust_decimal::Decimal;
use std::str::FromStr;
use tracing::{info, debug};

/// 增强的投资组合管理器，支持内部可变性和并发安全
pub struct DefaultPortfolioManager {
    /// 资产余额 (使用内部可变性)
    balances: Arc<RwLock<HashMap<String, Balance>>>,
    /// 初始资金
    initial_value: Amount,
    /// 🔥 价格管理器 (替代简单的价格缓存)
    price_manager: Arc<PriceManager>,
}

impl DefaultPortfolioManager {
    pub fn new() -> Self {
        Self {
            balances: Arc::new(RwLock::new(HashMap::new())),
            initial_value: Amount::ZERO,
            price_manager: Arc::new(PriceManager::new()),
        }
    }

    pub fn with_initial_value(initial_value: Amount) -> Self {
        let mut initial_balances = HashMap::new();

        // 设置初始USDT余额
        initial_balances.insert("USDT".to_string(), Balance::new(
            ExchangeId::Simulator,
            "USDT".to_string(),
            initial_value,
            Amount::ZERO,
        ));

        Self {
            balances: Arc::new(RwLock::new(initial_balances)),
            initial_value,
            price_manager: Arc::new(PriceManager::new()),
        }
    }

    /// 🔥 新增：为网格策略创建带有基础资产余额的投资组合管理器
    pub fn with_initial_value_for_grid_strategy(
        initial_value: Amount,
        base_asset: &str,
        quote_asset: &str,
        base_price: Amount
    ) -> Self {
        let mut initial_balances = HashMap::new();

        // 50% 资金用于计价资产（用于买入操作）
        let quote_amount = initial_value * Decimal::from_str("0.5").unwrap();
        initial_balances.insert(quote_asset.to_string(), Balance::new(
            ExchangeId::Simulator,
            quote_asset.to_string(),
            quote_amount,
            Amount::ZERO,
        ));

        // 50% 资金转换为基础资产（用于卖出操作）
        let base_amount = (initial_value * Decimal::from_str("0.5").unwrap()) / base_price;
        initial_balances.insert(base_asset.to_string(), Balance::new(
            ExchangeId::Simulator,
            base_asset.to_string(),
            base_amount,
            Amount::ZERO,
        ));

        info!("🏦 为网格策略初始化投资组合:");
        info!("   {} 余额: {}", quote_asset, quote_amount);
        info!("   {} 余额: {} (基于价格 {})", base_asset, base_amount, base_price);
        info!("   总价值: {}", initial_value);

        Self {
            balances: Arc::new(RwLock::new(initial_balances)),
            initial_value,
            price_manager: Arc::new(PriceManager::new()),
        }
    }

    /// 🔥 核心方法：根据交易更新投资组合
    pub async fn update_from_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        info!("🔄 开始处理交易: {:?} {} {} @ {}",
              trade.side, trade.quantity, trade.trading_pair.symbol(), trade.price);

        // 1. 更新资产余额
        self.update_asset_balances(trade).await?;

        // 2. 扣除手续费
        self.deduct_trading_fees(trade).await?;

        // 3. 更新当前价格
        self.update_current_price(&trade.trading_pair.base, trade.price).await?;

        info!("✅ 交易处理完成");
        Ok(())
    }

    /// 更新资产余额
    async fn update_asset_balances(&self, trade: &Trade) -> SigmaXResult<()> {
        let mut balances = self.balances.write().await;

        let base_asset = &trade.trading_pair.base;
        let quote_asset = &trade.trading_pair.quote;

        match trade.side {
            OrderSide::Buy => {
                // 买入：增加基础资产，减少计价资产
                debug!("📈 买入交易处理:");

                // 增加基础资产
                let base_balance = balances.entry(base_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        base_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let old_base = base_balance.free;
                base_balance.free += trade.quantity;
                debug!("   {} 余额: {} -> {}", base_asset, old_base, base_balance.free);

                // 减少计价资产
                let quote_balance = balances.entry(quote_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        quote_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let cost = trade.quantity * trade.price;
                let old_quote = quote_balance.free;
                quote_balance.free -= cost;
                debug!("   {} 余额: {} -> {}", quote_asset, old_quote, quote_balance.free);
            }
            OrderSide::Sell => {
                // 卖出：减少基础资产，增加计价资产
                debug!("📉 卖出交易处理:");

                // 减少基础资产
                let base_balance = balances.entry(base_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        base_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let old_base = base_balance.free;
                base_balance.free -= trade.quantity;
                debug!("   {} 余额: {} -> {}", base_asset, old_base, base_balance.free);

                // 增加计价资产
                let quote_balance = balances.entry(quote_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        quote_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let revenue = trade.quantity * trade.price;
                let old_quote = quote_balance.free;
                quote_balance.free += revenue;
                debug!("   {} 余额: {} -> {}", quote_asset, old_quote, quote_balance.free);
            }
        }

        Ok(())
    }

    /// 扣除交易手续费
    async fn deduct_trading_fees(&self, trade: &Trade) -> SigmaXResult<()> {
        if trade.fee > Decimal::ZERO {
            if let Some(fee_asset) = &trade.fee_asset {
                let mut balances = self.balances.write().await;

                let fee_balance = balances.entry(fee_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        fee_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));

                let old_balance = fee_balance.free;
                fee_balance.free -= trade.fee;
                debug!("💰 扣除手续费: {} {} (余额: {} -> {})",
                       trade.fee, fee_asset, old_balance, fee_balance.free);
            }
        }
        Ok(())
    }

    /// 更新当前价格
    async fn update_current_price(&self, asset: &str, price: Decimal) -> SigmaXResult<()> {
        self.price_manager.update_price_from_trade(
            &sigmax_core::TradingPair::new(asset, "USDT"),
            price
        ).await?;
        Ok(())
    }
}

#[async_trait]
impl PortfolioManager for DefaultPortfolioManager {
    async fn get_balances(&self) -> SigmaXResult<HashMap<String, Balance>> {
        let balances = self.balances.read().await;
        Ok(balances.clone())
    }

    async fn update_balance(&mut self, balance: Balance) -> SigmaXResult<()> {
        let mut balances = self.balances.write().await;
        balances.insert(balance.asset.clone(), balance);
        Ok(())
    }

    async fn calculate_total_value(&self) -> SigmaXResult<Amount> {
        let balances = self.balances.read().await;

        // 将Balance转换为简单的HashMap<String, Decimal>
        let balance_amounts: HashMap<String, Decimal> = balances.iter()
            .map(|(asset, balance)| (asset.clone(), balance.free))
            .collect();

        // 使用价格管理器计算总价值
        self.price_manager.calculate_portfolio_value(&balance_amounts).await
    }

    async fn get_pnl(&self) -> SigmaXResult<Amount> {
        let current_value = self.calculate_total_value().await?;
        Ok(current_value - self.initial_value)
    }

    /// 🔥 实现类型转换支持
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[async_trait]
impl EnhancedPortfolioManager for DefaultPortfolioManager {
    /// 🔥 根据交易更新投资组合（并发安全）
    async fn update_from_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        // 直接调用我们已经实现的方法
        self.update_from_trade(trade).await
    }

    /// 🔥 更新当前价格（用于价值计算）
    async fn update_current_price(&self, asset: &str, price: Amount) -> SigmaXResult<()> {
        self.update_current_price(asset, price).await
    }

    /// 🔥 批量更新余额（并发安全）
    async fn batch_update_balances(&self, balances: HashMap<String, Balance>) -> SigmaXResult<()> {
        let mut current_balances = self.balances.write().await;
        for (asset, balance) in balances {
            current_balances.insert(asset, balance);
        }
        Ok(())
    }

    /// 🔥 获取特定资产余额
    async fn get_asset_balance(&self, asset: &str) -> SigmaXResult<Option<Balance>> {
        let balances = self.balances.read().await;
        Ok(balances.get(asset).cloned())
    }

    /// 🔥 检查资金充足性
    async fn check_sufficient_balance(&self, asset: &str, required_amount: Amount) -> SigmaXResult<bool> {
        let balances = self.balances.read().await;
        if let Some(balance) = balances.get(asset) {
            Ok(balance.free >= required_amount)
        } else {
            Ok(false)
        }
    }
}
