//! 仓位跟踪器

use sigmax_core::{Position, SigmaXResult, TradingPair, Trade, Amount};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rust_decimal::Decimal;
use tracing::{info, debug, warn};

/// 增强的仓位跟踪器，支持并发安全和实时更新
pub struct PositionTracker {
    /// 仓位信息 (使用内部可变性)
    positions: Arc<RwLock<HashMap<TradingPair, Position>>>,
    /// 当前价格缓存 (用于计算未实现盈亏)
    current_prices: Arc<RwLock<HashMap<String, Decimal>>>,
}

impl PositionTracker {
    pub fn new() -> Self {
        Self {
            positions: Arc::new(RwLock::new(HashMap::new())),
            current_prices: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 🔥 核心方法：根据交易更新仓位
    pub async fn update_position_from_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        info!("🔄 更新仓位: {:?} {} {} @ {}",
              trade.side, trade.quantity, trade.trading_pair.symbol(), trade.price);

        let mut positions = self.positions.write().await;

        let position = positions
            .entry(trade.trading_pair.clone())
            .or_insert_with(|| Position {
                trading_pair: trade.trading_pair.clone(),
                quantity: Decimal::ZERO,
                average_price: Decimal::ZERO,
                unrealized_pnl: Decimal::ZERO,
                realized_pnl: Decimal::ZERO,
            });

        // 更新仓位逻辑
        match trade.side {
            sigmax_core::OrderSide::Buy => {
                debug!("📈 处理买入仓位更新:");

                let old_quantity = position.quantity;
                let old_avg_price = position.average_price;

                // 计算新的平均成本价格
                let old_total_cost = position.quantity * position.average_price;
                let new_trade_cost = trade.quantity * trade.price;
                let new_total_quantity = position.quantity + trade.quantity;

                if new_total_quantity > Decimal::ZERO {
                    position.average_price = (old_total_cost + new_trade_cost) / new_total_quantity;
                }
                position.quantity = new_total_quantity;

                debug!("   持仓数量: {} -> {}", old_quantity, position.quantity);
                debug!("   平均价格: {} -> {}", old_avg_price, position.average_price);
            }
            sigmax_core::OrderSide::Sell => {
                debug!("📉 处理卖出仓位更新:");

                let old_quantity = position.quantity;
                let old_realized_pnl = position.realized_pnl;

                // 计算已实现盈亏
                let realized_pnl = trade.quantity * (trade.price - position.average_price);
                position.realized_pnl += realized_pnl;
                position.quantity -= trade.quantity;

                debug!("   持仓数量: {} -> {}", old_quantity, position.quantity);
                debug!("   已实现盈亏: +{} (总计: {} -> {})",
                       realized_pnl, old_realized_pnl, position.realized_pnl);
            }
        }

        // 释放positions锁
        drop(positions);

        // 更新当前价格并重新计算未实现盈亏
        self.update_current_price(&trade.trading_pair.base, trade.price).await?;
        self.recalculate_unrealized_pnl(&trade.trading_pair).await?;

        info!("✅ 仓位更新完成");
        Ok(())
    }

    /// 更新当前价格
    pub async fn update_current_price(&self, asset: &str, price: Decimal) -> SigmaXResult<()> {
        let mut prices = self.current_prices.write().await;
        prices.insert(asset.to_string(), price);
        debug!("💰 更新价格: {} = {}", asset, price);
        Ok(())
    }

    /// 重新计算特定交易对的未实现盈亏
    async fn recalculate_unrealized_pnl(&self, trading_pair: &TradingPair) -> SigmaXResult<()> {
        let prices = self.current_prices.read().await;
        let mut positions = self.positions.write().await;

        if let Some(position) = positions.get_mut(trading_pair) {
            if position.quantity > Decimal::ZERO {
                if let Some(current_price) = prices.get(&trading_pair.base) {
                    let old_unrealized_pnl = position.unrealized_pnl;
                    position.unrealized_pnl = position.quantity * (current_price - position.average_price);
                    debug!("📊 更新未实现盈亏: {} -> {} (当前价格: {}, 平均成本: {})",
                           old_unrealized_pnl, position.unrealized_pnl, current_price, position.average_price);
                } else {
                    warn!("缺少 {} 的价格数据，无法计算未实现盈亏", trading_pair.base);
                }
            }
        }

        Ok(())
    }

    /// 获取所有仓位的快照
    pub async fn get_positions_snapshot(&self) -> SigmaXResult<HashMap<TradingPair, Position>> {
        let positions = self.positions.read().await;
        Ok(positions.clone())
    }

    /// 获取特定交易对的仓位
    pub async fn get_position(&self, trading_pair: &TradingPair) -> SigmaXResult<Option<Position>> {
        let positions = self.positions.read().await;
        Ok(positions.get(trading_pair).cloned())
    }

    /// 计算总的已实现盈亏
    pub async fn total_realized_pnl(&self) -> SigmaXResult<Amount> {
        let positions = self.positions.read().await;
        let total = positions.values()
            .map(|position| position.realized_pnl)
            .sum();
        Ok(total)
    }

    /// 计算总的未实现盈亏
    pub async fn total_unrealized_pnl(&self) -> SigmaXResult<Amount> {
        let positions = self.positions.read().await;
        let total = positions.values()
            .map(|position| position.unrealized_pnl)
            .sum();
        Ok(total)
    }

    /// 计算总盈亏
    pub async fn total_pnl(&self) -> SigmaXResult<Amount> {
        let realized = self.total_realized_pnl().await?;
        let unrealized = self.total_unrealized_pnl().await?;
        Ok(realized + unrealized)
    }
}
