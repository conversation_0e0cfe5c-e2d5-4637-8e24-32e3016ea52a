//! SigmaX 网格交易系统主程序

use sigmax_core::*;
use sigmax_database::DatabaseManager;
use sigmax_web::{create_app_state, start_server};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 SigmaX Trading System Starting...");

    // 1. 加载配置
    let config = AppConfig::default();
    println!("✅ Configuration loaded");

    // 2. 初始化日志系统
    init_logging(&config.logging)?;
    tracing::info!("SigmaX system starting up");

    // 3. 测试数据库连接（如果配置了数据库）
    if !config.database.url.is_empty() {
        match DatabaseManager::new(config.database.clone()).await {
            Ok(db_manager) => {
                match db_manager.health_check().await {
                    Ok(_) => {
                        tracing::info!("✅ Database connection successful");

                        // 获取数据库统计信息
                        match db_manager.get_stats().await {
                            Ok(stats) => {
                                tracing::info!("📊 Database stats: {:?}", stats);
                            }
                            Err(e) => {
                                tracing::warn!("Failed to get database stats: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ Database health check failed: {}", e);
                    }
                }
            }
            Err(e) => {
                tracing::warn!("⚠️ Database connection failed (this is expected if no database is configured): {}", e);
            }
        }
    } else {
        tracing::info!("📝 No database configured, skipping database initialization");
    }

    // 4. 显示系统信息
    tracing::info!("🎯 SigmaX Trading System initialized successfully!");
    tracing::info!("📋 System Configuration:");
    tracing::info!("  - Web Host: {}:{}", config.web.host, config.web.port);
    tracing::info!("  - Log Level: {}", config.logging.level);
    tracing::info!("  - Database URL: {}", if config.database.url.is_empty() { "Not configured" } else { "Configured" });

    // 5. 创建应用状态
    let app_state = create_app_state(config.clone()).await?;

    // 6. 启动Web服务器
    tracing::info!("🌐 Starting web server on {}:{}", config.web.host, config.web.port);
    println!("🌐 Web server starting on http://{}:{}", config.web.host, config.web.port);

    // 启动服务器（这会阻塞直到服务器停止）
    start_server(app_state, &config.web.host, config.web.port).await?;

    tracing::info!("👋 SigmaX system shutting down...");
    println!("✅ SigmaX Trading System stopped.");

    Ok(())
}
