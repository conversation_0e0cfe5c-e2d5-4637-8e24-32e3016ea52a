{"performance_thresholds": {"response_time": {"health_check": {"target": 100, "warning": 500, "critical": 1000, "unit": "ms", "description": "健康检查API响应时间"}, "file_list": {"target": 500, "warning": 2000, "critical": 5000, "unit": "ms", "description": "文件列表API响应时间"}, "engine_creation": {"target": 1000, "warning": 3000, "critical": 10000, "unit": "ms", "description": "引擎创建API响应时间"}, "backtest_start": {"target": 500, "warning": 1500, "critical": 5000, "unit": "ms", "description": "回测启动API响应时间"}, "result_retrieval": {"target": 1000, "warning": 3000, "critical": 8000, "unit": "ms", "description": "结果获取API响应时间"}}, "throughput": {"concurrent_requests": {"target": 100, "warning": 50, "critical": 20, "unit": "requests/second", "description": "并发请求处理能力"}, "data_processing": {"target": 1000, "warning": 500, "critical": 100, "unit": "records/second", "description": "数据处理吞吐量"}}, "resource_usage": {"memory": {"target": 512, "warning": 1024, "critical": 2048, "unit": "MB", "description": "内存使用量"}, "cpu": {"target": 50, "warning": 80, "critical": 95, "unit": "percent", "description": "CPU使用率"}, "disk_io": {"target": 100, "warning": 500, "critical": 1000, "unit": "MB/s", "description": "磁盘IO速率"}}, "availability": {"uptime": {"target": 99.9, "warning": 99.5, "critical": 99.0, "unit": "percent", "description": "系统可用性"}, "error_rate": {"target": 0.1, "warning": 1.0, "critical": 5.0, "unit": "percent", "description": "错误率"}}, "websocket": {"connection_time": {"target": 500, "warning": 1000, "critical": 3000, "unit": "ms", "description": "WebSocket连接建立时间"}, "message_latency": {"target": 100, "warning": 300, "critical": 1000, "unit": "ms", "description": "消息传输延迟"}, "connection_stability": {"target": 99.5, "warning": 98.0, "critical": 95.0, "unit": "percent", "description": "连接稳定性"}}, "database": {"query_time": {"target": 50, "warning": 200, "critical": 1000, "unit": "ms", "description": "数据库查询时间"}, "connection_pool": {"target": 10, "warning": 50, "critical": 100, "unit": "connections", "description": "数据库连接池使用"}}}, "sla_targets": {"availability": {"monthly": 99.9, "weekly": 99.95, "daily": 99.99}, "performance": {"p50_response_time": 500, "p95_response_time": 2000, "p99_response_time": 5000}, "reliability": {"error_rate": 0.1, "timeout_rate": 0.05, "success_rate": 99.9}}, "monitoring_intervals": {"real_time": 1, "short_term": 60, "medium_term": 300, "long_term": 3600}, "alert_rules": {"critical": {"response_time_exceeded": {"condition": "response_time > critical_threshold", "duration": 30, "action": "immediate_alert"}, "error_rate_high": {"condition": "error_rate > 5%", "duration": 60, "action": "immediate_alert"}, "service_unavailable": {"condition": "availability < 95%", "duration": 10, "action": "immediate_alert"}}, "warning": {"performance_degradation": {"condition": "response_time > warning_threshold", "duration": 300, "action": "warning_alert"}, "resource_usage_high": {"condition": "cpu_usage > 80% or memory_usage > 80%", "duration": 600, "action": "warning_alert"}}}}