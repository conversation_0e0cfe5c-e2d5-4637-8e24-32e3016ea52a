{"environments": {"local": {"name": "本地开发环境", "api_base_url": "http://127.0.0.1:8080", "websocket_url": "ws://127.0.0.1:8080/ws", "database_url": "", "timeout": 30, "retry_count": 3, "enabled": true, "description": "本地开发和测试环境"}, "staging": {"name": "预发布环境", "api_base_url": "http://staging.sigmaxtrading.com:8080", "websocket_url": "ws://staging.sigmaxtrading.com:8080/ws", "database_url": "******************************************************/sigmaxdb", "timeout": 60, "retry_count": 5, "enabled": false, "description": "预发布测试环境"}, "production": {"name": "生产环境", "api_base_url": "https://api.sigmaxtrading.com", "websocket_url": "wss://api.sigmaxtrading.com/ws", "database_url": "*********************************************/sigmaxdb", "timeout": 120, "retry_count": 2, "enabled": false, "description": "生产环境（仅只读验证）"}}, "validation_settings": {"concurrent_limit": 5, "batch_size": 10, "report_interval": 60, "log_level": "INFO", "enable_screenshots": false, "enable_video_recording": false, "cleanup_after_test": true}, "notification_settings": {"email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "headers": {}}, "slack": {"enabled": false, "webhook_url": "", "channel": "#api-validation"}}, "security_settings": {"api_key": "", "jwt_token": "", "basic_auth": {"username": "", "password": ""}, "ssl_verify": true, "rate_limit": {"requests_per_second": 10, "burst_limit": 50}}}