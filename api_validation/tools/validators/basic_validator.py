#!/usr/bin/env python3
"""
基础验证器 - 验证API基础功能
包括连通性、基本CRUD操作、错误响应等基础验证
"""

import asyncio
import time
import requests
import json
from typing import Dict, List, Any, Optional
from urllib3.util import Retry
from requests.adapters import HTTPAdapter
import logging

logger = logging.getLogger(__name__)

class BasicValidator:
    """基础API验证器"""
    
    def __init__(self, environment: Dict[str, Any], performance_thresholds: Dict[str, Any]):
        self.environment = environment
        self.performance_thresholds = performance_thresholds
        self.session = self._create_session()
        self.results = []
        
    def _create_session(self) -> requests.Session:
        """创建配置了重试机制的会话"""
        session = requests.Session()
        
        retry_strategy = Retry(
            total=self.environment.get("retry_count", 3),
            backoff_factor=0.1,
            status_forcelist=[502, 503, 504, 429]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.environment['api_base_url']}{endpoint}"
        start_time = time.time()
        
        try:
            response = self.session.request(
                method, url, 
                timeout=self.environment.get("timeout", 30),
                **kwargs
            )
            
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            result = {
                "success": True,
                "status_code": response.status_code,
                "response_time": response_time,
                "url": url,
                "method": method
            }
            
            # 尝试解析JSON响应
            try:
                if response.content:
                    result["data"] = response.json()
                else:
                    result["data"] = {}
            except json.JSONDecodeError:
                result["data"] = {"text": response.text}
            
            return result
            
        except requests.exceptions.RequestException as e:
            response_time = (time.time() - start_time) * 1000
            return {
                "success": False,
                "error": str(e),
                "response_time": response_time,
                "url": url,
                "method": method
            }
    
    def _validate_response_time(self, response_time: float, endpoint_type: str) -> Dict[str, Any]:
        """验证响应时间"""
        thresholds = self.performance_thresholds["performance_thresholds"]["response_time"]
        
        if endpoint_type in thresholds:
            threshold = thresholds[endpoint_type]
            
            if response_time <= threshold["target"]:
                status = "excellent"
            elif response_time <= threshold["warning"]:
                status = "good"
            elif response_time <= threshold["critical"]:
                status = "warning"
            else:
                status = "critical"
            
            return {
                "status": status,
                "response_time": response_time,
                "target": threshold["target"],
                "warning": threshold["warning"],
                "critical": threshold["critical"]
            }
        
        return {"status": "unknown", "response_time": response_time}
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查API"""
        logger.info("测试健康检查API")
        
        result = self._make_request("GET", "/api/health")
        
        # 验证响应时间
        perf_result = self._validate_response_time(result["response_time"], "health_check")
        
        test_result = {
            "test_name": "health_check",
            "success": result["success"] and result.get("status_code") == 200,
            "response_time": result["response_time"],
            "performance": perf_result,
            "details": result
        }
        
        # 验证响应内容
        if result["success"] and result.get("data"):
            data = result["data"]
            if "status" in data or "overall" in data:
                test_result["content_valid"] = True
            else:
                test_result["content_valid"] = False
                test_result["content_error"] = "Missing status field in response"
        
        self.results.append(test_result)
        return test_result
    
    async def test_backtest_files_list(self) -> Dict[str, Any]:
        """测试回测文件列表API"""
        logger.info("测试回测文件列表API")
        
        result = self._make_request("GET", "/api/backtest/files")
        
        # 验证响应时间
        perf_result = self._validate_response_time(result["response_time"], "file_list")
        
        test_result = {
            "test_name": "backtest_files_list",
            "success": result["success"] and result.get("status_code") == 200,
            "response_time": result["response_time"],
            "performance": perf_result,
            "details": result
        }
        
        # 验证响应内容
        if result["success"] and result.get("data"):
            data = result["data"]
            if "files" in data and isinstance(data["files"], list):
                test_result["content_valid"] = True
                test_result["file_count"] = len(data["files"])
            else:
                test_result["content_valid"] = False
                test_result["content_error"] = "Missing or invalid files array"
        
        self.results.append(test_result)
        return test_result
    
    async def test_engine_creation(self) -> Dict[str, Any]:
        """测试引擎创建API"""
        logger.info("测试引擎创建API")
        
        payload = {
            "engine_type": "Backtest",
            "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
            "initial_capital": "700.00"
        }
        
        result = self._make_request("POST", "/api/engines", json=payload)
        
        # 验证响应时间
        perf_result = self._validate_response_time(result["response_time"], "engine_creation")
        
        test_result = {
            "test_name": "engine_creation",
            "success": result["success"] and result.get("status_code") in [200, 201],
            "response_time": result["response_time"],
            "performance": perf_result,
            "details": result
        }
        
        # 验证响应内容
        if result["success"] and result.get("data"):
            data = result["data"]
            if "id" in data:
                test_result["content_valid"] = True
                test_result["engine_id"] = data["id"]
                
                # 保存引擎ID用于后续清理
                test_result["cleanup_required"] = True
            else:
                test_result["content_valid"] = False
                test_result["content_error"] = "Missing engine ID in response"
        
        self.results.append(test_result)
        return test_result
    
    async def test_invalid_endpoint(self) -> Dict[str, Any]:
        """测试无效端点的错误处理"""
        logger.info("测试无效端点错误处理")
        
        result = self._make_request("GET", "/api/nonexistent")
        
        test_result = {
            "test_name": "invalid_endpoint",
            "success": result.get("status_code") == 404,  # 期望404错误
            "response_time": result["response_time"],
            "details": result
        }
        
        self.results.append(test_result)
        return test_result
    
    async def test_invalid_json_payload(self) -> Dict[str, Any]:
        """测试无效JSON负载的错误处理"""
        logger.info("测试无效JSON负载错误处理")
        
        # 发送无效JSON
        try:
            response = self.session.post(
                f"{self.environment['api_base_url']}/api/engines",
                data="invalid json",
                headers={"Content-Type": "application/json"},
                timeout=self.environment.get("timeout", 30)
            )
            
            test_result = {
                "test_name": "invalid_json_payload",
                "success": response.status_code == 400,  # 期望400错误
                "status_code": response.status_code,
                "details": {"response_text": response.text}
            }
            
        except Exception as e:
            test_result = {
                "test_name": "invalid_json_payload",
                "success": False,
                "error": str(e)
            }
        
        self.results.append(test_result)
        return test_result
    
    async def cleanup_test_resources(self):
        """清理测试资源"""
        logger.info("清理测试资源")
        
        for result in self.results:
            if result.get("cleanup_required") and result.get("engine_id"):
                engine_id = result["engine_id"]
                try:
                    cleanup_result = self._make_request("DELETE", f"/api/engines/{engine_id}")
                    logger.info(f"清理引擎 {engine_id}: {cleanup_result['success']}")
                except Exception as e:
                    logger.warning(f"清理引擎 {engine_id} 失败: {str(e)}")
    
    async def validate(self) -> Dict[str, Any]:
        """运行基础验证"""
        logger.info("开始基础验证")
        
        # 运行所有基础测试
        tests = [
            self.test_health_check(),
            self.test_backtest_files_list(),
            self.test_engine_creation(),
            self.test_invalid_endpoint(),
            self.test_invalid_json_payload()
        ]
        
        # 并发执行测试
        test_results = await asyncio.gather(*tests, return_exceptions=True)
        
        # 处理异常结果
        for i, result in enumerate(test_results):
            if isinstance(result, Exception):
                logger.error(f"测试 {i} 执行失败: {str(result)}")
                self.results.append({
                    "test_name": f"test_{i}",
                    "success": False,
                    "error": str(result)
                })
        
        # 清理资源
        await self.cleanup_test_resources()
        
        # 生成验证报告
        return self._generate_validation_report()
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.get("success", False))
        failed_tests = total_tests - passed_tests
        
        # 计算平均响应时间
        response_times = [r.get("response_time", 0) for r in self.results if "response_time" in r]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 性能分析
        performance_issues = []
        for result in self.results:
            if result.get("performance", {}).get("status") in ["warning", "critical"]:
                performance_issues.append({
                    "test": result["test_name"],
                    "response_time": result.get("response_time"),
                    "status": result.get("performance", {}).get("status")
                })
        
        report = {
            "status": "pass" if failed_tests == 0 else "fail",
            "test_count": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "performance_data": {
                "avg_response_time": avg_response_time,
                "response_times": {r["test_name"]: r.get("response_time") for r in self.results if "response_time" in r},
                "performance_issues": performance_issues
            },
            "test_results": self.results,
            "summary": {
                "connectivity": "OK" if any(r["test_name"] == "health_check" and r["success"] for r in self.results) else "FAIL",
                "data_access": "OK" if any(r["test_name"] == "backtest_files_list" and r["success"] for r in self.results) else "FAIL",
                "resource_creation": "OK" if any(r["test_name"] == "engine_creation" and r["success"] for r in self.results) else "FAIL",
                "error_handling": "OK" if any(r["test_name"] == "invalid_endpoint" and r["success"] for r in self.results) else "FAIL"
            }
        }
        
        logger.info(f"基础验证完成: {passed_tests}/{total_tests} 通过")
        return report
