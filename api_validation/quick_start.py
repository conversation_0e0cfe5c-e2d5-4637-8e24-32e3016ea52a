#!/usr/bin/env python3
"""
SigmaX API验证中心 - 快速启动脚本
提供简单易用的验证入口，适合快速验证和演示
"""

import asyncio
import sys
import os
from pathlib import Path

# 设置正确的工作目录
script_dir = Path(__file__).parent
os.chdir(script_dir)

# 添加项目路径
sys.path.append(str(script_dir))

from validate import APIValidationCenter

async def quick_validation():
    """快速验证"""
    print("🚀 SigmaX API验证中心 - 快速验证")
    print("=" * 50)

    try:
        # 创建验证中心
        validation_center = APIValidationCenter()

        # 获取环境配置
        environment = validation_center.get_active_environment()
        print(f"📍 使用环境: {environment['name']}")

        # 初始化验证器
        validation_center.initialize_validators(environment)

        # 运行基础验证
        print("\n🔍 运行基础验证...")
        await validation_center.run_validation_level("basic")

        # 生成摘要
        summary = validation_center.generate_summary_report()

        # 输出结果
        print("\n📊 验证结果:")
        print("-" * 30)
        print(f"总测试数: {summary['validation_summary']['total_tests']}")
        print(f"通过: {summary['validation_summary']['passed_tests']}")
        print(f"失败: {summary['validation_summary']['failed_tests']}")
        print(f"成功率: {summary['validation_summary']['success_rate']:.1f}%")

        if summary['validation_summary']['failed_tests'] == 0:
            print("\n✅ 所有基础验证都通过了！")
            print("\n💡 下一步可以运行:")
            print("   python validate.py --all")
        else:
            print("\n❌ 部分验证失败，请检查系统状态")

        return summary['validation_summary']['failed_tests'] == 0

    except Exception as e:
        print(f"\n❌ 验证失败: {str(e)}")
        print("\n🔧 请检查:")
        print("   1. SigmaX服务器是否运行")
        print("   2. 网络连接是否正常")
        print("   3. 配置文件是否正确")
        return False

def main():
    """主函数"""
    success = asyncio.run(quick_validation())
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
