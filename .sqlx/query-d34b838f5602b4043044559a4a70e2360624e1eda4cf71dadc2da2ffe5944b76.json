{"db_name": "PostgreSQL", "query": "\n            SELECT\n                o.id, o.strategy_id, o.trading_pair_id, o.exchange_id,\n                o.exchange_order_id, o.parent_order_id,\n                o.order_type as \"order_type: PgOrderType\",\n                o.side as \"side: PgOrderSide\",\n                o.quantity, o.price, o.stop_price,\n                o.status as \"status: PgOrderStatus\",\n                o.filled_quantity, o.average_price,\n                o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,\n                tp.symbol as trading_pair_symbol,\n                tp.base_asset as trading_pair_base,\n                tp.quote_asset as trading_pair_quote,\n                e.name as exchange_name\n            FROM orders o\n            JOIN trading_pairs tp ON o.trading_pair_id = tp.id\n            JOIN exchanges e ON o.exchange_id = e.id\n            WHERE o.id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "strategy_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "trading_pair_id", "type_info": "Int4"}, {"ordinal": 3, "name": "exchange_id", "type_info": "Int4"}, {"ordinal": 4, "name": "exchange_order_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "parent_order_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 6, "name": "order_type: PgOrderType", "type_info": {"Custom": {"name": "order_type", "kind": {"Enum": ["Market", "Limit", "StopLoss", "StopLimit", "TakeProfit", "TakeProfitLimit"]}}}}, {"ordinal": 7, "name": "side: PgOrderSide", "type_info": {"Custom": {"name": "order_side", "kind": {"Enum": ["Buy", "<PERSON>ll"]}}}}, {"ordinal": 8, "name": "quantity", "type_info": "Numeric"}, {"ordinal": 9, "name": "price", "type_info": "Numeric"}, {"ordinal": 10, "name": "stop_price", "type_info": "Numeric"}, {"ordinal": 11, "name": "status: PgOrderStatus", "type_info": {"Custom": {"name": "order_status", "kind": {"Enum": ["Pending", "Open", "PartiallyFilled", "Filled", "Cancelled", "Rejected", "Expired"]}}}}, {"ordinal": 12, "name": "filled_quantity", "type_info": "Numeric"}, {"ordinal": 13, "name": "average_price", "type_info": "Numeric"}, {"ordinal": 14, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 15, "name": "updated_at", "type_info": "Timestamptz"}, {"ordinal": 16, "name": "submitted_at", "type_info": "Timestamptz"}, {"ordinal": 17, "name": "filled_at", "type_info": "Timestamptz"}, {"ordinal": 18, "name": "cancelled_at", "type_info": "Timestamptz"}, {"ordinal": 19, "name": "trading_pair_symbol", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 20, "name": "trading_pair_base", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 21, "name": "trading_pair_quote", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 22, "name": "exchange_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, true, false, false, true, true, false, false, false, true, true, false, false, true, false, false, true, true, true, false, false, false, false]}, "hash": "d34b838f5602b4043044559a4a70e2360624e1eda4cf71dadc2da2ffe5944b76"}