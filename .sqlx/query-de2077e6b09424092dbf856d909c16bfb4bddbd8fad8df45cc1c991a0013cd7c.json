{"db_name": "PostgreSQL", "query": "\n            INSERT INTO risk_checks (\n                id, timestamp, trading_pair, side, quantity, price, order_type,\n                passed, risk_score, violations, warnings, recommendations,\n                max_allowed_quantity, suggested_price_min, suggested_price_max,\n                processing_time_ms, created_at\n            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "Timestamptz", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Numeric", "Numeric", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "Numeric", "Jsonb", "Jsonb", "Jsonb", "Numeric", "Numeric", "Numeric", "Int4", "Timestamptz"]}, "nullable": []}, "hash": "de2077e6b09424092dbf856d909c16bfb4bddbd8fad8df45cc1c991a0013cd7c"}