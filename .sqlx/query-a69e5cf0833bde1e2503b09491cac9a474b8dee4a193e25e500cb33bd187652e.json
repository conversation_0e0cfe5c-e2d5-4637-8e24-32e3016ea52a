{"db_name": "PostgreSQL", "query": "\n            INSERT INTO risk_rules (\n                id, name, rule_type, description, parameters, enabled, priority, created_at, updated_at\n            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Text", "Jsonb", "Bool", "Int4", "Timestamptz", "Timestamptz"]}, "nullable": []}, "hash": "a69e5cf0833bde1e2503b09491cac9a474b8dee4a193e25e500cb33bd187652e"}