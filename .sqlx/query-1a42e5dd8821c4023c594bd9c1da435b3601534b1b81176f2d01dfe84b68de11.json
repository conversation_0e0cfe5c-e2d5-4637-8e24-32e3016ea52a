{"db_name": "PostgreSQL", "query": "\n                    INSERT INTO risk_violations (\n                        risk_check_id, rule_id, rule_name, severity, message,\n                        current_value, limit_value, created_at\n                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n                    ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Timestamptz"]}, "nullable": []}, "hash": "1a42e5dd8821c4023c594bd9c1da435b3601534b1b81176f2d01dfe84b68de11"}