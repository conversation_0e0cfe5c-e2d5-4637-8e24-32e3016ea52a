//! 基准对比模块
//! 
//! 实现回测中的基准对比功能，包括基准数据管理、相对表现分析等

use sigmax_core::{Amount, SigmaXResult, SigmaXError, Candle, TradingPair};
use rust_decimal::{Decimal, MathematicalOps};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// 基准类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BenchmarkType {
    /// 市场指数 (如BTC, ETH)
    MarketIndex(String),
    /// 自定义基准
    Custom(String),
    /// 无风险利率
    RiskFree(Decimal),
    /// 等权重组合
    EqualWeight(Vec<TradingPair>),
}

/// 基准配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkConfig {
    pub id: Uuid,
    pub name: String,
    pub benchmark_type: BenchmarkType,
    pub data_source: Option<String>,
    pub rebalance_frequency: Option<RebalanceFrequency>,
}

/// 重平衡频率
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RebalanceFrequency {
    Daily,
    Weekly,
    Monthly,
    Quarterly,
}

/// 基准数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkDataPoint {
    pub timestamp: DateTime<Utc>,
    pub value: Decimal,
    pub return_rate: Decimal,
}

/// 基准对比结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkComparison {
    pub strategy_return: Decimal,
    pub benchmark_return: Decimal,
    pub excess_return: Decimal,
    pub tracking_error: Decimal,
    pub information_ratio: Decimal,
    pub beta: Decimal,
    pub alpha: Decimal,
    pub correlation: Decimal,
    pub relative_sharpe: Decimal,
    pub relative_max_drawdown: Decimal,
    pub outperformance_ratio: Decimal,
    pub calculated_at: DateTime<Utc>,
}

/// 基准管理器
pub struct BenchmarkManager {
    benchmarks: HashMap<Uuid, BenchmarkConfig>,
    benchmark_data: HashMap<Uuid, Vec<BenchmarkDataPoint>>,
    strategy_returns: Vec<Decimal>,
    strategy_values: Vec<Decimal>,
    timestamps: Vec<DateTime<Utc>>,
}

impl BenchmarkManager {
    pub fn new() -> Self {
        Self {
            benchmarks: HashMap::new(),
            benchmark_data: HashMap::new(),
            strategy_returns: Vec::new(),
            strategy_values: Vec::new(),
            timestamps: Vec::new(),
        }
    }

    /// 添加基准
    pub fn add_benchmark(&mut self, config: BenchmarkConfig) -> SigmaXResult<()> {
        self.benchmarks.insert(config.id, config);
        Ok(())
    }

    /// 加载基准数据
    pub async fn load_benchmark_data(
        &mut self,
        benchmark_id: Uuid,
        candles: Vec<Candle>,
    ) -> SigmaXResult<()> {
        let mut data_points = Vec::new();
        let mut previous_value = None;

        for candle in candles {
            let current_value = candle.close;
            let return_rate = if let Some(prev) = previous_value {
                if prev > Decimal::ZERO {
                    (current_value - prev) / prev
                } else {
                    Decimal::ZERO
                }
            } else {
                Decimal::ZERO
            };

            data_points.push(BenchmarkDataPoint {
                timestamp: candle.timestamp,
                value: current_value,
                return_rate,
            });

            previous_value = Some(current_value);
        }

        self.benchmark_data.insert(benchmark_id, data_points);
        Ok(())
    }

    /// 更新策略数据
    pub fn update_strategy_data(
        &mut self,
        timestamp: DateTime<Utc>,
        portfolio_value: Amount,
    ) -> SigmaXResult<()> {
        // 计算收益率
        let return_rate = if let Some(&last_value) = self.strategy_values.last() {
            if last_value > Decimal::ZERO {
                (portfolio_value - last_value) / last_value
            } else {
                Decimal::ZERO
            }
        } else {
            Decimal::ZERO
        };

        self.timestamps.push(timestamp);
        self.strategy_values.push(portfolio_value);
        self.strategy_returns.push(return_rate);

        Ok(())
    }

    /// 计算基准对比结果
    pub fn calculate_benchmark_comparison(
        &self,
        benchmark_id: Uuid,
    ) -> SigmaXResult<BenchmarkComparison> {
        let benchmark_data = self.benchmark_data.get(&benchmark_id)
            .ok_or_else(|| SigmaXError::InvalidOperation("基准数据不存在".to_string()))?;

        if self.strategy_returns.is_empty() || benchmark_data.is_empty() {
            return Err(SigmaXError::InvalidOperation("数据不足".to_string()));
        }

        // 对齐时间序列
        let (aligned_strategy_returns, aligned_benchmark_returns) = 
            self.align_time_series(benchmark_data)?;

        if aligned_strategy_returns.len() < 2 {
            return Err(SigmaXError::InvalidOperation("对齐后数据不足".to_string()));
        }

        // 计算基本收益率
        let strategy_return = self.calculate_total_return(&aligned_strategy_returns);
        let benchmark_return = self.calculate_total_return(&aligned_benchmark_returns);
        let excess_return = strategy_return - benchmark_return;

        // 计算跟踪误差
        let tracking_error = self.calculate_tracking_error(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
        )?;

        // 计算信息比率
        let information_ratio = if tracking_error > Decimal::ZERO {
            let mean_excess = self.calculate_mean_excess_return(
                &aligned_strategy_returns,
                &aligned_benchmark_returns,
            );
            mean_excess / tracking_error * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16))
        } else {
            Decimal::ZERO
        };

        // 计算贝塔系数
        let beta = self.calculate_beta(&aligned_strategy_returns, &aligned_benchmark_returns)?;

        // 计算阿尔法
        let alpha = self.calculate_alpha(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
            beta,
        )?;

        // 计算相关性
        let correlation = self.calculate_correlation(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
        )?;

        // 计算相对夏普比率
        let relative_sharpe = self.calculate_relative_sharpe(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
        )?;

        // 计算相对最大回撤
        let relative_max_drawdown = self.calculate_relative_max_drawdown(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
        )?;

        // 计算超越表现比率
        let outperformance_ratio = self.calculate_outperformance_ratio(
            &aligned_strategy_returns,
            &aligned_benchmark_returns,
        );

        Ok(BenchmarkComparison {
            strategy_return,
            benchmark_return,
            excess_return,
            tracking_error,
            information_ratio,
            beta,
            alpha,
            correlation,
            relative_sharpe,
            relative_max_drawdown,
            outperformance_ratio,
            calculated_at: Utc::now(),
        })
    }

    /// 对齐时间序列
    fn align_time_series(
        &self,
        benchmark_data: &[BenchmarkDataPoint],
    ) -> SigmaXResult<(Vec<Decimal>, Vec<Decimal>)> {
        let mut aligned_strategy = Vec::new();
        let mut aligned_benchmark = Vec::new();

        // 简化实现：假设时间戳已经对齐
        let min_len = std::cmp::min(self.strategy_returns.len(), benchmark_data.len());
        
        for i in 0..min_len {
            if let (Some(&strategy_return), Some(benchmark_point)) = 
                (self.strategy_returns.get(i), benchmark_data.get(i)) {
                aligned_strategy.push(strategy_return);
                aligned_benchmark.push(benchmark_point.return_rate);
            }
        }

        Ok((aligned_strategy, aligned_benchmark))
    }

    /// 计算总收益率
    fn calculate_total_return(&self, returns: &[Decimal]) -> Decimal {
        returns.iter().fold(Decimal::ONE, |acc, &r| acc * (Decimal::ONE + r)) - Decimal::ONE
    }

    /// 计算跟踪误差
    fn calculate_tracking_error(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> SigmaXResult<Decimal> {
        if strategy_returns.len() != benchmark_returns.len() || strategy_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let excess_returns: Vec<Decimal> = strategy_returns
            .iter()
            .zip(benchmark_returns.iter())
            .map(|(s, b)| s - b)
            .collect();

        let mean_excess = excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len());
        
        let variance = excess_returns
            .iter()
            .map(|r| {
                let diff = *r - mean_excess;
                diff * diff
            })
            .sum::<Decimal>() / Decimal::from(excess_returns.len() - 1);

        let variance_f64: f64 = variance.to_string().parse().unwrap_or(0.0);
        let std_dev = variance_f64.sqrt();
        
        // 年化跟踪误差
        let annualized_te = std_dev * (252.0_f64).sqrt();
        Ok(Decimal::from_f64_retain(annualized_te).unwrap_or(Decimal::ZERO))
    }

    /// 计算平均超额收益
    fn calculate_mean_excess_return(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> Decimal {
        let excess_returns: Vec<Decimal> = strategy_returns
            .iter()
            .zip(benchmark_returns.iter())
            .map(|(s, b)| s - b)
            .collect();

        if excess_returns.is_empty() {
            return Decimal::ZERO;
        }

        excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len())
    }

    /// 计算贝塔系数
    fn calculate_beta(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> SigmaXResult<Decimal> {
        if strategy_returns.len() != benchmark_returns.len() || strategy_returns.len() < 2 {
            return Ok(Decimal::ONE);
        }

        let strategy_mean = strategy_returns.iter().sum::<Decimal>() / Decimal::from(strategy_returns.len());
        let benchmark_mean = benchmark_returns.iter().sum::<Decimal>() / Decimal::from(benchmark_returns.len());

        let mut covariance = Decimal::ZERO;
        let mut benchmark_variance = Decimal::ZERO;

        for (strategy_return, benchmark_return) in strategy_returns.iter().zip(benchmark_returns.iter()) {
            let strategy_diff = *strategy_return - strategy_mean;
            let benchmark_diff = *benchmark_return - benchmark_mean;
            
            covariance += strategy_diff * benchmark_diff;
            benchmark_variance += benchmark_diff * benchmark_diff;
        }

        let n = Decimal::from(strategy_returns.len());
        covariance /= n;
        benchmark_variance /= n;

        if benchmark_variance == Decimal::ZERO {
            return Ok(Decimal::ONE);
        }

        Ok(covariance / benchmark_variance)
    }

    /// 计算阿尔法
    fn calculate_alpha(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
        beta: Decimal,
    ) -> SigmaXResult<Decimal> {
        if strategy_returns.is_empty() || benchmark_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let strategy_mean = strategy_returns.iter().sum::<Decimal>() / Decimal::from(strategy_returns.len());
        let benchmark_mean = benchmark_returns.iter().sum::<Decimal>() / Decimal::from(benchmark_returns.len());
        
        // 假设无风险利率为0
        let alpha = strategy_mean - beta * benchmark_mean;
        
        // 年化阿尔法
        Ok(alpha * Decimal::from(252))
    }

    /// 计算相关性
    fn calculate_correlation(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> SigmaXResult<Decimal> {
        if strategy_returns.len() != benchmark_returns.len() || strategy_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let strategy_mean = strategy_returns.iter().sum::<Decimal>() / Decimal::from(strategy_returns.len());
        let benchmark_mean = benchmark_returns.iter().sum::<Decimal>() / Decimal::from(benchmark_returns.len());

        let mut covariance = Decimal::ZERO;
        let mut strategy_variance = Decimal::ZERO;
        let mut benchmark_variance = Decimal::ZERO;

        for (strategy_return, benchmark_return) in strategy_returns.iter().zip(benchmark_returns.iter()) {
            let strategy_diff = *strategy_return - strategy_mean;
            let benchmark_diff = *benchmark_return - benchmark_mean;
            
            covariance += strategy_diff * benchmark_diff;
            strategy_variance += strategy_diff * strategy_diff;
            benchmark_variance += benchmark_diff * benchmark_diff;
        }

        let strategy_std = if strategy_variance > Decimal::ZERO {
            let var_f64: f64 = strategy_variance.to_string().parse().unwrap_or(0.0);
            Decimal::from_f64_retain(var_f64.sqrt()).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        let benchmark_std = if benchmark_variance > Decimal::ZERO {
            let var_f64: f64 = benchmark_variance.to_string().parse().unwrap_or(0.0);
            Decimal::from_f64_retain(var_f64.sqrt()).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        if strategy_std == Decimal::ZERO || benchmark_std == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok(covariance / (strategy_std * benchmark_std))
    }

    /// 计算相对夏普比率
    fn calculate_relative_sharpe(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> SigmaXResult<Decimal> {
        let strategy_sharpe = self.calculate_sharpe_ratio(strategy_returns)?;
        let benchmark_sharpe = self.calculate_sharpe_ratio(benchmark_returns)?;
        
        Ok(strategy_sharpe - benchmark_sharpe)
    }

    /// 计算夏普比率
    fn calculate_sharpe_ratio(&self, returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_return = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
        
        let variance = returns
            .iter()
            .map(|r| {
                let diff = *r - mean_return;
                diff * diff
            })
            .sum::<Decimal>() / Decimal::from(returns.len() - 1);

        let variance_f64: f64 = variance.to_string().parse().unwrap_or(0.0);
        let std_dev = Decimal::from_f64_retain(variance_f64.sqrt()).unwrap_or(Decimal::ZERO);

        if std_dev == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        // 年化夏普比率
        let annualized_return = mean_return * Decimal::from(252);
        let annualized_vol = std_dev * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16));
        
        Ok(annualized_return / annualized_vol)
    }

    /// 计算相对最大回撤
    fn calculate_relative_max_drawdown(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> SigmaXResult<Decimal> {
        let strategy_dd = self.calculate_max_drawdown(strategy_returns)?;
        let benchmark_dd = self.calculate_max_drawdown(benchmark_returns)?;
        
        Ok(strategy_dd - benchmark_dd)
    }

    /// 计算最大回撤
    fn calculate_max_drawdown(&self, returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut equity_curve = vec![Decimal::ONE];
        for &return_rate in returns {
            let new_value = equity_curve.last().unwrap() * (Decimal::ONE + return_rate);
            equity_curve.push(new_value);
        }

        let mut max_drawdown = Decimal::ZERO;
        let mut peak = equity_curve[0];

        for &value in equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                if drawdown > max_drawdown {
                    max_drawdown = drawdown;
                }
            }
        }

        Ok(max_drawdown)
    }

    /// 计算超越表现比率
    fn calculate_outperformance_ratio(
        &self,
        strategy_returns: &[Decimal],
        benchmark_returns: &[Decimal],
    ) -> Decimal {
        if strategy_returns.len() != benchmark_returns.len() || strategy_returns.is_empty() {
            return Decimal::ZERO;
        }

        let outperform_count = strategy_returns
            .iter()
            .zip(benchmark_returns.iter())
            .filter(|(s, b)| s > b)
            .count();

        Decimal::from(outperform_count) / Decimal::from(strategy_returns.len())
    }

    /// 获取所有基准配置
    pub fn get_benchmarks(&self) -> &HashMap<Uuid, BenchmarkConfig> {
        &self.benchmarks
    }

    /// 获取基准数据
    pub fn get_benchmark_data(&self, benchmark_id: &Uuid) -> Option<&Vec<BenchmarkDataPoint>> {
        self.benchmark_data.get(benchmark_id)
    }
}
