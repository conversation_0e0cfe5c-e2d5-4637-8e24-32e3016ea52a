//! SigmaX 执行引擎模块 - 重构版
//!
//! ## 核心设计原则体现
//!
//! ### 1. 高内聚，低耦合
//! - 专注执行逻辑，风控逻辑移至 risk_control 模块
//! - 引擎间通过接口松耦合
//!
//! ### 2. 关注点分离
//! - 纯执行逻辑，不涉及风控决策
//! - 配置、数据访问分离到专门模块
//!
//! ### 3. 面向接口设计  
//! - 实现 sigmax-interfaces 定义的执行接口
//! - 依赖抽象的 RiskController 接口
//!
//! ### 4. 可测试性设计
//! - 支持依赖注入
//! - 可Mock的风控依赖
//!
//! ### 5. 简洁与可演化性
//! - 简化的引擎架构
//! - 可扩展的引擎类型

use sigmax_interfaces::{RiskController, ExecutionEngine};
use std::sync::Arc;

// ============================================================================
// 执行引擎模块 - 专注执行逻辑
// ============================================================================

/// 回测执行引擎
pub mod backtest;

/// 实盘执行引擎
pub mod live;

/// 模拟执行引擎
pub mod paper;

/// 引擎管理器
pub mod manager;

/// 引擎工厂
pub mod factory;

/// 基础引擎组件
pub mod base;

// ============================================================================
// 风控相关模块已完全迁移到 sigmax-risk 模块
// ============================================================================

// 注意：风控相关的所有功能现在通过 sigmax-risk 模块提供
// 使用 sigmax_interfaces::RiskController 接口进行依赖注入

// ============================================================================
// 其他保留模块
// ============================================================================

/// 性能基准测试
pub mod benchmark;

/// 恢复管理器
pub mod recovery_manager;

/// 增强指标
pub mod enhanced_metrics;

/// 配置验证器
pub mod config_validator;

/// 统一错误处理
pub mod error_handling;

// ============================================================================
// 公共导出 - 仅执行相关
// ============================================================================

// 执行引擎导出
pub use backtest::BacktestEngine;
pub use live::LiveTradingEngine as LiveEngine;
pub use paper::PaperEngine;

// 管理组件导出
pub use manager::EngineManager;
pub use factory::EngineFactory;
pub use base::BaseEngineConfig;

// 其他组件导出
pub use benchmark::*;
pub use recovery_manager::*;
pub use enhanced_metrics::*;
pub use config_validator::{ConfigurationManager, ConfigValidator};
pub use error_handling::{ErrorContext, EngineErrorExt, ResultExt, AsyncResultExt, BatchErrorHandler};

// ============================================================================
// 执行引擎配置
// ============================================================================

use serde::{Deserialize, Serialize};
use sigmax_core::EngineType;
use std::collections::HashMap;

/// 执行引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionEngineConfig {
    pub engine_type: EngineType,
    pub risk_controller: Option<Arc<dyn RiskController>>, // 通过依赖注入获取
    pub settings: HashMap<String, serde_json::Value>,
}

/// 执行引擎构建器
pub struct ExecutionEngineBuilder {
    config: ExecutionEngineConfig,
}

impl ExecutionEngineBuilder {
    pub fn new(engine_type: EngineType) -> Self {
        Self {
            config: ExecutionEngineConfig {
                engine_type,
                risk_controller: None,
                settings: HashMap::new(),
            },
        }
    }
    
    /// 注入风控控制器
    pub fn with_risk_controller(mut self, risk_controller: Arc<dyn RiskController>) -> Self {
        self.config.risk_controller = Some(risk_controller);
        self
    }
    
    /// 添加设置
    pub fn with_setting(mut self, key: &str, value: serde_json::Value) -> Self {
        self.config.settings.insert(key.to_string(), value);
        self
    }
    
    /// 构建执行引擎
    pub async fn build(self) -> sigmax_core::SigmaXResult<Box<dyn ExecutionEngine>> {
        match self.config.engine_type {
            EngineType::Backtest => {
                let engine = backtest::BacktestEngine::new(self.config).await?;
                Ok(Box::new(engine))
            },
            EngineType::Live => {
                let engine = live::LiveTradingEngine::new(self.config).await?;
                Ok(Box::new(engine))
            },
            EngineType::Paper => {
                let engine = paper::PaperEngine::new(self.config).await?;
                Ok(Box::new(engine))
            },
            EngineType::Simulation => {
                // 模拟交易使用Paper引擎
                let engine = paper::PaperEngine::new(self.config).await?;
                Ok(Box::new(engine))
            },
        }
    }
}

// ============================================================================
// 迁移说明
// ============================================================================

/// 迁移指南
/// 
/// ## 风控相关迁移
/// 
/// ### 旧代码
/// ```rust,ignore
/// use sigmax_engines::risk::{UnifiedRiskManager, SimpleRiskAdapter};
/// 
/// let risk_manager = UnifiedRiskManager::new().await?;
/// let adapter = SimpleRiskAdapter::new().await?;
/// ```
/// 
/// ### 新代码
/// ```rust,ignore
/// use sigmax_risk_control::{RiskControllerFactory};
/// use sigmax_interfaces::RiskController;
/// 
/// let risk_controller = RiskControllerFactory::create_standard().await;
/// ```
/// 
/// ## 执行引擎迁移
/// 
/// ### 旧代码
/// ```rust,ignore
/// use sigmax_engines::{LiveTradingEngine};
/// 
/// let engine = LiveTradingEngine::new(config).await?;
/// ```
/// 
/// ### 新代码
/// ```rust,ignore
/// use sigmax_engines::{ExecutionEngineBuilder};
/// use sigmax_interfaces::ExecutionEngine;
/// 
/// let engine = ExecutionEngineBuilder::new(EngineType::Live)
///     .with_risk_controller(risk_controller)
///     .build()
///     .await?;
/// ```
pub mod migration_guide {
    //! 迁移指南模块
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_execution_engine_builder() {
        // TODO: 实现测试
        // 测试新的ExecutionEngineBuilder
    }
}