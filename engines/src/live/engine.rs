//! 实盘引擎 - 集成新的LiveTradingRiskAdapter
//! 
//! 设计原则：
//! - 低延迟：毫秒级响应，实时交易优化
//! - 高可靠性：熔断器保护，故障恢复
//! - 实时监控：详细的性能指标和告警

use async_trait::async_trait;
use sigmax_core::{EngineType, EngineId, EngineStatus, EngineConfig, EngineStatistics, SigmaXResult, SigmaXError, Order};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, error};
use chrono::{DateTime, Utc};

use crate::risk::{SimpleAdapterFactory, UnifiedRiskManagerFactory, EngineRiskAdapter};
use super::{LiveTradingConfig, LiveTradingMonitor, CircuitBreaker};
use super::circuit_breaker::CircuitBreakerStats;

/// 新的实盘引擎 - 集成LiveTradingRiskAdapter
pub struct LiveTradingEngine {
    /// 引擎ID
    id: EngineId,
    /// 引擎状态
    status: Arc<RwLock<EngineStatus>>,
    /// 引擎配置
    config: EngineConfig,
    /// 实盘特定配置
    live_config: Arc<RwLock<Option<LiveTradingConfig>>>,
    /// 风控适配器（简化版）
    risk_adapter: Arc<RwLock<Option<Arc<dyn EngineRiskAdapter>>>>,
    /// 性能监控器
    monitor: Arc<LiveTradingMonitor>,
    /// 熔断器
    circuit_breaker: Arc<CircuitBreaker>,
    /// 引擎统计
    statistics: Arc<RwLock<EngineStatistics>>,
    /// 运行状态
    is_running: Arc<AtomicBool>,
    /// 最后心跳时间
    last_heartbeat: Arc<RwLock<DateTime<Utc>>>,
}

impl LiveTradingEngine {
    /// 创建新的实盘引擎
    pub async fn new(config: LiveTradingConfig) -> SigmaXResult<Self> {
        let id = uuid::Uuid::new_v4();
        info!("Creating new LiveTradingEngine with ID: {}", id);
        
        // 创建熔断器
        let circuit_breaker = Arc::new(CircuitBreaker::new(
            0.1, // 10%失败率阈值
            std::time::Duration::from_secs(30), // 30秒恢复时间
            5, // 最少5个请求
        ));
        
        let engine = Self {
            id,
            status: Arc::new(RwLock::new(EngineStatus::Stopped)),
            config: EngineConfig::default(),
            live_config: Arc::new(RwLock::new(Some(config))),
            risk_adapter: Arc::new(RwLock::new(None)),
            monitor: Arc::new(LiveTradingMonitor::new()),
            circuit_breaker,
            statistics: Arc::new(RwLock::new(EngineStatistics::new(id))),
            is_running: Arc::new(AtomicBool::new(false)),
            last_heartbeat: Arc::new(RwLock::new(Utc::now())),
        };
        
        info!("LiveTradingEngine created successfully");
        Ok(engine)
    }
    
    /// 初始化风控适配器（简化版）
    pub async fn initialize_risk_adapter(&self) -> SigmaXResult<()> {
        info!("初始化简化风控适配器");
        
        // 使用新的简化适配器工厂
        let risk_adapter = SimpleAdapterFactory::create_live_adapter().await?;
        
        // 存储适配器
        {
            let mut adapter_lock = self.risk_adapter.write().await;
            *adapter_lock = Some(risk_adapter);
        }
        
        info!("简化风控适配器初始化成功");
        Ok(())
    }
    
    /// 风控检查 - 使用简化的统一适配器
    pub async fn risk_check(&self, order: &Order) -> SigmaXResult<bool> {
        let start_time = std::time::Instant::now();
        
        let adapter_lock = self.risk_adapter.read().await;
        let result = if let Some(adapter) = adapter_lock.as_ref() {
            // 使用统一的风控检查接口
            adapter.check_order_risk(order).await
        } else {
            Err(SigmaXError::Configuration("Risk adapter not initialized".to_string()))
        };
        
        // 记录延迟指标
        let latency = start_time.elapsed().as_millis() as f64;
        self.monitor.record_order_processing(latency).await;
        
        match &result {
            Ok(passed) => {
                self.monitor.record_risk_check(*passed, false).await; // 假设非缓存命中
                debug!("Low-latency risk check completed: {} in {:.1}ms", passed, latency);
            }
            Err(e) => {
                self.monitor.record_error().await;
                error!("Low-latency risk check failed: {} in {:.1}ms", e, latency);
            }
        }
        
        result
    }
    
    /// 处理实时订单
    pub async fn process_real_time_order(&self, order: &Order) -> SigmaXResult<bool> {
        info!("Processing real-time order: {:?} {} @ {:?}", order.side, order.quantity, order.price);
        
        // 检查熔断器状态
        if !self.circuit_breaker.can_execute().await {
            warn!("Circuit breaker is open, rejecting order");
            self.monitor.record_circuit_breaker_trip().await;
            return Ok(false);
        }
        
        // 执行低延迟风控检查
        let risk_passed = self.circuit_breaker.execute(|| async {
            self.risk_check(order).await
        }).await?;
        
        if risk_passed {
            info!("Order passed risk check, proceeding with execution");
            // 这里应该调用实际的订单执行逻辑
            self.execute_order(order).await?;
            Ok(true)
        } else {
            warn!("Order rejected by risk check");
            Ok(false)
        }
    }
    
    /// 执行订单 (模拟)
    async fn execute_order(&self, order: &Order) -> SigmaXResult<()> {
        debug!("Executing order: {:?} {} @ {:?}", order.side, order.quantity, order.price);
        
        // 这里应该调用实际的交易所API
        // 目前使用模拟执行
        
        // 模拟执行延迟
        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        
        info!("Order executed successfully");
        Ok(())
    }
    
    /// 启动实时监控
    pub async fn start_monitoring(&self) -> SigmaXResult<()> {
        info!("Starting real-time monitoring");
        
        let monitor = self.monitor.clone();
        let circuit_breaker = self.circuit_breaker.clone();
        let is_running = self.is_running.clone();
        
        // 启动监控任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                if !is_running.load(Ordering::SeqCst) {
                    break;
                }
                
                // 检查系统健康状态
                let health_status = monitor.health_check().await;
                let alerts = monitor.check_alerts().await;
                
                if !alerts.is_empty() {
                    warn!("Health alerts detected:");
                    for alert in alerts {
                        alert.print();
                    }
                }
                
                // 根据健康状态调整熔断器
                match health_status {
                    super::HealthStatus::Critical => {
                        circuit_breaker.force_open().await;
                        error!("System health critical, circuit breaker forced open");
                    }
                    super::HealthStatus::Healthy => {
                        // 健康状态良好，可以重置熔断器
                        if circuit_breaker.is_open().await {
                            circuit_breaker.reset().await;
                            info!("System health recovered, circuit breaker reset");
                        }
                    }
                    _ => {} // Warning状态不做特殊处理
                }
            }
        });
        
        info!("Real-time monitoring started");
        Ok(())
    }
    
    /// 获取性能报告
    /// 
    /// 注意：性能报告功能已迁移至 sigmax-risk 模块
    pub async fn get_performance_report(&self) -> super::LiveTradingStats {
        // 返回基本统计信息
        super::LiveTradingStats::new()
    }
    
    /// 获取实时统计
    pub async fn get_real_time_stats(&self) -> super::LiveTradingStats {
        self.monitor.get_stats().await
    }
    
    /// 刷新热缓存
    /// 
    /// 注意：缓存功能已迁移至 sigmax-risk 模块
    pub async fn refresh_hot_cache(&self) -> SigmaXResult<()> {
        // 缓存功能已迁移，此方法保留以维持兼容性
        Ok(())
    }
    
    /// 更新心跳
    async fn update_heartbeat(&self) {
        let mut heartbeat = self.last_heartbeat.write().await;
        *heartbeat = Utc::now();
    }
    
    /// 检查引擎健康状态
    pub async fn health_check(&self) -> EngineHealthStatus {
        let last_heartbeat = *self.last_heartbeat.read().await;
        let heartbeat_age = Utc::now().signed_duration_since(last_heartbeat);
        
        let is_healthy = heartbeat_age.num_seconds() < 60; // 1分钟内有心跳认为健康
        let circuit_breaker_open = self.circuit_breaker.is_open().await;
        let monitor_health = self.monitor.health_check().await;
        
        EngineHealthStatus {
            is_healthy: is_healthy && !circuit_breaker_open && matches!(monitor_health, super::HealthStatus::Healthy),
            last_heartbeat,
            circuit_breaker_open,
            monitor_health,
            uptime: heartbeat_age,
        }
    }
}

/// 引擎健康状态
#[derive(Debug, Clone)]
pub struct EngineHealthStatus {
    pub is_healthy: bool,
    pub last_heartbeat: DateTime<Utc>,
    pub circuit_breaker_open: bool,
    pub monitor_health: super::HealthStatus,
    pub uptime: chrono::Duration,
}

#[async_trait]
impl sigmax_core::traits::TradingEngine for LiveTradingEngine {
    fn id(&self) -> EngineId {
        self.id
    }
    
    fn engine_type(&self) -> EngineType {
        EngineType::Live
    }
    
    async fn get_status(&self) -> SigmaXResult<EngineStatus> {
        let status = self.status.read().await;
        Ok(*status)
    }
    
    async fn start(&self) -> SigmaXResult<()> {
        info!("Starting LiveTradingEngine");
        
        {
            let mut status = self.status.write().await;
            *status = EngineStatus::Starting;
        }
        
        // 启动实时监控
        self.start_monitoring().await?;
        
        // 设置运行状态
        self.is_running.store(true, Ordering::SeqCst);
        
        // 更新心跳
        self.update_heartbeat().await;
        
        {
            let mut status = self.status.write().await;
            *status = EngineStatus::Running;
        }
        
        info!("LiveTradingEngine started successfully");
        Ok(())
    }
    
    async fn stop(&self) -> SigmaXResult<()> {
        info!("Stopping LiveTradingEngine");
        
        self.is_running.store(false, Ordering::SeqCst);
        
        {
            let mut status = self.status.write().await;
            *status = EngineStatus::Stopped;
        }
        
        info!("LiveTradingEngine stopped successfully");
        Ok(())
    }
    
    async fn pause(&self) -> SigmaXResult<()> {
        info!("Pausing LiveTradingEngine");
        
        {
            let mut status = self.status.write().await;
            *status = EngineStatus::Paused;
        }
        
        Ok(())
    }
    
    async fn resume(&self) -> SigmaXResult<()> {
        info!("Resuming LiveTradingEngine");
        
        {
            let mut status = self.status.write().await;
            *status = EngineStatus::Running;
        }
        
        // 更新心跳
        self.update_heartbeat().await;
        
        Ok(())
    }
    
    async fn get_config(&self) -> SigmaXResult<EngineConfig> {
        Ok(self.config.clone())
    }
    
    async fn update_config(&self, config: EngineConfig) -> SigmaXResult<()> {
        info!("Updating LiveTradingEngine config");
        // 这里应该更新配置
        Ok(())
    }
    
    async fn get_statistics(&self) -> SigmaXResult<EngineStatistics> {
        let stats = self.statistics.read().await;
        Ok(stats.clone())
    }
    
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_live_engine_creation() {
        let config = LiveTradingConfig {
            enable_detailed_logging: true,
            enable_performance_monitoring: true,
            enable_debug_mode: false,
            connection_timeout_ms: 5000,
            max_retries: 3,
        };
        
        let engine = LiveTradingEngine::new(config).await;
        assert!(engine.is_ok());
        
        let engine = engine.unwrap();
        assert_eq!(engine.engine_type(), EngineType::Live);
        
        let status = engine.get_status().await.unwrap();
        assert_eq!(status, EngineStatus::Stopped);
    }
    
    #[tokio::test]
    async fn test_live_engine_lifecycle() {
        let config = LiveTradingConfig {
            enable_detailed_logging: false,
            enable_performance_monitoring: true,
            enable_debug_mode: false,
            connection_timeout_ms: 1000,
            max_retries: 2,
        };
        
        let engine = LiveTradingEngine::new(config).await.unwrap();
        
        // 测试启动
        engine.start().await.unwrap();
        let status = engine.get_status().await.unwrap();
        assert_eq!(status, EngineStatus::Running);
        
        // 测试暂停
        engine.pause().await.unwrap();
        let status = engine.get_status().await.unwrap();
        assert_eq!(status, EngineStatus::Paused);
        
        // 测试恢复
        engine.resume().await.unwrap();
        let status = engine.get_status().await.unwrap();
        assert_eq!(status, EngineStatus::Running);
        
        // 测试停止
        engine.stop().await.unwrap();
        let status = engine.get_status().await.unwrap();
        assert_eq!(status, EngineStatus::Stopped);
    }
    
    #[tokio::test]
    async fn test_health_check() {
        let config = LiveTradingConfig::default();
        let engine = LiveTradingEngine::new(config).await.unwrap();
        
        let health = engine.health_check().await;
        // 新创建的引擎应该是健康的
        assert!(health.is_healthy);
    }
}
