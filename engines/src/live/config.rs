//! 实盘引擎配置

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// 实盘引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiveTradingConfig {
    /// 启用详细日志
    pub enable_detailed_logging: bool,
    /// 启用性能监控
    pub enable_performance_monitoring: bool,
    /// 启用调试模式
    pub enable_debug_mode: bool,
    /// 连接超时时间 (毫秒)
    pub connection_timeout_ms: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 心跳间隔 (秒)
    pub heartbeat_interval_secs: u64,
    /// 订单超时时间 (毫秒)
    pub order_timeout_ms: u64,
    /// 最大并发订单数
    pub max_concurrent_orders: usize,
}

impl Default for LiveTradingConfig {
    fn default() -> Self {
        Self {
            enable_detailed_logging: false,
            enable_performance_monitoring: true,
            enable_debug_mode: false,
            connection_timeout_ms: 5000, // 5秒
            max_retries: 3,
            heartbeat_interval_secs: 30, // 30秒
            order_timeout_ms: 10000, // 10秒
            max_concurrent_orders: 100,
        }
    }
}

/// 实盘适配器配置 - 注意：已迁移至 sigmax-risk 模块
/// 使用 sigmax_interfaces::RiskController 配置替代
// pub use super::risk_adapter::LiveAdapterConfig;

/// 临时的适配器配置占位符
#[derive(Debug, Clone, Default)]
pub struct LiveAdapterConfig {
    pub low_latency_mode: bool,
    pub hot_cache_size: usize,
    pub timeout_ms: u64,
    pub circuit_breaker_threshold: f64,
    pub max_retries: u32,
    pub enable_debug_logging: bool,
}

impl LiveAdapterConfig {
    pub fn default() -> Self {
        Self {
            low_latency_mode: false,
            hot_cache_size: 1000,
            timeout_ms: 100,
            circuit_breaker_threshold: 0.1,
            max_retries: 3,
            enable_debug_logging: false,
        }
    }
}

/// 实盘配置构建器
pub struct LiveTradingConfigBuilder {
    config: LiveTradingConfig,
    adapter_config: LiveAdapterConfig,
}

impl LiveTradingConfigBuilder {
    pub fn new() -> Self {
        Self {
            config: LiveTradingConfig::default(),
            adapter_config: LiveAdapterConfig::default(),
        }
    }
    
    /// 设置为生产模式
    pub fn production_mode(mut self) -> Self {
        self.config.enable_detailed_logging = false;
        self.config.enable_performance_monitoring = true;
        self.config.enable_debug_mode = false;
        self.config.connection_timeout_ms = 3000;
        self.config.max_retries = 2;
        
        self.adapter_config.low_latency_mode = true;
        self.adapter_config.hot_cache_size = 1000;
        self.adapter_config.timeout_ms = 50;
        self.adapter_config.circuit_breaker_threshold = 0.05; // 5%
        
        self
    }
    
    /// 设置为测试模式
    pub fn testing_mode(mut self) -> Self {
        self.config.enable_detailed_logging = true;
        self.config.enable_performance_monitoring = true;
        self.config.enable_debug_mode = false;
        self.config.connection_timeout_ms = 10000;
        self.config.max_retries = 5;
        
        self.adapter_config.low_latency_mode = false;
        self.adapter_config.hot_cache_size = 500;
        self.adapter_config.timeout_ms = 200;
        self.adapter_config.circuit_breaker_threshold = 0.2; // 20%
        
        self
    }
    
    /// 设置为开发模式
    pub fn development_mode(mut self) -> Self {
        self.config.enable_detailed_logging = true;
        self.config.enable_performance_monitoring = true;
        self.config.enable_debug_mode = true;
        self.config.connection_timeout_ms = 30000;
        self.config.max_retries = 10;
        
        self.adapter_config.low_latency_mode = false;
        self.adapter_config.hot_cache_size = 100;
        self.adapter_config.timeout_ms = 1000;
        self.adapter_config.circuit_breaker_threshold = 0.5; // 50%
        self.adapter_config.enable_debug_logging = true;
        
        self
    }
    
    /// 设置连接超时
    pub fn connection_timeout_ms(mut self, timeout_ms: u64) -> Self {
        self.config.connection_timeout_ms = timeout_ms;
        self.adapter_config.timeout_ms = timeout_ms;
        self
    }
    
    /// 设置最大重试次数
    pub fn max_retries(mut self, retries: u32) -> Self {
        self.config.max_retries = retries;
        self.adapter_config.max_retries = retries;
        self
    }
    
    /// 设置热缓存大小
    pub fn hot_cache_size(mut self, size: usize) -> Self {
        self.adapter_config.hot_cache_size = size;
        self
    }
    
    /// 设置熔断器阈值
    pub fn circuit_breaker_threshold(mut self, threshold: f64) -> Self {
        self.adapter_config.circuit_breaker_threshold = threshold;
        self
    }
    
    /// 启用低延迟模式
    pub fn enable_low_latency(mut self, enable: bool) -> Self {
        self.adapter_config.low_latency_mode = enable;
        self
    }
    
    /// 构建配置
    pub fn build(self) -> (LiveTradingConfig, LiveAdapterConfig) {
        (self.config, self.adapter_config)
    }
}

impl Default for LiveTradingConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 实盘引擎性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LivePerformanceConfig {
    /// 延迟监控阈值 (毫秒)
    pub latency_threshold_ms: f64,
    /// 错误率阈值
    pub error_rate_threshold: f64,
    /// 吞吐量监控间隔 (秒)
    pub throughput_monitor_interval_secs: u64,
    /// 性能报告间隔 (秒)
    pub performance_report_interval_secs: u64,
}

impl Default for LivePerformanceConfig {
    fn default() -> Self {
        Self {
            latency_threshold_ms: 100.0, // 100ms
            error_rate_threshold: 0.05,  // 5%
            throughput_monitor_interval_secs: 60, // 1分钟
            performance_report_interval_secs: 300, // 5分钟
        }
    }
}

/// 实盘引擎安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiveSecurityConfig {
    /// API密钥轮换间隔 (小时)
    pub api_key_rotation_hours: u64,
    /// 最大单笔订单金额
    pub max_single_order_amount: f64,
    /// 最大日交易金额
    pub max_daily_trading_amount: f64,
    /// 启用IP白名单
    pub enable_ip_whitelist: bool,
    /// 启用订单签名验证
    pub enable_order_signature: bool,
}

impl Default for LiveSecurityConfig {
    fn default() -> Self {
        Self {
            api_key_rotation_hours: 24, // 24小时
            max_single_order_amount: 10000.0, // $10,000
            max_daily_trading_amount: 100000.0, // $100,000
            enable_ip_whitelist: true,
            enable_order_signature: true,
        }
    }
}

/// 完整的实盘引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompleteLiveConfig {
    pub engine: LiveTradingConfig,
    pub adapter: LiveAdapterConfig,
    pub performance: LivePerformanceConfig,
    pub security: LiveSecurityConfig,
}

impl Default for CompleteLiveConfig {
    fn default() -> Self {
        Self {
            engine: LiveTradingConfig::default(),
            adapter: LiveAdapterConfig::default(),
            performance: LivePerformanceConfig::default(),
            security: LiveSecurityConfig::default(),
        }
    }
}

impl CompleteLiveConfig {
    /// 创建生产环境配置
    pub fn production() -> Self {
        let (engine, adapter) = LiveTradingConfigBuilder::new()
            .production_mode()
            .build();
        
        Self {
            engine,
            adapter,
            performance: LivePerformanceConfig {
                latency_threshold_ms: 50.0, // 更严格的延迟要求
                error_rate_threshold: 0.01, // 更严格的错误率要求
                ..Default::default()
            },
            security: LiveSecurityConfig {
                max_single_order_amount: 5000.0, // 更保守的单笔限额
                max_daily_trading_amount: 50000.0, // 更保守的日限额
                ..Default::default()
            },
        }
    }
    
    /// 创建测试环境配置
    pub fn testing() -> Self {
        let (engine, adapter) = LiveTradingConfigBuilder::new()
            .testing_mode()
            .build();
        
        Self {
            engine,
            adapter,
            performance: LivePerformanceConfig {
                latency_threshold_ms: 200.0, // 宽松的延迟要求
                error_rate_threshold: 0.1,   // 宽松的错误率要求
                ..Default::default()
            },
            security: LiveSecurityConfig {
                max_single_order_amount: 1000.0, // 测试限额
                max_daily_trading_amount: 10000.0, // 测试限额
                enable_ip_whitelist: false, // 测试环境不启用IP白名单
                ..Default::default()
            },
        }
    }
    
    /// 创建开发环境配置
    pub fn development() -> Self {
        let (engine, adapter) = LiveTradingConfigBuilder::new()
            .development_mode()
            .build();
        
        Self {
            engine,
            adapter,
            performance: LivePerformanceConfig {
                latency_threshold_ms: 1000.0, // 很宽松的延迟要求
                error_rate_threshold: 0.5,    // 很宽松的错误率要求
                throughput_monitor_interval_secs: 30, // 更频繁的监控
                performance_report_interval_secs: 60,  // 更频繁的报告
            },
            security: LiveSecurityConfig {
                max_single_order_amount: 100.0, // 开发限额
                max_daily_trading_amount: 1000.0, // 开发限额
                enable_ip_whitelist: false,
                enable_order_signature: false, // 开发环境简化验证
                ..Default::default()
            },
        }
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<(), String> {
        // 验证引擎配置
        if self.engine.connection_timeout_ms == 0 {
            return Err("Connection timeout cannot be zero".to_string());
        }
        
        if self.engine.max_retries == 0 {
            return Err("Max retries cannot be zero".to_string());
        }
        
        // 验证适配器配置
        if self.adapter.hot_cache_size == 0 {
            return Err("Hot cache size cannot be zero".to_string());
        }
        
        if self.adapter.circuit_breaker_threshold <= 0.0 || self.adapter.circuit_breaker_threshold > 1.0 {
            return Err("Circuit breaker threshold must be between 0 and 1".to_string());
        }
        
        // 验证性能配置
        if self.performance.latency_threshold_ms <= 0.0 {
            return Err("Latency threshold must be positive".to_string());
        }
        
        if self.performance.error_rate_threshold <= 0.0 || self.performance.error_rate_threshold > 1.0 {
            return Err("Error rate threshold must be between 0 and 1".to_string());
        }
        
        // 验证安全配置
        if self.security.max_single_order_amount <= 0.0 {
            return Err("Max single order amount must be positive".to_string());
        }
        
        if self.security.max_daily_trading_amount <= 0.0 {
            return Err("Max daily trading amount must be positive".to_string());
        }
        
        if self.security.max_single_order_amount > self.security.max_daily_trading_amount {
            return Err("Max single order amount cannot exceed max daily trading amount".to_string());
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_live_trading_config_default() {
        let config = LiveTradingConfig::default();
        assert!(!config.enable_detailed_logging);
        assert!(config.enable_performance_monitoring);
        assert!(!config.enable_debug_mode);
        assert_eq!(config.connection_timeout_ms, 5000);
        assert_eq!(config.max_retries, 3);
    }
    
    #[test]
    fn test_config_builder_production_mode() {
        let (engine_config, adapter_config) = LiveTradingConfigBuilder::new()
            .production_mode()
            .build();
        
        assert!(!engine_config.enable_detailed_logging);
        assert!(adapter_config.low_latency_mode);
        assert_eq!(adapter_config.circuit_breaker_threshold, 0.05);
        assert_eq!(adapter_config.timeout_ms, 50);
    }
    
    #[test]
    fn test_config_builder_development_mode() {
        let (engine_config, adapter_config) = LiveTradingConfigBuilder::new()
            .development_mode()
            .build();
        
        assert!(engine_config.enable_detailed_logging);
        assert!(engine_config.enable_debug_mode);
        assert!(!adapter_config.low_latency_mode);
        assert!(adapter_config.enable_debug_logging);
    }
    
    #[test]
    fn test_complete_config_validation() {
        let config = CompleteLiveConfig::production();
        assert!(config.validate().is_ok());
        
        let mut invalid_config = config.clone();
        invalid_config.engine.connection_timeout_ms = 0;
        assert!(invalid_config.validate().is_err());
        
        let mut invalid_config2 = config.clone();
        invalid_config2.security.max_single_order_amount = 100000.0;
        invalid_config2.security.max_daily_trading_amount = 50000.0;
        assert!(invalid_config2.validate().is_err());
    }
    
    #[test]
    fn test_environment_configs() {
        let prod_config = CompleteLiveConfig::production();
        let test_config = CompleteLiveConfig::testing();
        let dev_config = CompleteLiveConfig::development();
        
        // 生产环境应该有最严格的限制
        assert!(prod_config.performance.latency_threshold_ms < test_config.performance.latency_threshold_ms);
        assert!(prod_config.performance.error_rate_threshold < test_config.performance.error_rate_threshold);
        
        // 开发环境应该有最宽松的限制
        assert!(dev_config.performance.latency_threshold_ms > test_config.performance.latency_threshold_ms);
        assert!(dev_config.performance.error_rate_threshold > test_config.performance.error_rate_threshold);
        
        // 验证所有配置都是有效的
        assert!(prod_config.validate().is_ok());
        assert!(test_config.validate().is_ok());
        assert!(dev_config.validate().is_ok());
    }
}
