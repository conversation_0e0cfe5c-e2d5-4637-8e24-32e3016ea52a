//! 引擎基础辅助结构体
//!
//! BaseEngine 是一个纯粹的辅助结构体，提供通用的状态管理和单策略管理功能。
//! 重构为支持单引擎单策略模式，确保系统稳定性和资源安全。

use sigmax_core::{EngineType, EngineStatus, SigmaXResult, SigmaXError, Strategy, StrategyId};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error};

/// 基础引擎辅助结构体
///
/// 重构为单策略模式：
/// - 只支持单个策略运行，避免资源竞争和冲突
/// - 提供策略生命周期管理
/// - 确保引擎状态与策略状态的一致性
pub struct BaseEngine {
    engine_type: EngineType,
    status: RwLock<EngineStatus>,
    /// 当前运行的策略（单策略模式）
    current_strategy: RwLock<Option<Arc<dyn Strategy>>>,
}

impl BaseEngine {
    pub fn new(engine_type: EngineType) -> Self {
        Self {
            engine_type,
            status: RwLock::new(EngineStatus::Stopped),
            current_strategy: RwLock::new(None),
        }
    }

    /// 设置引擎状态
    pub async fn set_status(&self, status: EngineStatus) {
        let mut current_status = self.status.write().await;
        *current_status = status;
    }

    /// 检查引擎是否正在运行
    pub async fn is_running(&self) -> bool {
        let status = self.status.read().await;
        matches!(*status, EngineStatus::Running)
    }

    /// 设置当前策略（单策略模式）
    pub async fn set_strategy(&self, strategy: Arc<dyn Strategy>) -> SigmaXResult<()> {
        let mut current_strategy = self.current_strategy.write().await;

        // 如果已有策略运行，先停止它
        if let Some(existing_strategy) = current_strategy.as_ref() {
            warn!("替换现有策略: {} -> {}", existing_strategy.name(), strategy.name());
            existing_strategy.stop().await?;
        }

        info!("设置新策略: {}", strategy.name());
        *current_strategy = Some(strategy);
        Ok(())
    }

    /// 移除当前策略
    pub async fn remove_strategy(&self) -> SigmaXResult<()> {
        let mut current_strategy = self.current_strategy.write().await;

        if let Some(strategy) = current_strategy.take() {
            info!("移除策略: {}", strategy.name());
            strategy.stop().await?;
        }

        Ok(())
    }

    /// 获取当前策略
    pub async fn get_current_strategy(&self) -> Option<Arc<dyn Strategy>> {
        let current_strategy = self.current_strategy.read().await;
        current_strategy.clone()
    }

    /// 检查是否有策略运行
    pub async fn has_strategy(&self) -> bool {
        let current_strategy = self.current_strategy.read().await;
        current_strategy.is_some()
    }

    /// 获取引擎类型
    pub fn engine_type(&self) -> EngineType {
        self.engine_type
    }

    /// 获取引擎状态（非阻塞）
    pub fn status(&self) -> EngineStatus {
        // 使用try_read来避免阻塞
        self.status.try_read().map(|s| s.clone()).unwrap_or(EngineStatus::Error)
    }

    /// 启动引擎的通用逻辑（单策略模式）
    pub async fn start_common(&self) -> SigmaXResult<()> {
        info!("启动引擎: {:?}", self.engine_type);
        self.set_status(EngineStatus::Starting).await;

        // 初始化当前策略（如果存在）
        if let Some(strategy) = self.get_current_strategy().await {
            info!("初始化策略: {}", strategy.name());
            strategy.initialize().await?;
        } else {
            warn!("引擎启动时没有配置策略");
        }

        self.set_status(EngineStatus::Running).await;
        info!("引擎启动完成: {:?}", self.engine_type);
        Ok(())
    }

    /// 停止引擎的通用逻辑（单策略模式）
    pub async fn stop_common(&self) -> SigmaXResult<()> {
        info!("停止引擎: {:?}", self.engine_type);
        self.set_status(EngineStatus::Stopping).await;

        // 停止当前策略
        if let Some(strategy) = self.get_current_strategy().await {
            info!("停止策略: {}", strategy.name());
            strategy.stop().await?;
        }

        self.set_status(EngineStatus::Stopped).await;
        info!("引擎停止完成: {:?}", self.engine_type);
        Ok(())
    }

    /// 暂停引擎（保持策略状态）
    pub async fn pause_common(&self) -> SigmaXResult<()> {
        info!("暂停引擎: {:?}", self.engine_type);
        // 注意：暂停时不停止策略，只是暂停引擎处理
        // 策略状态保持不变，等待恢复
        Ok(())
    }

    /// 恢复引擎运行
    pub async fn resume_common(&self) -> SigmaXResult<()> {
        info!("恢复引擎: {:?}", self.engine_type);
        self.set_status(EngineStatus::Running).await;
        Ok(())
    }

    /// 检查引擎和策略状态一致性
    pub async fn validate_state_consistency(&self) -> SigmaXResult<()> {
        let engine_running = self.is_running().await;
        let has_strategy = self.has_strategy().await;

        if engine_running && !has_strategy {
            return Err(SigmaXError::InvalidState(
                "引擎正在运行但没有配置策略".to_string()
            ));
        }

        if let Some(strategy) = self.get_current_strategy().await {
            if !strategy.is_initialized() && engine_running {
                return Err(SigmaXError::InvalidState(
                    format!("策略 {} 未初始化但引擎正在运行", strategy.name())
                ));
            }
        }

        Ok(())
    }
}
