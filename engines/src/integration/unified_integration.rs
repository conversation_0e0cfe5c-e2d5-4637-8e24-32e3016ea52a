//! 统一风控系统集成模块
//! 
//! ⚠️ DEPRECATED MODULE: 此模块已废弃
//! 
//! ## 迁移指南
//! 
//! 旧的风控集成方式已被新的架构替代：
//! 
//! ### 旧代码
//! ```rust,ignore
//! use sigmax_engines::integration::{UnifiedRiskIntegration, IntegrationConfig};
//! 
//! let integration = UnifiedRiskIntegration::new(config).await?;
//! let result = integration.check_order_risk(&order).await?;
//! ```
//! 
//! ### 新代码
//! ```rust,ignore
//! use sigmax_interfaces::RiskController;
//! use sigmax_risk::RiskManagementFactory;
//! 
//! let risk_controller = RiskManagementFactory::create_risk_controller().await;
//! let result = risk_controller.validate_order(&order).await;
//! ```

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, Order, Balance};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error};

/// 统一风控系统集成器
/// 
/// ⚠️ DEPRECATED: 此结构体已废弃
/// 
/// 请使用新的风控系统架构：
/// - sigmax_interfaces::RiskController 作为风控接口
/// - sigmax_risk::control 提供实时风控业务逻辑
/// - sigmax_risk::analytics 提供风险分析工具
#[deprecated(note = "Use sigmax_interfaces::RiskController and sigmax_risk modules instead")]
pub struct UnifiedRiskIntegration {
    // 保留基本结构以维持编译兼容性
    _placeholder: (),
}

/// 集成配置
/// 
/// ⚠️ DEPRECATED: 请使用 sigmax_risk 模块的配置系统
#[deprecated(note = "Use sigmax_risk configuration system instead")]
#[derive(Debug, Clone)]
pub struct IntegrationConfig {
    pub enable_backtest_adapter: bool,
    pub enable_live_adapter: bool,
    pub enable_webapi_adapter: bool,
    pub enable_strategy_adapter: bool,
    pub enable_unified_metrics: bool,
    pub enable_data_persistence: bool,
    pub backward_compatibility_mode: bool,
}

impl Default for IntegrationConfig {
    fn default() -> Self {
        Self {
            enable_backtest_adapter: true,
            enable_live_adapter: true,
            enable_webapi_adapter: true,
            enable_strategy_adapter: true,
            enable_unified_metrics: true,
            enable_data_persistence: true,
            backward_compatibility_mode: true,
        }
    }
}

/// 集成健康状态
/// 
/// ⚠️ DEPRECATED: 请使用 sigmax_risk 模块的健康检查
#[deprecated(note = "Use sigmax_risk health check system instead")]
#[derive(Debug, Clone, PartialEq)]
pub enum IntegrationHealthStatus {
    Healthy,
    Warning,
    Critical,
    Offline,
}

/// 集成统计信息
/// 
/// ⚠️ DEPRECATED: 请使用 sigmax_risk 模块的指标系统
#[deprecated(note = "Use sigmax_risk metrics system instead")]
#[derive(Debug, Clone)]
pub struct IntegrationStats {
    pub total_checks: u64,
    pub successful_checks: u64,
    pub failed_checks: u64,
    pub avg_response_time_ms: f64,
    pub cache_hit_rate: f64,
    pub error_rate: f64,
}

impl UnifiedRiskIntegration {
    /// 创建新的集成实例
    /// 
    /// ⚠️ DEPRECATED: 请使用新的风控系统
    #[deprecated(note = "Use sigmax_risk::RiskManagementFactory instead")]
    pub async fn new(_config: IntegrationConfig) -> SigmaXResult<Self> {
        Err(SigmaXError::NotImplemented(
            "UnifiedRiskIntegration is deprecated. Use sigmax_risk module instead.".to_string()
        ))
    }
    
    /// 获取健康状态
    /// 
    /// ⚠️ DEPRECATED: 请使用新的风控系统
    #[deprecated(note = "Use sigmax_risk health check instead")]
    pub async fn health_status(&self) -> IntegrationHealthStatus {
        IntegrationHealthStatus::Offline
    }
    
    /// 获取统计信息
    /// 
    /// ⚠️ DEPRECATED: 请使用新的风控系统
    #[deprecated(note = "Use sigmax_risk metrics instead")]
    pub async fn get_stats(&self) -> IntegrationStats {
        IntegrationStats {
            total_checks: 0,
            successful_checks: 0,
            failed_checks: 0,
            avg_response_time_ms: 0.0,
            cache_hit_rate: 0.0,
            error_rate: 0.0,
        }
    }
}

// ============================================================================
// 全局集成实例管理 - DEPRECATED
// ============================================================================

static mut GLOBAL_INTEGRATION: Option<Arc<UnifiedRiskIntegration>> = None;
static INIT_ONCE: std::sync::Once = std::sync::Once::new();

/// 初始化全局集成实例
/// 
/// ⚠️ DEPRECATED: 请使用依赖注入模式
#[deprecated(note = "Use dependency injection with sigmax_interfaces::RiskController instead")]
pub async fn initialize_global_integration(_config: IntegrationConfig) -> SigmaXResult<()> {
    Err(SigmaXError::NotImplemented(
        "Global integration is deprecated. Use dependency injection instead.".to_string()
    ))
}

/// 获取全局集成实例
/// 
/// ⚠️ DEPRECATED: 请使用依赖注入模式
#[deprecated(note = "Use dependency injection with sigmax_interfaces::RiskController instead")]
pub fn get_global_integration() -> Option<Arc<UnifiedRiskIntegration>> {
    None
}

/// 全局订单风控检查
/// 
/// ⚠️ DEPRECATED: 请使用依赖注入的 RiskController
#[deprecated(note = "Use injected RiskController::validate_order instead")]
pub async fn global_check_order_risk(_order: &Order) -> SigmaXResult<bool> {
    Err(SigmaXError::NotImplemented(
        "Global risk check is deprecated. Use RiskController::validate_order instead.".to_string()
    ))
}

/// 全局持仓风控检查
/// 
/// ⚠️ DEPRECATED: 请使用依赖注入的 RiskController
#[deprecated(note = "Use injected RiskController::check_position_limits instead")]
pub async fn global_check_position_risk(_balance: &Balance) -> SigmaXResult<bool> {
    Err(SigmaXError::NotImplemented(
        "Global position risk check is deprecated. Use RiskController::check_position_limits instead.".to_string()
    ))
}

// ============================================================================
// 迁移指南
// ============================================================================

/// 迁移指南模块
pub mod migration_guide {
    //! # UnifiedRiskIntegration 迁移指南
    //! 
    //! ## 概述
    //! 
    //! UnifiedRiskIntegration 模块已被新的模块化风控系统替代：
    //! - `sigmax_interfaces::RiskController` - 风控接口
    //! - `sigmax_risk::control` - 实时风控业务逻辑
    //! - `sigmax_risk::analytics` - 风险分析工具
    //! 
    //! ## 迁移步骤
    //! 
    //! ### 1. 更新依赖
    //! 
    //! ```toml
    //! [dependencies]
    //! # 移除旧依赖
    //! # sigmax-engines = { workspace = true, features = ["integration"] }
    //! 
    //! # 添加新依赖
    //! sigmax-interfaces = { workspace = true }
    //! sigmax-risk = { workspace = true }
    //! ```
    //! 
    //! ### 2. 更新代码
    //! 
    //! #### 旧代码
    //! ```rust,ignore
    //! use sigmax_engines::integration::{
    //!     UnifiedRiskIntegration, IntegrationConfig,
    //!     initialize_global_integration, global_check_order_risk
    //! };
    //! 
    //! // 初始化
    //! let config = IntegrationConfig::default();
    //! initialize_global_integration(config).await?;
    //! 
    //! // 使用
    //! let is_safe = global_check_order_risk(&order).await?;
    //! ```
    //! 
    //! #### 新代码
    //! ```rust,ignore
    //! use sigmax_interfaces::RiskController;
    //! use sigmax_risk::RiskManagementFactory;
    //! 
    //! // 创建风控控制器
    //! let risk_controller = RiskManagementFactory::create_risk_controller().await;
    //! 
    //! // 使用依赖注入
    //! let engine = ExecutionEngineBuilder::new(EngineType::Live)
    //!     .with_risk_controller(risk_controller)
    //!     .build()
    //!     .await?;
    //! 
    //! // 直接使用
    //! let result = risk_controller.validate_order(&order).await;
    //! ```
    //! 
    //! ## 优势
    //! 
    //! 新的架构提供了以下优势：
    //! - 更好的模块化和关注点分离
    //! - 支持依赖注入，提高可测试性
    //! - 面向接口设计，降低耦合度
    //! - 更清晰的风控业务逻辑和分析工具分离
}