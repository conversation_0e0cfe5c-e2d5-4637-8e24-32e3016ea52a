# SigmaX Trading Analysis

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

SigmaX Trading Analysis 是一个综合性的交易分析库，提供全面的交易绩效评估、风险分析和投资组合管理功能。

## 🚀 特性

### 📊 FIFO 盈亏分析
- **FIFO 会计准则**: 严格按照先进先出原则进行交易配对
- **多交易对支持**: 同时分析多个交易对的交易数据
- **详细盈亏分析**: 提供胜率、平均盈利/亏损、盈亏比等关键指标
- **未平仓头寸跟踪**: 自动识别和跟踪未平仓头寸

### 📈 绩效指标分析
- **夏普比率**: 风险调整后收益评估
- **最大回撤**: 投资组合风险度量
- **卡尔马比率**: 收益与最大回撤的比值
- **索提诺比率**: 只考虑下行风险的夏普比率
- **年化收益率**: 标准化的收益率比较

### ⚠️ 风险分析
- **VaR & CVaR**: 风险价值和条件风险价值计算
- **波动率分析**: 日波动率和年化波动率
- **下行偏差**: 专注于负收益的风险度量
- **连续亏损分析**: 最大连续亏损次数和金额
- **贝塔系数**: 相对于基准的系统性风险

### 📊 技术指标
- **移动平均**: SMA、EMA 等趋势指标
- **动量指标**: RSI、威廉指标、随机指标
- **波动性指标**: 布林带等
- **趋势指标**: MACD 等

### 🔧 技术特性
- **高性能**: 优化的算法实现，支持大量交易数据
- **类型安全**: 使用 Rust 的类型系统确保数据安全
- **模块化设计**: 独立的分析模块，易于扩展

## 📦 安装

将以下内容添加到您的 `Cargo.toml` 文件中：

```toml
[dependencies]
sigmax-trading-analysis = { path = "../trading_analysis" }
sigmax-core = { path = "../core" }
```

## 🔧 快速开始

```rust
use sigmax_trading_analysis::{
    analyze_trades_with_fifo,
    calculate_performance_metrics,
    calculate_risk_metrics,
    calculate_technical_indicators,
};
use sigmax_core::{Trade, TradingPair, OrderSide};
use rust_decimal::Decimal;

// 准备交易数据
let trades: Vec<Trade> = vec![
    // 您的交易数据...
];

// 1. FIFO 盈亏分析
let fifo_result = analyze_trades_with_fifo(&trades)?;
println!("总已实现盈亏: {}", fifo_result.total_realized_pnl());
println!("胜率: {:.2}%", fifo_result.win_rate() * Decimal::from(100));

// 2. 绩效指标分析
let performance = calculate_performance_metrics(
    &fifo_result,
    Decimal::from(100000), // 初始资金
    Decimal::from_f32_retain(0.03).unwrap(), // 无风险利率
)?;
println!("夏普比率: {:.2}", performance.sharpe_ratio);
println!("最大回撤: {:.2}%", performance.max_drawdown * Decimal::from(100));

// 3. 风险分析
let risk_metrics = calculate_risk_metrics(
    &fifo_result.realized_pnl_pairs,
    Decimal::from(100000),
    None,
)?;
println!("VaR (95%): {:.2}%", risk_metrics.var_95 * Decimal::from(100));

// 4. 技术指标分析
let prices = vec![/* 价格序列 */];
let technical = calculate_technical_indicators(&prices, 14)?;
println!("RSI 指标数量: {}", technical.rsi.len());
```

## 📊 核心概念

### FIFO 原则

FIFO（First In, First Out）是一种会计方法，确保：
- 最早买入的资产最先被卖出
- 交易配对严格按照时间顺序进行
- 符合大多数税务管辖区的要求

### 数据结构

#### `RealizedTradePair`
表示一个已完成的买卖配对：
```rust
pub struct RealizedTradePair {
    pub trading_pair: TradingPair,
    pub entry_timestamp: DateTime<Utc>,
    pub exit_timestamp: DateTime<Utc>,
    pub quantity: Quantity,
    pub entry_price: Price,
    pub exit_price: Price,
    pub pnl: Decimal,
    pub pnl_percentage: Decimal,
}
```

#### `UnrealizedPosition`
表示未平仓头寸：
```rust
pub struct UnrealizedPosition {
    pub trading_pair: TradingPair,
    pub quantity: Quantity,
    pub average_entry_price: Price,
    pub first_entry_timestamp: DateTime<Utc>,
}
```

#### `FifoAnalysisResult`
包含完整的分析结果：
```rust
pub struct FifoAnalysisResult {
    pub realized_pnl_pairs: Vec<RealizedTradePair>,
    pub unrealized_positions: Vec<UnrealizedPosition>,
}
```

## 📈 分析指标

该库提供以下关键分析指标：

- **总已实现盈亏**: 所有已配对交易的总盈亏
- **胜率**: 盈利交易占总交易的比例
- **平均盈利**: 盈利交易的平均盈利额
- **平均亏损**: 亏损交易的平均亏损额
- **盈亏比**: 平均盈利与平均亏损的比值
- **交易次数**: 已实现交易对的总数

## 🔍 示例

查看 `examples/` 目录中的完整示例：

```bash
cargo run --example basic_usage
```

## 🧪 测试

运行测试套件：

```bash
cargo test
```

运行特定测试：

```bash
cargo test test_perfect_fifo_matching
```

## 🏗️ 架构

该库采用模块化设计：

```
fifo_analysis/
├── src/
│   ├── lib.rs          # 库入口和公共API
│   ├── analysis.rs     # 核心FIFO分析算法
│   ├── types.rs        # 数据类型定义
│   └── error.rs        # 错误处理
├── tests/
│   └── integration_tests.rs  # 集成测试
├── examples/
│   └── basic_usage.rs  # 使用示例
└── README.md
```

## 🤝 贡献

欢迎贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 📄 许可证

该项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关项目

- [SigmaX Core](../core) - SigmaX 核心类型和特征
- [SigmaX Engines](../engines) - SigmaX 交易引擎

## 📞 支持

如果您有任何问题或建议，请：

1. 查看 [文档](https://docs.rs/sigmax-fifo-analysis)
2. 搜索现有的 [Issues](https://github.com/your-org/sigmax/issues)
3. 创建新的 Issue

---

**注意**: 该库仅用于教育和研究目的。在实际交易中使用前，请确保充分理解 FIFO 会计原则和相关的税务影响。
