//! FIFO分析核心算法实现

use crate::{Result, RealizedTradePair, UnrealizedPosition, FifoAnalysisResult};
use sigmax_core::{Trade, Quantity, TradingPair, OrderSide};
use rust_decimal::Decimal;
use std::collections::{HashMap, VecDeque};

/// 内部使用的买入交易记录
#[derive(Debug, Clone)]
struct BuyTradeEntry {
    trade: Trade,
    remaining_quantity: Quantity,
}

impl BuyTradeEntry {
    fn new(trade: Trade) -> Self {
        let remaining_quantity = trade.quantity;
        Self {
            trade,
            remaining_quantity,
        }
    }
}

/// FIFO (先进先出) 盈亏分析函数
/// 
/// 接收一个扁平化的交易列表，应用FIFO会计准则，
/// 输出已配对的闭环交易和未平仓头寸。
/// 
/// # 参数
/// * `trades` - 按时间排序的交易记录slice
/// 
/// # 返回值
/// * `FifoAnalysisResult` - 包含已实现交易对和未平仓头寸的分析结果
/// 
/// # 错误
/// * 当输入数据格式错误或计算过程中出现异常时返回错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_trading_analysis::analyze_trades_with_fifo;
/// use sigmax_core::{Trade, TradingPair, OrderSide, ExchangeId};
/// use chrono::Utc;
/// use uuid::Uuid;
/// 
/// let trading_pair = TradingPair::new("BTC", "USDT");
/// let trades = vec![
///     Trade {
///         id: Uuid::new_v4(),
///         order_id: Uuid::new_v4(),
///         exchange_id: ExchangeId::Simulator,
///         trading_pair: trading_pair.clone(),
///         side: OrderSide::Buy,
///         quantity: "1.0".parse().unwrap(),
///         price: "50000.0".parse().unwrap(),
///         fee: "0.0".parse().unwrap(),
///         fee_asset: Some("USDT".to_string()),
///         executed_at: Utc::now(),
///         created_at: Utc::now(),
///     },
///     // 更多交易...
/// ];
/// 
/// let result = analyze_trades_with_fifo(&trades)?;
/// println!("总已实现盈亏: {}", result.total_realized_pnl());
/// # Ok::<(), Box<dyn std::error::Error>>(())
/// ```
pub fn analyze_trades_with_fifo(trades: &[Trade]) -> Result<FifoAnalysisResult> {
    // 边界情况：空交易列表
    if trades.is_empty() {
        return Ok(FifoAnalysisResult::new());
    }

    // 按交易对分组处理
    let mut grouped_trades = HashMap::new();
    for trade in trades {
        grouped_trades
            .entry(trade.trading_pair.clone())
            .or_insert_with(Vec::new)
            .push(trade.clone());
    }

    let mut all_realized_pairs = Vec::new();
    let mut all_unrealized_positions = Vec::new();

    // 对每个交易对单独进行FIFO分析
    for (trading_pair, pair_trades) in grouped_trades {
        let (realized_pairs, unrealized_positions) = 
            analyze_single_pair_fifo(&trading_pair, &pair_trades)?;
        
        all_realized_pairs.extend(realized_pairs);
        all_unrealized_positions.extend(unrealized_positions);
    }

    Ok(FifoAnalysisResult {
        realized_pnl_pairs: all_realized_pairs,
        unrealized_positions: all_unrealized_positions,
    })
}

/// 对单个交易对进行FIFO分析
fn analyze_single_pair_fifo(
    trading_pair: &TradingPair,
    trades: &[Trade],
) -> Result<(Vec<RealizedTradePair>, Vec<UnrealizedPosition>)> {
    // 初始化买入队列（FIFO队列）
    let mut buy_queue: VecDeque<BuyTradeEntry> = VecDeque::new();
    let mut realized_pairs = Vec::new();

    // 按时间顺序处理交易
    let mut sorted_trades = trades.to_vec();
    sorted_trades.sort_by(|a, b| a.executed_at.cmp(&b.executed_at));

    for trade in sorted_trades {
        match trade.side {
            OrderSide::Buy => {
                // 买入交易：直接加入队列
                buy_queue.push_back(BuyTradeEntry::new(trade));
            }
            OrderSide::Sell => {
                // 卖出交易：与买入队列进行FIFO配对
                let mut remaining_sell_quantity = trade.quantity;
                let sell_price = trade.price;
                let sell_timestamp = trade.executed_at;

                // 持续配对直到卖出数量用完或买入队列为空
                while remaining_sell_quantity > Decimal::ZERO && !buy_queue.is_empty() {
                    if let Some(mut buy_entry) = buy_queue.pop_front() {
                        let buy_price = buy_entry.trade.price;
                        let buy_timestamp = buy_entry.trade.executed_at;

                        if remaining_sell_quantity >= buy_entry.remaining_quantity {
                            // 情况一：买单被完全或超额平仓
                            let matched_quantity = buy_entry.remaining_quantity;

                            // 创建已实现交易对
                            let realized_pair = RealizedTradePair::new(
                                trading_pair.clone(),
                                buy_timestamp,
                                sell_timestamp,
                                matched_quantity,
                                buy_price,
                                sell_price,
                            );
                            realized_pairs.push(realized_pair);

                            // 更新剩余卖出数量
                            remaining_sell_quantity -= matched_quantity;

                            // 买单完全消耗，不需要放回队列
                        } else {
                            // 情况二：买单被部分平仓
                            let matched_quantity = remaining_sell_quantity;

                            // 创建已实现交易对
                            let realized_pair = RealizedTradePair::new(
                                trading_pair.clone(),
                                buy_timestamp,
                                sell_timestamp,
                                matched_quantity,
                                buy_price,
                                sell_price,
                            );
                            realized_pairs.push(realized_pair);

                            // 更新买单剩余数量并放回队列前端
                            buy_entry.remaining_quantity -= matched_quantity;
                            buy_queue.push_front(buy_entry);

                            // 卖出数量用完
                            remaining_sell_quantity = Decimal::ZERO;
                        }
                    }
                }

                // 如果还有剩余卖出数量，说明出现了异常情况（卖出超过买入）
                if remaining_sell_quantity > Decimal::ZERO {
                    tracing::warn!(
                        "FIFO分析警告: 交易对 {} 在时间 {} 出现卖出数量 {} 超过可用买入数量的情况",
                        trading_pair.symbol(),
                        sell_timestamp,
                        remaining_sell_quantity
                    );
                }
            }
        }
    }

    // 处理未平仓头寸
    let mut unrealized_positions = Vec::new();
    for buy_entry in buy_queue {
        if buy_entry.remaining_quantity > Decimal::ZERO {
            let unrealized_position = UnrealizedPosition::new(
                trading_pair.clone(),
                buy_entry.remaining_quantity,
                buy_entry.trade.price,
                buy_entry.trade.executed_at,
            );
            unrealized_positions.push(unrealized_position);
        }
    }

    Ok((realized_pairs, unrealized_positions))
}

/// 批量分析多个交易对的FIFO盈亏
/// 
/// 这是一个便利函数，用于同时分析多个交易对的交易数据。
/// 
/// # 参数
/// * `trades_by_pair` - 按交易对分组的交易数据
/// 
/// # 返回值
/// * `HashMap<TradingPair, FifoAnalysisResult>` - 每个交易对的分析结果
pub fn analyze_multiple_pairs(
    trades_by_pair: &HashMap<TradingPair, Vec<Trade>>
) -> Result<HashMap<TradingPair, FifoAnalysisResult>> {
    let mut results = HashMap::new();
    
    for (trading_pair, trades) in trades_by_pair {
        let result = analyze_trades_with_fifo(trades)?;
        results.insert(trading_pair.clone(), result);
    }
    
    Ok(results)
}

/// 合并多个FIFO分析结果
/// 
/// 将多个分析结果合并为一个总体结果，用于获取整体的投资组合表现。
/// 
/// # 参数
/// * `results` - 要合并的分析结果列表
/// 
/// # 返回值
/// * `FifoAnalysisResult` - 合并后的总体分析结果
pub fn merge_analysis_results(results: &[FifoAnalysisResult]) -> FifoAnalysisResult {
    let mut merged = FifoAnalysisResult::new();
    
    for result in results {
        merged.realized_pnl_pairs.extend(result.realized_pnl_pairs.clone());
        merged.unrealized_positions.extend(result.unrealized_positions.clone());
    }
    
    // 按时间排序已实现交易对
    merged.realized_pnl_pairs.sort_by(|a, b| a.exit_timestamp.cmp(&b.exit_timestamp));
    
    // 按时间排序未平仓头寸
    merged.unrealized_positions.sort_by(|a, b| a.first_entry_timestamp.cmp(&b.first_entry_timestamp));
    
    merged
}
