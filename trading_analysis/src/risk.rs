//! 风险分析模块
//! 
//! 提供各种风险度量指标，包括VaR、CVaR、波动率等。

use crate::{Result, RealizedTradePair};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use std::collections::HashMap;

/// 风险分析结果
#[derive(Debug, Clone)]
pub struct RiskMetrics {
    /// 风险价值 (VaR) - 95%置信度
    pub var_95: Decimal,
    /// 风险价值 (VaR) - 99%置信度  
    pub var_99: Decimal,
    /// 条件风险价值 (CVaR) - 95%置信度
    pub cvar_95: Decimal,
    /// 条件风险价值 (CVaR) - 99%置信度
    pub cvar_99: Decimal,
    /// 日波动率
    pub daily_volatility: Decimal,
    /// 年化波动率
    pub annual_volatility: Decimal,
    /// 下行偏差
    pub downside_deviation: Decimal,
    /// 最大连续亏损
    pub max_consecutive_losses: u32,
    /// 最大连续亏损金额
    pub max_consecutive_loss_amount: Decimal,
    /// 贝塔系数（相对于基准）
    pub beta: Option<Decimal>,
    /// 跟踪误差
    pub tracking_error: Option<Decimal>,
}

impl RiskMetrics {
    /// 创建新的风险指标实例
    pub fn new() -> Self {
        Self {
            var_95: Decimal::ZERO,
            var_99: Decimal::ZERO,
            cvar_95: Decimal::ZERO,
            cvar_99: Decimal::ZERO,
            daily_volatility: Decimal::ZERO,
            annual_volatility: Decimal::ZERO,
            downside_deviation: Decimal::ZERO,
            max_consecutive_losses: 0,
            max_consecutive_loss_amount: Decimal::ZERO,
            beta: None,
            tracking_error: None,
        }
    }

    /// 获取风险等级评估
    pub fn risk_assessment(&self) -> RiskAssessment {
        let high_risk_conditions = [
            self.var_95.abs() > Decimal::from_f32_retain(0.05).unwrap(), // VaR > 5%
            self.annual_volatility > Decimal::from_f32_retain(0.3).unwrap(), // 年化波动率 > 30%
            self.max_consecutive_losses > 5,
        ];

        let medium_risk_conditions = [
            self.var_95.abs() > Decimal::from_f32_retain(0.02).unwrap(), // VaR > 2%
            self.annual_volatility > Decimal::from_f32_retain(0.15).unwrap(), // 年化波动率 > 15%
            self.max_consecutive_losses > 3,
        ];

        if high_risk_conditions.iter().any(|&x| x) {
            RiskAssessment::High
        } else if medium_risk_conditions.iter().any(|&x| x) {
            RiskAssessment::Medium
        } else {
            RiskAssessment::Low
        }
    }
}

/// 风险评估等级
#[derive(Debug, Clone, PartialEq)]
pub enum RiskAssessment {
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
}

/// 计算风险指标
/// 
/// # 参数
/// * `trades` - 已实现交易对列表
/// * `initial_capital` - 初始资金
/// * `benchmark_returns` - 基准收益率序列（可选，用于计算贝塔系数）
/// 
/// # 返回值
/// * `RiskMetrics` - 完整的风险分析结果
pub fn calculate_risk_metrics(
    trades: &[RealizedTradePair],
    initial_capital: Decimal,
    benchmark_returns: Option<&[Decimal]>,
) -> Result<RiskMetrics> {
    if trades.is_empty() {
        return Ok(RiskMetrics::new());
    }

    let mut metrics = RiskMetrics::new();
    
    // 计算每日收益率
    let daily_returns = calculate_daily_returns(trades, initial_capital)?;
    
    // VaR计算
    metrics.var_95 = calculate_var(&daily_returns, 0.95)?;
    metrics.var_99 = calculate_var(&daily_returns, 0.99)?;
    
    // CVaR计算
    metrics.cvar_95 = calculate_cvar(&daily_returns, 0.95)?;
    metrics.cvar_99 = calculate_cvar(&daily_returns, 0.99)?;
    
    // 波动率计算
    metrics.daily_volatility = calculate_volatility(&daily_returns)?;
    metrics.annual_volatility = metrics.daily_volatility * Decimal::from_f64_retain(252.0_f64.sqrt()).unwrap_or(Decimal::ONE);
    
    // 下行偏差
    metrics.downside_deviation = calculate_downside_deviation(&daily_returns)?;
    
    // 连续亏损分析
    let (max_losses, max_loss_amount) = calculate_consecutive_losses(trades)?;
    metrics.max_consecutive_losses = max_losses;
    metrics.max_consecutive_loss_amount = max_loss_amount;
    
    // 如果提供了基准数据，计算贝塔系数和跟踪误差
    if let Some(benchmark) = benchmark_returns {
        if benchmark.len() == daily_returns.len() {
            metrics.beta = Some(calculate_beta(&daily_returns, benchmark)?);
            metrics.tracking_error = Some(calculate_tracking_error(&daily_returns, benchmark)?);
        }
    }
    
    Ok(metrics)
}

/// 计算每日收益率
fn calculate_daily_returns(trades: &[RealizedTradePair], initial_capital: Decimal) -> Result<Vec<Decimal>> {
    let mut daily_pnl: HashMap<chrono::NaiveDate, Decimal> = HashMap::new();
    
    for trade in trades {
        let date = trade.exit_timestamp.date_naive();
        *daily_pnl.entry(date).or_insert(Decimal::ZERO) += trade.pnl;
    }
    
    let daily_returns: Vec<Decimal> = daily_pnl.values()
        .map(|pnl| pnl / initial_capital)
        .collect();
    
    Ok(daily_returns)
}

/// 计算VaR (Value at Risk)
fn calculate_var(returns: &[Decimal], confidence_level: f64) -> Result<Decimal> {
    if returns.is_empty() {
        return Ok(Decimal::ZERO);
    }
    
    let mut sorted_returns = returns.to_vec();
    sorted_returns.sort();
    
    let index = ((1.0 - confidence_level) * returns.len() as f64) as usize;
    let var = sorted_returns.get(index).copied().unwrap_or(Decimal::ZERO);
    
    Ok(var)
}

/// 计算CVaR (Conditional Value at Risk)
fn calculate_cvar(returns: &[Decimal], confidence_level: f64) -> Result<Decimal> {
    if returns.is_empty() {
        return Ok(Decimal::ZERO);
    }
    
    let var = calculate_var(returns, confidence_level)?;
    
    // CVaR是所有小于等于VaR的收益率的平均值
    let tail_returns: Vec<Decimal> = returns.iter()
        .filter(|&&r| r <= var)
        .copied()
        .collect();
    
    if tail_returns.is_empty() {
        return Ok(var);
    }
    
    let cvar = tail_returns.iter().sum::<Decimal>() / Decimal::from(tail_returns.len());
    Ok(cvar)
}

/// 计算波动率
fn calculate_volatility(returns: &[Decimal]) -> Result<Decimal> {
    if returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }
    
    let mean = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
    
    let variance: Decimal = returns.iter()
        .map(|r| (*r - mean) * (*r - mean))
        .sum::<Decimal>() / Decimal::from(returns.len() - 1);
    
    Ok(variance.sqrt().unwrap_or(Decimal::ZERO))
}

/// 计算下行偏差
fn calculate_downside_deviation(returns: &[Decimal]) -> Result<Decimal> {
    let negative_returns: Vec<Decimal> = returns.iter()
        .filter(|&&r| r < Decimal::ZERO)
        .copied()
        .collect();
    
    if negative_returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }
    
    let mean_negative = negative_returns.iter().sum::<Decimal>() / Decimal::from(negative_returns.len());
    
    let variance: Decimal = negative_returns.iter()
        .map(|r| (*r - mean_negative) * (*r - mean_negative))
        .sum::<Decimal>() / Decimal::from(negative_returns.len() - 1);
    
    Ok(variance.sqrt().unwrap_or(Decimal::ZERO))
}

/// 计算最大连续亏损
fn calculate_consecutive_losses(trades: &[RealizedTradePair]) -> Result<(u32, Decimal)> {
    if trades.is_empty() {
        return Ok((0, Decimal::ZERO));
    }
    
    let mut sorted_trades = trades.to_vec();
    sorted_trades.sort_by(|a, b| a.exit_timestamp.cmp(&b.exit_timestamp));
    
    let mut max_consecutive = 0u32;
    let mut current_consecutive = 0u32;
    let mut max_loss_amount = Decimal::ZERO;
    let mut current_loss_amount = Decimal::ZERO;
    
    for trade in sorted_trades {
        if trade.pnl < Decimal::ZERO {
            current_consecutive += 1;
            current_loss_amount += trade.pnl.abs();
            
            if current_consecutive > max_consecutive {
                max_consecutive = current_consecutive;
                max_loss_amount = current_loss_amount;
            }
        } else {
            current_consecutive = 0;
            current_loss_amount = Decimal::ZERO;
        }
    }
    
    Ok((max_consecutive, max_loss_amount))
}

/// 计算贝塔系数
fn calculate_beta(returns: &[Decimal], benchmark_returns: &[Decimal]) -> Result<Decimal> {
    if returns.len() != benchmark_returns.len() || returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }
    
    let mean_return = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
    let mean_benchmark = benchmark_returns.iter().sum::<Decimal>() / Decimal::from(benchmark_returns.len());
    
    let mut covariance = Decimal::ZERO;
    let mut benchmark_variance = Decimal::ZERO;
    
    for i in 0..returns.len() {
        let return_diff = returns[i] - mean_return;
        let benchmark_diff = benchmark_returns[i] - mean_benchmark;
        
        covariance += return_diff * benchmark_diff;
        benchmark_variance += benchmark_diff * benchmark_diff;
    }
    
    if benchmark_variance == Decimal::ZERO {
        return Ok(Decimal::ZERO);
    }
    
    let beta = covariance / benchmark_variance;
    Ok(beta)
}

/// 计算跟踪误差
fn calculate_tracking_error(returns: &[Decimal], benchmark_returns: &[Decimal]) -> Result<Decimal> {
    if returns.len() != benchmark_returns.len() || returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }
    
    let excess_returns: Vec<Decimal> = returns.iter()
        .zip(benchmark_returns.iter())
        .map(|(r, b)| r - b)
        .collect();
    
    calculate_volatility(&excess_returns)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::RealizedTradePair;
    use sigmax_core::TradingPair;
    use chrono::{Utc, TimeZone};

    #[test]
    fn test_risk_metrics_calculation() {
        let trading_pair = TradingPair::new("BTC", "USDT");
        let trades = vec![
            RealizedTradePair::new(
                trading_pair.clone(),
                Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 1, 2, 0, 0, 0).unwrap(),
                "1.0".parse().unwrap(),
                "50000.0".parse().unwrap(),
                "48000.0".parse().unwrap(), // 亏损交易
            ),
            RealizedTradePair::new(
                trading_pair.clone(),
                Utc.with_ymd_and_hms(2023, 1, 3, 0, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 1, 4, 0, 0, 0).unwrap(),
                "1.0".parse().unwrap(),
                "48000.0".parse().unwrap(),
                "52000.0".parse().unwrap(), // 盈利交易
            ),
        ];

        let initial_capital = Decimal::from(100000);
        let metrics = calculate_risk_metrics(&trades, initial_capital, None).unwrap();
        
        assert!(metrics.daily_volatility >= Decimal::ZERO);
        assert!(metrics.annual_volatility >= Decimal::ZERO);
        assert!(metrics.var_95 <= Decimal::ZERO); // VaR应该是负数或零
    }

    #[test]
    fn test_consecutive_losses() {
        let trading_pair = TradingPair::new("BTC", "USDT");
        let trades = vec![
            RealizedTradePair::new(
                trading_pair.clone(),
                Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 1, 2, 0, 0, 0).unwrap(),
                "1.0".parse().unwrap(),
                "50000.0".parse().unwrap(),
                "48000.0".parse().unwrap(), // 亏损 -2000
            ),
            RealizedTradePair::new(
                trading_pair.clone(),
                Utc.with_ymd_and_hms(2023, 1, 3, 0, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 1, 4, 0, 0, 0).unwrap(),
                "1.0".parse().unwrap(),
                "48000.0".parse().unwrap(),
                "46000.0".parse().unwrap(), // 亏损 -2000
            ),
        ];

        let (consecutive_losses, loss_amount) = calculate_consecutive_losses(&trades).unwrap();
        assert_eq!(consecutive_losses, 2);
        assert_eq!(loss_amount, Decimal::from(4000));
    }
}
