//! FIFO分析相关的数据类型定义

use sigmax_core::{Amount, Quantity, TradingPair};
use chrono::{DateTime, Utc, Duration};
use rust_decimal::Decimal;
use serde::{Serialize, Deserialize};

/// 单个已实现的闭环交易对
/// 
/// 表示一个完整的买入-卖出配对，包含所有相关的盈亏信息。
/// 这是FIFO分析的核心输出之一。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct RealizedTradePair {
    /// 交易对符号
    pub trading_pair: TradingPair,
    
    /// 入场时间戳（买入时间）
    pub entry_timestamp: DateTime<Utc>,
    
    /// 出场时间戳（卖出时间）
    pub exit_timestamp: DateTime<Utc>,
    
    /// 持仓时长
    pub holding_duration: Duration,
    
    /// 交易数量
    pub quantity: Quantity,
    
    /// 平均买入价
    pub entry_price: Amount,
    
    /// 平均卖出价
    pub exit_price: Amount,
    
    /// 纯盈亏 (收入 - 成本)
    pub pnl: Amount,
    
    /// 盈亏百分比
    pub pnl_percentage: Amount,
}

impl RealizedTradePair {
    /// 创建新的已实现交易对
    /// 
    /// # 参数
    /// 
    /// * `trading_pair` - 交易对
    /// * `entry_timestamp` - 买入时间
    /// * `exit_timestamp` - 卖出时间
    /// * `quantity` - 交易数量
    /// * `entry_price` - 买入价格
    /// * `exit_price` - 卖出价格
    /// 
    /// # 返回值
    /// 
    /// 返回计算好盈亏信息的交易对实例
    pub fn new(
        trading_pair: TradingPair,
        entry_timestamp: DateTime<Utc>,
        exit_timestamp: DateTime<Utc>,
        quantity: Quantity,
        entry_price: Amount,
        exit_price: Amount,
    ) -> Self {
        let holding_duration = exit_timestamp.signed_duration_since(entry_timestamp);
        let cost = quantity * entry_price;
        let revenue = quantity * exit_price;
        let pnl = revenue - cost;
        let pnl_percentage = if cost > Decimal::ZERO {
            (pnl / cost) * Decimal::from(100)
        } else {
            Decimal::ZERO
        };

        Self {
            trading_pair,
            entry_timestamp,
            exit_timestamp,
            holding_duration,
            quantity,
            entry_price,
            exit_price,
            pnl,
            pnl_percentage,
        }
    }

    /// 判断这个交易对是否盈利
    pub fn is_profitable(&self) -> bool {
        self.pnl > Decimal::ZERO
    }

    /// 判断这个交易对是否亏损
    pub fn is_loss(&self) -> bool {
        self.pnl < Decimal::ZERO
    }

    /// 获取持仓天数
    pub fn holding_days(&self) -> i64 {
        self.holding_duration.num_days()
    }

    /// 获取年化收益率（假设365天为一年）
    pub fn annualized_return(&self) -> Decimal {
        if self.holding_duration.num_days() <= 0 {
            return Decimal::ZERO;
        }
        
        let days = Decimal::from(self.holding_duration.num_days());
        let year_days = Decimal::from(365);
        
        self.pnl_percentage * (year_days / days)
    }
}

/// 未实现头寸
/// 
/// 表示尚未平仓的买入头寸，等待后续的卖出交易来配对。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UnrealizedPosition {
    /// 交易对
    pub trading_pair: TradingPair,
    
    /// 持仓数量
    pub quantity: Quantity,
    
    /// 平均入场价格
    pub average_entry_price: Amount,
    
    /// 首次入场时间
    pub first_entry_timestamp: DateTime<Utc>,
}

impl UnrealizedPosition {
    /// 创建新的未实现头寸
    pub fn new(
        trading_pair: TradingPair,
        quantity: Quantity,
        average_entry_price: Amount,
        first_entry_timestamp: DateTime<Utc>,
    ) -> Self {
        Self {
            trading_pair,
            quantity,
            average_entry_price,
            first_entry_timestamp,
        }
    }

    /// 计算未实现盈亏（需要提供当前价格）
    /// 
    /// # 参数
    /// 
    /// * `current_price` - 当前市场价格
    /// 
    /// # 返回值
    /// 
    /// 返回基于当前价格的未实现盈亏
    pub fn calculate_unrealized_pnl(&self, current_price: Amount) -> Amount {
        let cost = self.quantity * self.average_entry_price;
        let current_value = self.quantity * current_price;
        current_value - cost
    }

    /// 计算未实现盈亏百分比
    pub fn calculate_unrealized_pnl_percentage(&self, current_price: Amount) -> Decimal {
        let cost = self.quantity * self.average_entry_price;
        if cost > Decimal::ZERO {
            let pnl = self.calculate_unrealized_pnl(current_price);
            (pnl / cost) * Decimal::from(100)
        } else {
            Decimal::ZERO
        }
    }

    /// 获取持仓天数
    pub fn holding_days(&self) -> i64 {
        Utc::now().signed_duration_since(self.first_entry_timestamp).num_days()
    }
}

/// FIFO分析的最终结果
/// 
/// 包含所有已实现的交易对和未平仓头寸，以及相关的统计方法。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FifoAnalysisResult {
    /// 所有已实现的闭环交易列表
    pub realized_pnl_pairs: Vec<RealizedTradePair>,
    
    /// 所有未平仓的头寸列表
    pub unrealized_positions: Vec<UnrealizedPosition>,
}

impl FifoAnalysisResult {
    /// 创建空的分析结果
    pub fn new() -> Self {
        Self {
            realized_pnl_pairs: Vec::new(),
            unrealized_positions: Vec::new(),
        }
    }

    /// 获取总的已实现盈亏
    pub fn total_realized_pnl(&self) -> Amount {
        self.realized_pnl_pairs.iter().map(|pair| pair.pnl).sum()
    }

    /// 获取胜率（盈利交易数 / 总交易数）
    pub fn win_rate(&self) -> Decimal {
        if self.realized_pnl_pairs.is_empty() {
            return Decimal::ZERO;
        }

        let winning_trades = self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_profitable())
            .count();

        Decimal::from(winning_trades) / Decimal::from(self.realized_pnl_pairs.len())
    }

    /// 获取平均盈利（只计算盈利的交易）
    pub fn average_win(&self) -> Decimal {
        let winning_trades: Vec<_> = self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_profitable())
            .collect();

        if winning_trades.is_empty() {
            return Decimal::ZERO;
        }

        let total_profit: Decimal = winning_trades.iter().map(|pair| pair.pnl).sum();
        total_profit / Decimal::from(winning_trades.len())
    }

    /// 获取平均亏损（只计算亏损的交易，返回正数）
    pub fn average_loss(&self) -> Decimal {
        let losing_trades: Vec<_> = self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_loss())
            .collect();

        if losing_trades.is_empty() {
            return Decimal::ZERO;
        }

        let total_loss: Decimal = losing_trades.iter().map(|pair| pair.pnl.abs()).sum();
        total_loss / Decimal::from(losing_trades.len())
    }

    /// 获取盈亏比（平均盈利 / 平均亏损）
    pub fn profit_factor(&self) -> Decimal {
        let avg_loss = self.average_loss();
        if avg_loss > Decimal::ZERO {
            self.average_win() / avg_loss
        } else {
            "999.99".parse::<Decimal>().unwrap() // 无亏损时给一个大值
        }
    }

    /// 获取最大盈利交易
    pub fn max_win(&self) -> Option<&RealizedTradePair> {
        self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_profitable())
            .max_by(|a, b| a.pnl.cmp(&b.pnl))
    }

    /// 获取最大亏损交易
    pub fn max_loss(&self) -> Option<&RealizedTradePair> {
        self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_loss())
            .min_by(|a, b| a.pnl.cmp(&b.pnl))
    }

    /// 获取总交易次数
    pub fn total_trades(&self) -> usize {
        self.realized_pnl_pairs.len()
    }

    /// 获取盈利交易次数
    pub fn winning_trades_count(&self) -> usize {
        self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_profitable())
            .count()
    }

    /// 获取亏损交易次数
    pub fn losing_trades_count(&self) -> usize {
        self.realized_pnl_pairs.iter()
            .filter(|pair| pair.is_loss())
            .count()
    }
}

impl Default for FifoAnalysisResult {
    fn default() -> Self {
        Self::new()
    }
}
