//! 技术分析模块
//! 
//! 提供常用的技术分析指标，包括移动平均、RSI、布林带等。

use crate::Result;
use rust_decimal::Decimal;
use rust_decimal::prelude::*;

/// 技术指标结果
#[derive(Debug, Clone)]
pub struct TechnicalIndicators {
    /// 简单移动平均线 (SMA)
    pub sma: Vec<Decimal>,
    /// 指数移动平均线 (EMA)
    pub ema: Vec<Decimal>,
    /// 相对强弱指数 (RSI)
    pub rsi: Vec<Decimal>,
    /// 布林带上轨
    pub bollinger_upper: Vec<Decimal>,
    /// 布林带中轨 (SMA)
    pub bollinger_middle: Vec<Decimal>,
    /// 布林带下轨
    pub bollinger_lower: Vec<Decimal>,
    /// MACD线
    pub macd_line: Vec<Decimal>,
    /// MACD信号线
    pub macd_signal: Vec<Decimal>,
    /// MACD柱状图
    pub macd_histogram: Vec<Decimal>,
}

impl TechnicalIndicators {
    /// 创建新的技术指标实例
    pub fn new() -> Self {
        Self {
            sma: Vec::new(),
            ema: Vec::new(),
            rsi: Vec::new(),
            bollinger_upper: Vec::new(),
            bollinger_middle: Vec::new(),
            bollinger_lower: Vec::new(),
            macd_line: Vec::new(),
            macd_signal: Vec::new(),
            macd_histogram: Vec::new(),
        }
    }
}

/// 计算技术指标
/// 
/// # 参数
/// * `prices` - 价格序列（通常是收盘价）
/// * `period` - 计算周期（用于SMA、RSI等）
/// 
/// # 返回值
/// * `TechnicalIndicators` - 完整的技术指标结果
pub fn calculate_technical_indicators(
    prices: &[Decimal],
    period: usize,
) -> Result<TechnicalIndicators> {
    if prices.is_empty() {
        return Ok(TechnicalIndicators::new());
    }

    let mut indicators = TechnicalIndicators::new();
    
    // 计算SMA
    indicators.sma = calculate_sma(prices, period);
    
    // 计算EMA
    indicators.ema = calculate_ema(prices, period);
    
    // 计算RSI
    indicators.rsi = calculate_rsi(prices, period)?;
    
    // 计算布林带
    let (upper, middle, lower) = calculate_bollinger_bands(prices, period, 2.0)?;
    indicators.bollinger_upper = upper;
    indicators.bollinger_middle = middle;
    indicators.bollinger_lower = lower;
    
    // 计算MACD
    let (macd_line, signal_line, histogram) = calculate_macd(prices, 12, 26, 9)?;
    indicators.macd_line = macd_line;
    indicators.macd_signal = signal_line;
    indicators.macd_histogram = histogram;
    
    Ok(indicators)
}

/// 计算简单移动平均线 (SMA)
pub fn calculate_sma(prices: &[Decimal], period: usize) -> Vec<Decimal> {
    if prices.len() < period {
        return Vec::new();
    }
    
    let mut sma = Vec::new();
    
    for i in period - 1..prices.len() {
        let start_idx = i.saturating_sub(period - 1);
        let sum: Decimal = prices[start_idx..=i].iter().sum();
        let average = sum / Decimal::from(period);
        sma.push(average);
    }
    
    sma
}

/// 计算指数移动平均线 (EMA)
pub fn calculate_ema(prices: &[Decimal], period: usize) -> Vec<Decimal> {
    if prices.is_empty() {
        return Vec::new();
    }
    
    let mut ema = Vec::new();
    let multiplier = Decimal::from(2) / Decimal::from(period + 1);
    
    // 第一个EMA值使用第一个价格
    ema.push(prices[0]);
    
    for i in 1..prices.len() {
        let current_ema = (prices[i] * multiplier) + (ema[i - 1] * (Decimal::ONE - multiplier));
        ema.push(current_ema);
    }
    
    ema
}

/// 计算相对强弱指数 (RSI)
pub fn calculate_rsi(prices: &[Decimal], period: usize) -> Result<Vec<Decimal>> {
    if prices.len() < period + 1 {
        return Ok(Vec::new());
    }
    
    let mut rsi = Vec::new();
    let mut gains = Vec::new();
    let mut losses = Vec::new();
    
    // 计算价格变化
    for i in 1..prices.len() {
        let change = prices[i] - prices[i - 1];
        if change > Decimal::ZERO {
            gains.push(change);
            losses.push(Decimal::ZERO);
        } else {
            gains.push(Decimal::ZERO);
            losses.push(change.abs());
        }
    }
    
    // 计算RSI
    for i in period - 1..gains.len() {
        let start_idx = i.saturating_sub(period - 1);
        let avg_gain: Decimal = gains[start_idx..=i].iter().sum::<Decimal>() / Decimal::from(period);
        let avg_loss: Decimal = losses[start_idx..=i].iter().sum::<Decimal>() / Decimal::from(period);
        
        if avg_loss == Decimal::ZERO {
            rsi.push(Decimal::from(100));
        } else {
            let rs = avg_gain / avg_loss;
            let rsi_value = Decimal::from(100) - (Decimal::from(100) / (Decimal::ONE + rs));
            rsi.push(rsi_value);
        }
    }
    
    Ok(rsi)
}

/// 计算布林带
pub fn calculate_bollinger_bands(
    prices: &[Decimal],
    period: usize,
    std_dev_multiplier: f64,
) -> Result<(Vec<Decimal>, Vec<Decimal>, Vec<Decimal>)> {
    if prices.len() < period {
        return Ok((Vec::new(), Vec::new(), Vec::new()));
    }
    
    let sma = calculate_sma(prices, period);
    let mut upper_band = Vec::new();
    let mut lower_band = Vec::new();
    let multiplier = Decimal::from_f64_retain(std_dev_multiplier).unwrap_or(Decimal::from(2));
    
    for i in period - 1..prices.len() {
        let start_idx = i.saturating_sub(period - 1);
        let window = &prices[start_idx..=i];
        let mean = sma[i.saturating_sub(period - 1)];
        
        // 计算标准差
        let variance: Decimal = window.iter()
            .map(|price| (*price - mean) * (*price - mean))
            .sum::<Decimal>() / Decimal::from(period);
        
        let std_dev = variance.sqrt().unwrap_or(Decimal::ZERO);
        
        upper_band.push(mean + (std_dev * multiplier));
        lower_band.push(mean - (std_dev * multiplier));
    }
    
    Ok((upper_band, sma, lower_band))
}

/// 计算MACD指标
pub fn calculate_macd(
    prices: &[Decimal],
    fast_period: usize,
    slow_period: usize,
    signal_period: usize,
) -> Result<(Vec<Decimal>, Vec<Decimal>, Vec<Decimal>)> {
    if prices.len() < slow_period {
        return Ok((Vec::new(), Vec::new(), Vec::new()));
    }
    
    let fast_ema = calculate_ema(prices, fast_period);
    let slow_ema = calculate_ema(prices, slow_period);
    
    // 计算MACD线 (快线EMA - 慢线EMA)
    let mut macd_line = Vec::new();
    let start_index = slow_period - fast_period;
    
    for i in start_index..fast_ema.len() {
        let macd_value = fast_ema[i] - slow_ema[i - start_index];
        macd_line.push(macd_value);
    }
    
    // 计算信号线 (MACD线的EMA)
    let signal_line = calculate_ema(&macd_line, signal_period);
    
    // 计算MACD柱状图 (MACD线 - 信号线)
    let mut histogram = Vec::new();
    let signal_start = signal_period - 1;
    
    for i in signal_start..macd_line.len() {
        let hist_value = macd_line[i] - signal_line[i - signal_start];
        histogram.push(hist_value);
    }
    
    Ok((macd_line, signal_line, histogram))
}

/// 计算威廉指标 (%R)
pub fn calculate_williams_r(
    highs: &[Decimal],
    lows: &[Decimal],
    closes: &[Decimal],
    period: usize,
) -> Result<Vec<Decimal>> {
    if highs.len() != lows.len() || lows.len() != closes.len() || closes.len() < period {
        return Ok(Vec::new());
    }
    
    let mut williams_r = Vec::new();
    
    for i in period - 1..closes.len() {
        let window_high = highs[i - period + 1..=i].iter().max().unwrap();
        let window_low = lows[i - period + 1..=i].iter().min().unwrap();
        let current_close = closes[i];
        
        if window_high == window_low {
            williams_r.push(Decimal::ZERO);
        } else {
            let wr = (window_high - current_close) / (window_high - window_low) * Decimal::from(-100);
            williams_r.push(wr);
        }
    }
    
    Ok(williams_r)
}

/// 计算随机指标 (Stochastic)
pub fn calculate_stochastic(
    highs: &[Decimal],
    lows: &[Decimal],
    closes: &[Decimal],
    k_period: usize,
    d_period: usize,
) -> Result<(Vec<Decimal>, Vec<Decimal>)> {
    if highs.len() != lows.len() || lows.len() != closes.len() || closes.len() < k_period {
        return Ok((Vec::new(), Vec::new()));
    }
    
    let mut k_values = Vec::new();
    
    // 计算%K
    for i in k_period - 1..closes.len() {
        let window_high = *highs[i - k_period + 1..=i].iter().max().unwrap();
        let window_low = *lows[i - k_period + 1..=i].iter().min().unwrap();
        let current_close = closes[i];
        
        if window_high == window_low {
            k_values.push(Decimal::from(50)); // 中性值
        } else {
            let k = (current_close - window_low) / (window_high - window_low) * Decimal::from(100);
            k_values.push(k);
        }
    }
    
    // 计算%D (K值的移动平均)
    let d_values = calculate_sma(&k_values, d_period);
    
    Ok((k_values, d_values))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sma_calculation() {
        let prices = vec![
            Decimal::from(10),
            Decimal::from(12),
            Decimal::from(14),
            Decimal::from(16),
            Decimal::from(18),
        ];
        
        let sma = calculate_sma(&prices, 3);
        assert_eq!(sma.len(), 3);
        assert_eq!(sma[0], Decimal::from(12)); // (10+12+14)/3
        assert_eq!(sma[1], Decimal::from(14)); // (12+14+16)/3
        assert_eq!(sma[2], Decimal::from(16)); // (14+16+18)/3
    }

    #[test]
    fn test_ema_calculation() {
        let prices = vec![
            Decimal::from(10),
            Decimal::from(12),
            Decimal::from(14),
        ];
        
        let ema = calculate_ema(&prices, 2);
        assert_eq!(ema.len(), 3);
        assert_eq!(ema[0], Decimal::from(10)); // 第一个值
    }

    #[test]
    fn test_rsi_calculation() {
        let prices = vec![
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(44),
            Decimal::from(55), // 大幅上涨
        ];
        
        let rsi = calculate_rsi(&prices, 14).unwrap();
        assert!(!rsi.is_empty());
        // RSI应该很高，因为最后一个价格大幅上涨
        assert!(rsi[0] > Decimal::from(50));
    }
}
