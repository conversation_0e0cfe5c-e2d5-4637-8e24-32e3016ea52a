[package]
name = "sigmax-risk"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
sigmax-core.workspace = true
sigmax-database.workspace = true
sigmax-interfaces.workspace = true
tokio.workspace = true
serde.workspace = true
chrono.workspace = true
rust_decimal.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
async-trait = "0.1"
uuid.workspace = true
serde_json.workspace = true
sqlx.workspace = true
reqwest = { version = "0.11", features = ["json"] }
rand = "0.8"
rand_chacha = "0.3"
rust_decimal_macros = "1.37"
redis.workspace = true

[[example]]
name = "complete_risk_system"
path = "examples/complete_risk_system.rs"
