# 风控配置管理最佳实践

## 概述

本文档描述了在 SigmaX 项目中管理风控配置的最佳实践，包括从数据库读取配置、缓存策略、配置转换和热更新机制。

## 架构设计

### 配置管理层次结构

```
Database (risk_rules) → Repository → ConfigManager → Cache → Risk Module
                                  ↓
                              ConfigConverter → Unified Config
```

### 核心组件

1. **RiskConfigManager**: 配置管理器，负责配置的读取、缓存和更新
2. **RiskConfigConverter**: 配置转换器，将数据库规则转换为统一配置
3. **ConfigCache**: 配置缓存，提供高性能的配置访问
4. **RiskRepository**: 数据库访问层，提供配置的持久化

## 最佳实践

### 1. 配置读取策略

#### 分层配置
```rust
// 全局配置（默认）
let global_config = config_manager.get_global_config().await?;

// 策略特定配置（继承全局配置并覆盖）
let strategy_config = config_manager.get_strategy_config("grid_trading").await?;

// 规则特定配置
let rules = config_manager.get_rules_by_type("max_order_size").await?;
```

#### 配置优先级
1. **策略特定配置** > **全局配置** > **默认配置**
2. **启用的规则** > **禁用的规则**
3. **高优先级规则** > **低优先级规则**

### 2. 缓存策略

#### 多层缓存设计
```rust
let cache_config = CacheConfig {
    config_ttl: Duration::from_secs(300),      // 配置缓存5分钟
    rules_ttl: Duration::from_secs(60),        // 规则缓存1分钟
    refresh_interval: Duration::from_secs(30), // 30秒自动刷新
    auto_refresh: true,                        // 启用自动刷新
};
```

#### 缓存失效策略
- **时间失效**: 基于 TTL 的自动失效
- **主动失效**: 配置变更时主动清除缓存
- **版本控制**: 基于配置版本的缓存验证

### 3. 配置转换

#### 规则到配置的映射
```rust
// 数据库规则类型 -> 配置参数
"max_order_size"    -> BasicRiskParams.max_order_amount
"position_limit"    -> PositionRiskParams.max_position_per_symbol
"daily_loss_limit"  -> BasicRiskParams.max_daily_loss_percent
"leverage_limit"    -> PositionRiskParams.max_leverage
"volatility_control"-> MarketRiskParams.volatility_threshold
```

#### 配置验证
```rust
// 转换后立即验证
let config = RiskConfigConverter::rules_to_config(&rules)?;
config.validate_complete()?; // 确保配置有效
```

### 4. 热更新机制

#### 配置变更检测
1. **数据库触发器**: 监听 risk_rules 表变更
2. **定期轮询**: 定时检查配置版本
3. **外部通知**: 通过消息队列接收变更通知

#### 更新流程
```rust
// 1. 检测到配置变更
// 2. 清除相关缓存
config_manager.refresh_cache().await?;

// 3. 重新加载配置
let new_config = config_manager.get_global_config().await?;

// 4. 通知风控模块更新
risk_controller.update_config(new_config).await?;
```

## 数据库设计

### risk_rules 表结构
```sql
CREATE TABLE risk_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    description TEXT,
    parameters JSONB NOT NULL DEFAULT '{}'::jsonb,
    enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    created_by VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

### 常见规则类型和参数

#### 订单大小限制
```json
{
  "rule_type": "max_order_size",
  "parameters": {
    "max_amount": 10000.0,
    "currency": "USDT"
  }
}
```

#### 持仓限制
```json
{
  "rule_type": "position_limit",
  "parameters": {
    "max_position_percent": 30.0,
    "concentration_limit": 50.0
  }
}
```

#### 日损失限制
```json
{
  "rule_type": "daily_loss_limit",
  "parameters": {
    "max_daily_loss_percent": 5.0,
    "max_drawdown_percent": 10.0
  }
}
```

## 使用示例

### 基本使用
```rust
use sigmax_risk::control::services::{RiskConfigManager, CacheConfig};

// 1. 创建配置管理器
let config_manager = RiskConfigManager::new(
    risk_repository,
    Some(CacheConfig::default())
);

// 2. 启动自动刷新
config_manager.start_auto_refresh().await?;

// 3. 读取配置
let config = config_manager.get_global_config().await?;

// 4. 在风控模块中使用
let risk_controller = RiskController::new(config);
```

### 高级使用
```rust
// 策略特定配置
let grid_config = config_manager.get_strategy_config("grid_trading").await?;

// 规则组合
let order_rules = config_manager.get_rules_by_type("max_order_size").await?;
let position_rules = config_manager.get_rules_by_type("position_limit").await?;

// 配置合并
let combined_rules = [order_rules, position_rules].concat();
let unified_config = RiskConfigConverter::rules_to_config(&combined_rules)?;
```

## 性能优化

### 1. 缓存优化
- **预加载**: 启动时预加载常用配置
- **批量读取**: 一次性读取多个相关配置
- **异步刷新**: 后台异步刷新缓存，避免阻塞

### 2. 数据库优化
- **索引优化**: 在 rule_type, enabled, priority 字段上建立索引
- **连接池**: 使用连接池减少数据库连接开销
- **读写分离**: 配置读取使用只读副本

### 3. 内存优化
- **配置共享**: 多个风控实例共享相同配置
- **增量更新**: 只更新变更的配置项
- **压缩存储**: 对大型配置进行压缩存储

## 监控和告警

### 关键指标
- **配置读取延迟**: 监控配置读取的响应时间
- **缓存命中率**: 监控缓存的有效性
- **配置变更频率**: 监控配置变更的频率
- **错误率**: 监控配置读取和转换的错误率

### 告警规则
- 配置读取延迟 > 100ms
- 缓存命中率 < 90%
- 配置转换失败率 > 1%
- 数据库连接失败

## 故障处理

### 常见问题
1. **数据库连接失败**: 使用缓存的配置继续运行
2. **配置格式错误**: 回退到默认配置
3. **缓存失效**: 降级到数据库直接读取
4. **配置冲突**: 按优先级解决冲突

### 恢复策略
```rust
// 故障恢复示例
match config_manager.get_global_config().await {
    Ok(config) => config,
    Err(_) => {
        tracing::warn!("Failed to load config, using default");
        RiskManagementConfig::default()
    }
}
```

## 安全考虑

### 配置安全
- **访问控制**: 限制配置修改权限
- **审计日志**: 记录所有配置变更
- **配置验证**: 严格验证配置参数
- **回滚机制**: 支持配置快速回滚

### 数据安全
- **敏感信息**: 避免在配置中存储敏感信息
- **加密传输**: 配置传输过程加密
- **备份恢复**: 定期备份配置数据

## 总结

通过实施这些最佳实践，可以构建一个高性能、高可用、易维护的风控配置管理系统。关键要点：

1. **分层设计**: 全局配置 + 策略特定配置
2. **智能缓存**: 多层缓存 + 自动刷新
3. **灵活转换**: 数据库规则 → 统一配置
4. **热更新**: 配置变更实时生效
5. **故障恢复**: 优雅降级和错误处理
