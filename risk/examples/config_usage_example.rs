//! 风控配置使用示例
//!
//! 展示如何在风控模块中使用配置管理服务

use sigmax_risk::control::services::{RiskConfigManager, RiskConfigConverter, CacheConfig};
use sigmax_database::repositories::sqlx::SqlRiskRepository;
use sigmax_database::Database;
use sigmax_core::{RiskManagementConfig, SigmaXResult};
use std::sync::Arc;
use tokio;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::init();

    println!("🚀 风控配置管理示例");
    
    // 1. 初始化数据库连接
    let database = Database::new("postgresql://user:password@localhost/sigmax").await?;
    let risk_repository = Arc::new(SqlRiskRepository::new(database));

    // 2. 创建配置管理器
    let cache_config = CacheConfig {
        config_ttl: std::time::Duration::from_secs(300),  // 5分钟缓存
        rules_ttl: std::time::Duration::from_secs(60),    // 1分钟规则缓存
        refresh_interval: std::time::Duration::from_secs(30), // 30秒刷新
        auto_refresh: true,
    };
    
    let config_manager = RiskConfigManager::new(risk_repository.clone(), Some(cache_config));

    // 3. 启动自动刷新
    config_manager.start_auto_refresh().await?;

    // 4. 演示配置读取
    demo_config_reading(&config_manager).await?;

    // 5. 演示规则转换
    demo_rule_conversion(&config_manager).await?;

    // 6. 演示缓存性能
    demo_cache_performance(&config_manager).await?;

    Ok(())
}

/// 演示配置读取
async fn demo_config_reading(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n📖 === 配置读取演示 ===");

    // 读取全局配置
    println!("🌍 读取全局风控配置...");
    let global_config = config_manager.get_global_config().await?;
    println!("✅ 全局配置: {}", global_config.name);
    println!("   - 最大订单金额: {}", global_config.risk_parameters.basic.max_order_amount);
    println!("   - 最大日损失: {}%", global_config.risk_parameters.basic.max_daily_loss_percent * rust_decimal::Decimal::from(100));

    // 读取策略特定配置
    println!("\n🎯 读取策略特定配置...");
    let strategy_config = config_manager.get_strategy_config("grid_trading").await?;
    println!("✅ 策略配置: {}", strategy_config.name);

    // 读取启用的规则
    println!("\n📋 读取启用的风控规则...");
    let enabled_rules = config_manager.get_enabled_rules().await?;
    println!("✅ 找到 {} 条启用的规则:", enabled_rules.len());
    for rule in &enabled_rules {
        println!("   - {}: {} (优先级: {})", rule.rule_type, rule.name, rule.priority);
    }

    // 按类型读取规则
    println!("\n🔍 按类型读取规则...");
    let order_size_rules = config_manager.get_rules_by_type("max_order_size").await?;
    println!("✅ 订单大小规则: {} 条", order_size_rules.len());

    Ok(())
}

/// 演示规则转换
async fn demo_rule_conversion(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n🔄 === 规则转换演示 ===");

    // 获取所有启用的规则
    let rules = config_manager.get_enabled_rules().await?;
    
    // 转换为统一配置
    println!("🔧 将 {} 条规则转换为统一配置...", rules.len());
    let converted_config = RiskConfigConverter::rules_to_config(&rules)?;
    
    println!("✅ 转换完成:");
    println!("   - 配置名称: {}", converted_config.name);
    println!("   - 最大订单金额: {}", converted_config.risk_parameters.basic.max_order_amount);
    println!("   - 最大持仓比例: {}%", converted_config.risk_parameters.position.max_position_per_symbol * rust_decimal::Decimal::from(100));
    println!("   - 最大杠杆: {}x", converted_config.risk_parameters.position.max_leverage);

    // 演示单个规则转换
    if let Some(rule) = rules.first() {
        println!("\n🎯 单个规则转换示例:");
        let rule_config = RiskConfigConverter::create_rule_config(rule)?;
        println!("✅ 规则 '{}' 转换为: {:?}", rule.name, rule_config);
    }

    Ok(())
}

/// 演示缓存性能
async fn demo_cache_performance(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n⚡ === 缓存性能演示 ===");

    // 第一次读取（从数据库）
    let start = std::time::Instant::now();
    let _config1 = config_manager.get_global_config().await?;
    let first_read_time = start.elapsed();
    println!("🐌 首次读取耗时: {:?} (从数据库)", first_read_time);

    // 第二次读取（从缓存）
    let start = std::time::Instant::now();
    let _config2 = config_manager.get_global_config().await?;
    let cached_read_time = start.elapsed();
    println!("🚀 缓存读取耗时: {:?} (从缓存)", cached_read_time);

    // 计算性能提升
    if cached_read_time.as_nanos() > 0 {
        let speedup = first_read_time.as_nanos() as f64 / cached_read_time.as_nanos() as f64;
        println!("📈 性能提升: {:.1}x", speedup);
    }

    // 强制刷新缓存
    println!("\n🔄 强制刷新缓存...");
    let start = std::time::Instant::now();
    config_manager.refresh_cache().await?;
    let refresh_time = start.elapsed();
    println!("✅ 缓存刷新完成，耗时: {:?}", refresh_time);

    Ok(())
}

/// 演示配置热更新场景
async fn demo_hot_reload_scenario(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n🔥 === 配置热更新演示 ===");

    // 模拟配置变更检测
    println!("🔍 检测配置变更...");
    
    // 在实际应用中，这里可能是：
    // 1. 监听数据库变更通知
    // 2. 定期检查配置版本
    // 3. 接收外部配置更新信号
    
    // 模拟配置更新
    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
    
    println!("📡 检测到配置变更，刷新缓存...");
    config_manager.refresh_cache().await?;
    
    println!("✅ 配置热更新完成");
    
    Ok(())
}

/// 演示错误处理
async fn demo_error_handling(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n❌ === 错误处理演示 ===");

    // 尝试读取不存在的策略配置
    match config_manager.get_strategy_config("non_existent_strategy").await {
        Ok(config) => {
            println!("✅ 获取到配置: {} (使用默认配置)", config.name);
        }
        Err(e) => {
            println!("❌ 配置读取失败: {}", e);
        }
    }

    // 尝试读取不存在的规则类型
    match config_manager.get_rules_by_type("non_existent_rule_type").await {
        Ok(rules) => {
            println!("✅ 获取到 {} 条规则", rules.len());
        }
        Err(e) => {
            println!("❌ 规则读取失败: {}", e);
        }
    }

    Ok(())
}

/// 演示配置验证
fn demo_config_validation() -> SigmaXResult<()> {
    println!("\n✅ === 配置验证演示 ===");

    // 创建一个测试配置
    let mut config = RiskManagementConfig::default();
    config.name = "测试配置".to_string();

    // 验证配置
    match config.validate_complete() {
        Ok(()) => {
            println!("✅ 配置验证通过");
        }
        Err(e) => {
            println!("❌ 配置验证失败: {}", e);
        }
    }

    // 测试无效配置
    config.risk_parameters.basic.max_order_amount = rust_decimal::Decimal::from(-100); // 无效值
    
    match config.validate_complete() {
        Ok(()) => {
            println!("⚠️  无效配置通过了验证（这不应该发生）");
        }
        Err(e) => {
            println!("✅ 正确捕获了无效配置: {}", e);
        }
    }

    Ok(())
}

/// 演示配置合并
async fn demo_config_merging(config_manager: &RiskConfigManager) -> SigmaXResult<()> {
    println!("\n🔀 === 配置合并演示 ===");

    // 获取全局配置
    let global_config = config_manager.get_global_config().await?;
    println!("🌍 全局配置最大订单金额: {}", global_config.risk_parameters.basic.max_order_amount);

    // 获取策略特定配置
    let strategy_config = config_manager.get_strategy_config("high_frequency").await?;
    println!("🎯 策略配置最大订单金额: {}", strategy_config.risk_parameters.basic.max_order_amount);

    // 在实际应用中，可以实现配置继承和覆盖逻辑
    println!("✅ 配置合并逻辑可以根据业务需求实现");

    Ok(())
}
