//! 完整的风控系统集成示例
//! 
//! 展示如何使用新的统一风控接口、异步服务、数据缓存和实时监控

use std::sync::Arc;
use tokio::time::{sleep, Duration};
use uuid::Uuid;
use chrono::Utc;
use rust_decimal::Decimal;

use sigmax_core::{Order, Balance, OrderSide, OrderType, TradingPair, SigmaXResult};
use sigmax_risk::{
    // 统一风控接口
    UnifiedRiskInterface, UnifiedRiskAdapter, RiskContext, RuleFilter,
    
    // 数据缓存系统
    RiskDataCache, CacheManager,
    
    // 异步风险检查服务
    AsyncRiskService,
    
    // 实时风险监控系统
    RiskMonitorManager, RiskThreshold, RiskMonitorEvent, RiskMonitorConfig,
    
    // 原有组件
    UnifiedRiskEngine, UnifiedRiskRule,
    
    // 测试用模拟仓库
    #[cfg(test)]
    MockUnifiedRiskRepository,
};

/// 完整的风控系统示例
pub struct CompleteRiskSystemExample {
    /// 统一风控接口
    risk_engine: Arc<dyn UnifiedRiskInterface>,
    
    /// 异步风险检查服务
    async_service: AsyncRiskService,
    
    /// 实时风险监控管理器
    monitor_manager: RiskMonitorManager,
    
    /// 缓存管理器
    cache_manager: CacheManager,
}

impl CompleteRiskSystemExample {
    /// 创建新的风控系统示例
    pub async fn new() -> SigmaXResult<Self> {
        // 1. 创建模拟数据仓库（实际使用中应该是真实的数据库连接）
        #[cfg(test)]
        let repository = Arc::new(MockUnifiedRiskRepository::new());
        
        #[cfg(not(test))]
        let repository = {
            // 在实际环境中，这里应该创建真实的数据库连接
            // let pool = sqlx::PgPool::connect(&database_url).await?;
            // Arc::new(SqlUnifiedRiskRepository::new(pool))
            panic!("需要配置真实的数据库连接");
        };
        
        // 2. 创建统一风控引擎
        let unified_engine = Arc::new(UnifiedRiskEngine::new(repository).await?);
        
        // 3. 创建统一风控接口适配器
        let risk_engine: Arc<dyn UnifiedRiskInterface> = Arc::new(UnifiedRiskAdapter::new(unified_engine));
        
        // 4. 创建数据缓存系统
        let cache = Arc::new(RiskDataCache::new());
        let cache_manager = CacheManager::new(cache);
        
        // 启动缓存清理任务（每5分钟清理一次过期缓存）
        cache_manager.start_cleanup_task(300);
        
        // 5. 创建异步风险检查服务
        let async_service = AsyncRiskService::new_with_options(
            risk_engine.clone(),
            100, // 队列大小
            Some(cache_manager.clone()),
        );
        
        // 6. 创建实时风险监控系统
        let monitor_config = RiskMonitorConfig {
            monitor_interval_seconds: 30,
            default_thresholds: vec![
                RiskThreshold {
                    id: Uuid::new_v4(),
                    name: "最大回撤预警".to_string(),
                    metric_type: "max_drawdown".to_string(),
                    warning_threshold: 0.10, // 10%
                    critical_threshold: 0.20, // 20%
                    enabled: true,
                    strategy_types: vec!["grid_trading".to_string(), "arbitrage".to_string()],
                    check_interval_seconds: 60,
                },
                RiskThreshold {
                    id: Uuid::new_v4(),
                    name: "波动率预警".to_string(),
                    metric_type: "volatility".to_string(),
                    warning_threshold: 30.0, // 30%
                    critical_threshold: 50.0, // 50%
                    enabled: true,
                    strategy_types: vec!["grid_trading".to_string()],
                    check_interval_seconds: 300,
                },
            ],
            enable_auto_response: true,
            event_buffer_size: 1000,
        };
        
        let monitor_manager = RiskMonitorManager::new(
            risk_engine.clone(),
            Some(cache_manager.clone()),
            Some(monitor_config),
        );
        
        // 初始化监控管理器
        monitor_manager.initialize().await?;
        
        Ok(Self {
            risk_engine,
            async_service,
            monitor_manager,
            cache_manager,
        })
    }
    
    /// 演示同步风险检查
    pub async fn demo_sync_risk_check(&self) -> SigmaXResult<()> {
        println!("=== 演示同步风险检查 ===");
        
        // 创建测试订单
        let order = Order {
            id: Uuid::new_v4(),
            strategy_id: Some(Uuid::new_v4()),
            trading_pair: TradingPair {
                base: "BTC".to_string(),
                quote: "USDT".to_string(),
            },
            side: OrderSide::Buy,
            order_type: OrderType::Limit,
            quantity: Decimal::new(1, 1), // 0.1 BTC
            price: Some(Decimal::new(500000, 1)), // 50000 USDT
            status: sigmax_core::OrderStatus::Pending,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        // 创建风险上下文
        let context = RiskContext::new()
            .with_order(order.clone())
            .with_strategy_type("grid_trading".to_string())
            .with_trading_pair("BTC_USDT".to_string());
        
        // 执行风险检查
        let start_time = std::time::Instant::now();
        let result = self.risk_engine.check_risk(context).await?;
        let duration = start_time.elapsed();
        
        println!("同步风险检查结果:");
        println!("  - 检查通过: {}", result.passed);
        println!("  - 执行规则数: {}", result.rules_executed);
        println!("  - 失败规则数: {}", result.failed_rules.len());
        println!("  - 跳过规则数: {}", result.skipped_rules.len());
        println!("  - 执行时间: {}ms", result.execution_time_ms);
        println!("  - 实际耗时: {:?}", duration);
        
        if !result.failed_rules.is_empty() {
            println!("  - 失败规则:");
            for failed_rule in &result.failed_rules {
                println!("    * {}: {}", failed_rule.rule_name, failed_rule.failure_reason);
            }
        }
        
        Ok(())
    }
    
    /// 演示异步风险检查
    pub async fn demo_async_risk_check(&self) -> SigmaXResult<()> {
        println!("\n=== 演示异步风险检查 ===");
        
        // 创建测试订单
        let order = Order {
            id: Uuid::new_v4(),
            strategy_id: Some(Uuid::new_v4()),
            trading_pair: TradingPair {
                base: "ETH".to_string(),
                quote: "USDT".to_string(),
            },
            side: OrderSide::Sell,
            order_type: OrderType::Market,
            quantity: Decimal::new(5, 0), // 5 ETH
            price: None,
            status: sigmax_core::OrderStatus::Pending,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        // 异步提交风险检查请求
        let start_time = std::time::Instant::now();
        let result = self.async_service.check_order_risk(
            order,
            Some("arbitrage".to_string()),
            Some(5000), // 5秒超时
        ).await?;
        let duration = start_time.elapsed();
        
        println!("异步风险检查结果:");
        println!("  - 检查通过: {}", result.passed);
        println!("  - 执行规则数: {}", result.rules_executed);
        println!("  - 失败规则数: {}", result.failed_rules.len());
        println!("  - 执行时间: {}ms", result.execution_time_ms);
        println!("  - 实际耗时: {:?}", duration);
        
        Ok(())
    }
    
    /// 演示批量异步风险检查
    pub async fn demo_batch_async_risk_check(&self) -> SigmaXResult<()> {
        println!("\n=== 演示批量异步风险检查 ===");
        
        let mut futures = Vec::new();
        
        // 创建多个测试订单
        for i in 0..5 {
            let order = Order {
                id: Uuid::new_v4(),
                strategy_id: Some(Uuid::new_v4()),
                trading_pair: TradingPair {
                    base: "BTC".to_string(),
                    quote: "USDT".to_string(),
                },
                side: if i % 2 == 0 { OrderSide::Buy } else { OrderSide::Sell },
                order_type: OrderType::Limit,
                quantity: Decimal::new((i + 1) * 10, 2), // 0.1, 0.2, 0.3, 0.4, 0.5 BTC
                price: Some(Decimal::new(500000 + i * 1000, 1)), // 50000, 50100, 50200, ...
                status: sigmax_core::OrderStatus::Pending,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };
            
            // 异步提交检查请求
            let future = self.async_service.check_order_risk_async(
                order,
                Some("grid_trading".to_string()),
                Some(1), // 高优先级
                Some(3000), // 3秒超时
            );
            
            futures.push(future);
        }
        
        // 等待所有请求完成
        let start_time = std::time::Instant::now();
        let mut results = Vec::new();
        
        for future in futures {
            let receiver = future.await?;
            let result = receiver.await.map_err(|_| sigmax_core::SigmaXError::Internal("接收结果失败".to_string()))??;
            results.push(result);
        }
        
        let duration = start_time.elapsed();
        
        println!("批量异步风险检查结果:");
        println!("  - 总订单数: {}", results.len());
        println!("  - 通过订单数: {}", results.iter().filter(|r| r.passed).count());
        println!("  - 失败订单数: {}", results.iter().filter(|r| !r.passed).count());
        println!("  - 总执行时间: {:?}", duration);
        println!("  - 平均执行时间: {:?}", duration / results.len() as u32);
        
        Ok(())
    }
    
    /// 演示风险规则管理
    pub async fn demo_rule_management(&self) -> SigmaXResult<()> {
        println!("\n=== 演示风险规则管理 ===");
        
        // 获取所有启用的规则
        let filter = RuleFilter {
            enabled: Some(true),
            ..Default::default()
        };
        
        let rules = self.risk_engine.get_rules(&filter).await?;
        println!("当前启用的风险规则数量: {}", rules.len());
        
        // 获取特定策略类型的规则
        let grid_filter = RuleFilter {
            strategy_type: Some("grid_trading".to_string()),
            enabled: Some(true),
            ..Default::default()
        };
        
        let grid_rules = self.risk_engine.get_rules(&grid_filter).await?;
        println!("网格交易策略的风险规则数量: {}", grid_rules.len());
        
        // 显示规则详情
        for rule in grid_rules.iter().take(3) {
            println!("  - 规则: {} ({})", rule.name, rule.rule_type);
            println!("    优先级: {}, 启用: {}", rule.priority, rule.enabled);
        }
        
        Ok(())
    }
    
    /// 演示实时风险监控
    pub async fn demo_real_time_monitoring(&self) -> SigmaXResult<()> {
        println!("\n=== 演示实时风险监控 ===");
        
        // 启动监控
        self.monitor_manager.start().await?;
        println!("实时风险监控已启动");
        
        // 订阅监控事件
        let mut event_receiver = self.monitor_manager.monitor().subscribe_events();
        
        // 启动事件处理任务
        let event_handler = tokio::spawn(async move {
            let mut event_count = 0;
            
            while let Ok(event) = event_receiver.recv().await {
                event_count += 1;
                
                match event {
                    RiskMonitorEvent::MetricsUpdated { strategy_id, metrics, timestamp } => {
                        println!("  [{}] 风险指标更新 - 策略: {}", 
                                timestamp.format("%H:%M:%S"), strategy_id);
                        println!("    最大回撤: {:.2}%, 波动率: {:.2}%, VaR95: {:.4}", 
                                metrics.max_drawdown, metrics.volatility, metrics.var_95);
                    }
                    RiskMonitorEvent::ThresholdTriggered { 
                        threshold_name, 
                        metric_type, 
                        current_value, 
                        threshold_value, 
                        risk_level,
                        timestamp,
                        .. 
                    } => {
                        println!("  [{}] 🚨 风险阈值触发!", timestamp.format("%H:%M:%S"));
                        println!("    阈值: {} ({})", threshold_name, metric_type);
                        println!("    当前值: {:.4}, 阈值: {:.4}, 风险等级: {:?}", 
                                current_value, threshold_value, risk_level);
                    }
                    RiskMonitorEvent::RiskAlert { alert, timestamp } => {
                        println!("  [{}] ⚠️  风险预警: {}", 
                                timestamp.format("%H:%M:%S"), alert.message);
                        println!("    严重程度: {:?}, 类型: {}", alert.severity, alert.alert_type);
                    }
                }
                
                // 限制演示时间
                if event_count >= 10 {
                    break;
                }
            }
            
            println!("事件处理任务结束，共处理 {} 个事件", event_count);
        });
        
        // 等待一段时间让监控系统运行
        println!("监控运行中，等待事件...");
        sleep(Duration::from_secs(10)).await;
        
        // 停止监控
        self.monitor_manager.stop().await?;
        println!("实时风险监控已停止");
        
        // 等待事件处理任务完成
        let _ = tokio::time::timeout(Duration::from_secs(2), event_handler).await;
        
        Ok(())
    }
    
    /// 演示风险指标获取
    pub async fn demo_risk_metrics(&self) -> SigmaXResult<()> {
        println!("\n=== 演示风险指标获取 ===");
        
        // 创建不同策略类型的上下文
        let strategies = vec!["grid_trading", "arbitrage", "market_making"];
        
        for strategy_type in strategies {
            let context = RiskContext::new()
                .with_strategy_type(strategy_type.to_string())
                .with_trading_pair("BTC_USDT".to_string());
            
            let metrics = self.risk_engine.get_risk_metrics(&context).await?;
            
            println!("策略类型: {}", strategy_type);
            println!("  - VaR 95%: {:.4}", metrics.var_95);
            println!("  - VaR 99%: {:.4}", metrics.var_99);
            println!("  - CVaR 95%: {:.4}", metrics.cvar_95);
            println!("  - CVaR 99%: {:.4}", metrics.cvar_99);
            println!("  - 波动率: {:.2}%", metrics.volatility);
            println!("  - 最大回撤: {:.2}%", metrics.max_drawdown);
            println!("  - 夏普比率: {:.2}", metrics.sharpe_ratio);
            if let Some(beta) = metrics.beta {
                println!("  - 贝塔系数: {:.2}", beta);
            }
            println!("  - 计算时间: {}", metrics.timestamp.format("%Y-%m-%d %H:%M:%S"));
            println!();
        }
        
        Ok(())
    }
    
    /// 运行完整演示
    pub async fn run_complete_demo(&self) -> SigmaXResult<()> {
        println!("🚀 SigmaX 风控系统完整演示开始");
        println!("=====================================");
        
        // 1. 同步风险检查演示
        self.demo_sync_risk_check().await?;
        
        // 2. 异步风险检查演示
        self.demo_async_risk_check().await?;
        
        // 3. 批量异步风险检查演示
        self.demo_batch_async_risk_check().await?;
        
        // 4. 风险规则管理演示
        self.demo_rule_management().await?;
        
        // 5. 风险指标获取演示
        self.demo_risk_metrics().await?;
        
        // 6. 实时风险监控演示
        self.demo_real_time_monitoring().await?;
        
        println!("\n✅ SigmaX 风控系统完整演示结束");
        println!("=====================================");
        
        Ok(())
    }
}

/// 主函数 - 运行完整演示
#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 创建风控系统示例
    let risk_system = CompleteRiskSystemExample::new().await?;
    
    // 运行完整演示
    risk_system.run_complete_demo().await?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_complete_risk_system() {
        let risk_system = CompleteRiskSystemExample::new().await.unwrap();
        
        // 测试同步风险检查
        risk_system.demo_sync_risk_check().await.unwrap();
        
        // 测试异步风险检查
        risk_system.demo_async_risk_check().await.unwrap();
        
        // 测试风险规则管理
        risk_system.demo_rule_management().await.unwrap();
        
        // 测试风险指标获取
        risk_system.demo_risk_metrics().await.unwrap();
    }
}