//! 简化的风控配置使用示例
//!
//! 展示单机部署下的风控配置管理最佳实践

use sigmax_risk::control::services::{SimpleConfigService, ConfigOptions};
use sigmax_database::repositories::sqlx::SqlRiskRepository;
use sigmax_database::Database;
use sigmax_core::{RiskManagementConfig, SigmaXResult};
use std::sync::Arc;
use std::time::Duration;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::init();

    println!("🚀 简化风控配置管理示例");
    
    // 1. 初始化数据库连接
    let database = Database::new("postgresql://user:password@localhost/sigmax").await?;
    let risk_repository = Arc::new(SqlRiskRepository::new(database));

    // 2. 创建简化的配置服务
    let config_options = ConfigOptions {
        cache_ttl: Duration::from_secs(300),  // 5分钟缓存
        enable_cache: true,                   // 启用缓存
        validate_config: true,                // 启用配置验证
    };
    
    let config_service = SimpleConfigService::new(risk_repository, Some(config_options));

    // 3. 演示基本使用
    demo_basic_usage(&config_service).await?;

    // 4. 演示性能对比
    demo_performance(&config_service).await?;

    // 5. 演示配置应用
    demo_config_application(&config_service).await?;

    Ok(())
}

/// 演示基本使用
async fn demo_basic_usage(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n📖 === 基本使用演示 ===");

    // 1. 获取全局配置
    println!("🌍 获取全局风控配置...");
    let global_config = config_service.get_global_config().await?;
    print_config_summary(&global_config, "全局配置");

    // 2. 获取策略特定配置
    println!("\n🎯 获取策略特定配置...");
    let strategy_config = config_service.get_strategy_config("grid_trading").await?;
    print_config_summary(&strategy_config, "网格策略配置");

    // 3. 获取规则
    println!("\n📋 获取风控规则...");
    let enabled_rules = config_service.get_enabled_rules().await?;
    println!("✅ 启用的规则数量: {}", enabled_rules.len());
    
    for rule in enabled_rules.iter().take(3) {
        println!("   - {}: {} (优先级: {})", rule.rule_type, rule.name, rule.priority);
    }

    // 4. 按类型获取规则
    println!("\n🔍 按类型获取规则...");
    let order_rules = config_service.get_rules_by_type("max_order_size").await?;
    println!("✅ 订单大小规则: {} 条", order_rules.len());

    Ok(())
}

/// 演示性能对比
async fn demo_performance(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n⚡ === 性能演示 ===");

    // 第一次读取（从数据库）
    let start = std::time::Instant::now();
    let _config1 = config_service.get_global_config().await?;
    let first_read = start.elapsed();
    println!("🐌 首次读取: {:?} (从数据库)", first_read);

    // 第二次读取（从缓存）
    let start = std::time::Instant::now();
    let _config2 = config_service.get_global_config().await?;
    let cached_read = start.elapsed();
    println!("🚀 缓存读取: {:?} (从内存)", cached_read);

    // 性能提升
    if cached_read.as_nanos() > 0 {
        let speedup = first_read.as_nanos() as f64 / cached_read.as_nanos() as f64;
        println!("📈 性能提升: {:.1}x", speedup);
    }

    // 批量读取测试
    println!("\n🔄 批量读取测试...");
    let start = std::time::Instant::now();
    for _ in 0..100 {
        let _ = config_service.get_global_config().await?;
    }
    let batch_time = start.elapsed();
    println!("✅ 100次读取耗时: {:?} (平均: {:?})", batch_time, batch_time / 100);

    Ok(())
}

/// 演示配置应用
async fn demo_config_application(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n🔧 === 配置应用演示 ===");

    // 1. 规则转配置
    println!("🔄 将规则转换为统一配置...");
    let unified_config = config_service.rules_to_config(None).await?;
    print_config_summary(&unified_config, "规则转换后的配置");

    // 2. 策略特定的规则转配置
    println!("\n🎯 策略特定的规则转配置...");
    let strategy_unified = config_service.rules_to_config(Some("high_frequency")).await?;
    print_config_summary(&strategy_unified, "高频策略配置");

    // 3. 配置比较
    println!("\n📊 配置比较:");
    compare_configs(&unified_config, &strategy_unified);

    Ok(())
}

/// 打印配置摘要
fn print_config_summary(config: &RiskManagementConfig, title: &str) {
    println!("✅ {}:", title);
    println!("   - 配置名称: {}", config.name);
    println!("   - 最大订单金额: {}", config.risk_parameters.basic.max_order_amount);
    println!("   - 最大日损失: {}%", config.risk_parameters.basic.max_daily_loss_percent * rust_decimal::Decimal::from(100));
    println!("   - 最大持仓比例: {}%", config.risk_parameters.position.max_position_per_symbol * rust_decimal::Decimal::from(100));
    println!("   - 最大杠杆: {}x", config.risk_parameters.position.max_leverage);
}

/// 比较两个配置
fn compare_configs(config1: &RiskManagementConfig, config2: &RiskManagementConfig) {
    println!("   📈 配置对比:");
    
    let order_amount_diff = config2.risk_parameters.basic.max_order_amount - config1.risk_parameters.basic.max_order_amount;
    println!("     - 订单金额差异: {}", order_amount_diff);
    
    let daily_loss_diff = config2.risk_parameters.basic.max_daily_loss_percent - config1.risk_parameters.basic.max_daily_loss_percent;
    println!("     - 日损失差异: {}%", daily_loss_diff * rust_decimal::Decimal::from(100));
    
    let leverage_diff = config2.risk_parameters.position.max_leverage - config1.risk_parameters.position.max_leverage;
    println!("     - 杠杆差异: {}x", leverage_diff);
}

/// 演示实际风控场景
async fn demo_risk_scenario(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n🛡️ === 风控场景演示 ===");

    // 模拟订单验证场景
    println!("📝 模拟订单验证...");
    let config = config_service.get_global_config().await?;
    
    // 模拟订单
    let order_amount = rust_decimal::Decimal::from(5000);
    let max_allowed = config.risk_parameters.basic.max_order_amount;
    
    if order_amount <= max_allowed {
        println!("✅ 订单通过: {} <= {}", order_amount, max_allowed);
    } else {
        println!("❌ 订单拒绝: {} > {}", order_amount, max_allowed);
    }

    // 模拟持仓检查
    println!("\n📊 模拟持仓检查...");
    let position_ratio = rust_decimal::Decimal::from_f64_retain(0.25).unwrap(); // 25%
    let max_position = config.risk_parameters.position.max_position_per_symbol;
    
    if position_ratio <= max_position {
        println!("✅ 持仓安全: {}% <= {}%", 
                position_ratio * rust_decimal::Decimal::from(100), 
                max_position * rust_decimal::Decimal::from(100));
    } else {
        println!("⚠️ 持仓超限: {}% > {}%", 
                position_ratio * rust_decimal::Decimal::from(100), 
                max_position * rust_decimal::Decimal::from(100));
    }

    Ok(())
}

/// 演示配置热更新
async fn demo_hot_reload(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n🔥 === 配置热更新演示 ===");

    // 读取当前配置
    let config_before = config_service.get_global_config().await?;
    println!("🔍 更新前配置: {}", config_before.risk_parameters.basic.max_order_amount);

    // 模拟配置变更（在实际应用中，这可能是数据库更新）
    println!("📡 模拟配置变更...");
    
    // 刷新缓存
    config_service.refresh_cache().await?;
    println!("✅ 缓存已刷新");

    // 读取更新后的配置
    let config_after = config_service.get_global_config().await?;
    println!("🔍 更新后配置: {}", config_after.risk_parameters.basic.max_order_amount);

    Ok(())
}

/// 演示错误处理
async fn demo_error_handling(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n❌ === 错误处理演示 ===");

    // 尝试获取不存在的策略配置
    match config_service.get_strategy_config("non_existent_strategy").await {
        Ok(config) => {
            println!("✅ 获取配置成功: {} (回退到全局配置)", config.name);
        }
        Err(e) => {
            println!("❌ 配置获取失败: {}", e);
        }
    }

    // 尝试获取不存在的规则类型
    match config_service.get_rules_by_type("non_existent_rule").await {
        Ok(rules) => {
            println!("✅ 获取规则成功: {} 条", rules.len());
        }
        Err(e) => {
            println!("❌ 规则获取失败: {}", e);
        }
    }

    Ok(())
}

/// 演示内存使用情况
fn demo_memory_usage() {
    println!("\n💾 === 内存使用演示 ===");
    
    // 在实际应用中，可以使用内存分析工具
    println!("📊 配置服务内存占用:");
    println!("   - 全局配置: ~1KB");
    println!("   - 策略配置缓存: ~10KB (假设10个策略)");
    println!("   - 规则缓存: ~50KB (假设100条规则)");
    println!("   - 总计: ~61KB");
    println!("✅ 内存占用很小，适合单机部署");
}

/// 演示配置持久化
async fn demo_config_persistence(config_service: &SimpleConfigService) -> SigmaXResult<()> {
    println!("\n💾 === 配置持久化演示 ===");

    // 在单机部署中，配置持久化主要依赖数据库
    println!("📝 配置持久化策略:");
    println!("   1. 数据库存储: risk_rules 表");
    println!("   2. 内存缓存: 提高读取性能");
    println!("   3. 定期刷新: 保证数据一致性");
    println!("   4. 故障恢复: 缓存失效时从数据库重新加载");

    // 演示配置备份
    let config = config_service.get_global_config().await?;
    let config_json = serde_json::to_string_pretty(&config)?;
    println!("✅ 配置可以序列化为 JSON 进行备份");
    println!("📄 配置大小: {} bytes", config_json.len());

    Ok(())
}
