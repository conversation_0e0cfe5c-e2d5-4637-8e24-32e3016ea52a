//! 分析报告
//!
//! 实现风险分析报告生成器

use sigmax_interfaces::risk::{Portfolio, RiskContext, RiskAlert};
use crate::analytics::{
    MetricsCalculationResult, StressTestResult, MonteCarloResult,
    CorrelationResult, VolatilityResult
};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use uuid::Uuid;

// ============================================================================
// 风险报告生成器
// ============================================================================

/// 报告生成器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskReporterConfig {
    /// 默认报告格式
    pub default_format: ReportFormat,
    /// 是否包含详细分析
    pub include_detailed_analysis: bool,
    /// 是否包含图表数据
    pub include_charts: bool,
    /// 报告语言
    pub language: ReportLanguage,
    /// 自定义模板路径
    pub custom_template_path: Option<String>,
}

impl Default for RiskReporterConfig {
    fn default() -> Self {
        Self {
            default_format: ReportFormat::Json,
            include_detailed_analysis: true,
            include_charts: true,
            language: ReportLanguage::English,
            custom_template_path: None,
        }
    }
}

/// 风险报告生成器
pub struct RiskReporter {
    config: RiskReporterConfig,
}

impl RiskReporter {
    /// 创建新的报告生成器
    pub fn new(config: RiskReporterConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建报告生成器
    pub fn with_default_config() -> Self {
        Self::new(RiskReporterConfig::default())
    }

    /// 生成综合风险报告
    pub async fn generate_comprehensive_report(
        &self,
        portfolio: &Portfolio,
        context: &RiskContext,
        metrics_result: Option<&MetricsCalculationResult>,
        stress_test_result: Option<&StressTestResult>,
        monte_carlo_result: Option<&MonteCarloResult>,
        correlation_result: Option<&CorrelationResult>,
        volatility_result: Option<&VolatilityResult>,
        alerts: &[RiskAlert],
    ) -> RiskReport {
        let mut report = RiskReport {
            id: Uuid::new_v4(),
            generated_at: Utc::now(),
            report_type: ReportType::Comprehensive,
            portfolio_summary: self.generate_portfolio_summary(portfolio),
            risk_summary: self.generate_risk_summary(context, metrics_result),
            detailed_analysis: if self.config.include_detailed_analysis {
                Some(self.generate_detailed_analysis(
                    portfolio,
                    metrics_result,
                    stress_test_result,
                    monte_carlo_result,
                    correlation_result,
                    volatility_result,
                ))
            } else {
                None
            },
            alerts_summary: self.generate_alerts_summary(alerts),
            recommendations: self.generate_recommendations(portfolio, context, alerts),
            charts_data: if self.config.include_charts {
                Some(self.generate_charts_data(portfolio, metrics_result, volatility_result))
            } else {
                None
            },
            metadata: self.generate_metadata(),
        };

        // 设置风险等级
        report.risk_level = self.determine_overall_risk_level(&report);

        report
    }

    /// 生成快速风险报告
    pub async fn generate_quick_report(
        &self,
        portfolio: &Portfolio,
        context: &RiskContext,
        alerts: &[RiskAlert],
    ) -> RiskReport {
        RiskReport {
            id: Uuid::new_v4(),
            generated_at: Utc::now(),
            report_type: ReportType::Quick,
            portfolio_summary: self.generate_portfolio_summary(portfolio),
            risk_summary: self.generate_risk_summary(context, None),
            detailed_analysis: None,
            alerts_summary: self.generate_alerts_summary(alerts),
            recommendations: self.generate_basic_recommendations(portfolio, alerts),
            charts_data: None,
            risk_level: self.determine_basic_risk_level(portfolio, alerts),
            metadata: self.generate_metadata(),
        }
    }

    /// 生成投资组合摘要
    fn generate_portfolio_summary(&self, portfolio: &Portfolio) -> PortfolioSummary {
        let total_assets = portfolio.balances.len();
        let total_value = portfolio.total_value;

        let largest_position = portfolio.balances
            .iter()
            .max_by(|a, b| {
                let a_value = a.total * a.price.unwrap_or(Decimal::ONE);
                let b_value = b.total * b.price.unwrap_or(Decimal::ONE);
                a_value.cmp(&b_value)
            });

        let largest_position_percentage = if let Some(position) = largest_position {
            if total_value > Decimal::ZERO {
                let position_value = position.total * position.price.unwrap_or(Decimal::ONE);
                (position_value / total_value) * Decimal::from(100)
            } else {
                Decimal::ZERO
            }
        } else {
            Decimal::ZERO
        };

        // 计算资产分布
        let mut asset_distribution = HashMap::new();
        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let percentage = if total_value > Decimal::ZERO {
                (asset_value / total_value) * Decimal::from(100)
            } else {
                Decimal::ZERO
            };
            asset_distribution.insert(balance.asset.clone(), percentage);
        }

        PortfolioSummary {
            total_assets,
            total_value,
            largest_position_asset: largest_position.map(|p| p.asset.clone()),
            largest_position_percentage,
            asset_distribution,
            last_updated: portfolio.updated_at,
        }
    }

    /// 生成风险摘要
    fn generate_risk_summary(&self, context: &RiskContext, metrics_result: Option<&MetricsCalculationResult>) -> RiskSummary {
        let mut risk_factors = Vec::new();
        let mut overall_score = Decimal::from_str_exact("0.5").unwrap(); // 默认中等风险

        // 从指标结果中提取风险信息
        if let Some(metrics) = metrics_result {
            if let Some(volatility) = metrics.metrics.get("volatility") {
                if let Ok(vol) = serde_json::from_value::<Decimal>(volatility.clone()) {
                    if vol > Decimal::from_str_exact("0.3").unwrap() {
                        risk_factors.push("High volatility detected".to_string());
                        overall_score += Decimal::from_str_exact("0.2").unwrap();
                    }
                }
            }

            if let Some(concentration) = metrics.metrics.get("concentration") {
                if let Ok(conc) = serde_json::from_value::<Decimal>(concentration.clone()) {
                    if conc > Decimal::from_str_exact("0.5").unwrap() {
                        risk_factors.push("High concentration risk".to_string());
                        overall_score += Decimal::from_str_exact("0.15").unwrap();
                    }
                }
            }

            if let Some(leverage) = metrics.metrics.get("leverage") {
                if let Ok(lev) = serde_json::from_value::<Decimal>(leverage.clone()) {
                    if lev > Decimal::from(3) {
                        risk_factors.push("High leverage detected".to_string());
                        overall_score += Decimal::from_str_exact("0.25").unwrap();
                    }
                }
            }
        }

        // 检查市场风险
        if let Some(market_data) = &context.market_data {
            if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
                let price_range = (high - low) / market_data.price;
                if price_range > Decimal::from_str_exact("0.1").unwrap() {
                    risk_factors.push("High market volatility".to_string());
                    overall_score += Decimal::from_str_exact("0.1").unwrap();
                }
            }
        }

        overall_score = overall_score.min(Decimal::ONE);

        RiskSummary {
            overall_risk_score: overall_score,
            risk_level: self.score_to_risk_level(overall_score),
            key_risk_factors: risk_factors,
            risk_trend: RiskTrend::Stable, // 简化实现
            last_assessment: Utc::now(),
        }
    }

    /// 生成预警摘要
    fn generate_alerts_summary(&self, alerts: &[RiskAlert]) -> AlertsSummary {
        let total_alerts = alerts.len();
        let critical_alerts = alerts.iter().filter(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::Critical)).count();
        let high_alerts = alerts.iter().filter(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::High)).count();
        let medium_alerts = alerts.iter().filter(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::Medium)).count();

        let recent_alerts: Vec<String> = alerts
            .iter()
            .take(5) // 最近5个预警
            .map(|a| format!("[{}] {}", a.severity, a.message))
            .collect();

        AlertsSummary {
            total_alerts,
            critical_alerts,
            high_alerts,
            medium_alerts,
            recent_alerts,
        }
    }

    /// 生成基础建议
    fn generate_basic_recommendations(&self, portfolio: &Portfolio, alerts: &[RiskAlert]) -> Vec<Recommendation> {
        let mut recommendations = Vec::new();

        // 基于预警生成建议
        if alerts.iter().any(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::Critical)) {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::Critical,
                category: RecommendationCategory::RiskReduction,
                title: "Immediate Risk Reduction Required".to_string(),
                description: "Critical risk alerts detected. Consider reducing position sizes immediately.".to_string(),
                action_items: vec![
                    "Review and reduce high-risk positions".to_string(),
                    "Implement stop-loss orders".to_string(),
                    "Consider hedging strategies".to_string(),
                ],
            });
        }

        // 基于投资组合分析生成建议
        if portfolio.balances.len() < 3 {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::Medium,
                category: RecommendationCategory::Diversification,
                title: "Improve Portfolio Diversification".to_string(),
                description: "Portfolio has limited diversification. Consider adding more assets.".to_string(),
                action_items: vec![
                    "Add assets from different sectors".to_string(),
                    "Consider low-correlation assets".to_string(),
                    "Review asset allocation strategy".to_string(),
                ],
            });
        }

        recommendations
    }

    /// 生成完整建议
    fn generate_recommendations(&self, portfolio: &Portfolio, context: &RiskContext, alerts: &[RiskAlert]) -> Vec<Recommendation> {
        let mut recommendations = self.generate_basic_recommendations(portfolio, alerts);

        // 添加更多详细建议
        // 这里可以根据具体的分析结果添加更多建议

        recommendations
    }

    /// 生成图表数据
    fn generate_charts_data(&self, portfolio: &Portfolio, metrics_result: Option<&MetricsCalculationResult>, volatility_result: Option<&VolatilityResult>) -> ChartsData {
        let mut charts = HashMap::new();

        // 资产分布饼图
        let mut asset_allocation = HashMap::new();
        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let percentage = if portfolio.total_value > Decimal::ZERO {
                (asset_value / portfolio.total_value) * Decimal::from(100)
            } else {
                Decimal::ZERO
            };
            asset_allocation.insert(balance.asset.clone(), percentage);
        }
        charts.insert("asset_allocation".to_string(), serde_json::json!(asset_allocation));

        // 风险指标雷达图
        if let Some(metrics) = metrics_result {
            charts.insert("risk_metrics".to_string(), serde_json::json!(metrics.metrics));
        }

        // 波动率趋势图
        if let Some(volatility) = volatility_result {
            charts.insert("volatility_chart".to_string(), serde_json::json!(volatility.asset_volatilities));
        }

        ChartsData { charts }
    }

    /// 生成元数据
    fn generate_metadata(&self) -> ReportMetadata {
        ReportMetadata {
            generator_version: "1.0.0".to_string(),
            language: self.config.language.clone(),
            format: self.config.default_format.clone(),
            generation_time_ms: 0, // 实际实现中应该测量生成时间
        }
    }

    /// 确定整体风险等级
    fn determine_overall_risk_level(&self, report: &RiskReport) -> RiskLevel {
        self.score_to_risk_level(report.risk_summary.overall_risk_score)
    }

    /// 确定基础风险等级
    fn determine_basic_risk_level(&self, portfolio: &Portfolio, alerts: &[RiskAlert]) -> RiskLevel {
        if alerts.iter().any(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::Critical)) {
            RiskLevel::Critical
        } else if alerts.iter().any(|a| matches!(a.severity, sigmax_interfaces::risk::AlertSeverity::High)) {
            RiskLevel::High
        } else if portfolio.balances.len() < 3 {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        }
    }

    /// 评分转风险等级
    fn score_to_risk_level(&self, score: Decimal) -> RiskLevel {
        if score >= Decimal::from_str_exact("0.8").unwrap() {
            RiskLevel::Critical
        } else if score >= Decimal::from_str_exact("0.6").unwrap() {
            RiskLevel::High
        } else if score >= Decimal::from_str_exact("0.4").unwrap() {
            RiskLevel::Medium
        } else if score >= Decimal::from_str_exact("0.2").unwrap() {
            RiskLevel::Low
        } else {
            RiskLevel::Minimal
        }
    }

    /// 计算性能指标
    fn calculate_performance_indicators(&self, metrics: &MetricsCalculationResult) -> HashMap<String, Decimal> {
        let mut indicators = HashMap::new();

        // 从指标中提取性能数据
        for (key, value) in &metrics.metrics {
            if let Ok(decimal_value) = serde_json::from_value::<Decimal>(value.clone()) {
                indicators.insert(key.clone(), decimal_value);
            }
        }

        indicators
    }

    /// 估算恢复时间
    fn estimate_recovery_time(&self, loss_percentage: Decimal) -> String {
        let abs_loss = loss_percentage.abs();

        if abs_loss > Decimal::from(50) {
            "12-24 months".to_string()
        } else if abs_loss > Decimal::from(30) {
            "6-12 months".to_string()
        } else if abs_loss > Decimal::from(20) {
            "3-6 months".to_string()
        } else {
            "1-3 months".to_string()
        }
    }

    /// 生成场景分析
    fn generate_scenario_analysis(&self, portfolio: &Portfolio) -> ScenarioAnalysis {
        // 简化的场景分析
        let scenarios = vec![
            ("Bull Market".to_string(), Decimal::from_str_exact("0.3").unwrap()),
            ("Bear Market".to_string(), Decimal::from_str_exact("-0.2").unwrap()),
            ("Sideways Market".to_string(), Decimal::from_str_exact("0.05").unwrap()),
        ];

        let mut scenario_impacts = HashMap::new();
        for (scenario_name, market_change) in scenarios {
            let impact = portfolio.total_value * market_change;
            scenario_impacts.insert(scenario_name, impact);
        }

        ScenarioAnalysis {
            scenario_impacts,
            most_likely_scenario: "Sideways Market".to_string(),
            scenario_probabilities: vec![
                ("Bull Market".to_string(), Decimal::from_str_exact("0.3").unwrap()),
                ("Bear Market".to_string(), Decimal::from_str_exact("0.2").unwrap()),
                ("Sideways Market".to_string(), Decimal::from_str_exact("0.5").unwrap()),
            ],
        }
    }
}

// ============================================================================
// 数据结构定义
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskReport {
    pub id: Uuid,
    pub generated_at: DateTime<Utc>,
    pub report_type: ReportType,
    pub portfolio_summary: PortfolioSummary,
    pub risk_summary: RiskSummary,
    pub detailed_analysis: Option<DetailedAnalysis>,
    pub alerts_summary: AlertsSummary,
    pub recommendations: Vec<Recommendation>,
    pub charts_data: Option<ChartsData>,
    pub risk_level: RiskLevel,
    pub metadata: ReportMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportType {
    Quick,
    Comprehensive,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportFormat {
    Json,
    Html,
    Pdf,
    Excel,
    Csv,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportLanguage {
    English,
    Chinese,
    Japanese,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Minimal,
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskTrend {
    Decreasing,
    Stable,
    Increasing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSummary {
    pub total_assets: usize,
    pub total_value: Decimal,
    pub largest_position_asset: Option<String>,
    pub largest_position_percentage: Decimal,
    pub asset_distribution: HashMap<String, Decimal>,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskSummary {
    pub overall_risk_score: Decimal,
    pub risk_level: RiskLevel,
    pub key_risk_factors: Vec<String>,
    pub risk_trend: RiskTrend,
    pub last_assessment: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertsSummary {
    pub total_alerts: usize,
    pub critical_alerts: usize,
    pub high_alerts: usize,
    pub medium_alerts: usize,
    pub recent_alerts: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedAnalysis {
    pub metrics_analysis: Option<MetricsAnalysis>,
    pub stress_test_analysis: Option<StressTestAnalysis>,
    pub monte_carlo_analysis: Option<MonteCarloAnalysis>,
    pub correlation_analysis: Option<CorrelationAnalysis>,
    pub volatility_analysis: Option<VolatilityAnalysis>,
    pub scenario_analysis: ScenarioAnalysis,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsAnalysis {
    pub key_insights: Vec<String>,
    pub warnings: Vec<String>,
    pub performance_indicators: HashMap<String, Decimal>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTestAnalysis {
    pub worst_case_scenario: String,
    pub vulnerability_assessment: Vec<String>,
    pub scenario_breakdown: HashMap<String, serde_json::Value>,
    pub recovery_time_estimate: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloAnalysis {
    pub expected_return: Decimal,
    pub confidence_intervals: Vec<(u8, Decimal)>,
    pub probability_of_loss: Decimal,
    pub value_at_risk: Decimal,
    pub expected_shortfall: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationAnalysis {
    pub average_correlation: Decimal,
    pub high_correlation_pairs: Vec<String>,
    pub diversification_score: Decimal,
    pub correlation_risk_level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityAnalysis {
    pub portfolio_volatility: Decimal,
    pub high_volatility_assets: Vec<String>,
    pub volatility_trend: String,
    pub risk_adjusted_performance: HashMap<String, Decimal>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScenarioAnalysis {
    pub scenario_impacts: HashMap<String, Decimal>,
    pub most_likely_scenario: String,
    pub scenario_probabilities: Vec<(String, Decimal)>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    pub priority: RecommendationPriority,
    pub category: RecommendationCategory,
    pub title: String,
    pub description: String,
    pub action_items: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationCategory {
    RiskReduction,
    Diversification,
    PositionSizing,
    Hedging,
    Monitoring,
    Compliance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChartsData {
    pub charts: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportMetadata {
    pub generator_version: String,
    pub language: ReportLanguage,
    pub format: ReportFormat,
    pub generation_time_ms: u64,
}