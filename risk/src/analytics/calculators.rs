//! 分析计算器
//!
//! 实现各种风险分析和计算工具

use sigmax_interfaces::risk::{Portfolio, RiskContext, MarketData};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use rand::prelude::*;
use rand_chacha::ChaCha8Rng;

// ============================================================================
// 指标计算器
// ============================================================================

/// 指标计算器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsCalculatorConfig {
    /// VaR 置信度
    pub var_confidence_level: Decimal,
    /// 历史数据窗口大小
    pub historical_window: u32,
    /// 是否启用高级指标计算
    pub enable_advanced_metrics: bool,
}

impl Default for MetricsCalculatorConfig {
    fn default() -> Self {
        Self {
            var_confidence_level: Decimal::from_str_exact("0.95").unwrap(),
            historical_window: 252, // 一年交易日
            enable_advanced_metrics: true,
        }
    }
}

/// 指标计算器
pub struct MetricsCalculator {
    config: MetricsCalculatorConfig,
}

impl MetricsCalculator {
    /// 创建新的指标计算器
    pub fn new(config: MetricsCalculatorConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建指标计算器
    pub fn with_default_config() -> Self {
        Self::new(MetricsCalculatorConfig::default())
    }

    /// 计算基础指标
    pub async fn calculate_basic_metrics(&self, context: &RiskContext) -> MetricsCalculationResult {
        let mut metrics = HashMap::new();

        // 1. 计算波动率
        if let Some(market_data) = &context.market_data {
            let volatility = self.calculate_volatility(market_data);
            metrics.insert("volatility".to_string(), serde_json::json!(volatility));
        }

        // 2. 计算持仓集中度
        if let Some(portfolio) = &context.portfolio {
            let concentration = self.calculate_concentration(portfolio);
            metrics.insert("concentration".to_string(), serde_json::json!(concentration));

            // 3. 计算杠杆比率
            let leverage = self.calculate_leverage(portfolio);
            metrics.insert("leverage".to_string(), serde_json::json!(leverage));

            // 4. 计算投资组合价值
            metrics.insert("portfolio_value".to_string(), serde_json::json!(portfolio.total_value));
        }

        // 5. 计算流动性指标
        if let Some(market_data) = &context.market_data {
            let liquidity_score = self.calculate_liquidity_score(market_data);
            metrics.insert("liquidity_score".to_string(), serde_json::json!(liquidity_score));
        }

        MetricsCalculationResult {
            calculated_at: Utc::now(),
            metrics,
        }
    }

    /// 计算高级指标
    pub async fn calculate_advanced_metrics(&self, context: &RiskContext) -> MetricsCalculationResult {
        if !self.config.enable_advanced_metrics {
            return self.calculate_basic_metrics(context).await;
        }

        let mut metrics = HashMap::new();

        // 1. 计算 VaR
        if let Some(portfolio) = &context.portfolio {
            let var_result = self.calculate_var(portfolio, context.market_data.as_ref());
            metrics.insert("var_1d".to_string(), serde_json::json!(var_result.var_1d));
            metrics.insert("var_5d".to_string(), serde_json::json!(var_result.var_5d));
            metrics.insert("var_30d".to_string(), serde_json::json!(var_result.var_30d));

            // 2. 计算 CVaR
            let cvar = self.calculate_cvar(portfolio, context.market_data.as_ref());
            metrics.insert("cvar".to_string(), serde_json::json!(cvar));

            // 3. 计算最大回撤
            let max_drawdown = self.calculate_max_drawdown(portfolio);
            metrics.insert("max_drawdown".to_string(), serde_json::json!(max_drawdown));

            // 4. 计算夏普比率
            let sharpe_ratio = self.calculate_sharpe_ratio(portfolio);
            metrics.insert("sharpe_ratio".to_string(), serde_json::json!(sharpe_ratio));
        }

        // 5. 计算相关性矩阵
        if let Some(portfolio) = &context.portfolio {
            let correlation_matrix = self.calculate_correlation_matrix(portfolio);
            metrics.insert("correlation_matrix".to_string(), serde_json::json!(correlation_matrix));
        }

        MetricsCalculationResult {
            calculated_at: Utc::now(),
            metrics,
        }
    }

    /// 计算波动率
    fn calculate_volatility(&self, market_data: &MarketData) -> Decimal {
        // 使用24小时高低价计算简单波动率
        if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
            let price = market_data.price;
            if price > Decimal::ZERO {
                return (high - low) / price;
            }
        }

        // 默认波动率
        Decimal::from_str_exact("0.2").unwrap()
    }

    /// 计算持仓集中度
    fn calculate_concentration(&self, portfolio: &Portfolio) -> Decimal {
        if portfolio.total_value <= Decimal::ZERO || portfolio.balances.is_empty() {
            return Decimal::ZERO;
        }

        // 计算赫芬达尔指数 (Herfindahl Index)
        let mut hhi = Decimal::ZERO;

        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let weight = asset_value / portfolio.total_value;
            hhi += weight * weight;
        }

        hhi
    }

    /// 计算杠杆比率
    fn calculate_leverage(&self, portfolio: &Portfolio) -> Decimal {
        let total_borrowed = portfolio.balances
            .iter()
            .filter(|b| b.borrowed > Decimal::ZERO)
            .map(|b| b.borrowed * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        let total_equity = portfolio.balances
            .iter()
            .map(|b| b.available * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        if total_equity > Decimal::ZERO {
            (total_borrowed + total_equity) / total_equity
        } else {
            Decimal::ZERO
        }
    }

    /// 计算流动性评分
    fn calculate_liquidity_score(&self, market_data: &MarketData) -> Decimal {
        // 基于买卖价差和交易量计算流动性评分
        let spread_score = if let (Some(bid), Some(ask)) = (market_data.bid_price, market_data.ask_price) {
            let spread = (ask - bid) / ((ask + bid) / Decimal::from(2));
            // 价差越小，流动性越好
            (Decimal::ONE - spread.min(Decimal::ONE)).max(Decimal::ZERO)
        } else {
            Decimal::from_str_exact("0.5").unwrap() // 中等流动性
        };

        // 交易量评分
        let volume_score = if let Some(volume) = market_data.volume_24h {
            if volume > Decimal::from(1_000_000) {
                Decimal::ONE
            } else if volume > Decimal::from(100_000) {
                Decimal::from_str_exact("0.8").unwrap()
            } else if volume > Decimal::from(10_000) {
                Decimal::from_str_exact("0.6").unwrap()
            } else {
                Decimal::from_str_exact("0.3").unwrap()
            }
        } else {
            Decimal::from_str_exact("0.5").unwrap()
        };

        // 综合评分
        (spread_score + volume_score) / Decimal::from(2)
    }

    /// 计算 VaR (Value at Risk)
    fn calculate_var(&self, portfolio: &Portfolio, market_data: Option<&MarketData>) -> VarCalculationResult {
        let portfolio_value = portfolio.total_value;

        // 计算投资组合波动率
        let portfolio_volatility = if let Some(market_data) = market_data {
            self.calculate_volatility(market_data)
        } else {
            // 使用加权平均波动率
            self.calculate_portfolio_volatility(portfolio)
        };

        // 使用正态分布假设计算 VaR
        let confidence_factor = match self.config.var_confidence_level {
            level if level >= Decimal::from_str_exact("0.99").unwrap() => Decimal::from_str_exact("2.326").unwrap(), // 99%
            level if level >= Decimal::from_str_exact("0.95").unwrap() => Decimal::from_str_exact("1.645").unwrap(), // 95%
            _ => Decimal::from_str_exact("1.282").unwrap(), // 90%
        };

        let var_1d = portfolio_value * portfolio_volatility * confidence_factor;
        let var_5d = var_1d * Decimal::from_str_exact("2.236").unwrap(); // sqrt(5)
        let var_30d = var_1d * Decimal::from_str_exact("5.477").unwrap(); // sqrt(30)

        VarCalculationResult {
            var_1d,
            var_5d,
            var_30d,
        }
    }

    /// 计算 CVaR (Conditional Value at Risk)
    fn calculate_cvar(&self, portfolio: &Portfolio, market_data: Option<&MarketData>) -> Decimal {
        let var_result = self.calculate_var(portfolio, market_data);
        // CVaR 通常是 VaR 的 1.2-1.5 倍
        var_result.var_1d * Decimal::from_str_exact("1.3").unwrap()
    }

    /// 计算投资组合波动率
    fn calculate_portfolio_volatility(&self, portfolio: &Portfolio) -> Decimal {
        if portfolio.total_value <= Decimal::ZERO || portfolio.balances.is_empty() {
            return Decimal::from_str_exact("0.2").unwrap(); // 默认波动率
        }

        // 简化的投资组合波动率计算（假设资产间无相关性）
        let mut weighted_variance = Decimal::ZERO;

        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let weight = asset_value / portfolio.total_value;
            let asset_volatility = self.get_asset_volatility(&balance.asset);

            weighted_variance += weight * weight * asset_volatility * asset_volatility;
        }

        weighted_variance.sqrt().unwrap_or(Decimal::from_str_exact("0.2").unwrap())
    }

    /// 获取资产波动率
    fn get_asset_volatility(&self, asset: &str) -> Decimal {
        match asset.to_uppercase().as_str() {
            "BTC" => Decimal::from_str_exact("0.4").unwrap(),
            "ETH" => Decimal::from_str_exact("0.5").unwrap(),
            "USDT" | "USDC" | "DAI" => Decimal::from_str_exact("0.01").unwrap(),
            _ => Decimal::from_str_exact("0.6").unwrap(),
        }
    }

    /// 计算最大回撤
    fn calculate_max_drawdown(&self, portfolio: &Portfolio) -> Decimal {
        // 简化实现：基于当前持仓计算理论最大回撤
        let mut max_drawdown = Decimal::ZERO;

        for balance in &portfolio.balances {
            let asset_volatility = self.get_asset_volatility(&balance.asset);
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let weight = if portfolio.total_value > Decimal::ZERO {
                asset_value / portfolio.total_value
            } else {
                Decimal::ZERO
            };

            // 假设最大回撤为3倍波动率
            let asset_max_drawdown = asset_volatility * Decimal::from(3) * weight;
            max_drawdown += asset_max_drawdown;
        }

        max_drawdown.min(Decimal::ONE)
    }

    /// 计算夏普比率
    fn calculate_sharpe_ratio(&self, portfolio: &Portfolio) -> Decimal {
        // 简化实现：假设无风险利率为0，使用理论收益率
        let portfolio_volatility = self.calculate_portfolio_volatility(portfolio);

        if portfolio_volatility > Decimal::ZERO {
            // 假设年化收益率为10%
            let expected_return = Decimal::from_str_exact("0.1").unwrap();
            expected_return / portfolio_volatility
        } else {
            Decimal::ZERO
        }
    }

    /// 计算相关性矩阵
    fn calculate_correlation_matrix(&self, portfolio: &Portfolio) -> HashMap<String, HashMap<String, Decimal>> {
        let mut correlation_matrix = HashMap::new();

        for balance_i in &portfolio.balances {
            let mut correlations = HashMap::new();

            for balance_j in &portfolio.balances {
                let correlation = if balance_i.asset == balance_j.asset {
                    Decimal::ONE
                } else {
                    // 简化的相关性计算
                    self.get_asset_correlation(&balance_i.asset, &balance_j.asset)
                };

                correlations.insert(balance_j.asset.clone(), correlation);
            }

            correlation_matrix.insert(balance_i.asset.clone(), correlations);
        }

        correlation_matrix
    }

    /// 获取资产间相关性
    fn get_asset_correlation(&self, asset1: &str, asset2: &str) -> Decimal {
        // 简化的相关性模型
        match (asset1.to_uppercase().as_str(), asset2.to_uppercase().as_str()) {
            ("BTC", "ETH") | ("ETH", "BTC") => Decimal::from_str_exact("0.7").unwrap(),
            ("USDT", "USDC") | ("USDC", "USDT") => Decimal::from_str_exact("0.95").unwrap(),
            (a, b) if a.contains("USD") && b.contains("USD") => Decimal::from_str_exact("0.9").unwrap(),
            (a, b) if (a == "BTC" || a == "ETH") && (b == "BTC" || b == "ETH") => Decimal::from_str_exact("0.6").unwrap(),
            _ => Decimal::from_str_exact("0.3").unwrap(), // 默认相关性
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsCalculationResult {
    pub calculated_at: DateTime<Utc>,
    pub metrics: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VarCalculationResult {
    pub var_1d: Decimal,
    pub var_5d: Decimal,
    pub var_30d: Decimal,
}

// ============================================================================
// 压力测试器
// ============================================================================

/// 压力测试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTesterConfig {
    /// 测试场景
    pub scenarios: Vec<StressScenario>,
    /// 是否启用历史场景
    pub enable_historical_scenarios: bool,
    /// 是否启用假设场景
    pub enable_hypothetical_scenarios: bool,
}

impl Default for StressTesterConfig {
    fn default() -> Self {
        Self {
            scenarios: vec![
                StressScenario::new("Market Crash -20%", Decimal::from_str_exact("-0.2").unwrap()),
                StressScenario::new("Market Crash -30%", Decimal::from_str_exact("-0.3").unwrap()),
                StressScenario::new("Market Crash -50%", Decimal::from_str_exact("-0.5").unwrap()),
                StressScenario::new("Volatility Spike +100%", Decimal::from_str_exact("1.0").unwrap()),
            ],
            enable_historical_scenarios: true,
            enable_hypothetical_scenarios: true,
        }
    }
}

/// 压力测试器
pub struct StressTester {
    config: StressTesterConfig,
}

impl StressTester {
    /// 创建新的压力测试器
    pub fn new(config: StressTesterConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建压力测试器
    pub fn with_default_config() -> Self {
        Self::new(StressTesterConfig::default())
    }

    /// 运行压力测试
    pub async fn run_stress_test(&self, portfolio: &Portfolio, market_data: Option<&MarketData>) -> StressTestResult {
        let mut scenario_results = HashMap::new();
        let mut worst_case_loss = Decimal::ZERO;
        let mut best_case_gain = Decimal::ZERO;

        for scenario in &self.config.scenarios {
            let result = self.apply_scenario(portfolio, scenario, market_data);
            scenario_results.insert(scenario.name.clone(), serde_json::json!(result));

            if result.portfolio_change < worst_case_loss {
                worst_case_loss = result.portfolio_change;
            }
            if result.portfolio_change > best_case_gain {
                best_case_gain = result.portfolio_change;
            }
        }

        StressTestResult {
            tested_at: Utc::now(),
            scenarios: self.config.scenarios.iter().map(|s| s.name.clone()).collect(),
            scenario_results,
            worst_case_loss,
            best_case_gain,
            portfolio_value: portfolio.total_value,
        }
    }

    /// 应用压力测试场景
    fn apply_scenario(&self, portfolio: &Portfolio, scenario: &StressScenario, market_data: Option<&MarketData>) -> ScenarioResult {
        let original_value = portfolio.total_value;
        let mut new_value = Decimal::ZERO;

        // 对每个资产应用场景影响
        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let scenario_impact = scenario.market_change;

            // 简化的场景应用：假设所有资产都受到相同的市场影响
            let new_asset_value = asset_value * (Decimal::ONE + scenario_impact);
            new_value += new_asset_value;
        }

        let portfolio_change = new_value - original_value;
        let percentage_change = if original_value > Decimal::ZERO {
            portfolio_change / original_value
        } else {
            Decimal::ZERO
        };

        ScenarioResult {
            scenario_name: scenario.name.clone(),
            original_value,
            new_value,
            portfolio_change,
            percentage_change,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTestResult {
    pub tested_at: DateTime<Utc>,
    pub scenarios: Vec<String>,
    pub scenario_results: HashMap<String, serde_json::Value>,
    pub worst_case_loss: Decimal,
    pub best_case_gain: Decimal,
    pub portfolio_value: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressScenario {
    pub name: String,
    pub market_change: Decimal, // 市场变化百分比
}

impl StressScenario {
    pub fn new(name: &str, market_change: Decimal) -> Self {
        Self {
            name: name.to_string(),
            market_change,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScenarioResult {
    pub scenario_name: String,
    pub original_value: Decimal,
    pub new_value: Decimal,
    pub portfolio_change: Decimal,
    pub percentage_change: Decimal,
}

// ============================================================================
// 蒙特卡洛模拟器
// ============================================================================

/// 蒙特卡洛模拟配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloConfig {
    /// 模拟次数
    pub simulation_count: u32,
    /// 时间范围（天）
    pub time_horizon_days: u32,
    /// 随机种子
    pub random_seed: Option<u64>,
    /// 是否使用历史波动率
    pub use_historical_volatility: bool,
}

impl Default for MonteCarloConfig {
    fn default() -> Self {
        Self {
            simulation_count: 10000,
            time_horizon_days: 30,
            random_seed: None,
            use_historical_volatility: true,
        }
    }
}

/// 蒙特卡洛模拟器
pub struct MonteCarloSimulator {
    config: MonteCarloConfig,
    rng: ChaCha8Rng,
}

impl MonteCarloSimulator {
    /// 创建新的蒙特卡洛模拟器
    pub fn new(config: MonteCarloConfig) -> Self {
        let rng = if let Some(seed) = config.random_seed {
            ChaCha8Rng::seed_from_u64(seed)
        } else {
            ChaCha8Rng::from_entropy()
        };

        Self { config, rng }
    }

    /// 使用默认配置创建蒙特卡洛模拟器
    pub fn with_default_config() -> Self {
        Self::new(MonteCarloConfig::default())
    }

    /// 运行蒙特卡洛模拟
    pub async fn run_simulation(&mut self, portfolio: &Portfolio, market_data: Option<&MarketData>) -> MonteCarloResult {
        let mut simulation_results = Vec::new();
        let initial_value = portfolio.total_value;

        // 计算每个资产的波动率
        let asset_volatilities = self.calculate_asset_volatilities(portfolio, market_data);

        for _ in 0..self.config.simulation_count {
            let final_value = self.simulate_portfolio_path(portfolio, &asset_volatilities);
            let return_rate = if initial_value > Decimal::ZERO {
                (final_value - initial_value) / initial_value
            } else {
                Decimal::ZERO
            };
            simulation_results.push(return_rate);
        }

        // 排序结果用于计算分位数
        simulation_results.sort_by(|a, b| a.cmp(b));

        let percentile_5 = self.calculate_percentile(&simulation_results, 0.05);
        let percentile_25 = self.calculate_percentile(&simulation_results, 0.25);
        let percentile_50 = self.calculate_percentile(&simulation_results, 0.50);
        let percentile_75 = self.calculate_percentile(&simulation_results, 0.75);
        let percentile_95 = self.calculate_percentile(&simulation_results, 0.95);

        let mean_return = simulation_results.iter().sum::<Decimal>() / Decimal::from(simulation_results.len() as u32);
        let variance = simulation_results
            .iter()
            .map(|r| (*r - mean_return) * (*r - mean_return))
            .sum::<Decimal>() / Decimal::from(simulation_results.len() as u32);
        let std_deviation = variance.sqrt().unwrap_or(Decimal::ZERO);

        MonteCarloResult {
            simulated_at: Utc::now(),
            simulation_count: self.config.simulation_count,
            time_horizon_days: self.config.time_horizon_days,
            initial_portfolio_value: initial_value,
            mean_return,
            std_deviation,
            percentile_5,
            percentile_25,
            percentile_50,
            percentile_75,
            percentile_95,
            var_95: -percentile_5, // VaR 是负的第5百分位数
            expected_shortfall: self.calculate_expected_shortfall(&simulation_results, 0.05),
        }
    }

    /// 计算资产波动率
    fn calculate_asset_volatilities(&self, portfolio: &Portfolio, market_data: Option<&MarketData>) -> HashMap<String, Decimal> {
        let mut volatilities = HashMap::new();

        for balance in &portfolio.balances {
            let volatility = if self.config.use_historical_volatility && market_data.is_some() {
                // 使用市场数据计算波动率
                self.calculate_historical_volatility(&balance.asset, market_data.unwrap())
            } else {
                // 使用默认波动率
                self.get_default_volatility(&balance.asset)
            };
            volatilities.insert(balance.asset.clone(), volatility);
        }

        volatilities
    }

    /// 计算历史波动率
    fn calculate_historical_volatility(&self, _asset: &str, market_data: &MarketData) -> Decimal {
        // 简化实现：使用24小时高低价计算波动率
        if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
            let price = market_data.price;
            if price > Decimal::ZERO {
                return (high - low) / price;
            }
        }

        // 默认波动率
        Decimal::from_str_exact("0.3").unwrap()
    }

    /// 获取默认波动率
    fn get_default_volatility(&self, asset: &str) -> Decimal {
        // 根据资产类型设置不同的默认波动率
        match asset.to_uppercase().as_str() {
            "BTC" => Decimal::from_str_exact("0.4").unwrap(),
            "ETH" => Decimal::from_str_exact("0.5").unwrap(),
            "USDT" | "USDC" | "DAI" => Decimal::from_str_exact("0.01").unwrap(), // 稳定币
            _ => Decimal::from_str_exact("0.6").unwrap(), // 其他加密货币
        }
    }

    /// 模拟投资组合路径
    fn simulate_portfolio_path(&mut self, portfolio: &Portfolio, volatilities: &HashMap<String, Decimal>) -> Decimal {
        let mut total_value = Decimal::ZERO;
        let time_factor = (Decimal::from(self.config.time_horizon_days) / Decimal::from(365)).sqrt().unwrap_or(Decimal::ONE);

        for balance in &portfolio.balances {
            let initial_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let volatility = volatilities.get(&balance.asset).copied().unwrap_or(Decimal::from_str_exact("0.3").unwrap());

            // 生成正态分布随机数
            let random_return = self.generate_normal_random() * volatility * time_factor;
            let final_value = initial_value * (Decimal::ONE + random_return);

            total_value += final_value;
        }

        total_value
    }

    /// 生成正态分布随机数（Box-Muller变换）
    fn generate_normal_random(&mut self) -> Decimal {
        let u1: f64 = self.rng.gen();
        let u2: f64 = self.rng.gen();

        let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f64::consts::PI * u2).cos();
        Decimal::from_f64_retain(z).unwrap_or(Decimal::ZERO)
    }

    /// 计算百分位数
    fn calculate_percentile(&self, sorted_data: &[Decimal], percentile: f64) -> Decimal {
        if sorted_data.is_empty() {
            return Decimal::ZERO;
        }

        let index = (percentile * (sorted_data.len() - 1) as f64) as usize;
        sorted_data[index.min(sorted_data.len() - 1)]
    }

    /// 计算期望损失（Expected Shortfall）
    fn calculate_expected_shortfall(&self, sorted_data: &[Decimal], confidence_level: f64) -> Decimal {
        if sorted_data.is_empty() {
            return Decimal::ZERO;
        }

        let cutoff_index = ((1.0 - confidence_level) * sorted_data.len() as f64) as usize;
        if cutoff_index == 0 {
            return sorted_data[0];
        }

        let tail_sum: Decimal = sorted_data[..cutoff_index].iter().sum();
        tail_sum / Decimal::from(cutoff_index as u32)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloResult {
    pub simulated_at: DateTime<Utc>,
    pub simulation_count: u32,
    pub time_horizon_days: u32,
    pub initial_portfolio_value: Decimal,
    pub mean_return: Decimal,
    pub std_deviation: Decimal,
    pub percentile_5: Decimal,
    pub percentile_25: Decimal,
    pub percentile_50: Decimal,
    pub percentile_75: Decimal,
    pub percentile_95: Decimal,
    pub var_95: Decimal,
    pub expected_shortfall: Decimal,
}

