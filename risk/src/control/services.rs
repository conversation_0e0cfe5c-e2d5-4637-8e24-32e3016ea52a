//! 风控服务层
//!
//! 实现核心风控业务逻辑

/// 订单验证服务
pub mod order_validator;

/// 持仓监控服务  
pub mod position_monitor;

/// 风险计算服务
pub mod risk_calculator;

/// 预警管理服务
pub mod alert_manager;

/// 配置管理服务
pub mod config_manager;

/// 配置转换服务
pub mod config_converter;

// 重新导出所有服务
pub use order_validator::{
    OrderValidator, OrderValidationResult, OrderValidationError, OrderValidatorConfig
};
pub use position_monitor::{
    PositionMonitor, PositionStatus, PositionRiskLevel, PositionMonitorError,
    PositionMonitorConfig, PositionSummary
};
pub use risk_calculator::{
    RiskCalculator, RiskCalculationResult, RiskCalculationError, RiskCalculatorConfig
};
pub use alert_manager::{
    <PERSON>ertManager, AlertResult, AlertManagerError, AlertManagerConfig, AlertStatistics
};
pub use config_manager::{
    RiskConfigManager, CacheConfig
};
pub use config_converter::{
    RiskConfigConverter, RuleConfig, OrderSizeConfig, PositionLimitConfig,
    DailyLossConfig, LeverageConfig, VolatilityConfig
};