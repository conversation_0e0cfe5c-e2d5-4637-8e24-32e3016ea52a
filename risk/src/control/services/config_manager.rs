//! 风控配置管理服务
//!
//! 负责从数据库读取、缓存和管理风控配置

use sigmax_core::{RiskManagementConfig, SigmaXResult, SigmaXError};
use sigmax_database::repositories::traits::{RiskRepository, RiskRuleRecord};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use uuid::Uuid;
use tokio::sync::RwLock as AsyncRwLock;

// ============================================================================
// 配置管理器
// ============================================================================

/// 风控配置管理器
/// 
/// 负责：
/// 1. 从数据库读取风控配置
/// 2. 缓存配置以提高性能
/// 3. 监听配置变更
/// 4. 提供配置热更新功能
#[derive(Debug)]
pub struct RiskConfigManager {
    /// 数据库仓储
    repository: Arc<dyn RiskRepository>,
    /// 配置缓存
    config_cache: Arc<AsyncRwLock<ConfigCache>>,
    /// 规则缓存
    rules_cache: Arc<AsyncRwLock<RulesCache>>,
    /// 缓存配置
    cache_config: CacheConfig,
}

/// 配置缓存
#[derive(Debug, Clone)]
struct ConfigCache {
    /// 全局配置
    global_config: Option<CachedConfig>,
    /// 策略特定配置
    strategy_configs: HashMap<String, CachedConfig>,
    /// 最后更新时间
    last_updated: Instant,
}

/// 规则缓存
#[derive(Debug, Clone)]
struct RulesCache {
    /// 按类型分组的规则
    rules_by_type: HashMap<String, Vec<RiskRuleRecord>>,
    /// 所有启用的规则
    enabled_rules: Vec<RiskRuleRecord>,
    /// 最后更新时间
    last_updated: Instant,
}

/// 缓存的配置项
#[derive(Debug, Clone)]
struct CachedConfig {
    /// 配置内容
    config: RiskManagementConfig,
    /// 缓存时间
    cached_at: Instant,
    /// 配置版本（用于检测变更）
    version: u64,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 配置缓存过期时间
    pub config_ttl: Duration,
    /// 规则缓存过期时间
    pub rules_ttl: Duration,
    /// 自动刷新间隔
    pub refresh_interval: Duration,
    /// 是否启用自动刷新
    pub auto_refresh: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            config_ttl: Duration::from_secs(300),      // 5分钟
            rules_ttl: Duration::from_secs(60),        // 1分钟
            refresh_interval: Duration::from_secs(30), // 30秒
            auto_refresh: true,
        }
    }
}

impl RiskConfigManager {
    /// 创建新的配置管理器
    pub fn new(
        repository: Arc<dyn RiskRepository>,
        cache_config: Option<CacheConfig>,
    ) -> Self {
        Self {
            repository,
            config_cache: Arc::new(AsyncRwLock::new(ConfigCache::new())),
            rules_cache: Arc::new(AsyncRwLock::new(RulesCache::new())),
            cache_config: cache_config.unwrap_or_default(),
        }
    }

    /// 获取全局风控配置
    pub async fn get_global_config(&self) -> SigmaXResult<RiskManagementConfig> {
        // 首先尝试从缓存获取
        if let Some(config) = self.get_cached_global_config().await? {
            return Ok(config);
        }

        // 缓存未命中，从数据库读取
        let config = self.repository.get_risk_config().await?;
        
        // 更新缓存
        self.cache_global_config(&config).await?;
        
        Ok(config)
    }

    /// 获取策略特定的风控配置
    pub async fn get_strategy_config(&self, strategy_type: &str) -> SigmaXResult<RiskManagementConfig> {
        // 首先尝试从缓存获取
        if let Some(config) = self.get_cached_strategy_config(strategy_type).await? {
            return Ok(config);
        }

        // 缓存未命中，从数据库读取
        let config = match self.repository.get_risk_config_by_strategy_type(strategy_type).await? {
            Some(config) => config,
            None => {
                // 如果没有策略特定配置，使用全局配置
                self.get_global_config().await?
            }
        };
        
        // 更新缓存
        self.cache_strategy_config(strategy_type, &config).await?;
        
        Ok(config)
    }

    /// 获取启用的风控规则
    pub async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 首先尝试从缓存获取
        if let Some(rules) = self.get_cached_enabled_rules().await? {
            return Ok(rules);
        }

        // 缓存未命中，从数据库读取
        let rules = self.repository.get_enabled_risk_rules().await?;
        
        // 更新缓存
        self.cache_enabled_rules(&rules).await?;
        
        Ok(rules)
    }

    /// 根据类型获取风控规则
    pub async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 首先尝试从缓存获取
        if let Some(rules) = self.get_cached_rules_by_type(rule_type).await? {
            return Ok(rules);
        }

        // 缓存未命中，从数据库读取
        let rules = self.repository.get_risk_rules_by_type(rule_type).await?;
        
        // 更新缓存
        self.cache_rules_by_type(rule_type, &rules).await?;
        
        Ok(rules)
    }

    /// 强制刷新所有缓存
    pub async fn refresh_cache(&self) -> SigmaXResult<()> {
        // 清空缓存
        {
            let mut config_cache = self.config_cache.write().await;
            *config_cache = ConfigCache::new();
        }
        {
            let mut rules_cache = self.rules_cache.write().await;
            *rules_cache = RulesCache::new();
        }

        // 预加载常用配置
        let _ = self.get_global_config().await?;
        let _ = self.get_enabled_rules().await?;

        Ok(())
    }

    /// 启动自动刷新任务
    pub async fn start_auto_refresh(&self) -> SigmaXResult<()> {
        if !self.cache_config.auto_refresh {
            return Ok(());
        }

        let manager = Arc::new(self.clone());
        let interval = self.cache_config.refresh_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                if let Err(e) = manager.refresh_cache().await {
                    tracing::warn!("Failed to refresh risk config cache: {}", e);
                }
            }
        });

        Ok(())
    }
}

// ============================================================================
// 私有方法 - 缓存操作
// ============================================================================

impl RiskConfigManager {
    /// 从缓存获取全局配置
    async fn get_cached_global_config(&self) -> SigmaXResult<Option<RiskManagementConfig>> {
        let cache = self.config_cache.read().await;
        
        if let Some(cached) = &cache.global_config {
            if cached.cached_at.elapsed() < self.cache_config.config_ttl {
                return Ok(Some(cached.config.clone()));
            }
        }
        
        Ok(None)
    }

    /// 缓存全局配置
    async fn cache_global_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let mut cache = self.config_cache.write().await;
        
        cache.global_config = Some(CachedConfig {
            config: config.clone(),
            cached_at: Instant::now(),
            version: cache.global_config.as_ref().map(|c| c.version + 1).unwrap_or(1),
        });
        cache.last_updated = Instant::now();
        
        Ok(())
    }

    /// 从缓存获取策略配置
    async fn get_cached_strategy_config(&self, strategy_type: &str) -> SigmaXResult<Option<RiskManagementConfig>> {
        let cache = self.config_cache.read().await;
        
        if let Some(cached) = cache.strategy_configs.get(strategy_type) {
            if cached.cached_at.elapsed() < self.cache_config.config_ttl {
                return Ok(Some(cached.config.clone()));
            }
        }
        
        Ok(None)
    }

    /// 缓存策略配置
    async fn cache_strategy_config(&self, strategy_type: &str, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let mut cache = self.config_cache.write().await;
        
        let version = cache.strategy_configs.get(strategy_type)
            .map(|c| c.version + 1)
            .unwrap_or(1);
            
        cache.strategy_configs.insert(strategy_type.to_string(), CachedConfig {
            config: config.clone(),
            cached_at: Instant::now(),
            version,
        });
        cache.last_updated = Instant::now();
        
        Ok(())
    }

    /// 从缓存获取启用的规则
    async fn get_cached_enabled_rules(&self) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.rules_cache.read().await;
        
        if cache.last_updated.elapsed() < self.cache_config.rules_ttl {
            return Ok(Some(cache.enabled_rules.clone()));
        }
        
        Ok(None)
    }

    /// 缓存启用的规则
    async fn cache_enabled_rules(&self, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.rules_cache.write().await;
        
        cache.enabled_rules = rules.to_vec();
        cache.last_updated = Instant::now();
        
        Ok(())
    }

    /// 从缓存获取指定类型的规则
    async fn get_cached_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.rules_cache.read().await;
        
        if cache.last_updated.elapsed() < self.cache_config.rules_ttl {
            if let Some(rules) = cache.rules_by_type.get(rule_type) {
                return Ok(Some(rules.clone()));
            }
        }
        
        Ok(None)
    }

    /// 缓存指定类型的规则
    async fn cache_rules_by_type(&self, rule_type: &str, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.rules_cache.write().await;
        
        cache.rules_by_type.insert(rule_type.to_string(), rules.to_vec());
        cache.last_updated = Instant::now();
        
        Ok(())
    }
}

impl Clone for RiskConfigManager {
    fn clone(&self) -> Self {
        Self {
            repository: Arc::clone(&self.repository),
            config_cache: Arc::clone(&self.config_cache),
            rules_cache: Arc::clone(&self.rules_cache),
            cache_config: self.cache_config.clone(),
        }
    }
}

impl ConfigCache {
    fn new() -> Self {
        Self {
            global_config: None,
            strategy_configs: HashMap::new(),
            last_updated: Instant::now(),
        }
    }
}

impl RulesCache {
    fn new() -> Self {
        Self {
            rules_by_type: HashMap::new(),
            enabled_rules: Vec::new(),
            last_updated: Instant::now(),
        }
    }
}
