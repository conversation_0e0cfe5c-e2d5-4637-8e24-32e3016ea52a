//! 风险计算服务
//!
//! 负责计算各种风险指标和评分

use sigmax_interfaces::risk::{Portfolio, RiskContext};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

/// 风险计算配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCalculatorConfig {
    /// VaR 置信度
    pub var_confidence_level: Decimal,
    /// 历史数据天数
    pub historical_days: u32,
    /// 是否启用蒙特卡洛模拟
    pub enable_monte_carlo: bool,
    /// 模拟次数
    pub simulation_count: u32,
    /// 波动率计算窗口
    pub volatility_window: u32,
}

impl Default for RiskCalculatorConfig {
    fn default() -> Self {
        Self {
            var_confidence_level: Decimal::from_str_exact("0.95").unwrap(),
            historical_days: 252, // 一年交易日
            enable_monte_carlo: false,
            simulation_count: 10000,
            volatility_window: 30,
        }
    }
}

/// 风险计算服务
pub struct RiskCalculator {
    config: RiskCalculatorConfig,
}

impl RiskCalculator {
    /// 创建新的风险计算器
    pub fn new(config: RiskCalculatorConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建风险计算器
    pub fn with_default_config() -> Self {
        Self::new(RiskCalculatorConfig::default())
    }

    /// 计算基础风险指标
    pub async fn calculate_basic_metrics(&self, context: &RiskContext) -> RiskCalculationResult {
        let mut metrics = HashMap::new();
        let mut risk_score = Decimal::ZERO;

        // 1. 计算持仓风险
        if let Some(portfolio) = &context.portfolio {
            let position_risk = self.calculate_position_risk(portfolio);
            metrics.insert("position_risk".to_string(), serde_json::json!(position_risk));
            risk_score += position_risk * Decimal::from_str_exact("0.4").unwrap();
        }

        // 2. 计算流动性风险
        let liquidity_risk = self.calculate_liquidity_risk(context).await;
        metrics.insert("liquidity_risk".to_string(), serde_json::json!(liquidity_risk));
        risk_score += liquidity_risk * Decimal::from_str_exact("0.3").unwrap();

        // 3. 计算市场风险
        let market_risk = self.calculate_market_risk(context).await;
        metrics.insert("market_risk".to_string(), serde_json::json!(market_risk));
        risk_score += market_risk * Decimal::from_str_exact("0.3").unwrap();

        // 4. 计算波动率
        if let Some(market_data) = &context.market_data {
            let volatility = self.calculate_volatility(market_data);
            metrics.insert("volatility".to_string(), serde_json::json!(volatility));
        }

        RiskCalculationResult {
            risk_score,
            metrics,
            calculated_at: Utc::now(),
        }
    }

    /// 计算高级风险指标
    pub async fn calculate_advanced_metrics(&self, context: &RiskContext) -> RiskCalculationResult {
        let mut metrics = HashMap::new();
        let mut risk_score = Decimal::ZERO;

        // 1. 计算 VaR (Value at Risk)
        let var_result = self.calculate_var(context).await;
        metrics.insert("var_1d".to_string(), serde_json::json!(var_result.var_1d));
        metrics.insert("var_5d".to_string(), serde_json::json!(var_result.var_5d));
        risk_score += var_result.var_1d * Decimal::from_str_exact("0.3").unwrap();

        // 2. 计算 CVaR (Conditional Value at Risk)
        let cvar = self.calculate_cvar(context).await;
        metrics.insert("cvar".to_string(), serde_json::json!(cvar));
        risk_score += cvar * Decimal::from_str_exact("0.2").unwrap();

        // 3. 计算相关性风险
        if let Some(portfolio) = &context.portfolio {
            let correlation_risk = self.calculate_correlation_risk(portfolio).await;
            metrics.insert("correlation_risk".to_string(), serde_json::json!(correlation_risk));
            risk_score += correlation_risk * Decimal::from_str_exact("0.2").unwrap();
        }

        // 4. 计算压力测试结果
        let stress_test_result = self.run_stress_test(context).await;
        metrics.insert("stress_test".to_string(), serde_json::json!(stress_test_result));
        risk_score += stress_test_result.worst_case_loss * Decimal::from_str_exact("0.3").unwrap();

        RiskCalculationResult {
            risk_score,
            metrics,
            calculated_at: Utc::now(),
        }
    }

    /// 计算持仓风险
    fn calculate_position_risk(&self, portfolio: &Portfolio) -> Decimal {
        let mut risk = Decimal::ZERO;
        let total_value = portfolio.total_value;

        if total_value <= Decimal::ZERO {
            return risk;
        }

        // 计算集中度风险
        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let concentration = asset_value / total_value;

            // 集中度越高，风险越大
            if concentration > Decimal::from_str_exact("0.1").unwrap() {
                risk += concentration * concentration; // 平方增长
            }
        }

        risk.min(Decimal::ONE)
    }

    /// 计算流动性风险
    async fn calculate_liquidity_risk(&self, context: &RiskContext) -> Decimal {
        if let Some(market_data) = &context.market_data {
            // 基于买卖价差计算流动性风险
            if let (Some(bid), Some(ask)) = (market_data.bid_price, market_data.ask_price) {
                let spread = (ask - bid) / ((ask + bid) / Decimal::from(2));
                return spread.min(Decimal::ONE);
            }
        }

        // 默认中等流动性风险
        Decimal::from_str_exact("0.5").unwrap()
    }

    /// 计算市场风险
    async fn calculate_market_risk(&self, context: &RiskContext) -> Decimal {
        if let Some(market_data) = &context.market_data {
            // 基于价格波动计算市场风险
            let volatility = self.calculate_volatility(market_data);
            return volatility.min(Decimal::ONE);
        }

        // 默认中等市场风险
        Decimal::from_str_exact("0.5").unwrap()
    }

    /// 计算波动率
    fn calculate_volatility(&self, market_data: &sigmax_interfaces::risk::MarketData) -> Decimal {
        // 简化的波动率计算
        // 在实际应用中，这里应该使用历史价格数据
        if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
            let price = market_data.price;
            if price > Decimal::ZERO {
                return ((high - low) / price).min(Decimal::ONE);
            }
        }

        // 默认波动率
        Decimal::from_str_exact("0.2").unwrap()
    }

    /// 计算 VaR (Value at Risk)
    async fn calculate_var(&self, context: &RiskContext) -> VarResult {
        // 简化的 VaR 计算
        let base_volatility = if let Some(market_data) = &context.market_data {
            self.calculate_volatility(market_data)
        } else {
            Decimal::from_str_exact("0.2").unwrap()
        };

        let portfolio_value = context.portfolio
            .as_ref()
            .map(|p| p.total_value)
            .unwrap_or_default();

        // 使用正态分布假设计算 VaR
        let confidence_factor = Decimal::from_str_exact("1.645").unwrap(); // 95% 置信度
        let var_1d = portfolio_value * base_volatility * confidence_factor;
        let var_5d = var_1d * Decimal::from_str_exact("2.236").unwrap(); // sqrt(5)

        VarResult { var_1d, var_5d }
    }

    /// 计算 CVaR (Conditional Value at Risk)
    async fn calculate_cvar(&self, context: &RiskContext) -> Decimal {
        let var_result = self.calculate_var(context).await;
        // CVaR 通常是 VaR 的 1.2-1.5 倍
        var_result.var_1d * Decimal::from_str_exact("1.3").unwrap()
    }

    /// 计算相关性风险
    async fn calculate_correlation_risk(&self, portfolio: &Portfolio) -> Decimal {
        // 简化的相关性风险计算
        // 假设资产间存在一定的相关性
        let asset_count = portfolio.balances.len() as u32;

        if asset_count <= 1 {
            return Decimal::ZERO;
        }

        // 资产越多，分散化效果越好，相关性风险越低
        let diversification_factor = Decimal::ONE / Decimal::from(asset_count);
        let base_correlation = Decimal::from_str_exact("0.3").unwrap(); // 假设 30% 的基础相关性

        base_correlation * diversification_factor
    }

    /// 运行压力测试
    async fn run_stress_test(&self, context: &RiskContext) -> StressTestResult {
        let portfolio_value = context.portfolio
            .as_ref()
            .map(|p| p.total_value)
            .unwrap_or_default();

        // 模拟不同的压力情景
        let scenarios = vec![
            ("Market Crash -20%", Decimal::from_str_exact("0.2").unwrap()),
            ("Market Crash -30%", Decimal::from_str_exact("0.3").unwrap()),
            ("Market Crash -50%", Decimal::from_str_exact("0.5").unwrap()),
        ];

        let mut worst_case_loss = Decimal::ZERO;
        let mut scenario_results = HashMap::new();

        for (scenario_name, loss_rate) in scenarios {
            let loss = portfolio_value * loss_rate;
            scenario_results.insert(scenario_name.to_string(), loss);

            if loss > worst_case_loss {
                worst_case_loss = loss;
            }
        }

        StressTestResult {
            worst_case_loss,
            scenario_results,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCalculationResult {
    pub risk_score: Decimal,
    pub metrics: HashMap<String, serde_json::Value>,
    pub calculated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VarResult {
    pub var_1d: Decimal,
    pub var_5d: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTestResult {
    pub worst_case_loss: Decimal,
    pub scenario_results: HashMap<String, Decimal>,
}

#[derive(Debug, thiserror::Error)]
pub enum RiskCalculationError {
    #[error("Risk calculation failed: {reason}")]
    CalculationFailed { reason: String },

    #[error("Insufficient data: {missing}")]
    InsufficientData { missing: String },

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },

    #[error("Market data unavailable")]
    MarketDataUnavailable,
}