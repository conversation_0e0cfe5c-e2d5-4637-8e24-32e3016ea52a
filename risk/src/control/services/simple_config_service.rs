//! 简化的风控配置服务
//!
//! 专为单机部署设计的轻量级配置管理

use sigmax_core::{RiskManagementConfig, SigmaXResult, SigmaXError};
use sigmax_database::repositories::traits::{RiskRepository, RiskRuleRecord};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use uuid::Uuid;

// ============================================================================
// 简化的配置服务
// ============================================================================

/// 简化的风控配置服务
/// 
/// 特点：
/// 1. 单机内存缓存
/// 2. 简单的刷新机制
/// 3. 直接的配置转换
/// 4. 最小化的复杂度
#[derive(Debug)]
pub struct SimpleConfigService {
    /// 数据库仓储
    repository: Arc<dyn RiskRepository>,
    /// 内存缓存
    cache: Arc<RwLock<ConfigCache>>,
    /// 配置选项
    options: ConfigOptions,
}

/// 配置缓存
#[derive(Debug, Clone)]
struct ConfigCache {
    /// 全局配置
    global_config: Option<CachedItem<RiskManagementConfig>>,
    /// 策略配置映射
    strategy_configs: HashMap<String, CachedItem<RiskManagementConfig>>,
    /// 规则缓存
    rules_cache: Option<CachedItem<Vec<RiskRuleRecord>>>,
    /// 按类型分组的规则
    rules_by_type: HashMap<String, CachedItem<Vec<RiskRuleRecord>>>,
}

/// 缓存项
#[derive(Debug, Clone)]
struct CachedItem<T> {
    data: T,
    cached_at: Instant,
}

/// 配置选项
#[derive(Debug, Clone)]
pub struct ConfigOptions {
    /// 缓存过期时间
    pub cache_ttl: Duration,
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 配置验证
    pub validate_config: bool,
}

impl Default for ConfigOptions {
    fn default() -> Self {
        Self {
            cache_ttl: Duration::from_secs(300), // 5分钟
            enable_cache: true,
            validate_config: true,
        }
    }
}

impl SimpleConfigService {
    /// 创建新的配置服务
    pub fn new(repository: Arc<dyn RiskRepository>, options: Option<ConfigOptions>) -> Self {
        Self {
            repository,
            cache: Arc::new(RwLock::new(ConfigCache::new())),
            options: options.unwrap_or_default(),
        }
    }

    /// 获取全局风控配置
    pub async fn get_global_config(&self) -> SigmaXResult<RiskManagementConfig> {
        // 尝试从缓存获取
        if self.options.enable_cache {
            if let Some(config) = self.get_cached_global_config()? {
                return Ok(config);
            }
        }

        // 从数据库读取
        let config = self.load_global_config().await?;
        
        // 更新缓存
        if self.options.enable_cache {
            self.cache_global_config(&config)?;
        }
        
        Ok(config)
    }

    /// 获取策略特定配置
    pub async fn get_strategy_config(&self, strategy_type: &str) -> SigmaXResult<RiskManagementConfig> {
        // 尝试从缓存获取
        if self.options.enable_cache {
            if let Some(config) = self.get_cached_strategy_config(strategy_type)? {
                return Ok(config);
            }
        }

        // 从数据库读取
        let config = self.load_strategy_config(strategy_type).await?;
        
        // 更新缓存
        if self.options.enable_cache {
            self.cache_strategy_config(strategy_type, &config)?;
        }
        
        Ok(config)
    }

    /// 获取启用的规则
    pub async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 尝试从缓存获取
        if self.options.enable_cache {
            if let Some(rules) = self.get_cached_rules()? {
                return Ok(rules);
            }
        }

        // 从数据库读取
        let rules = self.repository.get_enabled_risk_rules().await?;
        
        // 更新缓存
        if self.options.enable_cache {
            self.cache_rules(&rules)?;
        }
        
        Ok(rules)
    }

    /// 根据类型获取规则
    pub async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 尝试从缓存获取
        if self.options.enable_cache {
            if let Some(rules) = self.get_cached_rules_by_type(rule_type)? {
                return Ok(rules);
            }
        }

        // 从数据库读取
        let rules = self.repository.get_risk_rules_by_type(rule_type).await?;
        
        // 更新缓存
        if self.options.enable_cache {
            self.cache_rules_by_type(rule_type, &rules)?;
        }
        
        Ok(rules)
    }

    /// 刷新所有缓存
    pub async fn refresh_cache(&self) -> SigmaXResult<()> {
        if !self.options.enable_cache {
            return Ok(());
        }

        // 清空缓存
        {
            let mut cache = self.cache.write()
                .map_err(|e| SigmaXError::Internal(format!("Failed to acquire cache lock: {}", e)))?;
            *cache = ConfigCache::new();
        }

        // 预加载常用配置
        let _ = self.get_global_config().await;
        let _ = self.get_enabled_rules().await;

        tracing::info!("Risk config cache refreshed");
        Ok(())
    }

    /// 将规则转换为配置
    pub async fn rules_to_config(&self, strategy_type: Option<&str>) -> SigmaXResult<RiskManagementConfig> {
        // 获取基础配置
        let mut config = match strategy_type {
            Some(strategy) => self.get_strategy_config(strategy).await?,
            None => self.get_global_config().await?,
        };

        // 获取相关规则
        let rules = self.get_enabled_rules().await?;
        
        // 应用规则到配置
        self.apply_rules_to_config(&mut config, &rules)?;
        
        // 验证配置
        if self.options.validate_config {
            config.validate_complete()?;
        }
        
        Ok(config)
    }
}

// ============================================================================
// 私有方法
// ============================================================================

impl SimpleConfigService {
    /// 从数据库加载全局配置
    async fn load_global_config(&self) -> SigmaXResult<RiskManagementConfig> {
        match self.repository.get_risk_config().await {
            Ok(config) => Ok(config),
            Err(_) => {
                tracing::warn!("Failed to load global config from database, using default");
                Ok(RiskManagementConfig::default())
            }
        }
    }

    /// 从数据库加载策略配置
    async fn load_strategy_config(&self, strategy_type: &str) -> SigmaXResult<RiskManagementConfig> {
        match self.repository.get_risk_config_by_strategy_type(strategy_type).await? {
            Some(config) => Ok(config),
            None => {
                // 如果没有策略特定配置，使用全局配置
                self.get_global_config().await
            }
        }
    }

    /// 应用规则到配置
    fn apply_rules_to_config(&self, config: &mut RiskManagementConfig, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        for rule in rules {
            if !rule.enabled {
                continue;
            }

            match rule.rule_type.as_str() {
                "max_order_size" => self.apply_order_size_rule(config, rule)?,
                "position_limit" => self.apply_position_limit_rule(config, rule)?,
                "daily_loss_limit" => self.apply_daily_loss_rule(config, rule)?,
                "leverage_limit" => self.apply_leverage_rule(config, rule)?,
                "volatility_control" => self.apply_volatility_rule(config, rule)?,
                _ => {
                    tracing::debug!("Unknown rule type: {}", rule.rule_type);
                }
            }
        }
        
        Ok(())
    }

    /// 应用订单大小规则
    fn apply_order_size_rule(&self, config: &mut RiskManagementConfig, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        if let Some(max_amount) = rule.parameters.get("max_amount") {
            if let Ok(amount) = serde_json::from_value::<f64>(max_amount.clone()) {
                config.risk_parameters.basic.max_order_amount = 
                    Decimal::from_f64_retain(amount).unwrap_or(config.risk_parameters.basic.max_order_amount);
            }
        }
        Ok(())
    }

    /// 应用持仓限制规则
    fn apply_position_limit_rule(&self, config: &mut RiskManagementConfig, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        if let Some(max_position) = rule.parameters.get("max_position_percent") {
            if let Ok(percent) = serde_json::from_value::<f64>(max_position.clone()) {
                config.risk_parameters.position.max_position_per_symbol = 
                    Decimal::from_f64_retain(percent / 100.0)
                        .unwrap_or(config.risk_parameters.position.max_position_per_symbol);
            }
        }
        Ok(())
    }

    /// 应用日损失规则
    fn apply_daily_loss_rule(&self, config: &mut RiskManagementConfig, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        if let Some(max_loss) = rule.parameters.get("max_daily_loss_percent") {
            if let Ok(percent) = serde_json::from_value::<f64>(max_loss.clone()) {
                config.risk_parameters.basic.max_daily_loss_percent = 
                    Decimal::from_f64_retain(percent / 100.0)
                        .unwrap_or(config.risk_parameters.basic.max_daily_loss_percent);
            }
        }
        Ok(())
    }

    /// 应用杠杆规则
    fn apply_leverage_rule(&self, config: &mut RiskManagementConfig, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        if let Some(max_leverage) = rule.parameters.get("max_leverage") {
            if let Ok(leverage) = serde_json::from_value::<f64>(max_leverage.clone()) {
                config.risk_parameters.position.max_leverage = 
                    Decimal::from_f64_retain(leverage)
                        .unwrap_or(config.risk_parameters.position.max_leverage);
            }
        }
        Ok(())
    }

    /// 应用波动率规则
    fn apply_volatility_rule(&self, config: &mut RiskManagementConfig, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        if let Some(threshold) = rule.parameters.get("volatility_threshold") {
            if let Ok(vol) = serde_json::from_value::<f64>(threshold.clone()) {
                config.risk_parameters.market.volatility_threshold = 
                    Decimal::from_f64_retain(vol)
                        .unwrap_or(config.risk_parameters.market.volatility_threshold);
            }
        }
        Ok(())
    }

    // 缓存相关方法
    fn get_cached_global_config(&self) -> SigmaXResult<Option<RiskManagementConfig>> {
        let cache = self.cache.read()
            .map_err(|e| SigmaXError::Internal(format!("Failed to read cache: {}", e)))?;
        
        if let Some(cached) = &cache.global_config {
            if cached.cached_at.elapsed() < self.options.cache_ttl {
                return Ok(Some(cached.data.clone()));
            }
        }
        
        Ok(None)
    }

    fn cache_global_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let mut cache = self.cache.write()
            .map_err(|e| SigmaXError::Internal(format!("Failed to write cache: {}", e)))?;
        
        cache.global_config = Some(CachedItem {
            data: config.clone(),
            cached_at: Instant::now(),
        });
        
        Ok(())
    }

    fn get_cached_strategy_config(&self, strategy_type: &str) -> SigmaXResult<Option<RiskManagementConfig>> {
        let cache = self.cache.read()
            .map_err(|e| SigmaXError::Internal(format!("Failed to read cache: {}", e)))?;
        
        if let Some(cached) = cache.strategy_configs.get(strategy_type) {
            if cached.cached_at.elapsed() < self.options.cache_ttl {
                return Ok(Some(cached.data.clone()));
            }
        }
        
        Ok(None)
    }

    fn cache_strategy_config(&self, strategy_type: &str, config: &RiskManagementConfig) -> SigmaXResult<()> {
        let mut cache = self.cache.write()
            .map_err(|e| SigmaXError::Internal(format!("Failed to write cache: {}", e)))?;
        
        cache.strategy_configs.insert(strategy_type.to_string(), CachedItem {
            data: config.clone(),
            cached_at: Instant::now(),
        });
        
        Ok(())
    }

    fn get_cached_rules(&self) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.cache.read()
            .map_err(|e| SigmaXError::Internal(format!("Failed to read cache: {}", e)))?;
        
        if let Some(cached) = &cache.rules_cache {
            if cached.cached_at.elapsed() < self.options.cache_ttl {
                return Ok(Some(cached.data.clone()));
            }
        }
        
        Ok(None)
    }

    fn cache_rules(&self, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.cache.write()
            .map_err(|e| SigmaXError::Internal(format!("Failed to write cache: {}", e)))?;
        
        cache.rules_cache = Some(CachedItem {
            data: rules.to_vec(),
            cached_at: Instant::now(),
        });
        
        Ok(())
    }

    fn get_cached_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.cache.read()
            .map_err(|e| SigmaXError::Internal(format!("Failed to read cache: {}", e)))?;
        
        if let Some(cached) = cache.rules_by_type.get(rule_type) {
            if cached.cached_at.elapsed() < self.options.cache_ttl {
                return Ok(Some(cached.data.clone()));
            }
        }
        
        Ok(None)
    }

    fn cache_rules_by_type(&self, rule_type: &str, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.cache.write()
            .map_err(|e| SigmaXError::Internal(format!("Failed to write cache: {}", e)))?;
        
        cache.rules_by_type.insert(rule_type.to_string(), CachedItem {
            data: rules.to_vec(),
            cached_at: Instant::now(),
        });
        
        Ok(())
    }
}

impl<T> CachedItem<T> {
    fn new(data: T) -> Self {
        Self {
            data,
            cached_at: Instant::now(),
        }
    }
}

impl ConfigCache {
    fn new() -> Self {
        Self {
            global_config: None,
            strategy_configs: HashMap::new(),
            rules_cache: None,
            rules_by_type: HashMap::new(),
        }
    }
}
