//! SigmaX 风险管理模块 - 重构版
//!
//! ## 核心设计原则体现
//!
//! ### 1. 高内聚，低耦合
//! - control 子模块：专注实时风控业务逻辑
//! - analytics 子模块：专注风险分析和计算工具
//!
//! ### 2. 关注点分离
//! - control：实时风控决策、订单验证、持仓监控
//! - analytics：风险指标计算、压力测试、分析报告
//!
//! ### 3. 面向接口设计
//! - 依赖 sigmax-interfaces 定义的接口契约
//! - 提供清晰的模块间接口
//!
//! ### 4. 可测试性设计
//! - 每个子模块可独立测试
//! - 支持Mock实现
//!
//! ### 5. 简洁与可演化性
//! - 清晰的模块分工
//! - 易于扩展新功能

// ============================================================================
// 🎯 风控领域子模块 - 专注风控逻辑
// ============================================================================

/// 风控控制子模块 - 实时风控业务逻辑
pub mod control;

// ============================================================================
// 📊 风险分析领域子模块 - 分析计算工具
// ============================================================================

/// 风险分析子模块 - 分析计算工具
pub mod analytics;

// ============================================================================
// 统一导出 - 保持向后兼容
// ============================================================================

// 风控控制相关导出
pub use control::{
    // 服务层
    OrderValidator, PositionMonitor, RiskCalculator as ControlRiskCalculator, AlertManager,
    // 策略层
    BasicRules, PositionLimits, VolatilityControl,
    // 模型层
    RiskContext, RiskMetrics as ControlRiskMetrics,
};

// 风险分析相关导出
pub use analytics::{
    // 计算器
    MetricsCalculator, StressTester, MonteCarloSimulator,
    // 模型
    CorrelationModel, VolatilityModel,
    // 报告
    RiskReporter,
};

// ============================================================================
// 向后兼容导出 - 保持现有接口
// ============================================================================

// 保留现有的核心接口导出
pub use control::services::{
    OrderValidationResult, PositionStatus, PositionRiskLevel, RiskCalculationResult, AlertResult,
    OrderValidatorConfig, PositionMonitorConfig, RiskCalculatorConfig, AlertManagerConfig,
    OrderValidationError, PositionMonitorError, RiskCalculationError, AlertManagerError,
    PositionSummary, AlertStatistics,
};

pub use analytics::calculators::{
    MetricsCalculationResult, StressTestResult, MonteCarloResult
};

// ============================================================================
// 工厂方法 - 统一创建入口
// ============================================================================

/// 风险管理工厂
pub struct RiskManagementFactory;

impl RiskManagementFactory {
    /// 创建风控控制器
    pub async fn create_risk_controller() -> control::RiskControllerImpl {
        use control::services::*;

        // 创建各个服务组件
        let order_validator = OrderValidator::with_default_config();
        let position_monitor = PositionMonitor::with_default_config();
        let risk_calculator = RiskCalculator::with_default_config();
        let alert_manager = AlertManager::with_default_config();

        control::RiskControllerImpl::new(
            order_validator,
            position_monitor,
            risk_calculator,
            alert_manager,
        )
    }

    /// 创建风险分析器
    pub fn create_risk_analyzer() -> analytics::RiskAnalyzerImpl {
        use analytics::*;

        // 创建各个分析组件
        let metrics_calculator = MetricsCalculator::with_default_config();
        let stress_tester = StressTester::with_default_config();
        let monte_carlo_simulator = MonteCarloSimulator::with_default_config();
        let risk_reporter = RiskReporter::with_default_config();

        analytics::RiskAnalyzerImpl::new(
            metrics_calculator,
            stress_tester,
            monte_carlo_simulator,
            risk_reporter,
        )
    }

    /// 创建完整的风险管理系统
    pub async fn create_complete_system() -> RiskManagementSystem {
        let controller = Self::create_risk_controller().await;
        let analyzer = Self::create_risk_analyzer();

        RiskManagementSystem {
            controller,
            analyzer,
        }
    }

    /// 创建自定义风控控制器
    pub async fn create_custom_risk_controller(
        order_validator_config: control::services::OrderValidatorConfig,
        position_monitor_config: control::services::PositionMonitorConfig,
        risk_calculator_config: control::services::RiskCalculatorConfig,
        alert_manager_config: control::services::AlertManagerConfig,
    ) -> control::RiskControllerImpl {
        use control::services::*;

        let order_validator = OrderValidator::new(order_validator_config);
        let position_monitor = PositionMonitor::new(position_monitor_config);
        let risk_calculator = RiskCalculator::new(risk_calculator_config);
        let alert_manager = AlertManager::new(alert_manager_config);

        control::RiskControllerImpl::new(
            order_validator,
            position_monitor,
            risk_calculator,
            alert_manager,
        )
    }

    /// 创建自定义风险分析器
    pub fn create_custom_risk_analyzer(
        metrics_calculator_config: analytics::MetricsCalculatorConfig,
        stress_tester_config: analytics::StressTesterConfig,
        monte_carlo_config: analytics::MonteCarloConfig,
        risk_reporter_config: analytics::RiskReporterConfig,
    ) -> analytics::RiskAnalyzerImpl {
        use analytics::*;

        let metrics_calculator = MetricsCalculator::new(metrics_calculator_config);
        let stress_tester = StressTester::new(stress_tester_config);
        let monte_carlo_simulator = MonteCarloSimulator::new(monte_carlo_config);
        let risk_reporter = RiskReporter::new(risk_reporter_config);

        analytics::RiskAnalyzerImpl::new(
            metrics_calculator,
            stress_tester,
            monte_carlo_simulator,
            risk_reporter,
        )
    }

    /// 创建策略组合器
    pub fn create_policy_combinator(require_all_pass: bool) -> control::policies::PolicyCombinator {
        control::policies::PolicyCombinator::new(require_all_pass)
    }

    /// 创建默认策略集合
    pub fn create_default_policies() -> Vec<Box<dyn sigmax_interfaces::risk::RiskPolicy>> {
        use control::policies::*;

        vec![
            Box::new(BasicRules::with_default_config()),
            Box::new(PositionLimits::with_default_config()),
            Box::new(VolatilityControl::with_default_config()),
        ]
    }
}

/// 完整的风险管理系统
pub struct RiskManagementSystem {
    pub controller: control::RiskControllerImpl,
    pub analyzer: analytics::RiskAnalyzerImpl,
}

// ============================================================================
// 迁移指南
// ============================================================================

/// 迁移指南
/// 
/// ## 新的模块结构
/// ```rust,ignore
/// // 风控业务逻辑 - 使用 control 子模块
/// use sigmax_risk::control::{OrderValidator, PositionMonitor, AlertManager};
/// 
/// // 风险分析计算 - 使用 analytics 子模块  
/// use sigmax_risk::analytics::{MetricsCalculator, StressTester, MonteCarloSimulator};
/// 
/// // 统一工厂创建
/// use sigmax_risk::RiskManagementFactory;
/// let system = RiskManagementFactory::create_complete_system().await;
/// ```
/// 
/// ## 已移除的重复模块
/// 
/// ### 替代方案
/// - `advanced_metrics.rs` → `analytics::calculators::MetricsCalculator`
/// - `correlation_model.rs` → `analytics::models::CorrelationModel`
/// - `credit_model.rs` → `analytics::models::CreditModel`
/// - `liquidity_model.rs` → `analytics::models::LiquidityModel`
/// - `monte_carlo.rs` → `analytics::calculators::MonteCarloSimulator`
/// - `sensitivity.rs` → `analytics::calculators::SensitivityAnalyzer`
/// - `stress_test.rs` → `analytics::calculators::StressTester`
/// - `metrics/` → `analytics::calculators::*`
/// - `services/` → `control::services::*`
/// - `core/` → `control::models::*`
pub mod migration_guide {
    //! 模块迁移指南
    //! 
    //! 所有原有功能都已迁移到新的 control 和 analytics 子模块中
}