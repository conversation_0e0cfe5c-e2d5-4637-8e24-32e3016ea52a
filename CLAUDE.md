# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SigmaX is a high-performance quantitative trading system built in Rust. It's organized as a Cargo workspace with 18 modules covering trading engines, strategies, risk management, data handling, and web interfaces.

## Common Commands

### Build & Development
```bash
cargo build                    # Build entire workspace
cargo test --workspace        # Run all tests (61/61 passing)
cargo run --bin sigmax        # Start main trading server
cargo fmt                     # Format code
cargo clippy -- -D warnings   # Lint with warnings as errors
```

### Testing Specific Modules
```bash
cargo test -p sigmax-strategies  # Test strategies module
cargo test -p sigmax-engines     # Test trading engines
cargo test -p sigmax-risk        # Test risk management
cargo test -p sigmax-core        # Test core functionality
```

### Documentation
```bash
cargo doc --no-deps --open    # Generate and open docs
```

## Architecture Overview

### Workspace Structure
- **`core/`** - Central traits, types, errors, and configuration
- **`engines/`** - Trading engines (Backtest, Live, Paper) with risk adapters
- **`risk/`** - Advanced risk management system with rules engine
- **`strategies/`** - Trading strategies (Grid, Asymmetric Grid, DCA)
- **`data/`** - Market data processing and storage
- **`exchange/`** - Multi-exchange interfaces and simulator
- **`web/`** - REST API server with Axum + WebSocket real-time data
- **`portfolio/`** - Portfolio management and asset tracking

### Key Architectural Patterns

**Trait-Based Design**: Extensive use of traits for modularity:
- `RiskEngine` - Core risk management interface
- `EngineRiskAdapter` - Adapter pattern for different engine types
- `Strategy` - Unified strategy interface
- `TradingEngine` - Engine lifecycle management

**Engine-Risk Architecture**: Each engine type has a corresponding risk adapter:
```
BacktestEngine → BacktestRiskAdapter ↘
LiveEngine     → LiveRiskAdapter     → RiskEngine  
PaperEngine    → PaperRiskAdapter   ↗
```

**Event-Driven**: Async-first with Tokio, event bus for decoupled communication.

## Technology Stack

- **Runtime**: Tokio async runtime
- **Web**: Axum framework with WebSocket support
- **Database**: PostgreSQL with SQLx (compile-time checked queries)
- **Caching**: Redis for high-performance data caching
- **Serialization**: Serde for JSON/binary data

## Environment Setup

```bash
export DATABASE_URL="postgresql://user:pass@host:port/db"
export WEB_HOST="0.0.0.0"
export WEB_PORT="8080"
export RUST_LOG="info"
```

## Configuration System

Multi-layer configuration with hot reload capabilities:
- System config (database, logging, web server)
- Trading config (exchanges, pairs, limits)
- Risk config (rules, thresholds, monitoring)
- Strategy config (strategy-specific parameters)

## Development Notes

- **Error Handling**: Uses `thiserror` for comprehensive error types
- **Testing**: 100% test coverage (61/61 tests passing)
- **Performance**: Optimized for high-throughput, low-latency trading
- **Type Safety**: Leverages Rust's ownership system extensively
- **Documentation**: Inline rustdoc with examples throughout

## Risk Management

The risk system is centralized with engine-specific adapters. Key files:
- `risk/src/core/` - Core risk engine implementation
- `engines/src/*/risk_adapter.rs` - Engine-specific risk adapters
- Risk rules are configurable and support hot reload

## Running the System

1. Ensure PostgreSQL is running and DATABASE_URL is set
2. Run `cargo run --bin sigmax` to start the main server
3. Access web interface at `http://localhost:8080`
4. WebSocket endpoint available for real-time data streams