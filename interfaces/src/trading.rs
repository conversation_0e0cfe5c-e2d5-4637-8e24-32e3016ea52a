//! 交易相关接口定义
//!
//! 定义交易编排、订单管理、投资组合管理的接口契约

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, Balance, SigmaXResult};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

// ============================================================================
// 核心交易接口
// ============================================================================

/// 交易编排器接口 - 协调各个服务
#[async_trait]
pub trait TradingOrchestrator: Send + Sync {
    /// 提交交易请求
    async fn submit_trade(&self, request: &TradeRequest) -> TradeResult;
    
    /// 获取投资组合
    async fn get_portfolio(&self) -> Portfolio;
    
    /// 获取交易状态
    async fn get_trade_status(&self, trade_id: &Uuid) -> Option<TradeStatus>;
    
    /// 取消交易
    async fn cancel_trade(&self, trade_id: &Uuid) -> TradeResult;
}

/// 订单管理器接口
#[async_trait]
pub trait OrderManager: Send + Sync {
    /// 创建订单
    async fn create_order(&self, order: &Order) -> OrderResult;
    
    /// 更新订单
    async fn update_order(&self, order_id: &Uuid, updates: &OrderUpdate) -> OrderResult;
    
    /// 取消订单
    async fn cancel_order(&self, order_id: &Uuid) -> OrderResult;
    
    /// 获取订单状态
    async fn get_order_status(&self, order_id: &Uuid) -> Option<OrderStatus>;
    
    /// 获取活跃订单
    async fn get_active_orders(&self) -> Vec<Order>;
}

/// 投资组合管理器接口
#[async_trait]
pub trait PortfolioManager: Send + Sync {
    /// 获取投资组合
    async fn get_portfolio(&self) -> Portfolio;
    
    /// 更新投资组合
    async fn update_portfolio(&self, updates: &PortfolioUpdate) -> Result<(), PortfolioError>;
    
    /// 获取余额
    async fn get_balances(&self) -> Vec<Balance>;
    
    /// 计算投资组合价值
    async fn calculate_portfolio_value(&self) -> Decimal;
    
    /// 获取投资组合历史
    async fn get_portfolio_history(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Vec<PortfolioSnapshot>;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 交易请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRequest {
    pub id: Uuid,
    pub order: Order,
    pub strategy_id: Option<Uuid>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

/// 交易结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeResult {
    pub trade_id: Uuid,
    pub status: TradeStatus,
    pub message: String,
    pub order_id: Option<Uuid>,
    pub executed_at: Option<DateTime<Utc>>,
}

/// 交易状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeStatus {
    /// 待处理
    Pending,
    /// 风控检查中
    RiskChecking,
    /// 执行中
    Executing,
    /// 已完成
    Completed,
    /// 已取消
    Cancelled,
    /// 失败
    Failed(String),
    /// 部分成交
    PartiallyFilled,
}

/// 投资组合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Portfolio {
    pub id: Uuid,
    pub balances: Vec<Balance>,
    pub total_value: Decimal,
    pub currency: String,
    pub updated_at: DateTime<Utc>,
}

/// 投资组合快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSnapshot {
    pub timestamp: DateTime<Utc>,
    pub portfolio: Portfolio,
    pub pnl: Decimal,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 订单更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderUpdate {
    pub status: Option<OrderStatus>,
    pub filled_quantity: Option<Decimal>,
    pub average_price: Option<Decimal>,
    pub updated_at: DateTime<Utc>,
}

/// 订单状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    Submitted,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Expired,
}

/// 投资组合更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioUpdate {
    pub balances: Option<Vec<Balance>>,
    pub updated_at: DateTime<Utc>,
}

/// 订单结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderResult {
    pub order_id: Uuid,
    pub status: OrderStatus,
    pub message: String,
    pub created_at: DateTime<Utc>,
}

// ============================================================================
// 错误类型
// ============================================================================

/// 交易错误
#[derive(Debug, thiserror::Error)]
pub enum TradeError {
    #[error("Risk rejection: {reason}")]
    RiskRejection { reason: String },
    
    #[error("Execution failed: {reason}")]
    ExecutionFailed { reason: String },
    
    #[error("Insufficient balance: need {required}, have {available}")]
    InsufficientBalance { required: Decimal, available: Decimal },
    
    #[error("Invalid trade request: {reason}")]
    InvalidRequest { reason: String },
    
    #[error("Trade not found: {trade_id}")]
    NotFound { trade_id: Uuid },
    
    #[error("Service unavailable: {service}")]
    ServiceUnavailable { service: String },
    
    #[error("Timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
}

/// 订单错误
#[derive(Debug, thiserror::Error)]
pub enum OrderError {
    #[error("Order creation failed: {reason}")]
    CreationFailed { reason: String },
    
    #[error("Order not found: {order_id}")]
    NotFound { order_id: Uuid },
    
    #[error("Order update failed: {reason}")]
    UpdateFailed { reason: String },
    
    #[error("Invalid order status transition: {from} -> {to}")]
    InvalidStatusTransition { from: String, to: String },
    
    #[error("Order already cancelled: {order_id}")]
    AlreadyCancelled { order_id: Uuid },
}

/// 投资组合错误
#[derive(Debug, thiserror::Error)]
pub enum PortfolioError {
    #[error("Portfolio calculation failed: {reason}")]
    CalculationFailed { reason: String },
    
    #[error("Portfolio update failed: {reason}")]
    UpdateFailed { reason: String },
    
    #[error("Portfolio data inconsistent: {reason}")]
    DataInconsistent { reason: String },
    
    #[error("Portfolio not found: {portfolio_id}")]
    NotFound { portfolio_id: Uuid },
}