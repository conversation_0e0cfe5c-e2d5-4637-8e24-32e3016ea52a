//! 风控相关接口定义
//!
//! 实现面向接口设计，定义清晰的风控接口契约

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, Balance, SigmaXResult};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

// ============================================================================
// 核心风控接口
// ============================================================================

/// 风控控制器接口 - 核心风控逻辑
#[async_trait]
pub trait RiskController: Send + Sync {
    /// 验证订单风控
    async fn validate_order(&self, order: &Order) -> RiskResult;
    
    /// 检查持仓限制
    async fn check_position_limits(&self, portfolio: &Portfolio) -> RiskResult;
    
    /// 计算风险指标
    async fn calculate_risk_metrics(&self, context: &RiskContext) -> RiskMetrics;
    
    /// 实时风险监控
    async fn monitor_risk(&self, context: &RiskContext) -> Vec<RiskAlert>;
}

/// 风控策略接口
pub trait RiskPolicy: Send + Sync {
    /// 评估风控策略
    fn evaluate(&self, context: &RiskContext) -> PolicyResult;
    
    /// 获取策略名称
    fn name(&self) -> &str;
    
    /// 获取策略描述
    fn description(&self) -> &str;
}

/// 风控计算器接口
#[async_trait]
pub trait RiskCalculator: Send + Sync {
    /// 计算基础风控指标
    async fn calculate_basic_metrics(&self, context: &RiskContext) -> BasicRiskMetrics;
    
    /// 计算高级风控指标
    async fn calculate_advanced_metrics(&self, context: &RiskContext) -> AdvancedRiskMetrics;
    
    /// 计算投资组合风险
    async fn calculate_portfolio_risk(&self, portfolio: &Portfolio) -> PortfolioRisk;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 风控结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskResult {
    /// 通过风控检查
    Approved,
    /// 被风控拒绝
    Rejected(String),
    /// 需要人工审核
    RequiresApproval(String),
    /// 风控检查出错
    Error(String),
}

impl RiskResult {
    pub fn is_approved(&self) -> bool {
        matches!(self, RiskResult::Approved)
    }
    
    pub fn is_rejected(&self) -> bool {
        matches!(self, RiskResult::Rejected(_))
    }
}

/// 风控上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskContext {
    pub order: Option<Order>,
    pub portfolio: Option<Portfolio>,
    pub balances: Vec<Balance>,
    pub market_data: Option<MarketData>,
    pub timestamp: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 投资组合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Portfolio {
    pub balances: Vec<PortfolioBalance>,
    pub total_value: Decimal,
    pub updated_at: DateTime<Utc>,
}

/// 投资组合余额（扩展了 Balance 以包含风控所需字段）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioBalance {
    pub asset: String,
    pub available: Decimal,  // 可用余额
    pub locked: Decimal,     // 冻结余额
    pub borrowed: Decimal,   // 借贷金额
    pub total: Decimal,      // 总余额
    pub price: Option<Decimal>, // 资产价格
}

impl From<Balance> for PortfolioBalance {
    fn from(balance: Balance) -> Self {
        Self {
            asset: balance.asset,
            available: balance.free,
            locked: balance.locked,
            borrowed: Decimal::ZERO,
            total: balance.free + balance.locked,
            price: None,
        }
    }
}

/// 市场数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub symbol: String,
    pub price: Decimal,
    pub bid_price: Option<Decimal>,
    pub ask_price: Option<Decimal>,
    pub bid_size: Option<Decimal>,
    pub ask_size: Option<Decimal>,
    pub volume_24h: Option<Decimal>,
    pub high_24h: Option<Decimal>,
    pub low_24h: Option<Decimal>,
    pub timestamp: DateTime<Utc>,
}

/// 风控指标（统一接口）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub exposure: Decimal,
    pub leverage: Decimal,
    pub var: Option<Decimal>, // Value at Risk
    pub max_drawdown: Option<Decimal>,
    pub sharpe_ratio: Option<Decimal>,
    pub calculated_at: DateTime<Utc>,
}

/// 基础风控指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BasicRiskMetrics {
    pub volatility: Option<Decimal>,
    pub leverage: Option<Decimal>,
    pub concentration: Option<Decimal>,
    pub liquidity_score: Option<Decimal>,
    pub calculated_at: DateTime<Utc>,
}

/// 高级风控指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedRiskMetrics {
    pub var_1d: Option<Decimal>,
    pub var_5d: Option<Decimal>,
    pub expected_shortfall: Option<Decimal>,
    pub beta: Option<Decimal>,
    pub correlation: Option<HashMap<String, Decimal>>,
}

/// 投资组合风险
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioRisk {
    pub total_exposure: Decimal,
    pub concentration_risk: Decimal,
    pub correlation_risk: Decimal,
    pub liquidity_risk: Decimal,
}

/// 风控告警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    pub id: Uuid,
    pub severity: AlertSeverity,
    pub message: String,
    pub source: String,
    pub timestamp: DateTime<Utc>,
}

/// 告警严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Low,
    Medium,
    High,
    Critical,
    Emergency,
}

/// 策略结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyResult {
    /// 策略通过
    Approved,
    /// 策略拒绝
    Rejected(String),
    /// 需要人工审核
    RequiresApproval(String),
    /// 策略警告（向后兼容）
    Warning(String),
    /// 策略错误
    Error(String),
}

// ============================================================================
// 错误类型
// ============================================================================

/// 风控错误
#[derive(Debug, thiserror::Error)]
pub enum RiskError {
    #[error("Risk calculation failed: {message}")]
    CalculationFailed { message: String },
    
    #[error("Risk policy violation: {policy} - {reason}")]
    PolicyViolation { policy: String, reason: String },
    
    #[error("Insufficient data for risk assessment: {missing}")]
    InsufficientData { missing: String },
    
    #[error("Risk service unavailable: {service}")]
    ServiceUnavailable { service: String },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("Timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
}

/// 策略错误
#[derive(Debug, thiserror::Error)]
pub enum PolicyError {
    #[error("Policy evaluation failed: {policy} - {reason}")]
    EvaluationFailed { policy: String, reason: String },
    
    #[error("Policy not found: {name}")]
    NotFound { name: String },
    
    #[error("Policy configuration invalid: {message}")]
    InvalidConfiguration { message: String },
}