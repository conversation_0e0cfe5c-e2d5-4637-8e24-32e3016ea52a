
graph TD
    A[策略层 Strategy] --> B[订单创建]
    B --> C[风险检查 RiskManager]
    C --> D[订单管理器 OrderManager]
    D --> E[执行引擎选择]
    E --> F1[回测引擎 BacktestEngine]
    E --> F2[实盘引擎 LiveEngine]
    E --> F3[纸上交易 PaperEngine]
    F1 --> G1[模拟执行]
    F2 --> G2[交易所路由 OrderRouter]
    F3 --> G3[模拟执行]
    G2 --> H1[Binance Exchange]
    G2 --> H2[Coinbase Exchange]
    G2 --> H3[Kraken Exchange]
    H1 --> I[订单状态回调]
    H2 --> I
    H3 --> I
    I --> J[策略状态更新]



    stateDiagram-v2
    [*] --> Created: 策略创建订单
    Created --> Validated: 风险检查通过
    Validated --> Pending: 提交到交易所
    Pending --> PartiallyFilled: 部分成交
    Pending --> Filled: 完全成交
    Pending --> Cancelled: 订单取消
    Pending --> Rejected: 交易所拒绝
    PartiallyFilled --> Filled: 完全成交
    PartiallyFilled --> Cancelled: 取消剩余
    Filled --> [*]
    Cancelled --> [*]
    Rejected --> [*]