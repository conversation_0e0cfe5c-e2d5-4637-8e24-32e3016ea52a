//! 新策略架构使用示例
//!
//! 展示如何使用重构后的简化策略系统

use sigmax_strategies::{
    core::{StrategyFactory, StrategyConfig},
    asymmetric_grid::AsymmetricGridConfig,
};

fn main() {
    println!("🚀 SigmaX 新策略架构示例");
    println!("========================");

    // 1. 展示支持的策略类型
    println!("📋 支持的策略类型:");
    for strategy_type in StrategyFactory::supported_strategies() {
        println!("   - {}", strategy_type);
    }

    // 2. 创建策略配置
    println!("\n⚙️  创建策略配置...");
    let config = AsymmetricGridConfig::balanced();
    println!("   配置类型: {}", config.strategy_type());
    println!("   配置描述: {}", config.description());

    // 3. 验证配置
    println!("\n✅ 验证配置...");
    match config.validate() {
        Ok(()) => println!("   配置验证通过"),
        Err(e) => println!("   配置验证失败: {}", e),
    }

    println!("\n🎉 示例完成！新架构运行正常。");
    println!("\n📈 架构优势:");
    println!("   ✓ 从21个模块简化到7个文件");
    println!("   ✓ 清晰的模块边界和职责");
    println!("   ✓ 简单的3步扩展流程");
    println!("   ✓ 无过度工程化设计");
    println!("   ✓ 易于测试和维护");

    println!("\n🔧 如何添加新策略:");
    println!("   1. 创建 src/dca/ 目录");
    println!("   2. 实现 Strategy trait");
    println!("   3. 在 factory.rs 中添加:");
    println!("      \"dca\" => create_dca_strategy(config, services).await");
    println!("   4. 在 lib.rs 中导出新类型");
    println!("   ✅ 完成！无需修改现有代码");
}
