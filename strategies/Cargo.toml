[package]
name = "sigmax-strategies"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
sigmax-core.workspace = true
sigmax-execution = { path = "../execution" }
sigmax-risk = { path = "../risk" }
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
uuid.workspace = true
rust_decimal.workspace = true
anyhow.workspace = true
tracing.workspace = true
async-trait = "0.1"
ctor = "0.2"
tracing-subscriber = "0.3"
thiserror = "1.0"

[[example]]
name = "new_architecture_example"
path = "examples/new_architecture_example.rs"
