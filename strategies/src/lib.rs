//! SigmaX Strategies Module
//!
//! 简化的策略系统，遵循YAGNI原则和最佳实践
//!
//! ## 设计原则
//! - **YAGNI原则**：只实现当前需要的功能
//! - **单一职责**：每个模块只负责一个明确的功能
//! - **开放封闭**：对扩展开放，对修改封闭
//! - **组合优于继承**：使用trait组合而不是复杂的继承层次
//!
//! ## 模块结构
//! - `core`: 核心抽象和服务容器
//! - `asymmetric_grid`: 非对称波动率网格策略
//! - `utils`: 通用工具和辅助功能
//!
//! ## 使用示例
//! ```rust
//! use strategies::{
//!     core::{StrategyFactory, StrategyServices},
//!     asymmetric_grid::AsymmetricGridConfig,
//! };
//!
//! // 创建服务容器
//! let services = StrategyServices::new(
//!     portfolio_manager,
//!     risk_manager,
//!     data_provider,
//!     order_manager,
//! );
//!
//! // 创建策略配置
//! let config = AsymmetricGridConfig::balanced();
//!
//! // 创建策略实例
//! let strategy = StrategyFactory::create_strategy(
//!     "asymmetric_volatility_grid",
//!     serde_json::to_value(config)?,
//!     services,
//! ).await?;
//! ```

pub mod core;
pub mod asymmetric_grid;
pub mod utils;

// 重新导出核心类型
pub use core::{
    StrategyFactory, StrategyServices, StrategyServicesBuilder, StrategyType, StrategyTypeConversion,
    StrategyServiceContainerAdapter, StrategyServiceContainerAdapterBuilder, OrderExecutorAdapter
};
pub use asymmetric_grid::{
    AsymmetricVolatilityGridStrategy,
    AsymmetricGridConfig,
    AsymmetricGridState,
    GridLevel,
    GridZone,
};
pub use utils::{StrategyValidator, TechnicalIndicators, VolatilityCalculator};

// 🔥 未来添加新策略时，只需在这里添加导出
// pub use dca::{DollarCostAveragingStrategy, DcaConfig};
// pub use momentum::{MomentumStrategy, MomentumConfig};
// pub use mean_reversion::{MeanReversionStrategy, MeanReversionConfig};
