//! 单策略引擎集成模块
//!
//! 集成市场信号检测、策略管理和风险控制，专注于单策略模式的稳定运行
//! 智能切换功能暂时注释，确保系统稳定性

use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc, Duration};
use sigmax_core::{SigmaXResult, SigmaXError, Strategy, Candle, Order};
use tracing::{info, debug, warn, error};
use rust_decimal::prelude::ToPrimitive;

use super::market_signal::{MarketSignalDetector, MarketSignal, MarketState};
use super::strategy_switcher::{IntelligentStrategySwitcher, SwitchDecision, SwitchAction};
use super::performance_tracker::{StrategyPerformanceTracker, PerformanceMetrics, TradeRecord};
use super::risk_manager::{StrategySwitchRiskManager, SwitchRiskAssessment};
use super::strategy_type::StrategyType;

/// 单策略引擎配置
#[derive(Debug, Clone)]
pub struct SingleStrategyEngineConfig {
    /// 初始资金
    pub initial_capital: f64,
    /// 市场数据更新间隔
    pub market_data_interval: Duration,
    /// 性能评估间隔
    pub performance_evaluation_interval: Duration,
    /// 是否启用智能切换（当前强制为false）
    pub enable_intelligent_switching: bool,
}

impl Default for SingleStrategyEngineConfig {
    fn default() -> Self {
        Self {
            initial_capital: 100000.0,
            market_data_interval: Duration::seconds(60),
            performance_evaluation_interval: Duration::minutes(5),
            enable_intelligent_switching: false, // 🔒 智能切换暂时禁用
        }
    }
}

/// 单策略引擎状态
#[derive(Debug, Clone, PartialEq)]
pub enum SingleStrategyEngineStatus {
    Stopped,
    Starting,
    Running,
    Paused,
    Stopping,
    Error(String),
}

/// 单策略引擎
/// 
/// 专注于单策略模式的稳定运行，为未来的智能切换做准备
pub struct SingleStrategyEngine {
    /// 配置
    config: SingleStrategyEngineConfig,
    /// 当前状态
    status: RwLock<SingleStrategyEngineStatus>,
    /// 当前策略
    current_strategy: RwLock<Option<Arc<dyn Strategy>>>,
    /// 市场信号检测器
    market_signal_detector: RwLock<MarketSignalDetector>,
    /// 策略切换器（暂时不启用）
    strategy_switcher: RwLock<IntelligentStrategySwitcher>,
    /// 性能跟踪器
    performance_tracker: RwLock<Option<StrategyPerformanceTracker>>,
    /// 风险管理器
    risk_manager: RwLock<StrategySwitchRiskManager>,
    /// 最后更新时间
    last_update_time: RwLock<Option<DateTime<Utc>>>,
}

impl SingleStrategyEngine {
    /// 创建新的单策略引擎
    pub fn new(config: SingleStrategyEngineConfig) -> Self {
        Self {
            config,
            status: RwLock::new(SingleStrategyEngineStatus::Stopped),
            current_strategy: RwLock::new(None),
            market_signal_detector: RwLock::new(MarketSignalDetector::with_default_config()),
            strategy_switcher: RwLock::new(IntelligentStrategySwitcher::with_default_config()),
            performance_tracker: RwLock::new(None),
            risk_manager: RwLock::new(StrategySwitchRiskManager::with_default_config()),
            last_update_time: RwLock::new(None),
        }
    }
    
    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(SingleStrategyEngineConfig::default())
    }
    
    /// 设置策略
    pub async fn set_strategy(&self, strategy: Arc<dyn Strategy>) -> SigmaXResult<()> {
        let mut current_strategy = self.current_strategy.write().await;
        let mut performance_tracker = self.performance_tracker.write().await;
        let mut strategy_switcher = self.strategy_switcher.write().await;
        
        // 如果已有策略，先停止它
        if let Some(existing_strategy) = current_strategy.as_ref() {
            warn!("替换现有策略: {} -> {}", existing_strategy.name(), strategy.name());
            existing_strategy.stop().await?;
        }
        
        info!("设置新策略: {}", strategy.name());
        
        // 设置新策略
        *current_strategy = Some(strategy.clone());
        
        // 创建性能跟踪器
        let strategy_type = StrategyType::AsymmetricVolatilityGrid; // 简化实现
        *performance_tracker = Some(StrategyPerformanceTracker::new(
            strategy.id(),
            strategy_type,
            self.config.initial_capital,
        ));

        // 更新策略切换器
        strategy_switcher.set_current_strategy(Some(strategy_type));
        
        Ok(())
    }
    
    /// 启动引擎
    pub async fn start(&self) -> SigmaXResult<()> {
        let mut status = self.status.write().await;
        
        if matches!(*status, SingleStrategyEngineStatus::Running) {
            return Err(SigmaXError::InvalidState("引擎已在运行".to_string()));
        }
        
        *status = SingleStrategyEngineStatus::Starting;
        drop(status);
        
        // 检查是否有策略
        let current_strategy = self.current_strategy.read().await;
        if current_strategy.is_none() {
            let mut status = self.status.write().await;
            *status = SingleStrategyEngineStatus::Error("没有设置策略".to_string());
            return Err(SigmaXError::InvalidState("启动前必须设置策略".to_string()));
        }
        
        // 初始化策略
        if let Some(strategy) = current_strategy.as_ref() {
            info!("初始化策略: {}", strategy.name());
            strategy.initialize().await?;
        }
        drop(current_strategy);
        
        // 设置为运行状态
        let mut status = self.status.write().await;
        *status = SingleStrategyEngineStatus::Running;
        
        info!("单策略引擎启动成功");
        Ok(())
    }
    
    /// 停止引擎
    pub async fn stop(&self) -> SigmaXResult<()> {
        let mut status = self.status.write().await;
        *status = SingleStrategyEngineStatus::Stopping;
        drop(status);
        
        // 停止当前策略
        let current_strategy = self.current_strategy.read().await;
        if let Some(strategy) = current_strategy.as_ref() {
            info!("停止策略: {}", strategy.name());
            strategy.stop().await?;
        }
        drop(current_strategy);
        
        // 设置为停止状态
        let mut status = self.status.write().await;
        *status = SingleStrategyEngineStatus::Stopped;
        
        info!("单策略引擎停止成功");
        Ok(())
    }
    
    /// 处理市场数据
    pub async fn process_market_data(&self, candle: &Candle) -> SigmaXResult<()> {
        // 检查引擎状态
        let status = self.status.read().await;
        if !matches!(*status, SingleStrategyEngineStatus::Running) {
            return Ok(()); // 非运行状态，跳过处理
        }
        drop(status);
        
        debug!("处理市场数据: {:?}", candle);
        
        // 1. 更新市场信号检测器
        let market_signal = {
            let mut detector = self.market_signal_detector.write().await;
            detector.update_and_analyze(candle).await?
        };
        
        // 2. 更新风险管理器的市场条件
        {
            let mut risk_manager = self.risk_manager.write().await;
            risk_manager.update_market_conditions(market_signal.clone()).await?;
        }
        
        // 3. 🔒 智能切换功能暂时注释
        // 当前专注于单策略模式的稳定运行
        /*
        if self.config.enable_intelligent_switching {
            let switch_decision = {
                let mut switcher = self.strategy_switcher.write().await;
                let detector = self.market_signal_detector.read().await;
                switcher.analyze_switch_decision(&market_signal, detector.get_current_state()).await?
            };
            
            self.handle_switch_decision(switch_decision).await?;
        }
        */
        
        // 4. 处理当前策略
        let current_strategy = self.current_strategy.read().await;
        if let Some(strategy) = current_strategy.as_ref() {
            match strategy.on_market_data(candle).await {
                Ok(orders) => {
                    debug!("策略 {} 生成 {} 个订单", strategy.name(), orders.len());
                    self.process_orders(orders).await?;
                }
                Err(e) => {
                    error!("策略 {} 处理市场数据失败: {}", strategy.name(), e);
                }
            }
        }
        
        // 5. 更新最后处理时间
        {
            let mut last_update = self.last_update_time.write().await;
            *last_update = Some(Utc::now());
        }
        
        Ok(())
    }
    
    /// 处理订单
    async fn process_orders(&self, orders: Vec<Order>) -> SigmaXResult<()> {
        for order in orders {
            // 简化实现：模拟订单执行
            debug!("模拟执行订单: {:?}", order);
            
            // 记录交易到性能跟踪器
            if let Some(performance_tracker) = self.performance_tracker.write().await.as_mut() {
                let trade_record = TradeRecord {
                    trade_id: uuid::Uuid::new_v4(),
                    strategy_id: order.strategy_id.unwrap_or_default(),
                    timestamp: Utc::now(),
                    symbol: format!("{:?}", order.trading_pair),
                    side: format!("{:?}", order.side),
                    quantity: order.quantity.to_f64().unwrap_or(0.0),
                    price: order.price.unwrap_or_default().to_f64().unwrap_or(0.0),
                    pnl: 0.0, // 简化实现
                    commission: order.quantity.to_f64().unwrap_or(0.0) * order.price.unwrap_or_default().to_f64().unwrap_or(0.0) * 0.001, // 0.1% 手续费
                    market_state: MarketState::Unknown, // 简化实现
                };

                performance_tracker.record_trade(trade_record).await?;
            }
        }
        
        Ok(())
    }
    
    /// 获取当前状态
    pub async fn get_status(&self) -> SingleStrategyEngineStatus {
        let status = self.status.read().await;
        status.clone()
    }
    
    /// 获取当前策略信息
    pub async fn get_current_strategy_info(&self) -> Option<String> {
        let current_strategy = self.current_strategy.read().await;
        current_strategy.as_ref().map(|strategy| {
            format!("策略: {}, 状态: {:?}", strategy.name(), strategy.status())
        })
    }
    
    /// 获取性能指标
    pub async fn get_performance_metrics(&self) -> SigmaXResult<Option<PerformanceMetrics>> {
        let performance_tracker = self.performance_tracker.read().await;
        if let Some(tracker) = performance_tracker.as_ref() {
            Ok(Some(tracker.calculate_metrics().await?))
        } else {
            Ok(None)
        }
    }
    
    /// 获取市场状态
    pub async fn get_market_state(&self) -> MarketState {
        let detector = self.market_signal_detector.read().await;
        detector.get_current_state().clone()
    }
    
    /// 检查引擎健康状态
    pub async fn health_check(&self) -> SigmaXResult<bool> {
        let status = self.status.read().await;
        let has_strategy = self.current_strategy.read().await.is_some();
        let last_update = self.last_update_time.read().await;
        
        // 检查基本状态
        if !matches!(*status, SingleStrategyEngineStatus::Running) {
            return Ok(false);
        }
        
        // 检查是否有策略
        if !has_strategy {
            return Ok(false);
        }
        
        // 检查最后更新时间（如果超过5分钟没有更新，认为不健康）
        if let Some(last_time) = *last_update {
            let now = Utc::now();
            if now - last_time > Duration::minutes(5) {
                warn!("引擎超过5分钟没有处理市场数据");
                return Ok(false);
            }
        }
        
        Ok(true)
    }
}
