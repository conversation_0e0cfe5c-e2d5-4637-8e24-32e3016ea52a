//! SigmaX策略核心模块
//!
//! 提供策略系统的核心抽象和服务，遵循最小化设计原则
//!
//! ## 设计原则
//! - **YAGNI原则**：只实现当前需要的功能
//! - **单一职责**：每个模块只负责一个明确的功能
//! - **开放封闭**：对扩展开放，对修改封闭
//! - **组合优于继承**：使用trait组合而不是复杂的继承层次

pub mod strategy;
pub mod factory;
pub mod services;
pub mod strategy_type;
pub mod market_signal;
pub mod strategy_switcher;
pub mod performance_tracker;
pub mod risk_manager;
pub mod single_strategy_engine;

// 重新导出核心类型
pub use strategy::{Strategy, StrategyConfig, StrategyState};
pub use factory::StrategyFactory;
pub use services::{
    StrategyServices, StrategyServicesBuilder,
    StrategyServiceContainerAdapter, StrategyServiceContainerAdapterBuilder,
    OrderExecutorAdapter
};
pub use strategy_type::{StrategyType, StrategyTypeConversion};
pub use market_signal::{MarketSignalDetector, MarketState, MarketSignal};
pub use strategy_switcher::{IntelligentStrategySwitcher, SwitchDecision};
pub use performance_tracker::{StrategyPerformanceTracker, PerformanceMetrics, TradeRecord};
pub use risk_manager::{StrategySwitchRiskManager, SwitchRiskAssessment, RiskFactor};
pub use single_strategy_engine::{SingleStrategyEngine, SingleStrategyEngineConfig, SingleStrategyEngineStatus};
