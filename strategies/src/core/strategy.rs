//! 策略核心抽象定义
//!
//! 重新导出core模块的Strategy trait，保持一致性

use serde::{Serialize, Deserialize};
use sigmax_core::SigmaXResult;

// 重新导出core模块的Strategy trait
pub use sigmax_core::Strategy;

/// 策略配置trait
///
/// 所有策略配置都必须实现此trait
pub trait StrategyConfig: Send + Sync + Clone + Serialize + for<'de> Deserialize<'de> {
    /// 验证配置有效性
    ///
    /// # 返回
    /// - `Ok(())`: 配置有效
    /// - `Err(SigmaXError)`: 配置无效，包含错误信息
    fn validate(&self) -> SigmaXResult<()>;

    /// 获取策略类型标识
    fn strategy_type(&self) -> &'static str;

    /// 获取配置描述（用于日志和调试）
    fn description(&self) -> String {
        format!("{} strategy configuration", self.strategy_type())
    }
}

/// 策略状态枚举
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum StrategyState {
    /// 已创建但未初始化
    Created,
    /// 正在初始化
    Initializing,
    /// 已初始化，准备运行
    Ready,
    /// 正在运行
    Running,
    /// 已暂停
    Paused,
    /// 已停止
    Stopped,
    /// 错误状态
    Error,
}

impl Default for StrategyState {
    fn default() -> Self {
        Self::Created
    }
}

impl StrategyState {
    /// 检查是否可以启动
    pub fn can_start(&self) -> bool {
        matches!(self, Self::Ready | Self::Paused)
    }

    /// 检查是否可以暂停
    pub fn can_pause(&self) -> bool {
        matches!(self, Self::Running)
    }

    /// 检查是否可以停止
    pub fn can_stop(&self) -> bool {
        matches!(self, Self::Running | Self::Paused | Self::Error)
    }

    /// 检查是否处于活跃状态
    pub fn is_active(&self) -> bool {
        matches!(self, Self::Running)
    }
}
