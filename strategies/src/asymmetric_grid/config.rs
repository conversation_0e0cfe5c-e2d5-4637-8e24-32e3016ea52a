//! 非对称波动率网格策略配置
//!
//! 提供完整的策略配置结构和验证逻辑

use serde::{Deserialize, Serialize};
use sigmax_core::{SigmaXResult, SigmaXError};
use crate::core::StrategyConfig;

/// 非对称波动率网格策略配置
///
/// ## 配置说明
///
/// ### 基础配置
/// - `base_price`: 基准价格，0表示使用当前市价
/// - `order_amount`: 每个网格的订单金额
///
/// ### 下跌区间配置（吸筹区）
/// - `down_range_start`: 下跌区间起始百分比（如-2%）
/// - `down_range_end`: 下跌区间结束百分比（如-10%）
/// - `down_grid_count`: 下跌区间网格数量
/// - `down_base_quantity`: 下跌区间每格基础数量
///
/// ### 上涨区间配置（止盈区）
/// - `up_range_start`: 上涨区间起始百分比（如+2%）
/// - `up_range_end`: 上涨区间结束百分比（如+15%）
/// - `up_grid_count`: 上涨区间网格数量
/// - `up_base_quantity`: 上涨区间每格基础数量
///
/// ### 波动率配置
/// - `volatility_window_hours`: 波动率计算窗口（小时）
/// - `volatility_multiplier`: 波动率乘数（调整网格间距）
/// - `enable_dynamic_volatility`: 是否启用动态波动率调整
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AsymmetricGridConfig {
    // 基础配置
    /// 基准价格（0表示使用当前市价）
    pub base_price: f64,
    /// 每个网格的订单金额
    pub order_amount: f64,

    // 交易配置 - 新增参数化支持
    /// 交易对（如 "BTC/USDT"）
    #[serde(default = "default_trading_pair")]
    pub trading_pair: String,
    /// 交易所ID
    #[serde(default = "default_exchange_id")]
    pub exchange_id: String,

    // 下跌区间配置（吸筹区）
    /// 下跌区间起始百分比（如-0.02表示-2%）
    pub down_range_start: f64,
    /// 下跌区间结束百分比（如-0.10表示-10%）
    pub down_range_end: f64,
    /// 下跌区间网格数量
    pub down_grid_count: u32,
    /// 下跌区间每格基础数量
    pub down_base_quantity: f64,
    /// 下跌区间网格分布算法
    #[serde(default)]
    pub down_grid_distribution: GridDistribution,
    /// 下跌区间数量模型
    #[serde(default)]
    pub down_quantity_model: QuantityModel,

    // 上涨区间配置（止盈区）
    /// 上涨区间起始百分比（如0.02表示+2%）
    pub up_range_start: f64,
    /// 上涨区间结束百分比（如0.15表示+15%）
    pub up_range_end: f64,
    /// 上涨区间网格数量
    pub up_grid_count: u32,
    /// 上涨区间每格基础数量
    pub up_base_quantity: f64,
    /// 上涨区间网格分布算法
    #[serde(default)]
    pub up_grid_distribution: GridDistribution,
    /// 上涨区间数量模型
    #[serde(default)]
    pub up_quantity_model: QuantityModel,

    // 波动率配置
    /// 波动率计算窗口（小时）
    pub volatility_window_hours: u32,
    /// 波动率乘数（调整网格间距）
    pub volatility_multiplier: f64,
    /// 是否启用动态波动率调整
    pub enable_dynamic_volatility: bool,

    // 风险控制配置
    /// 最大持仓金额
    pub max_position_amount: f64,
    /// 最大单日交易次数
    pub max_daily_trades: u32,
    /// 止损配置
    /// 支持多种动态止损策略
    #[serde(default)]
    pub stop_loss_config: StopLossConfig,

    // 预设策略类型
    pub strategy_preset: AsymmetricStrategyPreset,

    // 智能化与自适应配置
    /// 网格重置阈值百分比
    /// 当市价偏离基准价超过此百分比时，触发网格重置
    /// 例如：0.15 表示偏离15%时重置，None 表示禁用此功能
    pub recenter_threshold_percent: Option<f64>,

    /// 市场状态过滤器配置
    /// 用于判断市场宏观趋势，在不同市场状态下调整策略行为
    pub regime_filter: Option<RegimeFilterConfig>,
}

/// 移动平均线类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MaType {
    /// 简单移动平均线
    SMA,
    /// 指数移动平均线
    EMA,
}

impl Default for MaType {
    fn default() -> Self {
        Self::EMA
    }
}

impl MaType {
    /// 获取均线类型的描述
    pub fn description(&self) -> &'static str {
        match self {
            Self::SMA => "SMA",
            Self::EMA => "EMA",
        }
    }
}

/// 市场状态过滤器配置
///
/// 用于判断市场宏观趋势，帮助策略在不同市场环境下做出更智能的决策
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegimeFilterConfig {
    /// 是否启用市场状态过滤器
    pub enabled: bool,

    /// 用于判断趋势的移动平均线周期
    /// 建议值：50（短期）、100（中期）、200（长期）
    pub period: usize,

    /// 移动平均线类型
    pub ma_type: MaType,

    /// 市场状态更新频率（分钟）
    /// 避免每个tick都重新计算，提高性能
    pub update_interval_minutes: u32,
}

impl Default for RegimeFilterConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            period: 200,
            ma_type: MaType::EMA,
            update_interval_minutes: 60, // 每小时更新一次
        }
    }
}

impl RegimeFilterConfig {
    /// 验证配置的有效性
    pub fn validate(&self) -> SigmaXResult<()> {
        if self.period == 0 || self.period > 1000 {
            return Err(SigmaXError::Config(
                "移动平均线周期必须在 1-1000 范围内".to_string()
            ));
        }

        if self.update_interval_minutes == 0 || self.update_interval_minutes > 1440 {
            return Err(SigmaXError::Config(
                "更新频率必须在 1-1440 分钟范围内".to_string()
            ));
        }

        Ok(())
    }

    /// 获取配置描述
    pub fn description(&self) -> String {
        if !self.enabled {
            return "市场状态过滤器：已禁用".to_string();
        }

        format!(
            "市场状态过滤器：{:?}{} 周期，每{}分钟更新",
            self.ma_type,
            self.period,
            self.update_interval_minutes
        )
    }
}

/// 非对称策略预设类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AsymmetricStrategyPreset {
    /// 保守型：小范围，低频交易
    Conservative,
    /// 平衡型：中等范围，适中频率
    Balanced,
    /// 激进型：大范围，高频交易
    Aggressive,
    /// 自定义：完全自定义参数
    Custom,
}

impl Default for AsymmetricStrategyPreset {
    fn default() -> Self {
        Self::Balanced
    }
}

/// 默认交易对
fn default_trading_pair() -> String {
    "BTC/USDT".to_string()
}

/// 默认交易所ID
fn default_exchange_id() -> String {
    "simulator".to_string()
}

/// 网格分布算法类型
///
/// 定义了不同的网格间距分布方式，每种算法适用于不同的市场条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GridDistribution {
    /// 线性分布（默认）
    /// 网格间距均匀分布，适合稳定市场
    Linear,

    /// 指数分布
    /// 离基准价越远，网格越稀疏，适合波动较大的市场
    /// factor: 指数因子，建议范围 1.1-2.0
    Exponential { factor: f64 },

    /// 斐波那契分布
    /// 基于黄金比例的分布，更符合自然规律
    Fibonacci,

    /// 自定义分布
    /// 用户完全自定义每个网格的位置比例
    /// ratios: 每个网格在区间中的位置比例 [0.0, 1.0]
    Custom { ratios: Vec<f64> },
}

impl Default for GridDistribution {
    fn default() -> Self {
        Self::Linear
    }
}

impl GridDistribution {
    /// 验证分布参数的有效性
    pub fn validate(&self) -> SigmaXResult<()> {
        match self {
            GridDistribution::Linear => Ok(()),
            GridDistribution::Exponential { factor } => {
                if *factor <= 1.0 || *factor > 5.0 {
                    Err(SigmaXError::Config(
                        "指数分布因子必须在 1.0-5.0 范围内".to_string()
                    ))
                } else {
                    Ok(())
                }
            }
            GridDistribution::Fibonacci => Ok(()),
            GridDistribution::Custom { ratios } => {
                if ratios.is_empty() {
                    return Err(SigmaXError::Config(
                        "自定义分布比例不能为空".to_string()
                    ));
                }

                for (i, ratio) in ratios.iter().enumerate() {
                    if *ratio < 0.0 || *ratio > 1.0 {
                        return Err(SigmaXError::Config(
                            format!("自定义分布比例[{}]必须在 0.0-1.0 范围内: {}", i, ratio)
                        ));
                    }
                }

                // 检查是否单调递增
                for i in 1..ratios.len() {
                    if ratios[i] <= ratios[i-1] {
                        return Err(SigmaXError::Config(
                            "自定义分布比例必须单调递增".to_string()
                        ));
                    }
                }

                Ok(())
            }
        }
    }

    /// 获取分布算法的描述
    pub fn description(&self) -> String {
        match self {
            GridDistribution::Linear => "线性分布：均匀间距，适合稳定市场".to_string(),
            GridDistribution::Exponential { factor } => {
                format!("指数分布：因子={:.2}，离基准价越远越稀疏", factor)
            }
            GridDistribution::Fibonacci => "斐波那契分布：黄金比例，符合自然规律".to_string(),
            GridDistribution::Custom { ratios } => {
                format!("自定义分布：{}个自定义位置", ratios.len())
            }
        }
    }
}

/// 下单数量模型
///
/// 定义了不同的数量分配策略，每种模型适用于不同的风险偏好和市场策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QuantityModel {
    /// 固定数量模型
    /// 每个网格使用相同的数量，风险均匀分布
    Constant,

    /// 线性递增模型（默认）
    /// 数量随距离线性增加，适合温和的加仓策略
    /// factor: 递增因子，建议范围 0.5-2.0
    Linear { factor: f64 },

    /// 指数递增模型
    /// 数量随距离指数增加，适合激进的加仓策略
    /// base: 指数底数，建议范围 1.1-2.0
    Exponential { base: f64 },

    /// 对数递增模型
    /// 数量随距离对数增加，适合保守的加仓策略
    /// base: 对数底数，建议范围 2.0-10.0
    Logarithmic { base: f64 },
}

impl Default for QuantityModel {
    fn default() -> Self {
        Self::Linear { factor: 1.0 }
    }
}

impl QuantityModel {
    /// 验证数量模型参数的有效性
    pub fn validate(&self) -> SigmaXResult<()> {
        match self {
            QuantityModel::Constant => Ok(()),
            QuantityModel::Linear { factor } => {
                if *factor <= 0.0 || *factor > 5.0 {
                    Err(SigmaXError::Config(
                        "线性递增因子必须在 0.0-5.0 范围内".to_string()
                    ))
                } else {
                    Ok(())
                }
            }
            QuantityModel::Exponential { base } => {
                if *base <= 1.0 || *base > 3.0 {
                    Err(SigmaXError::Config(
                        "指数递增底数必须在 1.0-3.0 范围内".to_string()
                    ))
                } else {
                    Ok(())
                }
            }
            QuantityModel::Logarithmic { base } => {
                if *base <= 1.0 || *base > 20.0 {
                    Err(SigmaXError::Config(
                        "对数递增底数必须在 1.0-20.0 范围内".to_string()
                    ))
                } else {
                    Ok(())
                }
            }
        }
    }

    /// 获取数量模型的描述
    pub fn description(&self) -> String {
        match self {
            QuantityModel::Constant => "固定数量：每格相同，风险均匀".to_string(),
            QuantityModel::Linear { factor } => {
                format!("线性递增：因子={:.2}，温和加仓", factor)
            }
            QuantityModel::Exponential { base } => {
                format!("指数递增：底数={:.2}，激进加仓", base)
            }
            QuantityModel::Logarithmic { base } => {
                format!("对数递增：底数={:.2}，保守加仓", base)
            }
        }
    }

    /// 计算指定位置的数量乘数
    ///
    /// # 参数
    /// - `position_index`: 网格位置索引（从0开始）
    /// - `total_count`: 总网格数量
    /// - `base_quantity`: 基础数量
    ///
    /// # 返回
    /// 该位置应使用的实际数量
    pub fn calculate_quantity(&self, position_index: usize, total_count: u32, base_quantity: f64) -> f64 {
        let multiplier = match self {
            QuantityModel::Constant => 1.0,
            QuantityModel::Linear { factor } => {
                1.0 + (position_index as f64 / total_count as f64) * factor
            }
            QuantityModel::Exponential { base } => {
                base.powi(position_index as i32)
            }
            QuantityModel::Logarithmic { base } => {
                1.0 + (1.0 + position_index as f64).ln() / base.ln()
            }
        };

        base_quantity * multiplier
    }
}

/// 止损配置类型
///
/// 支持多种动态止损策略，每种策略适用于不同的市场环境和风险偏好
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StopLossConfig {
    /// 固定百分比止损
    /// 基于入场价格的固定百分比，简单可靠
    Fixed {
        /// 止损百分比 (0.0-1.0)，如0.1表示10%止损
        percent: f64
    },

    /// 追踪止损
    /// 当价格朝有利方向移动时，止损位也随之移动，可以锁定利润
    Trailing {
        /// 追踪百分比 (0.0-1.0)，如0.05表示追踪5%
        trail_percent: f64
    },

    /// ATR止损
    /// 基于平均真实波幅的动态止损，根据市场波动性自适应调整
    ATR {
        /// ATR计算周期，通常为14
        period: usize,
        /// ATR乘数，通常为1.5-3.0，越大止损越宽松
        multiplier: f64
    },

    /// 混合止损
    /// 结合多种止损方式，提供更全面的风险保护
    Hybrid {
        /// 固定止损百分比（最大亏损保护）
        max_loss_percent: f64,
        /// 追踪止损百分比（利润保护）
        trail_percent: f64,
        /// ATR乘数（波动性适应）
        atr_multiplier: f64,
        /// ATR周期
        atr_period: usize,
    },

    /// 禁用止损
    /// 完全依赖策略自身的风险控制
    Disabled,
}

impl Default for StopLossConfig {
    fn default() -> Self {
        Self::Fixed { percent: 0.15 } // 默认15%固定止损
    }
}

impl StopLossConfig {
    /// 验证止损配置的有效性
    pub fn validate(&self) -> SigmaXResult<()> {
        match self {
            StopLossConfig::Fixed { percent } => {
                if *percent <= 0.0 || *percent >= 1.0 {
                    return Err(SigmaXError::Config(
                        "固定止损百分比必须在 0.0-1.0 范围内".to_string()
                    ));
                }
            }
            StopLossConfig::Trailing { trail_percent } => {
                if *trail_percent <= 0.0 || *trail_percent >= 1.0 {
                    return Err(SigmaXError::Config(
                        "追踪止损百分比必须在 0.0-1.0 范围内".to_string()
                    ));
                }
            }
            StopLossConfig::ATR { period, multiplier } => {
                if *period == 0 || *period > 100 {
                    return Err(SigmaXError::Config(
                        "ATR周期必须在 1-100 范围内".to_string()
                    ));
                }
                if *multiplier <= 0.0 || *multiplier > 10.0 {
                    return Err(SigmaXError::Config(
                        "ATR乘数必须在 0.0-10.0 范围内".to_string()
                    ));
                }
            }
            StopLossConfig::Hybrid {
                max_loss_percent,
                trail_percent,
                atr_multiplier,
                atr_period
            } => {
                if *max_loss_percent <= 0.0 || *max_loss_percent >= 1.0 {
                    return Err(SigmaXError::Config(
                        "最大亏损百分比必须在 0.0-1.0 范围内".to_string()
                    ));
                }
                if *trail_percent <= 0.0 || *trail_percent >= 1.0 {
                    return Err(SigmaXError::Config(
                        "追踪止损百分比必须在 0.0-1.0 范围内".to_string()
                    ));
                }
                if *atr_multiplier <= 0.0 || *atr_multiplier > 10.0 {
                    return Err(SigmaXError::Config(
                        "ATR乘数必须在 0.0-10.0 范围内".to_string()
                    ));
                }
                if *atr_period == 0 || *atr_period > 100 {
                    return Err(SigmaXError::Config(
                        "ATR周期必须在 1-100 范围内".to_string()
                    ));
                }
            }
            StopLossConfig::Disabled => {
                // 禁用止损无需验证
            }
        }
        Ok(())
    }

    /// 获取止损配置的描述
    pub fn description(&self) -> String {
        match self {
            StopLossConfig::Fixed { percent } => {
                format!("固定止损: {:.1}%", percent * 100.0)
            }
            StopLossConfig::Trailing { trail_percent } => {
                format!("追踪止损: {:.1}%", trail_percent * 100.0)
            }
            StopLossConfig::ATR { period, multiplier } => {
                format!("ATR止损: {}周期, {:.1}倍", period, multiplier)
            }
            StopLossConfig::Hybrid {
                max_loss_percent,
                trail_percent,
                atr_multiplier,
                atr_period
            } => {
                format!(
                    "混合止损: 最大{:.1}%, 追踪{:.1}%, ATR{}周期{:.1}倍",
                    max_loss_percent * 100.0,
                    trail_percent * 100.0,
                    atr_period,
                    atr_multiplier
                )
            }
            StopLossConfig::Disabled => "禁用止损".to_string(),
        }
    }

    /// 从旧的 stop_loss_percent 配置迁移
    pub fn from_legacy_percent(percent: Option<f64>) -> Self {
        match percent {
            Some(p) if p > 0.0 && p < 1.0 => Self::Fixed { percent: p },
            _ => Self::default(),
        }
    }
}

impl AsymmetricGridConfig {
    /// 创建保守型预设配置
    pub fn conservative() -> Self {
        Self {
            base_price: 0.0,
            order_amount: 100.0,
            trading_pair: default_trading_pair(),
            exchange_id: default_exchange_id(),
            down_range_start: -0.01,
            down_range_end: -0.05,
            down_grid_count: 5,
            down_base_quantity: 0.1,
            down_grid_distribution: GridDistribution::Linear, // 保守型使用线性分布
            down_quantity_model: QuantityModel::Constant, // 保守型使用固定数量
            up_range_start: 0.01,
            up_range_end: 0.08,
            up_grid_count: 4,
            up_base_quantity: 0.1,
            up_grid_distribution: GridDistribution::Linear, // 保守型使用线性分布
            up_quantity_model: QuantityModel::Constant, // 保守型使用固定数量
            volatility_window_hours: 24,
            volatility_multiplier: 0.5,
            enable_dynamic_volatility: true,
            max_position_amount: 1000.0,
            max_daily_trades: 20,
            stop_loss_config: StopLossConfig::Fixed { percent: 0.15 },
            strategy_preset: AsymmetricStrategyPreset::Conservative,
            recenter_threshold_percent: Some(0.20), // 保守型：20%偏离才重置
            regime_filter: Some(RegimeFilterConfig {
                enabled: true,
                period: 200,
                ma_type: MaType::EMA,
                update_interval_minutes: 120, // 保守型：2小时更新一次
            }),
        }
    }

    /// 创建平衡型预设配置
    pub fn balanced() -> Self {
        Self {
            base_price: 0.0,
            order_amount: 200.0,
            trading_pair: default_trading_pair(),
            exchange_id: default_exchange_id(),
            down_range_start: -0.02,
            down_range_end: -0.10,
            down_grid_count: 8,
            down_base_quantity: 0.15,
            down_grid_distribution: GridDistribution::Exponential { factor: 1.2 }, // 平衡型使用轻微指数分布
            down_quantity_model: QuantityModel::Linear { factor: 1.0 }, // 平衡型使用线性递增
            up_range_start: 0.02,
            up_range_end: 0.15,
            up_grid_count: 6,
            up_base_quantity: 0.15,
            up_grid_distribution: GridDistribution::Linear, // 上涨区间保持线性
            up_quantity_model: QuantityModel::Constant, // 上涨区间使用固定数量
            volatility_window_hours: 12,
            volatility_multiplier: 1.0,
            enable_dynamic_volatility: true,
            max_position_amount: 2000.0,
            max_daily_trades: 50,
            stop_loss_config: StopLossConfig::Trailing { trail_percent: 0.10 },
            strategy_preset: AsymmetricStrategyPreset::Balanced,
            recenter_threshold_percent: Some(0.15), // 平衡型：15%偏离重置
            regime_filter: Some(RegimeFilterConfig {
                enabled: true,
                period: 200,
                ma_type: MaType::EMA,
                update_interval_minutes: 60, // 平衡型：1小时更新一次
            }),
        }
    }

    /// 创建激进型预设配置
    pub fn aggressive() -> Self {
        Self {
            base_price: 0.0,
            order_amount: 500.0,
            trading_pair: default_trading_pair(),
            exchange_id: default_exchange_id(),
            down_range_start: -0.03,
            down_range_end: -0.15,
            down_grid_count: 12,
            down_base_quantity: 0.2,
            down_grid_distribution: GridDistribution::Exponential { factor: 1.5 }, // 激进型使用指数分布
            down_quantity_model: QuantityModel::Exponential { base: 1.3 }, // 激进型使用指数递增
            up_range_start: 0.03,
            up_range_end: 0.25,
            up_grid_count: 8,
            up_base_quantity: 0.2,
            up_grid_distribution: GridDistribution::Fibonacci, // 激进型使用斐波那契分布
            up_quantity_model: QuantityModel::Linear { factor: 0.5 }, // 上涨区间使用温和线性递增
            volatility_window_hours: 6,
            volatility_multiplier: 1.5,
            enable_dynamic_volatility: true,
            max_position_amount: 5000.0,
            max_daily_trades: 100,
            stop_loss_config: StopLossConfig::Hybrid {
                max_loss_percent: 0.25,
                trail_percent: 0.08,
                atr_multiplier: 2.0,
                atr_period: 14
            },
            strategy_preset: AsymmetricStrategyPreset::Aggressive,
            recenter_threshold_percent: Some(0.12), // 激进型：12%偏离重置，更敏感
            regime_filter: Some(RegimeFilterConfig {
                enabled: true,
                period: 100, // 激进型使用更短周期
                ma_type: MaType::EMA,
                update_interval_minutes: 30, // 激进型：30分钟更新一次
            }),
        }
    }

    /// 计算总网格数量
    pub fn total_grid_count(&self) -> u32 {
        self.down_grid_count + self.up_grid_count
    }

    /// 计算下跌区间范围
    pub fn down_range_size(&self) -> f64 {
        (self.down_range_start - self.down_range_end).abs()
    }

    /// 计算上涨区间范围
    pub fn up_range_size(&self) -> f64 {
        (self.up_range_end - self.up_range_start).abs()
    }
}

impl StrategyConfig for AsymmetricGridConfig {
    fn validate(&self) -> SigmaXResult<()> {
        // 基础参数验证
        if self.order_amount <= 0.0 {
            return Err(SigmaXError::Config("订单金额必须大于0".to_string()));
        }

        // 交易配置验证
        if self.trading_pair.is_empty() {
            return Err(SigmaXError::Config("交易对不能为空".to_string()));
        }
        if !self.trading_pair.contains('/') {
            return Err(SigmaXError::Config("交易对格式错误，应为 BASE/QUOTE 格式".to_string()));
        }
        if self.exchange_id.is_empty() {
            return Err(SigmaXError::Config("交易所ID不能为空".to_string()));
        }

        // 网格分布验证
        self.down_grid_distribution.validate()?;
        self.up_grid_distribution.validate()?;

        // 数量模型验证
        self.down_quantity_model.validate()?;
        self.up_quantity_model.validate()?;

        // 止损配置验证
        self.stop_loss_config.validate()?;

        // 网格重置阈值验证
        if let Some(threshold) = self.recenter_threshold_percent {
            if threshold <= 0.0 || threshold > 1.0 {
                return Err(SigmaXError::Config(
                    "网格重置阈值必须在 0.0-1.0 范围内".to_string()
                ));
            }
        }

        // 市场状态过滤器验证
        if let Some(ref filter_config) = self.regime_filter {
            filter_config.validate()?;
        }

        // 下跌区间验证
        if self.down_range_start >= 0.0 {
            return Err(SigmaXError::Config("下跌区间起始必须为负数".to_string()));
        }
        if self.down_range_end >= self.down_range_start {
            return Err(SigmaXError::Config("下跌区间结束必须小于起始".to_string()));
        }
        if self.down_grid_count == 0 {
            return Err(SigmaXError::Config("下跌区间网格数量必须大于0".to_string()));
        }
        if self.down_base_quantity <= 0.0 {
            return Err(SigmaXError::Config("下跌区间基础数量必须大于0".to_string()));
        }

        // 上涨区间验证
        if self.up_range_start <= 0.0 {
            return Err(SigmaXError::Config("上涨区间起始必须为正数".to_string()));
        }
        if self.up_range_end <= self.up_range_start {
            return Err(SigmaXError::Config("上涨区间结束必须大于起始".to_string()));
        }
        if self.up_grid_count == 0 {
            return Err(SigmaXError::Config("上涨区间网格数量必须大于0".to_string()));
        }
        if self.up_base_quantity <= 0.0 {
            return Err(SigmaXError::Config("上涨区间基础数量必须大于0".to_string()));
        }

        // 波动率配置验证
        if self.volatility_window_hours == 0 {
            return Err(SigmaXError::Config("波动率窗口必须大于0小时".to_string()));
        }
        if self.volatility_multiplier <= 0.0 {
            return Err(SigmaXError::Config("波动率乘数必须大于0".to_string()));
        }

        // 风险控制验证
        if self.max_position_amount <= 0.0 {
            return Err(SigmaXError::Config("最大持仓金额必须大于0".to_string()));
        }
        if self.max_daily_trades == 0 {
            return Err(SigmaXError::Config("最大日交易次数必须大于0".to_string()));
        }

        // 止损配置已在上面验证

        Ok(())
    }

    fn strategy_type(&self) -> &'static str {
        "asymmetric_volatility_grid"
    }

    fn description(&self) -> String {
        format!(
            "非对称波动率网格策略 - 交易对:{}, 交易所:{}, 预设:{:?}, 下跌网格:{}({},{}), 上涨网格:{}({},{}), 波动率窗口:{}h",
            self.trading_pair,
            self.exchange_id,
            self.strategy_preset,
            self.down_grid_count,
            self.down_grid_distribution.description(),
            self.down_quantity_model.description(),
            self.up_grid_count,
            self.up_grid_distribution.description(),
            self.up_quantity_model.description(),
            self.volatility_window_hours
        )
    }
}
