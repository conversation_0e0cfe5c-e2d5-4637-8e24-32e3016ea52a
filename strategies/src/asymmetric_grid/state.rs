//! 非对称网格策略状态管理
//!
//! 管理策略运行时的状态信息，包括网格级别、订单状态、持仓信息等

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use sigmax_core::{OrderId, Order};

/// 非对称网格策略状态
///
/// 包含策略运行时的所有状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AsymmetricGridState {
    /// 当前基准价格
    pub current_base_price: f64,
    /// 当前市场价格
    pub current_market_price: f64,
    /// 当前波动率
    pub current_volatility: f64,

    /// 活跃的网格级别
    pub active_grid_levels: Vec<GridLevel>,
    /// 待处理订单
    pub pending_orders: HashMap<OrderId, Order>,
    /// 已完成订单历史
    pub completed_orders: Vec<Order>,
    /// 订单ID到网格级别ID的映射
    /// 用于在订单状态变化时快速找到对应的网格级别
    pub order_to_grid_mapping: HashMap<OrderId, Uuid>,

    /// 持仓信息
    pub position_info: PositionInfo,
    /// 交易统计
    pub trading_stats: TradingStats,

    /// 最后更新时间
    pub last_update_time: DateTime<Utc>,
    /// 策略启动时间
    pub start_time: DateTime<Utc>,

    // 智能化与自适应状态
    /// 当前市场宏观状态
    pub market_regime: MarketRegime,
    /// 长期均线值（用于市场状态判断）
    pub long_term_ma: f64,
    /// 市场状态最后更新时间
    pub regime_last_update: DateTime<Utc>,
}

/// 网格级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridLevel {
    /// 网格级别ID
    pub id: Uuid,
    /// 网格价格
    pub price: f64,
    /// 网格数量
    pub quantity: f64,
    /// 网格区域（上涨或下跌）
    pub zone: GridZone,
    /// 网格状态
    pub status: GridLevelStatus,
    /// 关联的订单ID（如果有）
    pub order_id: Option<OrderId>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

/// 网格区域
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum GridZone {
    /// 下跌区间（吸筹区）
    Down,
    /// 上涨区间（止盈区）
    Up,
}

/// 网格级别状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum GridLevelStatus {
    /// 等待触发
    Waiting,
    /// 已触发，订单已发送
    Triggered,
    /// 已成交
    Filled,
    /// 已取消
    Cancelled,
    /// 错误状态
    Error,
}

/// 市场宏观状态
///
/// 用于判断当前市场的整体趋势，帮助策略做出更智能的决策
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MarketRegime {
    /// 牛市（价格 > 长期均线）
    /// 在此状态下，策略可能减少卖出操作，让利润奔跑
    Bullish,

    /// 熊市（价格 < 长期均线）
    /// 在此状态下，策略可能减少买入操作，避免逆势接飞刀
    Bearish,

    /// 震荡市/未知状态
    /// 数据不足或市场处于震荡状态，策略正常运行
    Undefined,
}

impl Default for MarketRegime {
    fn default() -> Self {
        Self::Undefined
    }
}

impl MarketRegime {
    /// 获取市场状态的描述
    pub fn description(&self) -> &'static str {
        match self {
            MarketRegime::Bullish => "牛市：价格高于长期均线，趋势向上",
            MarketRegime::Bearish => "熊市：价格低于长期均线，趋势向下",
            MarketRegime::Undefined => "震荡市：趋势不明确或数据不足",
        }
    }

    /// 判断是否应该减少买入操作
    pub fn should_reduce_buying(&self) -> bool {
        matches!(self, MarketRegime::Bearish)
    }

    /// 判断是否应该减少卖出操作
    pub fn should_reduce_selling(&self) -> bool {
        matches!(self, MarketRegime::Bullish)
    }
}

/// 持仓信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionInfo {
    /// 总持仓数量
    pub total_quantity: f64,
    /// 平均成本价
    pub average_cost: f64,
    /// 当前市值
    pub current_value: f64,
    /// 未实现盈亏
    pub unrealized_pnl: f64,
    /// 已实现盈亏
    pub realized_pnl: f64,
    /// 持仓成本
    pub total_cost: f64,
    /// 最高价记录（用于追踪止损）
    /// 记录开仓以来的最高价格，用于计算追踪止损
    pub high_water_mark: Option<f64>,
}

/// 交易统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingStats {
    /// 总交易次数
    pub total_trades: u64,
    /// 盈利交易次数
    pub profitable_trades: u64,
    /// 亏损交易次数
    pub losing_trades: u64,
    /// 总盈亏
    pub total_pnl: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 胜率
    pub win_rate: f64,
    /// 今日交易次数
    pub daily_trades: u64,
    /// 今日盈亏
    pub daily_pnl: f64,
}

impl Default for AsymmetricGridState {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            current_base_price: 0.0,
            current_market_price: 0.0,
            current_volatility: 0.0,
            active_grid_levels: Vec::new(),
            pending_orders: HashMap::new(),
            completed_orders: Vec::new(),
            order_to_grid_mapping: HashMap::new(),
            position_info: PositionInfo::default(),
            trading_stats: TradingStats::default(),
            last_update_time: now,
            start_time: now,
            market_regime: MarketRegime::default(),
            long_term_ma: 0.0,
            regime_last_update: now,
        }
    }
}

impl Default for PositionInfo {
    fn default() -> Self {
        Self {
            total_quantity: 0.0,
            average_cost: 0.0,
            current_value: 0.0,
            unrealized_pnl: 0.0,
            realized_pnl: 0.0,
            total_cost: 0.0,
            high_water_mark: None,
        }
    }
}

impl Default for TradingStats {
    fn default() -> Self {
        Self {
            total_trades: 0,
            profitable_trades: 0,
            losing_trades: 0,
            total_pnl: 0.0,
            max_drawdown: 0.0,
            win_rate: 0.0,
            daily_trades: 0,
            daily_pnl: 0.0,
        }
    }
}

impl AsymmetricGridState {
    /// 创建新的策略状态
    pub fn new() -> Self {
        Self::default()
    }

    /// 更新基准价格
    pub fn update_base_price(&mut self, price: f64) {
        self.current_base_price = price;
        self.last_update_time = Utc::now();
    }

    /// 更新市场价格
    pub fn update_market_price(&mut self, price: f64) {
        self.current_market_price = price;
        self.last_update_time = Utc::now();

        // 更新持仓市值
        if self.position_info.total_quantity > 0.0 {
            self.position_info.current_value = self.position_info.total_quantity * price;
            self.position_info.unrealized_pnl =
                self.position_info.current_value - self.position_info.total_cost;

            // 更新最高价记录（用于追踪止损）
            self.position_info.high_water_mark = Some(
                self.position_info.high_water_mark.unwrap_or(price).max(price)
            );
        }
    }

    /// 更新波动率
    pub fn update_volatility(&mut self, volatility: f64) {
        self.current_volatility = volatility;
        self.last_update_time = Utc::now();
    }

    /// 添加网格级别
    pub fn add_grid_level(&mut self, level: GridLevel) {
        self.active_grid_levels.push(level);
        self.last_update_time = Utc::now();
    }

    /// 移除网格级别
    pub fn remove_grid_level(&mut self, level_id: Uuid) {
        self.active_grid_levels.retain(|level| level.id != level_id);
        self.last_update_time = Utc::now();
    }

    /// 更新网格级别状态
    pub fn update_grid_level_status(&mut self, level_id: Uuid, status: GridLevelStatus) {
        if let Some(level) = self.active_grid_levels.iter_mut().find(|l| l.id == level_id) {
            level.status = status;
            level.updated_at = Utc::now();
        }
        self.last_update_time = Utc::now();
    }

    /// 添加订单到网格的映射
    pub fn add_order_grid_mapping(&mut self, order_id: OrderId, grid_id: Uuid) {
        self.order_to_grid_mapping.insert(order_id, grid_id);
        self.last_update_time = Utc::now();
    }

    /// 移除订单到网格的映射
    pub fn remove_order_grid_mapping(&mut self, order_id: OrderId) -> Option<Uuid> {
        let result = self.order_to_grid_mapping.remove(&order_id);
        self.last_update_time = Utc::now();
        result
    }

    /// 根据订单ID查找对应的网格级别ID
    pub fn get_grid_id_by_order(&self, order_id: OrderId) -> Option<Uuid> {
        self.order_to_grid_mapping.get(&order_id).copied()
    }

    /// 根据网格ID查找对应的网格级别
    pub fn get_grid_level_by_id(&self, grid_id: Uuid) -> Option<&GridLevel> {
        self.active_grid_levels.iter().find(|level| level.id == grid_id)
    }

    /// 根据网格ID查找对应的网格级别（可变引用）
    pub fn get_grid_level_by_id_mut(&mut self, grid_id: Uuid) -> Option<&mut GridLevel> {
        self.active_grid_levels.iter_mut().find(|level| level.id == grid_id)
    }

    /// 添加待处理订单
    pub fn add_pending_order(&mut self, order: Order) {
        self.pending_orders.insert(order.id, order);
        self.last_update_time = Utc::now();
    }

    /// 移除待处理订单
    pub fn remove_pending_order(&mut self, order_id: OrderId) -> Option<Order> {
        let order = self.pending_orders.remove(&order_id);
        if order.is_some() {
            self.last_update_time = Utc::now();
        }
        order
    }

    /// 添加已完成订单
    pub fn add_completed_order(&mut self, order: Order) {
        self.completed_orders.push(order);
        self.last_update_time = Utc::now();
    }

    /// 更新交易统计
    pub fn update_trading_stats(&mut self, pnl: f64) {
        self.trading_stats.total_trades += 1;
        self.trading_stats.total_pnl += pnl;
        self.trading_stats.daily_trades += 1;
        self.trading_stats.daily_pnl += pnl;

        if pnl > 0.0 {
            self.trading_stats.profitable_trades += 1;
        } else if pnl < 0.0 {
            self.trading_stats.losing_trades += 1;
        }

        // 更新胜率
        if self.trading_stats.total_trades > 0 {
            self.trading_stats.win_rate =
                self.trading_stats.profitable_trades as f64 / self.trading_stats.total_trades as f64;
        }

        self.last_update_time = Utc::now();
    }

    /// 获取指定区域的网格级别
    pub fn get_grid_levels_by_zone(&self, zone: GridZone) -> Vec<&GridLevel> {
        self.active_grid_levels.iter().filter(|level| level.zone == zone).collect()
    }

    /// 获取指定状态的网格级别
    pub fn get_grid_levels_by_status(&self, status: GridLevelStatus) -> Vec<&GridLevel> {
        self.active_grid_levels.iter().filter(|level| level.status == status).collect()
    }

    /// 检查是否达到最大日交易次数
    pub fn is_daily_trade_limit_reached(&self, max_daily_trades: u32) -> bool {
        self.trading_stats.daily_trades >= max_daily_trades as u64
    }

    /// 重置日交易统计
    pub fn reset_daily_stats(&mut self) {
        self.trading_stats.daily_trades = 0;
        self.trading_stats.daily_pnl = 0.0;
        self.last_update_time = Utc::now();
    }

    /// 更新市场状态
    pub fn update_market_regime(&mut self, regime: MarketRegime, ma_value: f64) {
        self.market_regime = regime;
        self.long_term_ma = ma_value;
        self.regime_last_update = Utc::now();
        self.last_update_time = Utc::now();
    }

    /// 检查是否需要更新市场状态
    ///
    /// # 参数
    /// - `update_interval_minutes`: 更新间隔（分钟）
    ///
    /// # 返回
    /// 如果距离上次更新超过指定间隔，返回 true
    pub fn should_update_market_regime(&self, update_interval_minutes: u32) -> bool {
        let now = Utc::now();
        let interval = chrono::Duration::minutes(update_interval_minutes as i64);
        now.signed_duration_since(self.regime_last_update) >= interval
    }

    /// 获取市场状态信息
    pub fn get_market_regime_info(&self) -> (MarketRegime, f64, DateTime<Utc>) {
        (self.market_regime, self.long_term_ma, self.regime_last_update)
    }
}
