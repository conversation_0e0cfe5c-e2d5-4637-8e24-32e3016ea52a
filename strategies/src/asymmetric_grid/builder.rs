//! 非对称波动率网格策略构建器
//!
//! 提供类型安全的策略构建接口，替代动态配置解析

use tracing::{info, debug};
use uuid::Uuid;
use sigmax_core::{SigmaXResult, SigmaXError, Strategy, StrategyId};
use crate::core::{StrategyServices, StrategyConfig};
use super::{AsymmetricVolatilityGridStrategy, AsymmetricGridConfig};

/// 非对称波动率网格策略构建器
///
/// ## 设计原则
/// - **类型安全**：编译时检查配置类型
/// - **链式调用**：提供流畅的API体验
/// - **验证机制**：构建时进行配置验证
/// - **错误处理**：清晰的错误信息
///
/// ## 使用示例
/// ```rust
/// let strategy = AsymmetricVolatilityGridStrategy::builder()
///     .with_config(config)
///     .with_services(services)
///     .with_name("我的网格策略".to_string())
///     .build()
///     .await?;
/// ```
pub struct AsymmetricVolatilityGridStrategyBuilder {
    /// 策略配置
    config: Option<AsymmetricGridConfig>,
    /// 策略服务容器
    services: Option<StrategyServices>,
    /// 策略名称
    name: Option<String>,
    /// 策略ID（可选，默认自动生成）
    id: Option<StrategyId>,
}

impl AsymmetricVolatilityGridStrategyBuilder {
    /// 创建新的构建器实例
    ///
    /// # 返回
    /// 新的构建器实例
    pub fn new() -> Self {
        Self {
            config: None,
            services: None,
            name: None,
            id: None,
        }
    }

    /// 设置策略配置
    ///
    /// # 参数
    /// - `config`: 强类型的策略配置
    ///
    /// # 返回
    /// 更新后的构建器实例
    pub fn with_config(mut self, config: AsymmetricGridConfig) -> Self {
        debug!("设置策略配置: {:?}", config);
        self.config = Some(config);
        self
    }

    /// 设置策略服务容器
    ///
    /// # 参数
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// 更新后的构建器实例
    pub fn with_services(mut self, services: StrategyServices) -> Self {
        debug!("设置策略服务容器");
        self.services = Some(services);
        self
    }

    /// 设置策略名称
    ///
    /// # 参数
    /// - `name`: 策略名称
    ///
    /// # 返回
    /// 更新后的构建器实例
    pub fn with_name(mut self, name: String) -> Self {
        debug!("设置策略名称: {}", name);
        self.name = Some(name);
        self
    }

    /// 设置策略ID
    ///
    /// # 参数
    /// - `id`: 策略ID
    ///
    /// # 返回
    /// 更新后的构建器实例
    pub fn with_id(mut self, id: StrategyId) -> Self {
        debug!("设置策略ID: {:?}", id);
        self.id = Some(id);
        self
    }

    /// 构建策略实例
    ///
    /// # 返回
    /// - `Ok(AsymmetricVolatilityGridStrategy)`: 构建成功的策略实例
    /// - `Err(SigmaXError)`: 构建失败
    ///
    /// # 错误
    /// - 缺少必需的配置或服务
    /// - 配置验证失败
    /// - 策略初始化失败
    pub async fn build(self) -> SigmaXResult<AsymmetricVolatilityGridStrategy> {
        info!("开始构建非对称波动率网格策略");

        // 验证必需字段
        let config = self.config.ok_or_else(|| {
            SigmaXError::Config("策略配置不能为空".to_string())
        })?;

        let services = self.services.ok_or_else(|| {
            SigmaXError::Config("策略服务容器不能为空".to_string())
        })?;

        // 验证服务容器
        services.validate()?;

        // 验证配置（使用统一的验证接口）
        config.validate()?;

        // 生成策略ID（如果未提供）
        let id = self.id.unwrap_or_else(|| Uuid::new_v4());

        // 生成策略名称（如果未提供）
        let name = self.name.unwrap_or_else(|| {
            format!("AsymmetricGrid-{}", id.to_string()[..8].to_uppercase())
        });

        // 创建策略实例
        let mut strategy = AsymmetricVolatilityGridStrategy::new_with_builder(
            id,
            name,
            services,
        ).await?;

        // 配置策略
        strategy.configure_typed(config).await?;

        // 初始化策略
        strategy.initialize().await?;

        info!("非对称波动率网格策略构建成功: {}", strategy.name());
        Ok(strategy)
    }


}

impl Default for AsymmetricVolatilityGridStrategyBuilder {
    fn default() -> Self {
        Self::new()
    }
}



#[cfg(test)]
mod tests {
    use super::*;
    use crate::asymmetric_grid::AsymmetricGridConfig;

    #[tokio::test]
    async fn test_builder_missing_config() {
        let result = AsymmetricVolatilityGridStrategy::builder()
            .build()
            .await;

        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("策略配置不能为空"));
    }

    #[tokio::test]
    async fn test_builder_invalid_config() {
        let invalid_config = AsymmetricGridConfig {
            order_amount: -100.0, // 无效值
            ..AsymmetricGridConfig::balanced()
        };

        let result = AsymmetricVolatilityGridStrategy::builder()
            .with_config(invalid_config)
            .build()
            .await;

        assert!(result.is_err());
        let error_msg = result.unwrap_err().to_string();
        // 构建器首先检查服务容器，所以这里应该是服务容器的错误
        assert!(error_msg.contains("策略服务容器不能为空"));
    }

    #[test]
    fn test_unified_validation_logic() {
        // 测试验证逻辑统一性：确保 builder 使用的是 config.validate()
        let invalid_config = AsymmetricGridConfig {
            order_amount: -100.0, // 无效值
            ..AsymmetricGridConfig::balanced()
        };

        // 直接调用配置验证
        let config_validation_result = invalid_config.validate();
        assert!(config_validation_result.is_err());
        assert!(config_validation_result.unwrap_err().to_string().contains("订单金额必须大于0"));

        // 测试更复杂的验证规则（只有完整的 validate() 方法才有）
        let invalid_trading_pair_config = AsymmetricGridConfig {
            trading_pair: "INVALID".to_string(), // 缺少 '/' 分隔符
            ..AsymmetricGridConfig::balanced()
        };

        let result = invalid_trading_pair_config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("交易对格式错误"));
    }
}
