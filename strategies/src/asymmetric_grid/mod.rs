//! 非对称波动率网格策略模块
//!
//! 基于波动率的非对称网格交易策略：下跌时窄间距吸筹，上涨时宽间距止盈
//!
//! ## 策略特点
//! - **非对称设计**：下跌区间密集网格，上涨区间稀疏网格
//! - **波动率自适应**：根据市场波动率动态调整网格间距
//! - **风险控制**：内置多层风险管理机制
//! - **高性能**：异步设计，支持高频交易
//!
//! ## 使用示例
//! ```rust
//! use strategies::asymmetric_grid::{AsymmetricVolatilityGridStrategy, AsymmetricGridConfig};
//! use strategies::core::StrategyServices;
//!
//! let config = AsymmetricGridConfig {
//!     base_price: 100.0,
//!     down_range_start: -0.02,
//!     down_range_end: -0.10,
//!     // ... 其他配置
//! };
//!
//! let mut strategy = AsymmetricVolatilityGridStrategy::new(services).await?;
//! strategy.initialize(config).await?;
//! ```

pub mod config;
pub mod strategy;
pub mod state;
pub mod builder;
pub mod error;

// 重新导出主要类型
pub use config::AsymmetricGridConfig;
pub use strategy::AsymmetricVolatilityGridStrategy;
pub use state::{AsymmetricGridState, GridLevel, GridZone, GridLevelStatus};
pub use error::AsymmetricGridError;
