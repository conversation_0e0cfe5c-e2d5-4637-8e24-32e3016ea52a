//! 非对称波动率网格策略实现
//!
//! 基于波动率的非对称网格交易策略核心实现

use std::sync::Arc;
use async_trait::async_trait;
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tracing::{info, warn, debug};
use rust_decimal::prelude::*;

use sigmax_core::{
    SigmaXResult, SigmaXError, Candle, Order, OrderId, Trade, OrderSide, OrderType,
    TradingPair, ExchangeId, Strategy, StrategyId, StrategyStatus
};
use crate::core::{StrategyServices, StrategyConfig};
use crate::asymmetric_grid::{
    AsymmetricGridConfig, AsymmetricGridState, GridLevel, GridZone, GridLevelStatus
};
use crate::asymmetric_grid::config::{GridDistribution, StopLossConfig};
use crate::asymmetric_grid::state::MarketRegime;
use crate::asymmetric_grid::error::AsymmetricGridError;
use crate::utils::risk::StopLossCalculator;
use crate::utils::indicators::TechnicalIndicators;

/// 市场指标数据
///
/// 包含从市场数据中提取和计算的所有指标
#[derive(Debug, Clone)]
struct MarketMetrics {
    /// 当前市场价格
    current_price: f64,
    /// 当前波动率
    volatility: f64,
    /// 数据时间戳
    #[allow(dead_code)]
    timestamp: DateTime<Utc>,
}

/// 智能化检查结果
///
/// 表示智能化检查（网格重置、市场状态、止损等）的结果
#[derive(Debug, Clone, PartialEq)]
enum IntelligentCheckResult {
    /// 继续正常流程
    Continue,
    /// 提前返回（如网格重置后需要等待）
    EarlyReturn,
    /// 止损触发，停止策略
    StopLossTriggered,
}

/// 非对称波动率网格策略
///
/// ## 策略逻辑
/// 1. **非对称网格设计**：下跌区间密集网格（吸筹），上涨区间稀疏网格（止盈）
/// 2. **波动率自适应**：根据市场波动率动态调整网格间距
/// 3. **风险控制**：多层风险管理，包括持仓限制、日交易限制、止损等
/// 4. **高性能异步**：支持高频交易场景
#[derive(Debug)]
pub struct AsymmetricVolatilityGridStrategy {
    /// 策略ID
    id: StrategyId,
    /// 策略名称
    name: String,
    /// 策略配置
    config: Option<AsymmetricGridConfig>,
    /// 策略状态
    state: Arc<RwLock<AsymmetricGridState>>,
    /// 策略运行状态
    strategy_status: Arc<RwLock<StrategyStatus>>,
    /// 服务容器
    #[allow(dead_code)]
    services: StrategyServices,
    /// 波动率历史数据（用于计算）
    volatility_history: Arc<RwLock<Vec<f64>>>,
}

impl AsymmetricVolatilityGridStrategy {
    /// 创建新的策略实例
    ///
    /// # 参数
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// - `Ok(AsymmetricVolatilityGridStrategy)`: 创建成功
    /// - `Err(SigmaXError)`: 创建失败
    pub async fn new(services: StrategyServices) -> SigmaXResult<Self> {
        info!("创建非对称波动率网格策略实例");

        // 验证服务容器
        services.validate()?;

        let id = Uuid::new_v4();
        let name = format!("asymmetric_volatility_grid_{}", id);

        Ok(Self {
            id,
            name,
            config: None,
            state: Arc::new(RwLock::new(AsymmetricGridState::new())),
            strategy_status: Arc::new(RwLock::new(StrategyStatus::Created)),
            services,
            volatility_history: Arc::new(RwLock::new(Vec::new())),
        })
    }

    /// 通过Builder创建新的策略实例（内部方法）
    ///
    /// # 参数
    /// - `id`: 策略ID
    /// - `name`: 策略名称
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// - `Ok(AsymmetricVolatilityGridStrategy)`: 创建成功的策略实例
    /// - `Err(SigmaXError)`: 创建失败
    pub(crate) async fn new_with_builder(
        id: Uuid,
        name: String,
        services: StrategyServices,
    ) -> SigmaXResult<Self> {
        info!("通过Builder创建非对称波动率网格策略实例: {}", name);

        // 验证服务容器
        services.validate()?;

        Ok(Self {
            id,
            name,
            config: None,
            state: Arc::new(RwLock::new(AsymmetricGridState::new())),
            strategy_status: Arc::new(RwLock::new(StrategyStatus::Created)),
            services,
            volatility_history: Arc::new(RwLock::new(Vec::new())),
        })
    }

    /// 使用强类型配置配置策略（Builder专用）
    ///
    /// # 参数
    /// - `config`: 强类型的策略配置
    ///
    /// # 返回
    /// - `Ok(())`: 配置成功
    /// - `Err(SigmaXError)`: 配置失败
    pub(crate) async fn configure_typed(&mut self, config: AsymmetricGridConfig) -> SigmaXResult<()> {
        info!("使用强类型配置配置非对称波动率网格策略");

        // 验证配置
        config.validate()?;

        // 设置配置
        self.config = Some(config);

        info!("非对称波动率网格策略配置成功");
        Ok(())
    }

    /// 验证策略配置（内部方法）
    ///
    /// # 参数
    /// - `config`: 策略配置
    ///
    /// # 返回
    /// - `Ok(())`: 验证通过
    /// - `Err(SigmaXError)`: 验证失败
    #[allow(dead_code)]
    fn validate_config(&self, config: &AsymmetricGridConfig) -> SigmaXResult<()> {
        // 这里可以添加额外的验证逻辑
        // 目前使用配置自身的验证方法
        config.validate()
    }

    /// 配置策略
    ///
    /// # 参数
    /// - `config`: 策略配置（JSON格式）
    ///
    /// # 返回
    /// - `Ok(())`: 配置成功
    /// - `Err(SigmaXError)`: 配置失败
    pub async fn configure(&mut self, config: serde_json::Value) -> SigmaXResult<()> {
        info!("配置非对称波动率网格策略");

        // 解析配置
        let strategy_config: AsymmetricGridConfig = serde_json::from_value(config)
            .map_err(|e| SigmaXError::Config(format!("Invalid asymmetric grid config: {}", e)))?;

        // 验证配置
        strategy_config.validate()?;

        // 保存配置
        self.config = Some(strategy_config.clone());

        info!("非对称波动率网格策略配置完成: {}", strategy_config.description());
        Ok(())
    }

    /// 计算当前波动率
    ///
    /// 基于历史价格数据计算波动率
    async fn calculate_volatility(&self, candle: &Candle) -> SigmaXResult<f64> {
        let mut history = self.volatility_history.write().await;

        // 添加当前价格到历史数据
        let current_price = candle.close.to_f64().unwrap_or(0.0);
        history.push(current_price);

        // 获取配置中的窗口大小
        let window_size = self.config.as_ref()
            .map(|c| c.volatility_window_hours as usize)
            .unwrap_or(24);

        // 保持历史数据在窗口大小内
        if history.len() > window_size {
            let excess = history.len() - window_size;
            history.drain(0..excess);
        }

        // 计算波动率（标准差）
        if history.len() < 2 {
            return Ok(0.0);
        }

        let mean = history.iter().sum::<f64>() / history.len() as f64;
        let variance = history.iter()
            .map(|price| (price - mean).powi(2))
            .sum::<f64>() / (history.len() - 1) as f64;

        let volatility = (variance.sqrt() / mean) * 100.0; // 转换为百分比

        debug!("计算波动率: {:.4}%", volatility);
        Ok(volatility)
    }

    /// 生成网格级别
    ///
    /// 根据配置和当前市场条件生成非对称网格级别
    async fn generate_grid_levels(&self, base_price: f64, volatility: f64) -> SigmaXResult<Vec<GridLevel>> {
        let config = self.config.as_ref()
            .ok_or_else(|| SigmaXError::Config("策略未初始化".to_string()))?;

        let mut levels = Vec::new();

        // 计算波动率调整因子
        let volatility_factor = if config.enable_dynamic_volatility {
            1.0 + (volatility / 100.0) * config.volatility_multiplier
        } else {
            1.0
        };

        info!("生成网格级别: 基准价格={:.2}, 波动率={:.2}%, 调整因子={:.2}",
              base_price, volatility, volatility_factor);

        // 生成下跌区间网格（密集吸筹）
        self.generate_down_zone_grids(&mut levels, base_price, volatility_factor, config).await?;

        // 生成上涨区间网格（稀疏止盈）
        self.generate_up_zone_grids(&mut levels, base_price, volatility_factor, config).await?;

        info!("生成了 {} 个网格级别", levels.len());
        Ok(levels)
    }

    /// 生成下跌区间网格
    async fn generate_down_zone_grids(
        &self,
        levels: &mut Vec<GridLevel>,
        base_price: f64,
        volatility_factor: f64,
        config: &AsymmetricGridConfig,
    ) -> SigmaXResult<()> {
        let range_size = config.down_range_size() * volatility_factor;

        // 使用新的分布算法计算网格位置
        let price_offsets = self.calculate_grid_positions(
            &config.down_grid_distribution,
            config.down_grid_count,
            config.down_range_start,
            range_size,
        )?;

        for (i, price_offset) in price_offsets.iter().enumerate() {
            let grid_price = base_price * (1.0 + price_offset);

            // 使用新的数量模型计算数量
            let quantity = config.down_quantity_model.calculate_quantity(
                i,
                config.down_grid_count,
                config.down_base_quantity
            );

            let level = GridLevel {
                id: Uuid::new_v4(),
                price: grid_price,
                quantity,
                zone: GridZone::Down,
                status: GridLevelStatus::Waiting,
                order_id: None,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            levels.push(level);
            debug!("下跌网格 {} ({},{}): 价格={:.4}, 数量={:.4}",
                   i + 1,
                   config.down_grid_distribution.description(),
                   config.down_quantity_model.description(),
                   grid_price,
                   quantity);
        }

        Ok(())
    }

    /// 生成上涨区间网格
    async fn generate_up_zone_grids(
        &self,
        levels: &mut Vec<GridLevel>,
        base_price: f64,
        volatility_factor: f64,
        config: &AsymmetricGridConfig,
    ) -> SigmaXResult<()> {
        let range_size = config.up_range_size() * volatility_factor;

        // 使用新的分布算法计算网格位置
        let price_offsets = self.calculate_grid_positions(
            &config.up_grid_distribution,
            config.up_grid_count,
            config.up_range_start,
            range_size,
        )?;

        for (i, price_offset) in price_offsets.iter().enumerate() {
            let grid_price = base_price * (1.0 + price_offset);

            // 使用新的数量模型计算数量
            let quantity = config.up_quantity_model.calculate_quantity(
                i,
                config.up_grid_count,
                config.up_base_quantity
            );

            let level = GridLevel {
                id: Uuid::new_v4(),
                price: grid_price,
                quantity,
                zone: GridZone::Up,
                status: GridLevelStatus::Waiting,
                order_id: None,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            levels.push(level);
            debug!("上涨网格 {} ({},{}): 价格={:.4}, 数量={:.4}",
                   i + 1,
                   config.up_grid_distribution.description(),
                   config.up_quantity_model.description(),
                   grid_price,
                   quantity);
        }

        Ok(())
    }

    /// 检查网格触发条件
    ///
    /// 根据当前价格检查哪些网格级别应该被触发
    #[allow(dead_code)]
    async fn check_grid_triggers(&self, current_price: f64) -> SigmaXResult<Vec<GridLevel>> {
        let state = self.state.read().await;
        let mut triggered_levels = Vec::new();

        for level in &state.active_grid_levels {
            if level.status != GridLevelStatus::Waiting {
                continue;
            }

            let should_trigger = match level.zone {
                GridZone::Down => {
                    // 下跌网格：当价格跌到网格价格时触发买入
                    current_price <= level.price
                }
                GridZone::Up => {
                    // 上涨网格：当价格涨到网格价格时触发卖出
                    current_price >= level.price
                }
            };

            if should_trigger {
                triggered_levels.push(level.clone());
                debug!("网格触发: 价格={:.4}, 当前价格={:.4}, 区域={:?}",
                       level.price, current_price, level.zone);
            }
        }

        Ok(triggered_levels)
    }

    /// 解析交易对字符串
    ///
    /// 将 "BTC/USDT" 格式的字符串解析为 TradingPair
    fn parse_trading_pair(&self, trading_pair_str: &str) -> SigmaXResult<TradingPair> {
        let parts: Vec<&str> = trading_pair_str.split('/').collect();
        if parts.len() != 2 {
            return Err(SigmaXError::Config(format!("无效的交易对格式: {}", trading_pair_str)));
        }

        let base = parts[0].trim().to_uppercase();
        let quote = parts[1].trim().to_uppercase();

        if base.is_empty() || quote.is_empty() {
            return Err(SigmaXError::Config(format!("交易对基础资产或报价资产不能为空: {}", trading_pair_str)));
        }

        Ok(TradingPair::new(base, quote))
    }

    /// 解析交易所ID字符串
    ///
    /// 将字符串转换为 ExchangeId 枚举
    fn parse_exchange_id(&self, exchange_id_str: &str) -> SigmaXResult<ExchangeId> {
        match exchange_id_str.to_lowercase().as_str() {
            "simulator" => Ok(ExchangeId::Simulator),
            "binance" => Ok(ExchangeId::Binance),
            "coinbase" => Ok(ExchangeId::Coinbase),
            "kraken" => Ok(ExchangeId::Kraken),
            _ => Err(SigmaXError::Config(format!("不支持的交易所: {}", exchange_id_str))),
        }
    }

    /// 计算网格分布位置
    ///
    /// 根据分布算法计算每个网格在区间中的位置
    ///
    /// # 参数
    /// - `distribution`: 分布算法
    /// - `grid_count`: 网格数量
    /// - `range_start`: 区间起始位置
    /// - `range_size`: 区间大小
    ///
    /// # 返回
    /// 每个网格的绝对位置
    fn calculate_grid_positions(
        &self,
        distribution: &GridDistribution,
        grid_count: u32,
        range_start: f64,
        range_size: f64,
    ) -> SigmaXResult<Vec<f64>> {
        if grid_count == 0 {
            return Ok(vec![]);
        }

        let positions = match distribution {
            GridDistribution::Linear => {
                self.calculate_linear_positions(grid_count, range_start, range_size)
            }
            GridDistribution::Exponential { factor } => {
                self.calculate_exponential_positions(grid_count, range_start, range_size, *factor)
            }
            GridDistribution::Fibonacci => {
                self.calculate_fibonacci_positions(grid_count, range_start, range_size)
            }
            GridDistribution::Custom { ratios } => {
                self.calculate_custom_positions(ratios, range_start, range_size)
            }
        };

        positions
    }

    /// 计算线性分布位置
    fn calculate_linear_positions(
        &self,
        grid_count: u32,
        range_start: f64,
        range_size: f64,
    ) -> SigmaXResult<Vec<f64>> {
        let mut positions = Vec::new();
        let grid_spacing = range_size / grid_count as f64;

        for i in 0..grid_count {
            let position = range_start + (i as f64 + 0.5) * grid_spacing;
            positions.push(position);
        }

        Ok(positions)
    }

    /// 计算指数分布位置
    fn calculate_exponential_positions(
        &self,
        grid_count: u32,
        range_start: f64,
        range_size: f64,
        factor: f64,
    ) -> SigmaXResult<Vec<f64>> {
        let mut positions = Vec::new();

        // 计算总权重
        let total_weight: f64 = (0..grid_count)
            .map(|i| factor.powi(i as i32))
            .sum();

        let mut cumulative_weight = 0.0;
        for i in 0..grid_count {
            let weight = factor.powi(i as i32);
            cumulative_weight += weight;

            // 计算在区间中的相对位置
            let relative_position = cumulative_weight / total_weight;
            let position = range_start + relative_position * range_size;
            positions.push(position);
        }

        Ok(positions)
    }

    /// 计算斐波那契分布位置
    fn calculate_fibonacci_positions(
        &self,
        grid_count: u32,
        range_start: f64,
        range_size: f64,
    ) -> SigmaXResult<Vec<f64>> {
        let mut positions = Vec::new();

        // 生成斐波那契数列
        let fib_sequence = self.generate_fibonacci_sequence(grid_count as usize);
        let total_fib: u64 = fib_sequence.iter().sum();

        let mut cumulative_fib = 0u64;
        for i in 0..grid_count {
            cumulative_fib += fib_sequence[i as usize];

            // 计算在区间中的相对位置
            let relative_position = cumulative_fib as f64 / total_fib as f64;
            let position = range_start + relative_position * range_size;
            positions.push(position);
        }

        Ok(positions)
    }

    /// 计算自定义分布位置
    fn calculate_custom_positions(
        &self,
        ratios: &[f64],
        range_start: f64,
        range_size: f64,
    ) -> SigmaXResult<Vec<f64>> {
        let mut positions = Vec::new();

        for &ratio in ratios {
            let position = range_start + ratio * range_size;
            positions.push(position);
        }

        Ok(positions)
    }

    /// 生成斐波那契数列
    fn generate_fibonacci_sequence(&self, count: usize) -> Vec<u64> {
        if count == 0 {
            return vec![];
        }
        if count == 1 {
            return vec![1];
        }

        let mut fib = vec![1, 1];
        for i in 2..count {
            let next = fib[i - 1] + fib[i - 2];
            fib.push(next);
        }

        fib
    }

    /// 创建网格订单
    ///
    /// 根据触发的网格级别创建相应的交易订单
    async fn create_grid_orders(&self, triggered_levels: Vec<GridLevel>) -> SigmaXResult<Vec<Order>> {
        let mut orders = Vec::new();

        for level in triggered_levels {
            let order = match level.zone {
                GridZone::Down => {
                    // 下跌网格创建买入订单
                    self.create_buy_order(&level).await?
                }
                GridZone::Up => {
                    // 上涨网格创建卖出订单
                    self.create_sell_order(&level).await?
                }
            };

            // 建立订单到网格的映射关系
            {
                let mut state = self.state.write().await;
                state.add_order_grid_mapping(order.id, level.id);

                // 更新网格级别的订单ID和状态
                if let Some(grid_level) = state.get_grid_level_by_id_mut(level.id) {
                    grid_level.order_id = Some(order.id);
                    grid_level.status = GridLevelStatus::Triggered;
                    grid_level.updated_at = chrono::Utc::now();
                }
            }

            debug!("🔗 建立映射: 订单 {} -> 网格 {}", order.id, level.id);
            orders.push(order);
        }

        Ok(orders)
    }

    /// 创建买入订单
    async fn create_buy_order(&self, level: &GridLevel) -> SigmaXResult<Order> {
        let config = self.config.as_ref()
            .ok_or_else(|| SigmaXError::Config("策略未初始化".to_string()))?;

        // 从配置获取交易对和交易所信息
        let trading_pair = self.parse_trading_pair(&config.trading_pair)?;
        let exchange_id = self.parse_exchange_id(&config.exchange_id)?;

        let quantity = Decimal::from_f64(level.quantity)
            .ok_or_else(|| SigmaXError::Config("无效的数量".to_string()))?;
        let price = Decimal::from_f64(level.price)
            .ok_or_else(|| SigmaXError::Config("无效的价格".to_string()))?;

        let mut order = Order::new(
            exchange_id,
            trading_pair,
            OrderSide::Buy,
            OrderType::Limit,
            quantity,
            Some(price),
        );

        // 设置策略ID
        order.strategy_id = Some(self.id());

        info!("创建买入订单: 价格={:.4}, 数量={:.4}", level.price, level.quantity);
        Ok(order)
    }

    /// 创建卖出订单
    async fn create_sell_order(&self, level: &GridLevel) -> SigmaXResult<Order> {
        let config = self.config.as_ref()
            .ok_or_else(|| SigmaXError::Config("策略未初始化".to_string()))?;

        // 从配置获取交易对和交易所信息
        let trading_pair = self.parse_trading_pair(&config.trading_pair)?;
        let exchange_id = self.parse_exchange_id(&config.exchange_id)?;

        let quantity = Decimal::from_f64(level.quantity)
            .ok_or_else(|| SigmaXError::Config("无效的数量".to_string()))?;
        let price = Decimal::from_f64(level.price)
            .ok_or_else(|| SigmaXError::Config("无效的价格".to_string()))?;

        let mut order = Order::new(
            exchange_id,
            trading_pair,
            OrderSide::Sell,
            OrderType::Limit,
            quantity,
            Some(price),
        );

        // 设置策略ID
        order.strategy_id = Some(self.id());

        info!("创建卖出订单: 价格={:.4}, 数量={:.4}", level.price, level.quantity);
        Ok(order)
    }

    /// 风险检查
    ///
    /// 在生成订单前进行风险检查
    async fn risk_check(&self, orders: &[Order]) -> SigmaXResult<bool> {
        let config = self.config.as_ref()
            .ok_or_else(|| SigmaXError::Config("策略未初始化".to_string()))?;

        let state = self.state.read().await;

        // 检查日交易次数限制
        if state.is_daily_trade_limit_reached(config.max_daily_trades) {
            warn!("已达到日交易次数限制: {}", config.max_daily_trades);
            return Ok(false);
        }

        // 检查持仓限制
        let total_order_value: f64 = orders.iter()
            .filter(|order| order.side == OrderSide::Buy)
            .map(|order| {
                let price = order.price.unwrap_or(Decimal::ZERO).to_f64().unwrap_or(0.0);
                let quantity = order.quantity.to_f64().unwrap_or(0.0);
                price * quantity
            })
            .sum();

        let potential_position_value = state.position_info.current_value + total_order_value;
        if potential_position_value > config.max_position_amount {
            warn!("超出最大持仓限制: 当前={:.2}, 潜在={:.2}, 限制={:.2}",
                  state.position_info.current_value, potential_position_value, config.max_position_amount);
            return Ok(false);
        }

        // 检查动态止损
        if let Some(stop_loss_triggered) = self.check_stop_loss(config, &state).await? {
            if stop_loss_triggered {
                warn!("触发止损，停止策略运行");
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// 检查是否需要重置网格，并在需要时执行
    ///
    /// # 参数
    /// - `current_price`: 当前市场价格
    ///
    /// # 返回
    /// 如果执行了重置返回 true，否则返回 false
    async fn check_and_recenter_grid_if_needed(&self, current_price: f64) -> SigmaXResult<bool> {
        let config = self.config.as_ref().ok_or_else(|| {
            SigmaXError::Config("策略未初始化".to_string())
        })?;

        // 检查是否启用了网格重置功能
        let threshold = match config.recenter_threshold_percent {
            Some(t) => t,
            None => return Ok(false), // 未启用重置功能
        };

        let mut state = self.state.write().await;
        let base_price = state.current_base_price;

        if base_price <= 0.0 {
            return Ok(false); // 基准价无效
        }

        // 计算当前价格偏离基准价的百分比
        let deviation = (current_price - base_price).abs() / base_price;

        if deviation > threshold {
            info!(
                "🔄 触发网格重置: 市价 {:.4} 已偏离基准价 {:.4} 超过 {:.2}%",
                current_price, base_price, threshold * 100.0
            );

            // 1. 取消所有等待中的旧网格订单
            let mut orders_to_cancel = Vec::new();
            for level in state.active_grid_levels.iter_mut() {
                if level.status == GridLevelStatus::Waiting {
                    if let Some(order_id) = level.order_id {
                        orders_to_cancel.push(order_id);
                    }
                    level.status = GridLevelStatus::Cancelled;
                }
            }

            // 先释放状态锁，避免在异步调用中持有锁
            drop(state);

            // 真正取消交易所订单
            if !orders_to_cancel.is_empty() {
                info!("🔄 网格重置：需要取消 {} 个交易所订单", orders_to_cancel.len());
                self.cancel_orders_at_exchange(orders_to_cancel.clone()).await?;
            }

            // 重新获取状态锁并更新内部状态
            let mut state = self.state.write().await;

            // 更新内部状态
            for order_id in orders_to_cancel {
                state.remove_pending_order(order_id);
                // 同时移除订单-网格映射
                state.remove_order_grid_mapping(order_id);
            }

            // 2. 更新基准价为当前市价
            state.update_base_price(current_price);

            // 3. 重新生成所有网格级别
            let volatility = state.current_volatility;
            let new_grid_levels = self.generate_grid_levels(current_price, volatility).await?;
            state.active_grid_levels = new_grid_levels;

            // 4. 清除与旧网格相关的挂单记录
            state.pending_orders.clear();

            info!("✅ 网格重置完成，新的基准价为: {:.4}", current_price);
            return Ok(true);
        }

        Ok(false)
    }

    /// 检查动态止损条件
    ///
    /// 根据配置的止损类型进行相应的止损检查
    async fn check_stop_loss(
        &self,
        config: &AsymmetricGridConfig,
        state: &AsymmetricGridState,
    ) -> SigmaXResult<Option<bool>> {
        // 如果没有持仓，无需检查止损
        if state.position_info.total_quantity <= 0.0 {
            return Ok(None);
        }

        let current_price = state.current_market_price;
        let entry_price = state.position_info.average_cost;

        if entry_price <= 0.0 {
            return Ok(None);
        }

        match &config.stop_loss_config {
            StopLossConfig::Disabled => Ok(None),

            StopLossConfig::Fixed { percent } => {
                let stop_price = StopLossCalculator::fixed_stop_price(entry_price, *percent, true);
                let triggered = current_price <= stop_price;

                if triggered {
                    warn!("固定止损触发: 当前价格={:.4}, 止损价={:.4}, 入场价={:.4}",
                          current_price, stop_price, entry_price);
                }

                Ok(Some(triggered))
            }

            StopLossConfig::Trailing { trail_percent } => {
                // 获取或初始化最高价记录
                let high_water_mark = state.position_info.high_water_mark.unwrap_or(entry_price);

                // 更新最高价记录（只有在价格上涨时才更新）
                let new_high_water_mark = high_water_mark.max(current_price);

                // 计算追踪止损价格
                let stop_price = StopLossCalculator::trailing_stop_price(new_high_water_mark, *trail_percent, true);
                let triggered = current_price <= stop_price;

                if triggered {
                    warn!("追踪止损触发: 当前价格={:.4}, 止损价={:.4}, 最高价={:.4}",
                          current_price, stop_price, new_high_water_mark);
                } else {
                    debug!("追踪止损更新: 最高价={:.4} -> {:.4}, 止损价={:.4}",
                           high_water_mark, new_high_water_mark, stop_price);
                }

                Ok(Some(triggered))
            }

            StopLossConfig::ATR { period: _, multiplier } => {
                // 简化实现：使用当前波动率作为ATR的近似值
                let atr_value = state.current_volatility * current_price;
                let stop_price = StopLossCalculator::atr_stop_price(entry_price, atr_value, *multiplier, true);
                let triggered = current_price <= stop_price;

                if triggered {
                    warn!("ATR止损触发: 当前价格={:.4}, 止损价={:.4}, ATR={:.4}",
                          current_price, stop_price, atr_value);
                }

                Ok(Some(triggered))
            }

            StopLossConfig::Hybrid {
                max_loss_percent,
                trail_percent,
                atr_multiplier,
                atr_period: _
            } => {
                // 混合止损：检查所有条件，任一触发即止损

                // 1. 固定最大亏损检查
                let max_loss_stop = StopLossCalculator::fixed_stop_price(entry_price, *max_loss_percent, true);
                let max_loss_triggered = current_price <= max_loss_stop;

                // 2. 追踪止损检查
                let high_water_mark = state.position_info.high_water_mark.unwrap_or(entry_price);
                let new_high_water_mark = high_water_mark.max(current_price);
                let trail_stop = StopLossCalculator::trailing_stop_price(new_high_water_mark, *trail_percent, true);
                let trail_triggered = current_price <= trail_stop;

                // 3. ATR止损检查
                let atr_value = state.current_volatility * current_price;
                let atr_stop = StopLossCalculator::atr_stop_price(entry_price, atr_value, *atr_multiplier, true);
                let atr_triggered = current_price <= atr_stop;

                let triggered = max_loss_triggered || trail_triggered || atr_triggered;

                if triggered {
                    warn!("混合止损触发: 最大亏损={}, 追踪止损={}, ATR止损={}",
                          max_loss_triggered, trail_triggered, atr_triggered);
                    warn!("当前价格={:.4}, 止损价格: 最大亏损={:.4}, 追踪={:.4}, ATR={:.4}",
                          current_price, max_loss_stop, trail_stop, atr_stop);
                }

                Ok(Some(triggered))
            }
        }
    }

    /// 更新当前的市场宏观状态
    ///
    /// 根据长期移动平均线判断市场趋势，帮助策略做出更智能的决策
    async fn update_market_regime(&self, current_price: f64) -> SigmaXResult<()> {
        let config = self.config.as_ref().ok_or_else(|| {
            SigmaXError::Config("策略未初始化".to_string())
        })?;

        // 检查是否启用了市场状态过滤器
        let filter_config = match &config.regime_filter {
            Some(fc) if fc.enabled => fc,
            _ => return Ok(()), // 未启用或已禁用
        };

        // 检查是否需要更新（避免频繁计算）
        {
            let state = self.state.read().await;
            if !state.should_update_market_regime(filter_config.update_interval_minutes) {
                return Ok(()); // 还未到更新时间
            }
        }

        // 计算真实的移动平均线
        let ma_value = self.calculate_moving_average(filter_config).await?;

        // 根据价格与均线的关系判断市场状态
        let new_regime = if current_price > ma_value {
            MarketRegime::Bullish
        } else {
            MarketRegime::Bearish
        };

        // 更新状态
        let mut state = self.state.write().await;
        let old_regime = state.market_regime;
        state.update_market_regime(new_regime, ma_value);

        // 记录状态变化
        if old_regime != new_regime {
            info!(
                "📊 市场状态变化: {:?} -> {:?}, 当前价格: {:.4}, {}均线: {:.4}",
                old_regime, new_regime, current_price,
                filter_config.ma_type.description(), ma_value
            );
        } else {
            debug!(
                "📊 市场状态更新: {:?}, 当前价格: {:.4}, {}均线: {:.4}",
                new_regime, current_price,
                filter_config.ma_type.description(), ma_value
            );
        }

        Ok(())
    }

    /// 计算移动平均线
    ///
    /// 根据配置获取历史数据并计算真实的移动平均线
    async fn calculate_moving_average(&self, filter_config: &crate::asymmetric_grid::config::RegimeFilterConfig) -> SigmaXResult<f64> {
        let config = self.config.as_ref().ok_or_else(|| {
            SigmaXError::Config("策略未初始化".to_string())
        })?;

        // 解析交易对
        let trading_pair = self.parse_trading_pair(&config.trading_pair)?;

        // 从数据提供者获取历史K线数据
        // 我们需要比周期多一些数据来确保计算准确性
        let required_candles = filter_config.period + 50; // 额外50根K线确保数据充足

        // 计算时间范围：从现在往前推算所需的时间
        let end_time = chrono::Utc::now();
        let start_time = end_time - chrono::Duration::hours(required_candles as i64);

        let historical_candles = match self.services.data_provider.get_historical_candles(
            &trading_pair,
            sigmax_core::TimeFrame::H1, // 使用1小时K线
            start_time,
            end_time,
        ).await {
            Ok(candles) => candles,
            Err(e) => {
                warn!("⚠️  获取历史K线数据失败: {}, 使用当前价格作为均线值", e);
                // 如果无法获取历史数据，返回当前价格作为均线值
                // 这样至少不会产生错误的市场状态判断
                return Ok(self.get_current_price().await.unwrap_or(0.0));
            }
        };

        if historical_candles.is_empty() {
            warn!("⚠️  历史K线数据为空，使用当前价格作为均线值");
            return Ok(self.get_current_price().await.unwrap_or(0.0));
        }

        // 提取收盘价序列
        let close_prices: Vec<f64> = historical_candles
            .iter()
            .map(|candle| candle.close.to_f64().unwrap_or(0.0))
            .collect();

        if close_prices.len() < filter_config.period {
            warn!(
                "⚠️  历史数据不足: 需要{}根K线，实际{}根，使用当前价格作为均线值",
                filter_config.period, close_prices.len()
            );
            return Ok(self.get_current_price().await.unwrap_or(0.0));
        }

        // 根据配置的均线类型计算移动平均线
        let ma_value = match filter_config.ma_type {
            crate::asymmetric_grid::config::MaType::SMA => {
                TechnicalIndicators::sma(&close_prices, filter_config.period)
                    .unwrap_or_else(|| {
                        warn!("⚠️  SMA计算失败，使用当前价格");
                        close_prices.last().copied().unwrap_or(0.0)
                    })
            }
            crate::asymmetric_grid::config::MaType::EMA => {
                let ema_values = TechnicalIndicators::ema(&close_prices, filter_config.period);
                ema_values.last().copied().unwrap_or_else(|| {
                    warn!("⚠️  EMA计算失败，使用当前价格");
                    close_prices.last().copied().unwrap_or(0.0)
                })
            }
        };

        debug!(
            "📈 计算移动平均线: 类型={:?}, 周期={}, 数据点={}, 均线值={:.4}",
            filter_config.ma_type, filter_config.period, close_prices.len(), ma_value
        );

        Ok(ma_value)
    }

    /// 获取当前价格
    ///
    /// 从策略状态或数据提供者获取当前市场价格
    async fn get_current_price(&self) -> SigmaXResult<f64> {
        let config = self.config.as_ref().ok_or_else(|| {
            SigmaXError::Config("策略未初始化".to_string())
        })?;

        let trading_pair = self.parse_trading_pair(&config.trading_pair)?;

        match self.services.data_provider.get_price(&trading_pair).await {
            Ok(price) => Ok(price.to_f64().unwrap_or(0.0)),
            Err(_) => {
                // 如果无法获取最新价格，从策略状态获取
                let state = self.state.read().await;
                Ok(state.current_market_price)
            }
        }
    }



    /// 检查网格触发条件（应用市场状态过滤器）
    ///
    /// 根据当前市场状态决定是否应该触发特定的网格级别
    async fn check_grid_triggers_with_filter(&self, current_price: f64) -> SigmaXResult<Vec<GridLevel>> {
        let state = self.state.read().await;
        let config = self.config.as_ref().unwrap(); // 在此处应该是安全的

        // 检查是否启用了市场状态过滤器
        let filter_enabled = config.regime_filter
            .as_ref()
            .map_or(false, |fc| fc.enabled);

        let mut triggered_levels = Vec::new();

        info!("🔍 检查网格触发: 当前价格={:.4}, 活跃网格数量={}",
              current_price, state.active_grid_levels.len());

        for level in &state.active_grid_levels {
            if level.status != GridLevelStatus::Waiting {
                debug!("跳过网格 {:.4}: 状态={:?}", level.price, level.status);
                continue;
            }

            // 应用市场状态过滤器
            if filter_enabled {
                let should_skip = match (state.market_regime, &level.zone) {
                    // 熊市状态下，跳过下跌吸筹网格（避免逆势接飞刀）
                    (MarketRegime::Bearish, GridZone::Down) => {
                        debug!(
                            "🐻 熊市状态，跳过下跌吸筹网格 at price {:.4}",
                            level.price
                        );
                        true
                    }
                    // 牛市状态下，跳过上涨止盈网格（让利润奔跑）
                    (MarketRegime::Bullish, GridZone::Up) => {
                        debug!(
                            "🐂 牛市状态，跳过上涨止盈网格 at price {:.4}",
                            level.price
                        );
                        true
                    }
                    _ => false,
                };

                if should_skip {
                    continue;
                }
            }

            // 检查价格触发条件
            let should_trigger = match level.zone {
                GridZone::Down => current_price <= level.price,
                GridZone::Up => current_price >= level.price,
            };

            debug!("检查网格 {:.4} ({:?}): 当前价格={:.4}, 应触发={}",
                   level.price, level.zone, current_price, should_trigger);

            if should_trigger {
                triggered_levels.push(level.clone());
                info!(
                    "🎯 网格触发: {:?} zone at price {:.4}, 当前价格: {:.4}",
                    level.zone, level.price, current_price
                );
            }
        }

        if !triggered_levels.is_empty() {
            info!(
                "⚡ 触发 {} 个网格级别，市场状态: {:?}",
                triggered_levels.len(),
                state.market_regime
            );
        }

        Ok(triggered_levels)
    }

    // ==================== 重构后的辅助函数 ====================

    /// 1. 验证策略状态
    ///
    /// 检查策略是否处于可以处理市场数据的状态
    async fn validate_strategy_state(&self) -> SigmaXResult<()> {
        let strategy_status = self.strategy_status.read().await;
        if !matches!(*strategy_status, StrategyStatus::Running) {
            return Err(AsymmetricGridError::state_inconsistency(
                "Running",
                format!("{:?}", *strategy_status),
                "策略必须处于运行状态才能处理市场数据"
            ).into());
        }
        Ok(())
    }

    /// 2. 提取和计算市场指标
    ///
    /// 从K线数据中提取价格并计算相关技术指标
    async fn extract_and_calculate_metrics(&self, candle: &Candle) -> SigmaXResult<MarketMetrics> {
        let current_price = candle.close.to_f64().unwrap_or(0.0);
        if current_price <= 0.0 {
            return Err(AsymmetricGridError::invalid_market_data(
                current_price,
                candle.timestamp.to_string(),
                "价格必须大于0"
            ).into());
        }

        debug!("📊 处理市场数据: 价格={:.4}", current_price);

        // 计算当前波动率
        let volatility = self.calculate_volatility(candle).await?;

        Ok(MarketMetrics {
            current_price,
            volatility,
            timestamp: Utc::now(),
        })
    }

    /// 3. 更新策略状态
    ///
    /// 使用最新的市场指标更新策略内部状态
    async fn update_strategy_state(&self, metrics: &MarketMetrics) -> SigmaXResult<()> {
        let mut state = self.state.write().await;
        state.update_market_price(metrics.current_price);
        state.update_volatility(metrics.volatility);

        debug!("📈 状态更新: 价格={:.4}, 波动率={:.4}",
               metrics.current_price, metrics.volatility);

        Ok(())
    }

    /// 4. 初始化网格（如果需要）
    ///
    /// 检查是否需要初始化网格，并在需要时执行初始化
    async fn initialize_grid_if_needed(&self, current_price: f64, volatility: f64) -> SigmaXResult<bool> {
        let mut state = self.state.write().await;

        // 检查是否需要初始化
        let needs_initialization = state.active_grid_levels.is_empty() || state.current_base_price == 0.0;

        if !needs_initialization {
            return Ok(false);
        }

        // 确定基准价格
        let base_price = if let Some(config) = &self.config {
            if config.base_price > 0.0 {
                config.base_price
            } else {
                current_price
            }
        } else {
            current_price
        };

        // 更新基准价格
        state.update_base_price(base_price);

        // 生成网格级别
        drop(state); // 释放写锁，因为 generate_grid_levels 可能需要读锁
        let grid_levels = self.generate_grid_levels(base_price, volatility).await?;

        // 重新获取写锁并更新网格
        let mut state = self.state.write().await;
        state.active_grid_levels = grid_levels;

        info!("🏗️  网格初始化完成: 基准价格={:.4}, 网格数量={}",
              base_price, state.active_grid_levels.len());

        // 🔥 添加详细的网格信息
        for (i, level) in state.active_grid_levels.iter().enumerate() {
            info!("  网格 {}: {:?} zone, 价格={:.4}, 数量={:.4}, 状态={:?}",
                  i + 1, level.zone, level.price, level.quantity, level.status);
        }

        Ok(true)
    }

    /// 5. 执行智能化检查
    ///
    /// 执行网格重置、市场状态更新、止损检查等智能化功能
    async fn execute_intelligent_checks(&self, current_price: f64) -> SigmaXResult<IntelligentCheckResult> {
        // 5.1 检查是否需要重置网格（优先级最高）
        if self.check_and_recenter_grid_if_needed(current_price).await? {
            info!("🔄 网格已重置，等待下一轮数据");
            return Ok(IntelligentCheckResult::EarlyReturn);
        }

        // 5.2 更新市场状态（在网格触发检查之前）
        if let Err(e) = self.update_market_regime(current_price).await {
            warn!("⚠️  更新市场状态失败: {}", e);
            // 不影响主流程，继续执行
        }

        // 5.3 检查动态止损
        let config = self.config.as_ref().ok_or_else(|| {
            SigmaXError::Config("策略未初始化".to_string())
        })?;

        let state = self.state.read().await;
        if let Some(stop_loss_triggered) = self.check_stop_loss(config, &state).await? {
            if stop_loss_triggered {
                warn!("🛑 触发止损，停止策略运行");
                return Ok(IntelligentCheckResult::StopLossTriggered);
            }
        }

        Ok(IntelligentCheckResult::Continue)
    }

    /// 6. 处理网格触发
    ///
    /// 应用市场状态过滤器并检查网格触发条件
    async fn process_grid_triggers(&self, current_price: f64) -> SigmaXResult<Vec<GridLevel>> {
        let triggered_levels = self.check_grid_triggers_with_filter(current_price).await?;

        if !triggered_levels.is_empty() {
            info!("⚡ 检测到 {} 个网格触发", triggered_levels.len());
        }

        Ok(triggered_levels)
    }

    /// 7. 创建和验证订单
    ///
    /// 根据触发的网格级别创建订单并执行风险检查
    async fn create_and_validate_orders(&self, triggered_levels: Vec<GridLevel>) -> SigmaXResult<Vec<Order>> {
        // 7.1 创建订单
        let orders = self.create_grid_orders(triggered_levels).await?;

        if orders.is_empty() {
            return Ok(Vec::new());
        }

        // 7.2 风险检查
        if !self.risk_check(&orders).await? {
            warn!("⚠️  风险检查未通过，跳过订单生成");
            return Ok(Vec::new());
        }

        info!("✅ 订单验证通过: {} 个订单", orders.len());
        Ok(orders)
    }

    /// 8. 完成订单处理
    ///
    /// 更新网格状态并记录订单信息
    async fn finalize_order_processing(&self, orders: &[Order]) -> SigmaXResult<()> {
        if orders.is_empty() {
            return Ok(());
        }

        let mut state = self.state.write().await;

        // 添加待处理订单
        for order in orders {
            state.add_pending_order(order.clone());
        }

        info!("📝 订单处理完成: 生成 {} 个网格订单", orders.len());
        Ok(())
    }
}

#[async_trait]
impl Strategy for AsymmetricVolatilityGridStrategy {
    fn id(&self) -> StrategyId {
        self.id
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn status(&self) -> StrategyStatus {
        if let Ok(status) = self.strategy_status.try_read() {
            *status
        } else {
            StrategyStatus::Created
        }
    }

    async fn initialize(&self) -> SigmaXResult<()> {
        info!("初始化非对称波动率网格策略");

        // 设置策略状态为运行中
        {
            let mut strategy_status = self.strategy_status.write().await;
            *strategy_status = StrategyStatus::Running;
        }

        // 初始化策略状态
        {
            let mut state = self.state.write().await;
            state.start_time = Utc::now();
            state.last_update_time = Utc::now();
        }

        info!("非对称波动率网格策略初始化完成");
        Ok(())
    }

    async fn on_market_data(&self, candle: &Candle) -> SigmaXResult<Vec<Order>> {
        // 🎯 协调者模式：将复杂的处理流程分解为清晰的步骤

        // 1. 验证策略状态
        self.validate_strategy_state().await?;

        // 2. 提取和计算市场指标
        let metrics = self.extract_and_calculate_metrics(candle).await?;

        // 3. 更新策略状态
        self.update_strategy_state(&metrics).await?;

        // 4. 初始化网格（如果需要）
        if self.initialize_grid_if_needed(metrics.current_price, metrics.volatility).await? {
            info!("🏗️  网格已初始化，继续检查触发条件");
            // 🔥 修复：初始化后不要立即返回，而是继续检查触发条件
            // return Ok(Vec::new()); // 注释掉这行，允许在同一轮中检查触发
        }

        // 5. 执行智能化检查（网格重置、市场状态、止损等）
        match self.execute_intelligent_checks(metrics.current_price).await? {
            IntelligentCheckResult::EarlyReturn => return Ok(Vec::new()),
            IntelligentCheckResult::StopLossTriggered => return Ok(Vec::new()),
            IntelligentCheckResult::Continue => {},
        }

        // 6. 处理网格触发
        let triggered_levels = self.process_grid_triggers(metrics.current_price).await?;
        if triggered_levels.is_empty() {
            return Ok(Vec::new());
        }

        // 7. 创建和验证订单
        let orders = self.create_and_validate_orders(triggered_levels).await?;

        // 8. 完成订单处理
        self.finalize_order_processing(&orders).await?;

        Ok(orders)
    }

    async fn on_order_update(&self, order: &Order) -> SigmaXResult<()> {
        debug!("📋 处理订单更新: 订单ID={}, 状态={:?}", order.id, order.status);

        let mut state = self.state.write().await;

        // 更新待处理订单状态
        if let Some(pending_order) = state.pending_orders.get_mut(&order.id) {
            *pending_order = order.clone();
        }

        // 查找对应的网格级别
        let grid_id = state.get_grid_id_by_order(order.id);

        // 根据订单状态更新网格级别状态
        match order.status {
            sigmax_core::OrderStatus::Filled => {
                // 订单成交
                if let Some(completed_order) = state.remove_pending_order(order.id) {
                    state.add_completed_order(completed_order);
                }

                if let Some(grid_id) = grid_id {
                    self.handle_order_filled(&mut state, order, grid_id).await?;
                }
            }
            sigmax_core::OrderStatus::Cancelled => {
                // 订单取消
                if let Some(completed_order) = state.remove_pending_order(order.id) {
                    state.add_completed_order(completed_order);
                }

                if let Some(grid_id) = grid_id {
                    self.handle_order_cancelled(&mut state, order, grid_id).await?;
                }
            }
            sigmax_core::OrderStatus::Rejected => {
                // 订单被拒绝
                if let Some(completed_order) = state.remove_pending_order(order.id) {
                    state.add_completed_order(completed_order);
                }

                if let Some(grid_id) = grid_id {
                    self.handle_order_rejected(&mut state, order, grid_id).await?;
                }
            }
            _ => {
                // 其他状态（Pending, PartiallyFilled等）暂时不处理网格状态
                debug!("📋 订单状态 {:?} 暂不处理网格状态", order.status);
            }
        }

        Ok(())
    }

    async fn on_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        debug!("处理交易成交: 数量={:?}, 价格={:?}", trade.quantity, trade.price);

        let mut state = self.state.write().await;

        // 计算交易盈亏
        let trade_value = trade.quantity.to_f64().unwrap_or(0.0) * trade.price.to_f64().unwrap_or(0.0);
        let pnl = match trade.side {
            OrderSide::Buy => -trade_value, // 买入成本
            OrderSide::Sell => trade_value,  // 卖出收入
        };

        // 更新持仓信息
        match trade.side {
            OrderSide::Buy => {
                let trade_quantity = trade.quantity.to_f64().unwrap_or(0.0);

                state.position_info.total_quantity += trade_quantity;
                state.position_info.total_cost += trade_value;

                // 更新平均成本
                if state.position_info.total_quantity > 0.0 {
                    state.position_info.average_cost = state.position_info.total_cost / state.position_info.total_quantity;
                }
            }
            OrderSide::Sell => {
                let trade_quantity = trade.quantity.to_f64().unwrap_or(0.0);
                state.position_info.total_quantity -= trade_quantity;

                // 计算已实现盈亏
                let cost_basis = state.position_info.average_cost * trade_quantity;
                let realized_pnl = trade_value - cost_basis;
                state.position_info.realized_pnl += realized_pnl;

                // 更新总成本
                if state.position_info.total_quantity > 0.0 {
                    state.position_info.total_cost -= cost_basis;
                } else {
                    state.position_info.total_cost = 0.0;
                    state.position_info.average_cost = 0.0;
                }
            }
        }

        // 更新交易统计
        state.update_trading_stats(pnl);

        info!("交易完成: 方向={:?}, 数量={:.4}, 价格={:.4}, 盈亏={:.2}",
              trade.side, trade.quantity, trade.price, pnl);

        Ok(())
    }

    async fn stop(&self) -> SigmaXResult<()> {
        info!("停止非对称波动率网格策略");

        {
            let mut strategy_status = self.strategy_status.write().await;
            *strategy_status = StrategyStatus::Stopped;
        }

        // 取消所有待处理订单
        let pending_order_ids = {
            let state = self.state.read().await;
            state.pending_orders.keys().cloned().collect::<Vec<_>>()
        };

        if !pending_order_ids.is_empty() {
            info!("🛑 策略停止：需要取消 {} 个待处理订单", pending_order_ids.len());

            // 真正调用交易所取消订单
            self.cancel_orders_at_exchange(pending_order_ids.clone()).await?;

            // 更新内部状态
            let mut state = self.state.write().await;
            for order_id in pending_order_ids {
                state.remove_pending_order(order_id);
                // 同时移除订单-网格映射
                state.remove_order_grid_mapping(order_id);
            }
        }

        info!("非对称波动率网格策略已停止");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        matches!(self.status(), StrategyStatus::Running | StrategyStatus::Paused)
    }

    fn is_running(&self) -> bool {
        matches!(self.status(), StrategyStatus::Running)
    }
}

/// 订单状态处理方法
impl AsymmetricVolatilityGridStrategy {
    /// 处理订单成交
    ///
    /// 当订单成交时，更新对应网格级别的状态并决定下一步行动
    async fn handle_order_filled(
        &self,
        state: &mut AsymmetricGridState,
        order: &Order,
        grid_id: Uuid,
    ) -> SigmaXResult<()> {
        info!("✅ 订单成交: 订单ID={}, 网格ID={}", order.id, grid_id);

        // 移除订单-网格映射
        state.remove_order_grid_mapping(order.id);

        // 更新网格级别状态
        if let Some(grid_level) = state.get_grid_level_by_id_mut(grid_id) {
            grid_level.status = GridLevelStatus::Filled;
            grid_level.updated_at = chrono::Utc::now();

            let price = grid_level.price;
            let quantity = grid_level.quantity;
            let zone = grid_level.zone;

            info!(
                "🎯 网格成交: 价格={:.4}, 数量={:.4}, 区域={:?}",
                price, quantity, zone
            );
        } else {
            warn!("⚠️  找不到对应的网格级别: {}", grid_id);
            return Ok(());
        }

        // 根据策略配置决定网格的下一步行动
        self.decide_grid_next_action(state, grid_id).await?;

        Ok(())
    }

    /// 处理订单取消
    ///
    /// 当订单被取消时，重置网格级别状态以便重新触发
    async fn handle_order_cancelled(
        &self,
        state: &mut AsymmetricGridState,
        order: &Order,
        grid_id: Uuid,
    ) -> SigmaXResult<()> {
        info!("❌ 订单取消: 订单ID={}, 网格ID={}", order.id, grid_id);

        // 移除订单-网格映射
        state.remove_order_grid_mapping(order.id);

        // 重置网格级别状态，使其可以重新触发
        if let Some(grid_level) = state.get_grid_level_by_id_mut(grid_id) {
            grid_level.status = GridLevelStatus::Waiting;
            grid_level.order_id = None;
            grid_level.updated_at = chrono::Utc::now();

            info!("🔄 网格重置: 价格={:.4}, 状态=Waiting", grid_level.price);
        } else {
            warn!("⚠️  找不到对应的网格级别: {}", grid_id);
        }

        Ok(())
    }

    /// 处理订单被拒绝
    ///
    /// 当订单被拒绝时，标记网格为错误状态
    async fn handle_order_rejected(
        &self,
        state: &mut AsymmetricGridState,
        order: &Order,
        grid_id: Uuid,
    ) -> SigmaXResult<()> {
        warn!("🚫 订单被拒绝: 订单ID={}, 网格ID={}", order.id, grid_id);

        // 移除订单-网格映射
        state.remove_order_grid_mapping(order.id);

        // 标记网格为错误状态
        if let Some(grid_level) = state.get_grid_level_by_id_mut(grid_id) {
            grid_level.status = GridLevelStatus::Error;
            grid_level.order_id = None;
            grid_level.updated_at = chrono::Utc::now();

            warn!("💥 网格错误: 价格={:.4}, 状态=Error", grid_level.price);
        } else {
            warn!("⚠️  找不到对应的网格级别: {}", grid_id);
        }

        Ok(())
    }

    /// 决定网格的下一步行动
    ///
    /// 根据策略配置和当前市场状态，决定成交后的网格是否重置、移除或保持
    async fn decide_grid_next_action(
        &self,
        state: &mut AsymmetricGridState,
        grid_id: Uuid,
    ) -> SigmaXResult<()> {
        // 获取网格级别
        let grid_level = match state.get_grid_level_by_id_mut(grid_id) {
            Some(level) => level,
            None => {
                warn!("⚠️  找不到对应的网格级别: {}", grid_id);
                return Ok(());
            }
        };

        // 根据网格区域决定下一步行动
        // 目前简化处理：默认启用网格复用，成交后重置为等待状态
        match grid_level.zone {
            GridZone::Down => {
                // 下跌网格成交（买入完成）
                // 重置网格以便再次买入
                grid_level.status = GridLevelStatus::Waiting;
                grid_level.order_id = None;
                grid_level.updated_at = chrono::Utc::now();

                info!("🔄 下跌网格重置: 价格={:.4}, 可重新买入", grid_level.price);
            }
            GridZone::Up => {
                // 上涨网格成交（卖出完成）
                // 重置网格以便再次卖出
                grid_level.status = GridLevelStatus::Waiting;
                grid_level.order_id = None;
                grid_level.updated_at = chrono::Utc::now();

                info!("🔄 上涨网格重置: 价格={:.4}, 可重新卖出", grid_level.price);
            }
        }

        Ok(())
    }

    /// 统一的订单取消方法
    ///
    /// 真正调用交易所取消订单，而不仅仅是更新内部状态
    async fn cancel_orders_at_exchange(&self, order_ids: Vec<OrderId>) -> SigmaXResult<()> {
        if order_ids.is_empty() {
            return Ok(());
        }

        info!("🗑️  开始取消 {} 个交易所订单", order_ids.len());

        let mut success_count = 0;
        let mut failed_orders = Vec::new();

        for order_id in order_ids {
            match self.services.order_manager.cancel_order(order_id).await {
                Ok(()) => {
                    success_count += 1;
                    debug!("✅ 订单取消成功: {}", order_id);
                }
                Err(e) => {
                    failed_orders.push((order_id, e));
                    warn!("❌ 订单取消失败: {}, 错误: {}", order_id, failed_orders.last().unwrap().1);
                }
            }
        }

        // 报告取消结果
        if success_count > 0 {
            info!("✅ 成功取消 {} 个订单", success_count);
        }

        if !failed_orders.is_empty() {
            warn!("⚠️  {} 个订单取消失败", failed_orders.len());
            for (order_id, error) in &failed_orders {
                warn!("   订单 {}: {}", order_id, error);
            }

            // 对于取消失败的订单，我们仍然从内部状态中移除
            // 但记录警告，以便后续处理
            warn!("⚠️  取消失败的订单已从内部状态移除，但可能仍在交易所存在");
        }

        Ok(())
    }
}

/// 便捷构建方法
impl AsymmetricVolatilityGridStrategy {
    /// 创建策略构建器
    ///
    /// # 返回
    /// 新的策略构建器实例
    pub fn builder() -> crate::asymmetric_grid::builder::AsymmetricVolatilityGridStrategyBuilder {
        crate::asymmetric_grid::builder::AsymmetricVolatilityGridStrategyBuilder::new()
    }
}

#[cfg(test)]
mod tests {
    use uuid::Uuid;

    #[test]
    fn test_strategy_id_method() {
        // 测试策略ID方法的正确性
        let strategy_id = Uuid::new_v4();

        // 创建一个简单的策略实例来测试ID方法
        // 注意：这里我们只测试ID方法，不涉及复杂的服务依赖

        // 验证UUID生成和比较的正确性
        let another_id = Uuid::new_v4();
        assert_ne!(strategy_id, another_id, "不同的UUID应该不相等");

        // 验证UUID的一致性
        let same_id = strategy_id;
        assert_eq!(strategy_id, same_id, "相同的UUID应该相等");
    }

    #[test]
    fn test_order_grid_mapping() {
        use crate::asymmetric_grid::state::{AsymmetricGridState, GridLevel, GridZone, GridLevelStatus};
        use chrono::Utc;

        // 创建测试状态
        let mut state = AsymmetricGridState::new();

        // 创建测试网格级别
        let grid_id = Uuid::new_v4();
        let order_id = Uuid::new_v4();

        let grid_level = GridLevel {
            id: grid_id,
            price: 100.0,
            quantity: 0.1,
            zone: GridZone::Down,
            status: GridLevelStatus::Waiting,
            order_id: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        state.add_grid_level(grid_level);

        // 测试添加映射
        state.add_order_grid_mapping(order_id, grid_id);
        assert_eq!(state.get_grid_id_by_order(order_id), Some(grid_id), "应该能够通过订单ID找到网格ID");

        // 测试查找网格级别
        let found_grid = state.get_grid_level_by_id(grid_id);
        assert!(found_grid.is_some(), "应该能够通过网格ID找到网格级别");
        assert_eq!(found_grid.unwrap().id, grid_id, "找到的网格ID应该匹配");

        // 测试移除映射
        let removed_grid_id = state.remove_order_grid_mapping(order_id);
        assert_eq!(removed_grid_id, Some(grid_id), "移除映射应该返回正确的网格ID");
        assert_eq!(state.get_grid_id_by_order(order_id), None, "移除后应该找不到映射");
    }

    #[test]
    fn test_cancel_orders_logic() {
        // 测试订单取消逻辑的基本功能
        let order_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];

        // 验证订单ID列表不为空
        assert!(!order_ids.is_empty(), "测试订单ID列表不应为空");
        assert_eq!(order_ids.len(), 3, "应该有3个测试订单ID");

        // 验证每个订单ID都是唯一的
        for i in 0..order_ids.len() {
            for j in (i + 1)..order_ids.len() {
                assert_ne!(order_ids[i], order_ids[j], "订单ID应该是唯一的");
            }
        }
    }
}
