//! 非对称网格策略错误类型
//!
//! 提供结构化的、类型安全的错误处理

use thiserror::Error;
use uuid::Uuid;
use sigmax_core::{SigmaXError, OrderId};

/// 非对称网格策略错误
///
/// 提供详细的错误信息和上下文，便于调试和程序化处理
#[derive(Error, Debug)]
pub enum AsymmetricGridError {
    /// 策略未初始化
    #[error("策略未初始化: {reason}")]
    NotInitialized {
        reason: String,
    },

    /// 配置验证失败
    #[error("配置验证失败: {field} = {value}, 原因: {reason}")]
    ConfigValidation {
        field: String,
        value: String,
        reason: String,
    },

    /// 网格生成失败
    #[error("网格生成失败: 基准价格={base_price}, 波动率={volatility}, 原因: {reason}")]
    GridGeneration {
        base_price: f64,
        volatility: f64,
        reason: String,
    },

    /// 网格级别操作失败
    #[error("网格级别操作失败: level_id={level_id}, 操作: {operation}, 原因: {reason}")]
    GridLevelOperation {
        level_id: Uuid,
        operation: String,
        reason: String,
    },

    /// 订单创建失败
    #[error("订单创建失败: 网格级别={level_id}, 价格={price}, 数量={quantity}, 原因: {reason}")]
    OrderCreation {
        level_id: Uuid,
        price: f64,
        quantity: f64,
        reason: String,
    },

    /// 订单状态不一致
    #[error("订单状态不一致: order_id={order_id}, 期望状态: {expected}, 实际状态: {actual}")]
    OrderStateInconsistency {
        order_id: OrderId,
        expected: String,
        actual: String,
    },

    /// 市场数据无效
    #[error("市场数据无效: 价格={price}, 时间戳={timestamp}, 原因: {reason}")]
    InvalidMarketData {
        price: f64,
        timestamp: String,
        reason: String,
    },

    /// 波动率计算失败
    #[error("波动率计算失败: 数据点数量={data_points}, 窗口大小={window_size}, 原因: {reason}")]
    VolatilityCalculation {
        data_points: usize,
        window_size: usize,
        reason: String,
    },

    /// 网格重置失败
    #[error("网格重置失败: 当前价格={current_price}, 基准价格={base_price}, 偏离度={deviation:.2}%, 原因: {reason}")]
    GridReset {
        current_price: f64,
        base_price: f64,
        deviation: f64,
        reason: String,
    },

    /// 市场状态更新失败
    #[error("市场状态更新失败: 当前状态={current_regime:?}, 目标状态={target_regime:?}, 原因: {reason}")]
    MarketRegimeUpdate {
        current_regime: String,
        target_regime: String,
        reason: String,
    },

    /// 止损检查失败
    #[error("止损检查失败: 当前价格={current_price}, 止损价格={stop_price}, 止损类型={stop_type}, 原因: {reason}")]
    StopLossCheck {
        current_price: f64,
        stop_price: f64,
        stop_type: String,
        reason: String,
    },

    /// 状态不一致
    #[error("策略状态不一致: 期望状态={expected}, 实际状态={actual}, 上下文: {context}")]
    StateInconsistency {
        expected: String,
        actual: String,
        context: String,
    },

    /// 资源不足
    #[error("资源不足: 资源类型={resource_type}, 需要={required}, 可用={available}")]
    InsufficientResources {
        resource_type: String,
        required: f64,
        available: f64,
    },

    /// 并发访问冲突
    #[error("并发访问冲突: 资源={resource}, 操作={operation}, 冲突原因: {reason}")]
    ConcurrencyConflict {
        resource: String,
        operation: String,
        reason: String,
    },
}

impl AsymmetricGridError {
    /// 创建策略未初始化错误
    pub fn not_initialized(reason: impl Into<String>) -> Self {
        Self::NotInitialized {
            reason: reason.into(),
        }
    }

    /// 创建配置验证失败错误
    pub fn config_validation(field: impl Into<String>, value: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ConfigValidation {
            field: field.into(),
            value: value.into(),
            reason: reason.into(),
        }
    }

    /// 创建网格生成失败错误
    pub fn grid_generation(base_price: f64, volatility: f64, reason: impl Into<String>) -> Self {
        Self::GridGeneration {
            base_price,
            volatility,
            reason: reason.into(),
        }
    }

    /// 创建网格级别操作失败错误
    pub fn grid_level_operation(level_id: Uuid, operation: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::GridLevelOperation {
            level_id,
            operation: operation.into(),
            reason: reason.into(),
        }
    }

    /// 创建订单创建失败错误
    pub fn order_creation(level_id: Uuid, price: f64, quantity: f64, reason: impl Into<String>) -> Self {
        Self::OrderCreation {
            level_id,
            price,
            quantity,
            reason: reason.into(),
        }
    }

    /// 创建市场数据无效错误
    pub fn invalid_market_data(price: f64, timestamp: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::InvalidMarketData {
            price,
            timestamp: timestamp.into(),
            reason: reason.into(),
        }
    }

    /// 创建波动率计算失败错误
    pub fn volatility_calculation(data_points: usize, window_size: usize, reason: impl Into<String>) -> Self {
        Self::VolatilityCalculation {
            data_points,
            window_size,
            reason: reason.into(),
        }
    }

    /// 创建网格重置失败错误
    pub fn grid_reset(current_price: f64, base_price: f64, deviation: f64, reason: impl Into<String>) -> Self {
        Self::GridReset {
            current_price,
            base_price,
            deviation,
            reason: reason.into(),
        }
    }

    /// 创建状态不一致错误
    pub fn state_inconsistency(expected: impl Into<String>, actual: impl Into<String>, context: impl Into<String>) -> Self {
        Self::StateInconsistency {
            expected: expected.into(),
            actual: actual.into(),
            context: context.into(),
        }
    }

    /// 获取错误代码（用于程序化处理）
    pub fn error_code(&self) -> &'static str {
        match self {
            Self::NotInitialized { .. } => "GRID_NOT_INITIALIZED",
            Self::ConfigValidation { .. } => "GRID_CONFIG_VALIDATION",
            Self::GridGeneration { .. } => "GRID_GENERATION_FAILED",
            Self::GridLevelOperation { .. } => "GRID_LEVEL_OPERATION_FAILED",
            Self::OrderCreation { .. } => "GRID_ORDER_CREATION_FAILED",
            Self::OrderStateInconsistency { .. } => "GRID_ORDER_STATE_INCONSISTENCY",
            Self::InvalidMarketData { .. } => "GRID_INVALID_MARKET_DATA",
            Self::VolatilityCalculation { .. } => "GRID_VOLATILITY_CALCULATION_FAILED",
            Self::GridReset { .. } => "GRID_RESET_FAILED",
            Self::MarketRegimeUpdate { .. } => "GRID_MARKET_REGIME_UPDATE_FAILED",
            Self::StopLossCheck { .. } => "GRID_STOP_LOSS_CHECK_FAILED",
            Self::StateInconsistency { .. } => "GRID_STATE_INCONSISTENCY",
            Self::InsufficientResources { .. } => "GRID_INSUFFICIENT_RESOURCES",
            Self::ConcurrencyConflict { .. } => "GRID_CONCURRENCY_CONFLICT",
        }
    }

    /// 获取错误建议
    pub fn suggestion(&self) -> Option<&'static str> {
        match self {
            Self::NotInitialized { .. } => Some("请确保策略已正确初始化并配置"),
            Self::ConfigValidation { .. } => Some("请检查配置参数是否在有效范围内"),
            Self::GridGeneration { .. } => Some("请检查基准价格和波动率参数是否合理"),
            Self::GridLevelOperation { .. } => Some("请检查网格级别状态和操作序列"),
            Self::OrderCreation { .. } => Some("请检查订单参数和市场条件"),
            Self::OrderStateInconsistency { .. } => Some("请检查订单状态同步机制"),
            Self::InvalidMarketData { .. } => Some("请检查市场数据源和数据质量"),
            Self::VolatilityCalculation { .. } => Some("请确保有足够的历史数据进行波动率计算"),
            Self::GridReset { .. } => Some("请检查网格重置条件和阈值设置"),
            Self::MarketRegimeUpdate { .. } => Some("请检查市场状态判断逻辑和数据源"),
            Self::StopLossCheck { .. } => Some("请检查止损配置和价格数据"),
            Self::StateInconsistency { .. } => Some("请检查策略状态管理和并发控制"),
            Self::InsufficientResources { .. } => Some("请检查资金或仓位是否充足"),
            Self::ConcurrencyConflict { .. } => Some("请检查并发访问控制和锁机制"),
        }
    }

    /// 判断是否为可恢复的错误
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::NotInitialized { .. } => true,
            Self::ConfigValidation { .. } => true,
            Self::GridGeneration { .. } => true,
            Self::InvalidMarketData { .. } => true,
            Self::VolatilityCalculation { .. } => true,
            Self::InsufficientResources { .. } => true,
            Self::ConcurrencyConflict { .. } => true,
            _ => false,
        }
    }
}

impl From<AsymmetricGridError> for SigmaXError {
    fn from(err: AsymmetricGridError) -> Self {
        SigmaXError::Strategy(err.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation_and_properties() {
        // 测试配置验证错误
        let config_error = AsymmetricGridError::config_validation(
            "grid_count",
            "0",
            "网格数量必须大于0"
        );

        assert_eq!(config_error.error_code(), "GRID_CONFIG_VALIDATION");
        assert!(config_error.suggestion().is_some());
        assert!(config_error.is_recoverable());

        // 测试网格生成错误
        let grid_error = AsymmetricGridError::grid_generation(
            50000.0,
            0.02,
            "波动率过低"
        );

        assert_eq!(grid_error.error_code(), "GRID_GENERATION_FAILED");
        assert!(grid_error.is_recoverable());

        // 测试状态不一致错误
        let state_error = AsymmetricGridError::state_inconsistency(
            "Running",
            "Stopped",
            "策略状态检查"
        );

        assert_eq!(state_error.error_code(), "GRID_STATE_INCONSISTENCY");
        assert!(!state_error.is_recoverable());
    }

    #[test]
    fn test_error_display() {
        let error = AsymmetricGridError::invalid_market_data(
            -100.0,
            "2024-01-01T00:00:00Z",
            "价格不能为负数"
        );

        let error_string = error.to_string();
        assert!(error_string.contains("市场数据无效"));
        assert!(error_string.contains("-100"));
        assert!(error_string.contains("价格不能为负数"));
    }

    #[test]
    fn test_error_conversion_to_sigmax_error() {
        let grid_error = AsymmetricGridError::not_initialized("配置未设置");
        let sigmax_error: SigmaXError = grid_error.into();

        match sigmax_error {
            SigmaXError::Strategy(msg) => {
                assert!(msg.contains("策略未初始化"));
                assert!(msg.contains("配置未设置"));
            }
            _ => panic!("错误转换失败"),
        }
    }

    #[test]
    fn test_error_context_preservation() {
        let level_id = uuid::Uuid::new_v4();
        let error = AsymmetricGridError::grid_level_operation(
            level_id,
            "update_status",
            "网格级别不存在"
        );

        let error_string = error.to_string();
        assert!(error_string.contains(&level_id.to_string()));
        assert!(error_string.contains("update_status"));
        assert!(error_string.contains("网格级别不存在"));
    }
}
