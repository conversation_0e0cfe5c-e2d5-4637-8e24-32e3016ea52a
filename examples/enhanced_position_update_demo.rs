#!/usr/bin/env rust
//! 增强的仓位更新实现示例
//! 
//! 展示如何改进当前的投资组合更新机制，实现真实的余额更新和仓位跟踪

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rust_decimal::Decimal;
use sigmax_core::{Amount, Balance, Trade, OrderSide, TradingPair, SigmaXResult, ExchangeId};

/// 增强的投资组合管理器
/// 支持内部可变性和并发安全的余额更新
pub struct EnhancedPortfolioManager {
    /// 资产余额 (使用内部可变性)
    balances: Arc<RwLock<HashMap<String, Balance>>>,
    /// 初始资金
    initial_capital: Amount,
    /// 仓位跟踪器
    position_tracker: Arc<RwLock<PositionTracker>>,
    /// 当前价格缓存 (用于计算总价值)
    current_prices: Arc<RwLock<HashMap<String, Decimal>>>,
}

/// 仓位跟踪器
pub struct PositionTracker {
    /// 各交易对的仓位信息
    positions: HashMap<TradingPair, Position>,
}

/// 仓位信息
#[derive(Debug, Clone)]
pub struct Position {
    pub trading_pair: TradingPair,
    pub quantity: Decimal,
    pub average_price: Decimal,
    pub unrealized_pnl: Decimal,
    pub realized_pnl: Decimal,
    pub total_fees: Decimal,
}

impl EnhancedPortfolioManager {
    pub fn new(initial_capital: Amount) -> Self {
        let mut initial_balances = HashMap::new();
        
        // 设置初始USDT余额
        initial_balances.insert("USDT".to_string(), Balance::new(
            ExchangeId::Simulator,
            "USDT".to_string(),
            initial_capital,
            Amount::ZERO,
        ));
        
        Self {
            balances: Arc::new(RwLock::new(initial_balances)),
            initial_capital,
            position_tracker: Arc::new(RwLock::new(PositionTracker::new())),
            current_prices: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 🔥 核心方法：根据交易更新投资组合
    pub async fn update_from_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        println!("🔄 开始处理交易: {:?} {} {} @ {}", 
                 trade.side, trade.quantity, trade.trading_pair.symbol(), trade.price);
        
        // 1. 更新资产余额
        self.update_asset_balances(trade).await?;
        
        // 2. 更新仓位信息
        self.update_position_tracking(trade).await?;
        
        // 3. 扣除手续费
        self.deduct_trading_fees(trade).await?;
        
        // 4. 更新当前价格
        self.update_current_price(&trade.trading_pair, trade.price).await?;
        
        println!("✅ 交易处理完成");
        Ok(())
    }
    
    /// 更新资产余额
    async fn update_asset_balances(&self, trade: &Trade) -> SigmaXResult<()> {
        let mut balances = self.balances.write().await;
        
        let base_asset = &trade.trading_pair.base;
        let quote_asset = &trade.trading_pair.quote;
        
        match trade.side {
            OrderSide::Buy => {
                // 买入：增加基础资产，减少计价资产
                println!("📈 买入交易处理:");
                
                // 增加基础资产
                let base_balance = balances.entry(base_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        base_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                base_balance.free += trade.quantity;
                println!("   {} 余额: {} -> {}", base_asset, 
                         base_balance.free - trade.quantity, base_balance.free);
                
                // 减少计价资产
                let quote_balance = balances.entry(quote_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        quote_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let cost = trade.quantity * trade.price;
                quote_balance.free -= cost;
                println!("   {} 余额: {} -> {}", quote_asset,
                         quote_balance.free + cost, quote_balance.free);
            }
            OrderSide::Sell => {
                // 卖出：减少基础资产，增加计价资产
                println!("📉 卖出交易处理:");
                
                // 减少基础资产
                let base_balance = balances.entry(base_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        base_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                base_balance.free -= trade.quantity;
                println!("   {} 余额: {} -> {}", base_asset,
                         base_balance.free + trade.quantity, base_balance.free);
                
                // 增加计价资产
                let quote_balance = balances.entry(quote_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        quote_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                let revenue = trade.quantity * trade.price;
                quote_balance.free += revenue;
                println!("   {} 余额: {} -> {}", quote_asset,
                         quote_balance.free - revenue, quote_balance.free);
            }
        }
        
        Ok(())
    }
    
    /// 更新仓位跟踪
    async fn update_position_tracking(&self, trade: &Trade) -> SigmaXResult<()> {
        let mut tracker = self.position_tracker.write().await;
        
        let position = tracker.positions
            .entry(trade.trading_pair.clone())
            .or_insert_with(|| Position {
                trading_pair: trade.trading_pair.clone(),
                quantity: Decimal::ZERO,
                average_price: Decimal::ZERO,
                unrealized_pnl: Decimal::ZERO,
                realized_pnl: Decimal::ZERO,
                total_fees: Decimal::ZERO,
            });
        
        match trade.side {
            OrderSide::Buy => {
                println!("📊 更新买入仓位:");
                
                // 计算新的平均成本价格
                let old_total_cost = position.quantity * position.average_price;
                let new_trade_cost = trade.quantity * trade.price;
                let new_total_quantity = position.quantity + trade.quantity;
                
                if new_total_quantity > Decimal::ZERO {
                    position.average_price = (old_total_cost + new_trade_cost) / new_total_quantity;
                }
                position.quantity = new_total_quantity;
                
                println!("   持仓数量: {} -> {}", 
                         position.quantity - trade.quantity, position.quantity);
                println!("   平均价格: {} -> {}", 
                         old_total_cost / (position.quantity - trade.quantity).max(Decimal::ONE), 
                         position.average_price);
            }
            OrderSide::Sell => {
                println!("📊 更新卖出仓位:");
                
                // 计算已实现盈亏
                let realized_pnl = trade.quantity * (trade.price - position.average_price);
                position.realized_pnl += realized_pnl;
                position.quantity -= trade.quantity;
                
                println!("   持仓数量: {} -> {}", 
                         position.quantity + trade.quantity, position.quantity);
                println!("   已实现盈亏: +{} (总计: {})", 
                         realized_pnl, position.realized_pnl);
            }
        }
        
        // 累计手续费
        position.total_fees += trade.fee;
        
        Ok(())
    }
    
    /// 扣除交易手续费
    async fn deduct_trading_fees(&self, trade: &Trade) -> SigmaXResult<()> {
        if trade.fee > Decimal::ZERO {
            if let Some(fee_asset) = &trade.fee_asset {
                let mut balances = self.balances.write().await;
                
                let fee_balance = balances.entry(fee_asset.clone())
                    .or_insert_with(|| Balance::new(
                        ExchangeId::Simulator,
                        fee_asset.clone(),
                        Amount::ZERO,
                        Amount::ZERO,
                    ));
                
                fee_balance.free -= trade.fee;
                println!("💰 扣除手续费: {} {} (余额: {} -> {})", 
                         trade.fee, fee_asset,
                         fee_balance.free + trade.fee, fee_balance.free);
            }
        }
        Ok(())
    }
    
    /// 更新当前价格
    async fn update_current_price(&self, trading_pair: &TradingPair, price: Decimal) -> SigmaXResult<()> {
        let mut prices = self.current_prices.write().await;
        prices.insert(trading_pair.base.clone(), price);
        
        // 同时更新未实现盈亏
        self.update_unrealized_pnl(trading_pair, price).await?;
        
        Ok(())
    }
    
    /// 更新未实现盈亏
    async fn update_unrealized_pnl(&self, trading_pair: &TradingPair, current_price: Decimal) -> SigmaXResult<()> {
        let mut tracker = self.position_tracker.write().await;
        
        if let Some(position) = tracker.positions.get_mut(trading_pair) {
            if position.quantity > Decimal::ZERO {
                position.unrealized_pnl = position.quantity * (current_price - position.average_price);
                println!("📈 更新未实现盈亏: {} (当前价格: {}, 平均成本: {})",
                         position.unrealized_pnl, current_price, position.average_price);
            }
        }
        
        Ok(())
    }
    
    /// 计算总资产价值
    pub async fn calculate_total_value(&self) -> SigmaXResult<Amount> {
        let balances = self.balances.read().await;
        let prices = self.current_prices.read().await;
        
        let mut total_value = Decimal::ZERO;
        
        for (asset, balance) in balances.iter() {
            if asset == "USDT" || asset == "USD" {
                // 稳定币直接计算
                total_value += balance.free;
            } else {
                // 其他资产需要转换为USDT价值
                if let Some(price) = prices.get(asset) {
                    total_value += balance.free * price;
                }
            }
        }
        
        Ok(total_value)
    }
    
    /// 获取当前余额
    pub async fn get_balances(&self) -> SigmaXResult<HashMap<String, Balance>> {
        let balances = self.balances.read().await;
        Ok(balances.clone())
    }
    
    /// 获取总盈亏
    pub async fn get_total_pnl(&self) -> SigmaXResult<Amount> {
        let current_value = self.calculate_total_value().await?;
        Ok(current_value - self.initial_capital)
    }
    
    /// 获取仓位信息
    pub async fn get_positions(&self) -> SigmaXResult<HashMap<TradingPair, Position>> {
        let tracker = self.position_tracker.read().await;
        Ok(tracker.positions.clone())
    }
    
    /// 打印投资组合状态
    pub async fn print_portfolio_status(&self) -> SigmaXResult<()> {
        println!("\n📊 投资组合状态报告");
        println!("=" * 50);
        
        // 余额信息
        let balances = self.get_balances().await?;
        println!("💰 资产余额:");
        for (asset, balance) in &balances {
            println!("   {}: {}", asset, balance.free);
        }
        
        // 仓位信息
        let positions = self.get_positions().await?;
        println!("\n📈 持仓信息:");
        for (trading_pair, position) in &positions {
            if position.quantity > Decimal::ZERO {
                println!("   {}: 数量={}, 均价={}, 未实现盈亏={}, 已实现盈亏={}", 
                         trading_pair.symbol(), position.quantity, position.average_price,
                         position.unrealized_pnl, position.realized_pnl);
            }
        }
        
        // 总价值和盈亏
        let total_value = self.calculate_total_value().await?;
        let total_pnl = self.get_total_pnl().await?;
        println!("\n💎 总资产价值: {}", total_value);
        println!("📊 总盈亏: {} ({:.2}%)", total_pnl, 
                 (total_pnl / self.initial_capital) * Decimal::from(100));
        
        println!("=" * 50);
        Ok(())
    }
}

impl PositionTracker {
    pub fn new() -> Self {
        Self {
            positions: HashMap::new(),
        }
    }
}

/// 演示函数
#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("🚀 增强的仓位更新实现演示");
    println!("=" * 60);
    
    // 创建增强的投资组合管理器
    let portfolio = EnhancedPortfolioManager::new(Decimal::from(10000));
    
    // 创建测试交易对
    let bnb_usdt = TradingPair::new("BNB", "USDT");
    
    // 模拟一系列交易
    let trades = vec![
        // 买入交易
        Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Buy,
            Decimal::new(1, 1), // 0.1 BNB
            Decimal::from(300), // 300 USDT
            Decimal::new(3, 1), // 0.3 USDT 手续费
            Some("USDT".to_string()),
        ),
        // 再次买入
        Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Buy,
            Decimal::new(2, 1), // 0.2 BNB
            Decimal::from(310), // 310 USDT
            Decimal::new(62, 2), // 0.62 USDT 手续费
            Some("USDT".to_string()),
        ),
        // 卖出交易
        Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Sell,
            Decimal::new(15, 2), // 0.15 BNB
            Decimal::from(320), // 320 USDT
            Decimal::new(48, 2), // 0.48 USDT 手续费
            Some("USDT".to_string()),
        ),
    ];
    
    // 处理每笔交易
    for (i, trade) in trades.iter().enumerate() {
        println!("\n🔄 处理第{}笔交易", i + 1);
        println!("-" * 40);
        
        portfolio.update_from_trade(trade).await?;
        portfolio.print_portfolio_status().await?;
    }
    
    println!("\n🎉 演示完成！");
    println!("这个示例展示了如何实现真实的仓位更新机制，");
    println!("包括余额更新、仓位跟踪、盈亏计算和手续费处理。");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_buy_order_position_update() {
        let portfolio = EnhancedPortfolioManager::new(Decimal::from(10000));
        let bnb_usdt = TradingPair::new("BNB", "USDT");
        
        let trade = Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Buy,
            Decimal::new(1, 1), // 0.1 BNB
            Decimal::from(300), // 300 USDT
            Decimal::new(3, 1), // 0.3 USDT 手续费
            Some("USDT".to_string()),
        );
        
        portfolio.update_from_trade(&trade).await.unwrap();
        
        let balances = portfolio.get_balances().await.unwrap();
        let positions = portfolio.get_positions().await.unwrap();
        
        // 验证余额更新
        assert_eq!(balances.get("BNB").unwrap().free, Decimal::new(1, 1));
        assert_eq!(balances.get("USDT").unwrap().free, Decimal::from(9669.7)); // 10000 - 300 - 0.3
        
        // 验证仓位更新
        let position = positions.get(&bnb_usdt).unwrap();
        assert_eq!(position.quantity, Decimal::new(1, 1));
        assert_eq!(position.average_price, Decimal::from(300));
    }
    
    #[tokio::test]
    async fn test_sell_order_position_update() {
        let portfolio = EnhancedPortfolioManager::new(Decimal::from(10000));
        let bnb_usdt = TradingPair::new("BNB", "USDT");
        
        // 先买入
        let buy_trade = Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Buy,
            Decimal::new(2, 1), // 0.2 BNB
            Decimal::from(300), // 300 USDT
            Decimal::new(6, 1), // 0.6 USDT 手续费
            Some("USDT".to_string()),
        );
        
        portfolio.update_from_trade(&buy_trade).await.unwrap();
        
        // 再卖出
        let sell_trade = Trade::new(
            uuid::Uuid::new_v4(),
            ExchangeId::Simulator,
            bnb_usdt.clone(),
            OrderSide::Sell,
            Decimal::new(1, 1), // 0.1 BNB
            Decimal::from(320), // 320 USDT
            Decimal::new(32, 2), // 0.32 USDT 手续费
            Some("USDT".to_string()),
        );
        
        portfolio.update_from_trade(&sell_trade).await.unwrap();
        
        let positions = portfolio.get_positions().await.unwrap();
        let position = positions.get(&bnb_usdt).unwrap();
        
        // 验证仓位更新
        assert_eq!(position.quantity, Decimal::new(1, 1)); // 0.2 - 0.1 = 0.1
        assert_eq!(position.average_price, Decimal::from(300)); // 平均价格不变
        assert_eq!(position.realized_pnl, Decimal::from(2)); // (320 - 300) * 0.1 = 2
    }
}
