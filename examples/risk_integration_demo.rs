//! 风险检查集成演示程序
//! 
//! 这个程序演示了SigmaX系统中回测风险检查集成的功能

use sigmax_core::{
    Order, OrderSide, OrderType, TradingPair, ExchangeId, 
    RiskManager, PortfolioManager, Balance, SigmaXResult
};
use sigmax_engines::{UnifiedRiskChecker, RiskCheckType};
use std::sync::Arc;
use std::collections::HashMap;
use async_trait::async_trait;
use rust_decimal::Decimal;
use chrono::Utc;

// 模拟风险管理器
struct DemoRiskManager {
    max_order_size: Decimal,
    max_position_value: Decimal,
}

impl DemoRiskManager {
    fn new() -> Self {
        Self {
            max_order_size: Decimal::from(1000),
            max_position_value: Decimal::from(50000),
        }
    }
}

#[async_trait]
impl RiskManager for DemoRiskManager {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        // 检查订单大小
        if order.quantity > self.max_order_size {
            println!("❌ 订单风险检查失败: 订单大小 {} 超过限制 {}", 
                     order.quantity, self.max_order_size);
            return Ok(false);
        }

        // 检查订单价值
        if let Some(price) = order.price {
            let order_value = order.quantity * price;
            if order_value > self.max_position_value {
                println!("❌ 订单风险检查失败: 订单价值 {} 超过限制 {}", 
                         order_value, self.max_position_value);
                return Ok(false);
            }
        }

        println!("✅ 订单风险检查通过: {} {} @ {:?}", 
                 order.side, order.quantity, order.price);
        Ok(true)
    }

    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        let total_value: Decimal = balances.iter()
            .map(|b| b.free + b.locked)
            .sum();

        if total_value > self.max_position_value * Decimal::from(2) {
            println!("❌ 持仓风险检查失败: 总价值 {} 超过限制", total_value);
            return Ok(false);
        }

        println!("✅ 持仓风险检查通过: 总价值 {}", total_value);
        Ok(true)
    }

    async fn get_max_order_size(&self, _trading_pair: &TradingPair) -> SigmaXResult<sigmax_core::Quantity> {
        Ok(self.max_order_size)
    }
}

// 模拟投资组合管理器
struct DemoPortfolioManager {
    balances: HashMap<String, Balance>,
}

impl DemoPortfolioManager {
    fn new() -> Self {
        let mut balances = HashMap::new();
        balances.insert("USDT".to_string(), Balance {
            asset: "USDT".to_string(),
            free: Decimal::from(10000),
            locked: Decimal::ZERO,
            exchange_id: ExchangeId::Simulator,
            updated_at: Utc::now(),
        });
        balances.insert("BTC".to_string(), Balance {
            asset: "BTC".to_string(),
            free: Decimal::from(1),
            locked: Decimal::ZERO,
            exchange_id: ExchangeId::Simulator,
            updated_at: Utc::now(),
        });

        Self { balances }
    }
}

#[async_trait]
impl PortfolioManager for DemoPortfolioManager {
    async fn get_balances(&self) -> SigmaXResult<HashMap<String, Balance>> {
        Ok(self.balances.clone())
    }

    async fn update_balance(&mut self, balance: Balance) -> SigmaXResult<()> {
        self.balances.insert(balance.asset.clone(), balance);
        Ok(())
    }

    async fn calculate_total_value(&self) -> SigmaXResult<sigmax_core::Amount> {
        Ok(Decimal::from(50000))
    }

    async fn get_pnl(&self) -> SigmaXResult<sigmax_core::Amount> {
        Ok(Decimal::from(1000))
    }
}

fn create_test_orders() -> Vec<Order> {
    vec![
        // 正常订单 - 应该通过
        Order::new(
            ExchangeId::Simulator,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Market,
            Decimal::from(100), // 正常数量
            Some(Decimal::from(50000)),
        ),
        // 大订单 - 应该被拒绝
        Order::new(
            ExchangeId::Simulator,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Market,
            Decimal::from(2000), // 超过限制
            Some(Decimal::from(50000)),
        ),
        // 高价值订单 - 应该被拒绝
        Order::new(
            ExchangeId::Simulator,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Market,
            Decimal::from(500),
            Some(Decimal::from(200000)), // 高价格导致高价值
        ),
    ]
}

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("🚀 SigmaX 风险检查集成演示");
    println!("================================");

    // 创建风险管理器和投资组合管理器
    let risk_manager = Arc::new(DemoRiskManager::new());
    let portfolio_manager = Arc::new(DemoPortfolioManager::new());

    // 创建统一风险检查器
    let risk_checker = UnifiedRiskChecker::new(
        Some(risk_manager),
        Some(portfolio_manager),
        true, // 回测模式
    );

    println!("\n📋 风险检查器配置:");
    println!("- 模式: {}", if risk_checker.is_backtest() { "回测" } else { "实时交易" });
    println!("- 最大订单大小: 1000");
    println!("- 最大持仓价值: 50000");

    // 测试订单风险检查
    println!("\n🔍 测试订单风险检查:");
    println!("--------------------------------");

    let test_orders = create_test_orders();
    for (i, order) in test_orders.iter().enumerate() {
        println!("\n订单 {}: {} {} @ {:?}", 
                 i + 1, order.side, order.quantity, order.price);

        // 策略订单生成检查
        let strategy_result = risk_checker.check_order_risk(
            order, 
            RiskCheckType::StrategyOrderGeneration("GridStrategy".to_string())
        ).await?;

        if strategy_result.passed {
            // 如果策略检查通过，再进行执行检查
            let execution_result = risk_checker.check_order_risk(
                order, 
                RiskCheckType::OrderExecution
            ).await?;

            if execution_result.passed {
                println!("✅ 订单完全通过风险检查，可以执行");
            } else {
                println!("❌ 订单在执行检查时被拒绝: {}", 
                         execution_result.message.unwrap_or_default());
            }
        } else {
            println!("❌ 订单在策略检查时被拒绝: {}", 
                     strategy_result.message.unwrap_or_default());
        }
    }

    // 测试持仓风险检查
    println!("\n🏦 测试持仓风险检查:");
    println!("--------------------------------");

    let position_result = risk_checker.check_position_risk().await?;
    if position_result.passed {
        println!("✅ 持仓风险检查通过");
    } else {
        println!("❌ 持仓风险检查失败: {}", 
                 position_result.message.unwrap_or_default());
    }

    // 显示风险检查统计
    println!("\n📊 风险检查统计:");
    println!("--------------------------------");
    risk_checker.print_risk_report();

    let stats = risk_checker.get_risk_check_stats();
    println!("\n详细统计:");
    println!("- 总检查次数: {}", stats.total_checks);
    println!("- 通过次数: {}", stats.passed_checks);
    println!("- 失败次数: {}", stats.failed_checks);
    println!("- 策略拒绝: {}", stats.strategy_rejections);
    println!("- 执行拒绝: {}", stats.execution_rejections);
    println!("- 持仓警告: {}", stats.position_warnings);

    let success_rate = if stats.total_checks > 0 {
        (stats.passed_checks as f64 / stats.total_checks as f64) * 100.0
    } else {
        0.0
    };
    println!("- 成功率: {:.2}%", success_rate);

    println!("\n🎯 演示完成!");
    println!("这个演示展示了SigmaX系统中风险检查集成的核心功能:");
    println!("1. 统一的风险检查接口");
    println!("2. 多层次的风险验证");
    println!("3. 详细的统计和报告");
    println!("4. 回测和实时交易的一致性");

    Ok(())
}
