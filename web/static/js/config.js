// SigmaX Trading System - 系统配置
// 注意：API配置已迁移到 api-config.js，请使用 window.SigmaXAPIConfig
window.SigmaXConfig = {

    // UI配置
    ui: {
        theme: 'default',
        language: 'zh-CN',
        notifications: {
            duration: 3000, // 3秒自动消失
            position: 'top-right'
        },
        sidebar: {
            defaultOpen: true,
            mobileBreakpoint: 1024
        }
    },

    // 开发配置
    development: {
        debug: false,
        mockData: false,
        logLevel: 'info' // debug, info, warn, error
    },

    // 获取API配置 - 委托给统一的API配置中心
    getApiBaseUrl() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.apiBaseUrl : 'http://127.0.0.1:8080/api/v1';
    },

    // 获取健康检查URL
    getHealthUrl() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.getHealthUrl() : 'http://127.0.0.1:8080/api/v1/health';
    },

    // 获取完整的系统URL
    getSystemUrl() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.baseUrl : 'http://127.0.0.1:8080';
    },

    // 从环境变量或URL参数覆盖配置
    loadFromEnvironment() {
        // 从URL参数读取配置
        const urlParams = new URLSearchParams(window.location.search);

        if (urlParams.has('host')) {
            this.system.host = urlParams.get('host');
        }

        if (urlParams.has('port')) {
            this.system.port = parseInt(urlParams.get('port'));
        }

        if (urlParams.has('debug')) {
            this.development.debug = urlParams.get('debug') === 'true';
        }

        // 从localStorage读取保存的配置
        const savedConfig = localStorage.getItem('sigmax-config');
        if (savedConfig) {
            try {
                const config = JSON.parse(savedConfig);
                Object.assign(this, config);
            } catch (error) {
                console.warn('加载保存的配置失败:', error);
            }
        }
    },

    // 保存配置到localStorage
    save() {
        try {
            localStorage.setItem('sigmax-config', JSON.stringify({
                system: this.system,
                api: this.api,
                health: this.health,
                ui: this.ui,
                development: this.development
            }));
            return true;
        } catch (error) {
            console.error('保存配置失败:', error);
            return false;
        }
    },

    // 重置为默认配置
    reset() {
        this.system.host = '127.0.0.1';
        this.system.port = 8080;
        this.system.protocol = 'http';
        this.api.basePath = '/api';
        this.development.debug = false;
        localStorage.removeItem('sigmax-config');
    },

    // 验证配置
    validate() {
        const errors = [];

        if (!this.system.host) {
            errors.push('系统主机地址不能为空');
        }

        if (!this.system.port || this.system.port < 1 || this.system.port > 65535) {
            errors.push('系统端口必须在1-65535之间');
        }

        if (!this.api.basePath) {
            errors.push('API基础路径不能为空');
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    },

    // 打印当前配置
    print() {
        console.group('SigmaX 系统配置');
        console.log('系统URL:', this.getSystemUrl());
        console.log('API基础URL:', this.getApiBaseUrl());
        console.log('健康检查URL:', this.getHealthUrl());
        console.log('调试模式:', this.development.debug);
        console.groupEnd();
    }
};

// 页面加载时自动应用配置
document.addEventListener('DOMContentLoaded', () => {
    // 加载环境配置
    window.SigmaXConfig.loadFromEnvironment();

    // 应用到Utils类
    window.SigmaXConfig.applyToUtils();

    // 如果是调试模式，打印配置信息
    if (window.SigmaXConfig.development.debug) {
        window.SigmaXConfig.print();
    }
});
