// SigmaX Trading System - 工具函数
class Utils {
    // 获取API配置 - 委托给统一的API配置中心
    static get API_BASE_URL() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.apiBaseUrl : 'http://127.0.0.1:8080/api/v1';
    }

    static get SYSTEM_HOST() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.host : '127.0.0.1';
    }

    static get SYSTEM_PORT() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.port : 8080;
    }

    static get API_BASE_PATH() {
        return window.SigmaXAPIConfig ? window.SigmaXAPIConfig.apiPath : '/api/v1';
    }
    // 格式化数字
    static formatNumber(num, decimals = 2) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    // 格式化货币
    static formatCurrency(amount, currency = 'USD', decimals = 2) {
        if (amount === null || amount === undefined) return '$0.00';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(amount);
    }

    // 格式化百分比
    static formatPercentage(value, decimals = 2) {
        if (value === null || value === undefined) return '0%';
        return `${(value * 100).toFixed(decimals)}%`;
    }

    // 格式化时间
    static formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // 相对时间格式化
    static formatRelativeTime(timestamp) {
        const now = new Date();
        const date = new Date(timestamp);
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) return `${seconds}秒前`;
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        return this.formatTime(timestamp, 'YYYY-MM-DD');
    }

    // 深拷贝对象
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj);
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const cloned = {};
            Object.keys(obj).forEach(key => {
                cloned[key] = this.deepClone(obj[key]);
            });
            return cloned;
        }
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 生成UUID
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // 验证邮箱
    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // 验证手机号
    static validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    }

    // 获取URL参数
    static getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const result = {};
        for (const [key, value] of params) {
            result[key] = value;
        }
        return result;
    }

    // 设置本地存储
    static setStorage(key, value, type = 'localStorage') {
        try {
            const storage = type === 'sessionStorage' ? sessionStorage : localStorage;
            storage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('存储失败:', error);
            return false;
        }
    }

    // 获取本地存储
    static getStorage(key, type = 'localStorage') {
        try {
            const storage = type === 'sessionStorage' ? sessionStorage : localStorage;
            const value = storage.getItem(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            console.error('读取存储失败:', error);
            return null;
        }
    }

    // 删除本地存储
    static removeStorage(key, type = 'localStorage') {
        try {
            const storage = type === 'sessionStorage' ? sessionStorage : localStorage;
            storage.removeItem(key);
            return true;
        } catch (error) {
            console.error('删除存储失败:', error);
            return false;
        }
    }

    // 显示通知
    static showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }
    }

    // 确认对话框
    static confirm(message, title = '确认') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <h3 class="text-lg font-semibold mb-4">${title}</h3>
                    <p class="text-gray-600 mb-6">${message}</p>
                    <div class="flex space-x-4">
                        <button id="confirm-cancel" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            取消
                        </button>
                        <button id="confirm-ok" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            确认
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            modal.querySelector('#confirm-ok').onclick = () => {
                modal.remove();
                resolve(true);
            };

            modal.querySelector('#confirm-cancel').onclick = () => {
                modal.remove();
                resolve(false);
            };

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                    resolve(false);
                }
            };
        });
    }

    // HTTP请求封装
    static async request(url, options = {}) {
        // 构建完整URL
        let fullUrl;
        if (url.startsWith('http://') || url.startsWith('https://')) {
            // 已经是完整URL
            fullUrl = url;
        } else if (url.startsWith('/api/')) {
            // 已经包含完整API路径
            fullUrl = `http://${this.SYSTEM_HOST}:${this.SYSTEM_PORT}${url}`;
        } else {
            // 相对路径，添加完整的API基础URL
            const cleanUrl = url.startsWith('/') ? url : '/' + url;
            fullUrl = `${this.API_BASE_URL}${cleanUrl}`;
        }

        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const config = { ...defaultOptions, ...options };

        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            console.log(`API请求: ${config.method} ${fullUrl}`);
            const response = await fetch(fullUrl, config);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('请求失败:', error);
            throw error;
        }
    }

    // 颜色工具
    static getStatusColor(value, thresholds = { good: 0, warning: -5, danger: -10 }) {
        if (value >= thresholds.good) return 'text-green-600';
        if (value >= thresholds.warning) return 'text-yellow-600';
        return 'text-red-600';
    }

    // 计算技术指标颜色
    static getTrendColor(current, previous) {
        if (current > previous) return 'text-green-600';
        if (current < previous) return 'text-red-600';
        return 'text-gray-600';
    }

    // 系统健康检查
    static async checkSystemHealth() {
        try {
            const response = await this.request('/health');
            return {
                healthy: response.code === 200,
                data: response
            };
        } catch (error) {
            console.error('系统健康检查失败:', error);
            return {
                healthy: false,
                error: error.message
            };
        }
    }

    // 等待系统就绪
    static async waitForSystemReady(maxRetries = 5, retryInterval = 2000) {
        for (let i = 0; i < maxRetries; i++) {
            const health = await this.checkSystemHealth();
            if (health.healthy) {
                return true;
            }

            if (i < maxRetries - 1) {
                console.log(`系统未就绪，${retryInterval/1000}秒后重试... (${i + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, retryInterval));
            }
        }
        return false;
    }
}

// 导出工具类
window.Utils = Utils;
