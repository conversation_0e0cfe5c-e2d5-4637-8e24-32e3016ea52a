// SigmaX Trading System - 页面组件
window.PageComponents = {
    // 仪表盘组件
    dashboard: () => `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 系统状态卡片 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">系统状态</p>
                        <p class="text-2xl font-bold text-green-600">正常运行</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 活跃策略 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃策略</p>
                        <p class="text-2xl font-bold text-blue-600">3</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-brain text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 今日收益 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今日收益</p>
                        <p class="text-2xl font-bold text-green-600">+2.34%</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 总资产 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总资产</p>
                        <p class="text-2xl font-bold text-gray-900">$125,430</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-wallet text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 实时行情 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4">实时行情</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="font-medium">BTC/USDT</span>
                        <div class="text-right">
                            <span class="text-lg font-bold">$43,250.00</span>
                            <span class="text-green-600 text-sm ml-2">+2.34%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="font-medium">ETH/USDT</span>
                        <div class="text-right">
                            <span class="text-lg font-bold">$2,650.00</span>
                            <span class="text-red-600 text-sm ml-2">-1.23%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-2">
                        <span class="font-medium">BNB/USDT</span>
                        <div class="text-right">
                            <span class="text-lg font-bold">$310.50</span>
                            <span class="text-green-600 text-sm ml-2">+0.87%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近交易 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4">最近交易</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <div>
                            <span class="font-medium text-green-600">买入</span>
                            <span class="text-gray-600 ml-2">BTC/USDT</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">0.023 BTC</div>
                            <div class="text-xs text-gray-400">2分钟前</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <div>
                            <span class="font-medium text-red-600">卖出</span>
                            <span class="text-gray-600 ml-2">ETH/USDT</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">1.5 ETH</div>
                            <div class="text-xs text-gray-400">5分钟前</div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-2">
                        <div>
                            <span class="font-medium text-green-600">买入</span>
                            <span class="text-gray-600 ml-2">BNB/USDT</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">10 BNB</div>
                            <div class="text-xs text-gray-400">10分钟前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    // 回测系统组件
    backtest: () => `
        <!-- 策略选择区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div id="strategy-selector-container">
                <!-- 策略选择器将在这里渲染 -->
            </div>
        </div>

        <!-- 回测配置区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">回测配置</h3>

            <!-- 错误和成功消息 -->
            <div id="backtest-error" class="hidden mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span id="error-message"></span>
            </div>
            <div id="backtest-success" class="hidden mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                <i class="fas fa-check-circle mr-2"></i>
                <span id="success-message"></span>
            </div>

            <!-- 数据文件选择 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择回测数据文件</label>
                <div class="flex space-x-4">
                    <select id="data-file-selector" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">正在加载数据文件...</option>
                    </select>
                    <button id="refresh-files-btn" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-refresh mr-2"></i>刷新
                    </button>
                </div>
                <div id="file-info" class="mt-2 text-sm text-gray-600 hidden">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-gray-50 rounded-lg">
                        <div>
                            <span class="font-medium">交易对:</span>
                            <span id="file-trading-pair">-</span>
                        </div>
                        <div>
                            <span class="font-medium">时间框架:</span>
                            <span id="file-timeframe">-</span>
                        </div>
                        <div>
                            <span class="font-medium">K线数量:</span>
                            <span id="file-candle-count">-</span>
                        </div>
                        <div>
                            <span class="font-medium">文件大小:</span>
                            <span id="file-size">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <form id="backtest-config-form">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">交易对</label>
                        <input id="trading-pairs" type="text" value="BTCUSDT" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时间周期</label>
                        <select id="timeframe" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="1m">1分钟</option>
                            <option value="5m">5分钟</option>
                            <option value="15m">15分钟</option>
                            <option value="30m">30分钟</option>
                            <option value="1h" selected>1小时</option>
                            <option value="4h">4小时</option>
                            <option value="1d">1天</option>
                            <option value="1w">1周</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">初始资金 (USDT)</label>
                        <input id="initial-capital" type="number" value="10000" min="100" step="100" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                        <input id="start-time" type="datetime-local" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                        <input id="end-time" type="datetime-local" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- 隐藏的策略类型字段，由策略选择器控制 -->
                <input type="hidden" id="strategy-type" value="">

                <div class="flex space-x-4">
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                        <i class="fas fa-cog mr-2"></i>配置引擎
                    </button>
                    <button type="button" id="start-backtest-btn" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-play mr-2"></i>启动回测
                    </button>
                    <button type="button" id="stop-backtest-btn" class="hidden px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-stop mr-2"></i>停止回测
                    </button>
                    <button type="button" id="validate-file-btn" class="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                        <i class="fas fa-check mr-2"></i>验证文件
                    </button>
                </div>
            </form>
        </div>

        <!-- 回测进度监控区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">回测进度</h3>

            <!-- 状态显示 -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">当前状态</span>
                    <span id="backtest-status" class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">未启动</span>
                </div>

                <!-- 进度条 -->
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div class="text-right text-xs text-gray-500 mt-1">0%</div>
            </div>

            <!-- 实时统计 -->
            <div id="current-stats" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="font-semibold text-gray-900">0</div>
                    <div class="text-gray-600">交易数量</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="font-semibold text-gray-900">0.00</div>
                    <div class="text-gray-600">当前余额</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="font-semibold text-gray-900">0.00</div>
                    <div class="text-gray-600">未实现盈亏</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="font-semibold text-gray-900">0.0%</div>
                    <div class="text-gray-600">最大回撤</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold mb-4">回测结果</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">+15.67%</div>
                    <div class="text-sm text-gray-600">总收益率</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">1.23</div>
                    <div class="text-sm text-gray-600">夏普比率</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">-8.45%</div>
                    <div class="text-sm text-gray-600">最大回撤</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-900">156</div>
                    <div class="text-sm text-gray-600">交易次数</div>
                </div>
            </div>
            <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-500">收益曲线图表区域</span>
            </div>
        </div>
    `,

    // 实盘交易组件
    'live-trading': () => `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 交易控制面板 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4">交易控制</h3>
                    <div class="flex space-x-4 mb-4">
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-play mr-2"></i>启动交易
                        </button>
                        <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-stop mr-2"></i>停止交易
                        </button>
                        <button class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            <i class="fas fa-pause mr-2"></i>暂停交易
                        </button>
                    </div>
                </div>

                <!-- 持仓信息 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">当前持仓</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-2">交易对</th>
                                    <th class="text-left py-2">数量</th>
                                    <th class="text-left py-2">均价</th>
                                    <th class="text-left py-2">当前价</th>
                                    <th class="text-left py-2">盈亏</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-100">
                                    <td class="py-2">BTC/USDT</td>
                                    <td class="py-2">0.023</td>
                                    <td class="py-2">$42,100</td>
                                    <td class="py-2">$43,250</td>
                                    <td class="py-2 text-green-600">+$26.45</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="py-2">ETH/USDT</td>
                                    <td class="py-2">1.5</td>
                                    <td class="py-2">$2,680</td>
                                    <td class="py-2">$2,650</td>
                                    <td class="py-2 text-red-600">-$45.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">今日统计</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">交易次数</span>
                            <span class="font-semibold">12</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">成功率</span>
                            <span class="font-semibold text-green-600">75%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">总盈亏</span>
                            <span class="font-semibold text-green-600">+$234.56</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">风险监控</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">风险等级</span>
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">低</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">资金使用率</span>
                            <span class="font-semibold">45%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">最大回撤</span>
                            <span class="font-semibold text-red-600">-2.3%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    // 其他页面组件可以继续添加...
    portfolio: () => `
        <div class="text-center py-12">
            <i class="fas fa-wallet text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">投资组合</h3>
            <p class="text-gray-500">投资组合管理功能正在开发中...</p>
        </div>
    `,

    strategies: () => `
        <div class="text-center py-12">
            <i class="fas fa-brain text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">策略配置</h3>
            <p class="text-gray-500">策略配置功能正在开发中...</p>
        </div>
    `,

    'risk-management': () => `
        <div class="text-center py-12">
            <i class="fas fa-shield-alt text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">风险管理</h3>
            <p class="text-gray-500">风险管理功能正在开发中...</p>
        </div>
    `,

    performance: () => `
        <div class="text-center py-12">
            <i class="fas fa-chart-bar text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">绩效分析</h3>
            <p class="text-gray-500">绩效分析功能正在开发中...</p>
        </div>
    `,

    'market-data': () => `
        <div class="text-center py-12">
            <i class="fas fa-database text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">市场数据</h3>
            <p class="text-gray-500">市场数据功能正在开发中...</p>
        </div>
    `,

    'system-config': () => `
        <div class="space-y-6">
            <!-- 系统配置概览 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">系统配置管理</h2>
                        <p class="text-gray-600">管理SigmaX交易系统的核心配置</p>
                    </div>
                    <a href="system-config.html" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-cog mr-2"></i>进入配置管理
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-server text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">系统配置</h3>
                                <p class="text-sm text-gray-600">系统基础信息</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-plug text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">API配置</h3>
                                <p class="text-sm text-gray-600">接口服务设置</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-memory text-purple-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">缓存配置</h3>
                                <p class="text-sm text-gray-600">缓存策略设置</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-chart-line text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">监控配置</h3>
                                <p class="text-sm text-gray-600">系统监控设置</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="window.open('system-config.html#system', '_blank')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-edit text-blue-600 mr-3"></i>
                        <div class="text-left">
                            <div class="font-medium text-gray-900">编辑系统配置</div>
                            <div class="text-sm text-gray-600">修改系统基础信息</div>
                        </div>
                    </button>

                    <button onclick="window.open('system-config.html#api', '_blank')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-network-wired text-green-600 mr-3"></i>
                        <div class="text-left">
                            <div class="font-medium text-gray-900">API设置</div>
                            <div class="text-sm text-gray-600">配置API服务参数</div>
                        </div>
                    </button>

                    <button onclick="window.open('system-config.html#monitoring', '_blank')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-eye text-orange-600 mr-3"></i>
                        <div class="text-left">
                            <div class="font-medium text-gray-900">监控设置</div>
                            <div class="text-sm text-gray-600">配置系统监控</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    `,

    logs: () => `
        <div class="text-center py-12">
            <i class="fas fa-file-alt text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">系统日志</h3>
            <p class="text-gray-500">系统日志功能正在开发中...</p>
        </div>
    `
};
