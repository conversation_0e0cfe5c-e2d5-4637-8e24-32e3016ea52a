// SigmaX Trading System - 应用服务层
// 处理系统健康检查、状态管理等业务逻辑

/**
 * 应用服务类
 * 负责处理系统级别的API调用和业务逻辑
 */
class AppService {
    constructor() {
        this.systemReady = false;
        this.currentPage = 'dashboard';
        this.sidebarOpen = true;
        this.statusCheckInterval = null;

        // 页面信息映射
        this.pageMap = {
            'dashboard': { title: '仪表盘', subtitle: '系统概览和实时状态' },
            'backtest': { title: '回测系统', subtitle: '策略回测和性能分析' },
            'live-trading': { title: '实盘交易', subtitle: '实时交易执行和监控' },
            'portfolio': { title: '投资组合', subtitle: '资产配置和持仓管理' },
            'strategies': { title: '策略配置', subtitle: '交易策略设置和优化' },
            'risk-management': { title: '风险管理', subtitle: '风险控制和监控设置' },
            'performance': { title: '绩效分析', subtitle: '交易绩效和统计分析' },
            'market-data': { title: '市场数据', subtitle: '实时行情和历史数据' },
            'system-config': { title: '系统配置', subtitle: '系统参数和环境设置' },
            'logs': { title: '系统日志', subtitle: '系统运行日志和错误记录' }
        };
    }

    /**
     * 检查系统健康状态
     * @returns {Promise<boolean>} 系统是否健康
     */
    async checkSystemHealth() {
        try {
            console.log('正在检查系统健康状态...');
            const isReady = await Utils.waitForSystemReady(3, 3000);

            if (isReady) {
                console.log('系统健康检查通过');
                this.systemReady = true;
                Utils.showNotification('系统连接成功', 'success', 2000);
                return true;
            } else {
                console.error('系统健康检查失败');
                this.systemReady = false;
                return false;
            }
        } catch (error) {
            console.error('系统健康检查异常:', error);
            this.systemReady = false;
            return false;
        }
    }

    /**
     * 检查系统状态
     * @returns {Promise<boolean>} 当前系统状态
     */
    async checkSystemStatus() {
        try {
            const health = await Utils.checkSystemHealth();
            return health.healthy;
        } catch (error) {
            console.error('系统状态检查失败:', error);
            return false;
        }
    }

    /**
     * 启动系统状态定期检查
     * @param {Function} callback 状态更新回调函数
     */
    startSystemStatusCheck(callback) {
        // 清除之前的定时器
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
        }

        // 每30秒检查一次系统状态
        this.statusCheckInterval = setInterval(async () => {
            const healthy = await this.checkSystemStatus();
            if (callback) {
                callback(healthy);
            }
        }, 30000);
    }

    /**
     * 停止系统状态检查
     */
    stopSystemStatusCheck() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    /**
     * 获取页面信息
     * @param {string} page 页面名称
     * @returns {Object} 页面信息对象
     */
    getPageInfo(page) {
        return this.pageMap[page] || { title: '未知页面', subtitle: '页面不存在' };
    }

    /**
     * 设置当前页面
     * @param {string} page 页面名称
     */
    setCurrentPage(page) {
        this.currentPage = page;
    }

    /**
     * 获取当前页面
     * @returns {string} 当前页面名称
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 切换侧边栏状态
     * @returns {boolean} 新的侧边栏状态
     */
    toggleSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
        return this.sidebarOpen;
    }

    /**
     * 获取侧边栏状态
     * @returns {boolean} 侧边栏是否打开
     */
    getSidebarState() {
        return this.sidebarOpen;
    }

    /**
     * 设置侧边栏状态
     * @param {boolean} open 是否打开
     */
    setSidebarState(open) {
        this.sidebarOpen = open;
    }

    /**
     * 获取页面组件
     * @param {string} page 页面名称
     * @returns {Promise<string>} 页面组件HTML
     */
    async getPageComponent(page) {
        try {
            // 检查是否有预定义的页面组件
            if (window.PageComponents && window.PageComponents[page]) {
                return window.PageComponents[page]();
            }

            // 返回默认组件
            return this.getDefaultPageComponent(page);
        } catch (error) {
            console.error('获取页面组件失败:', error);
            throw error;
        }
    }

    /**
     * 获取默认页面组件
     * @param {string} page 页面名称
     * @returns {string} 默认组件HTML
     */
    getDefaultPageComponent(page) {
        const pageInfo = this.getPageInfo(page);
        return `
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="text-center py-12">
                    <i class="fas fa-tools text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">${pageInfo.title}</h3>
                    <p class="text-gray-500">此页面正在开发中，敬请期待...</p>
                    <button onclick="app.showDevelopmentInfo('${page}')" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        查看开发信息
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取开发信息
     * @param {string} page 页面名称
     * @returns {string} 开发信息文本
     */
    getDevelopmentInfo(page) {
        const pageInfo = this.getPageInfo(page);
        return `${pageInfo.title} 页面开发信息：

这个页面将包含以下功能：
- 数据展示
- 交互控制
- 实时更新
- 响应式设计

开发进度：规划中`;
    }

    /**
     * 初始化页面特定功能
     * @param {string} page 页面名称
     */
    async initPageSpecificFeatures(page) {
        switch (page) {
            case 'dashboard':
                console.log('初始化仪表盘');
                // 这里可以添加仪表盘特定的初始化逻辑
                break;
            case 'backtest':
                console.log('🚀 初始化回测页面...');
                await this.initBacktestPage();
                break;
            case 'live-trading':
                console.log('初始化实盘交易页面');
                // 这里可以添加实盘交易特定的初始化逻辑
                break;
            default:
                console.log(`初始化页面: ${page}`);
        }
    }

    /**
     * 初始化回测页面
     */
    async initBacktestPage() {
        try {
            console.log('🔧 开始初始化回测管理器...');

            // 等待DOM元素加载完成
            await this.waitForBacktestElements();

            // 初始化或重新初始化BacktestManager
            if (window.backtestConfigManager) {
                console.log('♻️ 重新初始化现有的回测管理器');
                await window.backtestConfigManager.init();
            } else {
                console.log('🆕 创建新的回测管理器');
                await window.initBacktestManager();
            }

            console.log('✅ 回测页面初始化完成');
        } catch (error) {
            console.error('❌ 回测页面初始化失败:', error);
        }
    }

    /**
     * 等待回测页面DOM元素加载完成
     */
    async waitForBacktestElements() {
        const requiredElements = [
            'data-file-selector',
            'backtest-config-form',
            'page-content'
        ];

        const maxWaitTime = 5000; // 最大等待5秒
        const checkInterval = 100; // 每100ms检查一次
        let waitTime = 0;

        return new Promise((resolve) => {
            const checkElements = () => {
                const missingElements = requiredElements.filter(id => !document.getElementById(id));

                if (missingElements.length === 0) {
                    console.log('✅ 回测页面DOM元素已就绪');
                    resolve();
                } else if (waitTime >= maxWaitTime) {
                    console.warn('⚠️ 等待回测页面DOM元素超时，缺少:', missingElements);
                    resolve(); // 不阻塞，继续执行
                } else {
                    waitTime += checkInterval;
                    setTimeout(checkElements, checkInterval);
                }
            };

            checkElements();
        });
    }

    /**
     * 获取当前应用状态
     * @returns {Object} 应用状态对象
     */
    getState() {
        return {
            systemReady: this.systemReady,
            currentPage: this.currentPage,
            sidebarOpen: this.sidebarOpen,
            hasStatusCheck: !!this.statusCheckInterval
        };
    }

    /**
     * 重置应用状态
     */
    reset() {
        this.systemReady = false;
        this.currentPage = 'dashboard';
        this.sidebarOpen = true;
        this.stopSystemStatusCheck();
        console.log('应用服务状态已重置');
    }

    /**
     * 销毁服务，清理资源
     */
    destroy() {
        this.stopSystemStatusCheck();
        console.log('应用服务已销毁');
    }
}

// 导出服务类
window.AppService = AppService;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppService;
}
