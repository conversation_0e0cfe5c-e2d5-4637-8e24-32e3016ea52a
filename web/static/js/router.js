// SigmaX Trading System - 路由管理器
class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.beforeRouteChange = null;
        this.afterRouteChange = null;
        this.init();
    }

    init() {
        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            this.handleRouteChange();
        });

        // 初始路由
        this.handleRouteChange();
    }

    // 注册路由
    register(path, handler, options = {}) {
        this.routes.set(path, {
            handler,
            title: options.title || '',
            requiresAuth: options.requiresAuth || false,
            middleware: options.middleware || []
        });
    }

    // 导航到指定路由
    navigate(path, state = {}) {
        if (this.currentRoute === path) return;

        // 执行路由前钩子
        if (this.beforeRouteChange) {
            const shouldContinue = this.beforeRouteChange(this.currentRoute, path);
            if (!shouldContinue) return;
        }

        // 更新URL
        window.history.pushState(state, '', `#${path}`);

        // 处理路由变化
        this.handleRouteChange();
    }

    // 替换当前路由
    replace(path, state = {}) {
        window.history.replaceState(state, '', `#${path}`);
        this.handleRouteChange();
    }

    // 处理路由变化
    async handleRouteChange() {
        const hash = window.location.hash.slice(1) || 'dashboard';
        const route = this.routes.get(hash);

        if (!route) {
            console.warn(`路由 ${hash} 未找到`);
            this.navigate('dashboard');
            return;
        }

        // 检查认证
        if (route.requiresAuth && !this.isAuthenticated()) {
            this.navigate('login');
            return;
        }

        // 执行中间件
        for (const middleware of route.middleware) {
            const result = await middleware(hash);
            if (!result) return; // 中间件阻止路由
        }

        // 更新当前路由
        const previousRoute = this.currentRoute;
        this.currentRoute = hash;

        try {
            // 执行路由处理器
            await route.handler(hash, previousRoute);

            // 执行路由后钩子
            if (this.afterRouteChange) {
                this.afterRouteChange(previousRoute, hash);
            }
        } catch (error) {
            console.error('路由处理失败:', error);
            this.handleRouteError(error, hash);
        }
    }

    // 处理路由错误
    handleRouteError(error, route) {
        console.error(`路由 ${route} 处理失败:`, error);
        // 可以显示错误页面或回退到默认路由
    }

    // 检查用户认证状态
    isAuthenticated() {
        // 这里可以检查用户登录状态
        return true; // 暂时返回true
    }

    // 获取当前路由
    getCurrentRoute() {
        return this.currentRoute;
    }

    // 获取路由参数
    getRouteParams() {
        const hash = window.location.hash.slice(1);
        const [path, queryString] = hash.split('?');
        const params = new URLSearchParams(queryString);
        return Object.fromEntries(params.entries());
    }

    // 设置路由前钩子
    beforeEach(callback) {
        this.beforeRouteChange = callback;
    }

    // 设置路由后钩子
    afterEach(callback) {
        this.afterRouteChange = callback;
    }

    // 返回上一页
    back() {
        window.history.back();
    }

    // 前进到下一页
    forward() {
        window.history.forward();
    }
}

// 路由中间件示例
const authMiddleware = async (route) => {
    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem('userToken');
    if (!isLoggedIn && route !== 'login') {
        router.navigate('login');
        return false;
    }
    return true;
};

const loadingMiddleware = async (route) => {
    // 显示加载状态
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.style.display = 'flex';
    }
    return true;
};

// 创建全局路由实例
const router = new Router();

// 注册路由
router.register('dashboard', async (route) => {
    console.log('加载仪表盘页面');
    // 这里可以加载仪表盘组件
}, { title: '仪表盘' });

router.register('backtest', async (route) => {
    console.log('加载回测页面');
    // 这里可以加载回测组件
}, { title: '回测系统' });

router.register('live-trading', async (route) => {
    console.log('加载实盘交易页面');
    // 这里可以加载实盘交易组件
}, { title: '实盘交易' });

router.register('portfolio', async (route) => {
    console.log('加载投资组合页面');
    // 这里可以加载投资组合组件
}, { title: '投资组合' });

router.register('strategies', async (route) => {
    console.log('加载策略配置页面');
    // 这里可以加载策略配置组件
}, { title: '策略配置' });

router.register('strategy-validation', async (route) => {
    console.log('加载策略验证页面');

    // 获取页面容器
    const container = document.getElementById('page-content');
    if (!container) {
        console.error('页面容器未找到');
        return;
    }

    // 更新页面标题
    const titleEl = document.getElementById('page-title');
    const subtitleEl = document.getElementById('page-subtitle');
    if (titleEl) titleEl.textContent = '策略验证';
    if (subtitleEl) subtitleEl.textContent = '验证您的交易策略配置，确保参数设置合理有效';

    // 创建并初始化策略验证控制器
    if (!window.strategyValidationController) {
        window.strategyValidationController = new StrategyValidationController();
    }

    await window.strategyValidationController.init(container);
}, { title: '策略验证' });

router.register('risk-management', async (route) => {
    console.log('加载风险管理页面');
    // 这里可以加载风险管理组件
}, { title: '风险管理' });

router.register('performance', async (route) => {
    console.log('加载绩效分析页面');
    // 这里可以加载绩效分析组件
}, { title: '绩效分析' });

router.register('market-data', async (route) => {
    console.log('加载市场数据页面');
    // 这里可以加载市场数据组件
}, { title: '市场数据' });

router.register('system-config', async (route) => {
    console.log('加载系统配置页面');
    // 这里可以加载系统配置组件
}, { title: '系统配置' });

router.register('logs', async (route) => {
    console.log('加载系统日志页面');
    // 这里可以加载系统日志组件
}, { title: '系统日志' });

// 设置路由钩子
router.beforeEach((from, to) => {
    console.log(`路由变化: ${from} -> ${to}`);
    return true;
});

router.afterEach((from, to) => {
    // 隐藏加载状态
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
});

// 导出路由实例
window.router = router;
