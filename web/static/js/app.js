// SigmaX Trading System - 重构后的主应用程序
// 采用MVC架构，分离关注点，提高可维护性

/**
 * 重构后的SigmaX应用程序
 * 使用MVC架构模式，将原有的单一类拆分为Service、View、Controller三层
 */
class SigmaXApp {
    constructor() {
        // 初始化各层组件
        this.service = new window.AppService();
        this.view = new window.AppView();
        this.controller = new window.AppController(this.service, this.view);

        // 绑定方法上下文
        this.init = this.init.bind(this);
    }

    /**
     * 初始化应用程序
     * 使用新的MVC架构
     */
    async init() {
        try {
            console.log('🚀 开始初始化重构后的SigmaX应用...');

            // 委托给控制器处理初始化
            await this.controller.init();

            console.log('✅ SigmaX应用初始化完成');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取服务层实例
     * @returns {AppService} 服务层实例
     */
    getService() {
        return this.service;
    }

    /**
     * 获取视图层实例
     * @returns {AppView} 视图层实例
     */
    getView() {
        return this.view;
    }

    /**
     * 获取控制器实例
     * @returns {AppController} 控制器实例
     */
    getController() {
        return this.controller;
    }

    /**
     * 导航到指定页面
     * @param {string} page 目标页面
     */
    async navigateTo(page) {
        return await this.controller.navigateTo(page);
    }

    /**
     * 显示开发信息
     * @param {string} page 页面名称
     */
    showDevelopmentInfo(page) {
        this.controller.showDevelopmentInfo(page);
    }

    /**
     * 获取当前应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return this.controller.getState();
    }

    /**
     * 重置应用状态
     */
    reset() {
        this.controller.reset();
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled 是否启用调试
     */
    setDebug(enabled) {
        this.controller.setDebug(enabled);
    }

    /**
     * 检查系统健康状态
     * @returns {Promise<boolean>} 系统健康状态
     */
    async checkHealth() {
        return await this.controller.checkHealth();
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        this.controller.destroy();
    }
}

// 重构后的全局实例 - 使用新的MVC架构
window.SigmaXApp = SigmaXApp;

// 创建全局实例
window.app = null;

// 初始化函数
window.initSigmaXApp = () => {
    if (window.app) {
        window.app.destroy();
    }

    window.app = new SigmaXApp();
    return window.app.init();
};

// 自动初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.initSigmaXApp();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SigmaXApp,
        initSigmaXApp: window.initSigmaXApp
    };
}
