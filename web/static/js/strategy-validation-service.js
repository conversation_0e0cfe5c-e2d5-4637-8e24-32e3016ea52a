/**
 * 策略验证服务模块
 * 负责与策略验证API的交互
 */

class StrategyValidationService {
    constructor() {
        this.baseUrl = window.API_CONFIG?.BASE_URL || 'http://127.0.0.1:8080/api/v1';
    }

    /**
     * 验证策略配置
     * @param {Object} validationRequest - 验证请求对象
     * @returns {Promise<Object>} 验证结果
     */
    async validateStrategy(validationRequest) {
        try {
            console.log('发送策略验证请求:', validationRequest);

            const response = await fetch(`${this.baseUrl}/strategies/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(validationRequest)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            console.log('策略验证响应:', result);
            return result;
        } catch (error) {
            console.error('策略验证失败:', error);
            throw error;
        }
    }

    /**
     * 获取支持的策略类型列表
     * @param {boolean} includeDetails - 是否包含详细信息
     * @returns {Promise<Array>} 策略类型列表
     */
    async getSupportedStrategyTypes(includeDetails = true) {
        try {
            console.log('从API获取支持的策略类型...');

            const params = includeDetails ? '?include_details=true' : '';
            const response = await fetch(`${this.baseUrl}/strategies/supported${params}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.code === 200 && result.data && result.data.strategies) {
                console.log(`成功获取 ${result.data.strategies.length} 个策略类型`);

                // 转换API数据格式为前端期望的格式
                return result.data.strategies.map(strategy => ({
                    id: strategy.strategy_type,
                    name: strategy.name,
                    description: strategy.description,
                    category: this.getCategoryFromStrategyType(strategy.strategy_type),
                    riskLevel: this.getRiskLevelFromStrategy(strategy),
                    available: strategy.available,
                    defaultConfig: strategy.config_template || {},
                    requiredParameters: strategy.required_parameters || [],
                    optionalParameters: strategy.optional_parameters || []
                }));
            } else {
                throw new Error(result.message || '获取策略类型失败');
            }
        } catch (error) {
            console.error('获取支持的策略类型失败:', error);

            // 回退到本地硬编码列表
            console.warn('回退到本地策略类型列表');
            return this.getLocalStrategyTypes();
        }
    }

    /**
     * 获取本地硬编码的策略类型列表（作为回退方案）
     * @returns {Array} 策略类型列表
     */
    getLocalStrategyTypes() {
        return [
            {
                id: 'asymmetric_volatility_grid_strategy',
                name: '非对称波动率网格策略',
                description: '基于市场波动率的非对称网格交易策略，下跌时密集吸筹，上涨时稀疏止盈',
                category: 'grid',
                riskLevel: 'medium',
                available: true,
                defaultConfig: {
                    trading_pair: 'BTCUSDT',
                    base_price: 0.0,
                    order_amount: 200.0,
                    down_range_start: -0.02,
                    down_range_end: -0.05,
                    down_grid_count: 10,
                    down_base_quantity: 0.1,
                    up_range_start: 0.02,
                    up_range_end: 0.08,
                    up_grid_count: 6,
                    up_base_quantity: 0.1,
                    volatility_window_hours: 24,
                    volatility_multiplier: 1.2,
                    enable_dynamic_volatility: true,
                    max_position_amount: 2000.0,
                    max_daily_trades: 50,
                    stop_loss_percent: 0.2,
                    strategy_preset: 'Balanced'
                }
            },
            {
                id: 'dca_strategy',
                name: 'DCA定投策略',
                description: '定期定额投资策略，分散投资风险',
                category: 'dca',
                riskLevel: 'low',
                available: true,
                defaultConfig: {
                    trading_pair: 'BTCUSDT',
                    investment_amount: 100,
                    investment_interval: '1d',
                    max_investment_count: 30
                }
            },
            {
                id: 'momentum_strategy',
                name: '动量策略',
                description: '基于价格动量的趋势跟踪策略',
                category: 'momentum',
                riskLevel: 'high',
                available: true,
                defaultConfig: {
                    trading_pair: 'BTCUSDT',
                    momentum_period: 14,
                    entry_threshold: 0.02,
                    exit_threshold: 0.01,
                    position_size: 0.3
                }
            },
            {
                id: 'mean_reversion_strategy',
                name: '均值回归策略',
                description: '基于价格均值回归的反转策略',
                category: 'mean_reversion',
                riskLevel: 'medium',
                available: true,
                defaultConfig: {
                    trading_pair: 'BTCUSDT',
                    lookback_period: 20,
                    deviation_threshold: 2.0,
                    position_size: 0.1,
                    max_positions: 5
                }
            },
            {
                id: 'grid',
                name: '网格策略',
                description: '在价格区间内设置多个买卖网格，通过价格波动获利',
                category: 'grid',
                riskLevel: 'medium',
                available: true,
                defaultConfig: {
                    trading_pair: 'BTCUSDT',
                    grid_levels: 10,
                    upper_price: 300.0,
                    lower_price: 200.0,
                    base_order_size: 0.1,
                    take_profit: 0.02
                }
            }
        ];
    }

    /**
     * 根据策略类型推断分类
     * @param {string} strategyType - 策略类型
     * @returns {string} 分类
     */
    getCategoryFromStrategyType(strategyType) {
        if (strategyType.includes('grid')) return 'grid';
        if (strategyType.includes('dca')) return 'dca';
        if (strategyType.includes('momentum')) return 'momentum';
        if (strategyType.includes('mean_reversion')) return 'mean_reversion';
        return 'other';
    }

    /**
     * 根据策略信息推断风险等级
     * @param {Object} strategy - 策略信息
     * @returns {string} 风险等级
     */
    getRiskLevelFromStrategy(strategy) {
        const strategyType = strategy.strategy_type.toLowerCase();

        if (strategyType.includes('dca')) return 'low';
        if (strategyType.includes('grid')) return 'medium';
        if (strategyType.includes('momentum')) return 'high';
        if (strategyType.includes('mean_reversion')) return 'medium';

        return 'medium'; // 默认中等风险
    }

    /**
     * 获取交易对列表
     * @returns {Array} 交易对列表
     */
    getTradingPairs() {
        return [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT',
            'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT',
            'TRXUSDT', 'ETCUSDT', 'XRPUSDT', 'ATOMUSDT', 'VETUSDT'
        ];
    }

    /**
     * 获取风险等级配置
     * @returns {Object} 风险等级配置
     */
    getRiskLevels() {
        return {
            low: {
                name: '低风险',
                color: 'green',
                description: '保守型策略，风险较低，收益稳定',
                maxLeverage: 1.0,
                maxDrawdown: 5.0
            },
            medium: {
                name: '中等风险',
                color: 'yellow',
                description: '平衡型策略，风险适中，收益较好',
                maxLeverage: 2.0,
                maxDrawdown: 10.0
            },
            high: {
                name: '高风险',
                color: 'red',
                description: '激进型策略，风险较高，收益潜力大',
                maxLeverage: 5.0,
                maxDrawdown: 20.0
            }
        };
    }

    /**
     * 验证初始资金
     * @param {number} capital - 初始资金
     * @returns {Object} 验证结果
     */
    validateInitialCapital(capital) {
        const minCapital = 100;
        const maxCapital = 1000000;

        if (!capital || capital <= 0) {
            return {
                valid: false,
                message: '初始资金必须大于0'
            };
        }

        if (capital < minCapital) {
            return {
                valid: false,
                message: `初始资金不能少于 ${minCapital} USDT`
            };
        }

        if (capital > maxCapital) {
            return {
                valid: false,
                message: `初始资金不能超过 ${maxCapital} USDT`
            };
        }

        return {
            valid: true,
            message: '初始资金验证通过'
        };
    }

    /**
     * 生成验证请求对象
     * @param {Object} formData - 表单数据
     * @returns {Object} 验证请求对象
     */
    buildValidationRequest(formData) {
        return {
            strategy_type: formData.strategyType,
            config: formData.config,
            initial_capital: parseFloat(formData.initialCapital)
        };
    }

    /**
     * 格式化验证结果
     * @param {Object} result - API返回的验证结果
     * @returns {Object} 格式化后的结果
     */
    formatValidationResult(result) {
        return {
            valid: result.data?.valid || false,
            errors: result.data?.errors || [],
            warnings: result.data?.warnings || [],
            suggestions: result.data?.suggestions || [],
            estimatedPerformance: result.data?.estimated_performance || null,
            riskAssessment: result.data?.risk_assessment || null
        };
    }
}

// 导出服务实例
window.StrategyValidationService = StrategyValidationService;
