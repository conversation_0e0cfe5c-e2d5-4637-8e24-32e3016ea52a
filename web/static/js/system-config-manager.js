/**
 * SigmaX Trading System - 系统配置管理器
 * 
 * 负责系统配置管理页面的交互逻辑和数据绑定
 */

class SystemConfigManager {
    constructor() {
        this.service = window.SystemConfigService;
        this.currentTab = 'system';
        this.configs = {};
        this.originalConfigs = {}; // 用于重置功能
        this.isDirty = false; // 标记是否有未保存的更改
        
        console.log('🎛️ SystemConfigManager initialized');
    }

    /**
     * 初始化配置管理器
     */
    async init() {
        try {
            console.log('🚀 初始化系统配置管理器...');
            
            // 绑定事件监听器
            this.bindEventListeners();
            
            // 加载所有配置
            await this.loadAllConfigs();
            
            // 初始化界面
            this.initializeUI();
            
            console.log('✅ 系统配置管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 系统配置管理器初始化失败:', error);
            this.showNotification('系统配置管理器初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 标签切换
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchTab(tabType);
            });
        });

        // 保存按钮
        document.getElementById('save-all-btn')?.addEventListener('click', () => this.saveAllConfigs());
        document.getElementById('reset-all-btn')?.addEventListener('click', () => this.resetAllConfigs());

        // 各配置面板的保存和重置按钮
        ['system', 'api', 'cache', 'monitoring'].forEach(type => {
            document.getElementById(`${type}-save-btn`)?.addEventListener('click', () => this.saveConfig(type));
            document.getElementById(`${type}-reset-btn`)?.addEventListener('click', () => this.resetConfig(type));
        });

        // 表单变化监听
        document.addEventListener('input', (e) => {
            if (e.target.closest('.config-panel')) {
                this.markDirty();
            }
        });

        // 页面离开前提醒
        window.addEventListener('beforeunload', (e) => {
            if (this.isDirty) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
            }
        });
    }

    /**
     * 加载所有配置
     */
    async loadAllConfigs() {
        try {
            this.showLoading(true);
            
            const result = await this.service.getAllConfigs();
            this.configs = result.configs;
            this.originalConfigs = JSON.parse(JSON.stringify(result.configs)); // 深拷贝
            
            if (result.errors.length > 0) {
                this.showNotification(`部分配置加载失败: ${result.errors.join(', ')}`, 'warning');
            }
            
            // 填充表单数据
            this.populateAllForms();
            
        } catch (error) {
            console.error('❌ 加载配置失败:', error);
            this.showNotification('加载配置失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 填充所有表单数据
     */
    populateAllForms() {
        Object.keys(this.configs).forEach(configType => {
            this.populateForm(configType, this.configs[configType]);
        });
    }

    /**
     * 填充指定配置类型的表单
     * @param {string} configType 配置类型
     * @param {Object} configData 配置数据
     */
    populateForm(configType, configData) {
        if (!configData) return;

        switch (configType) {
            case 'system':
                // 🔥 更新：使用后端真实字段
                this.setFormValue('maintenance-mode', configData.maintenance_mode?.toString());
                this.setFormValue('max-concurrent-strategies', configData.max_concurrent_strategies);
                this.setFormValue('data-retention-days', configData.data_retention_days);
                this.setFormValue('backup-enabled', configData.backup_enabled?.toString());
                this.setFormValue('log-level', configData.log_level);
                break;

            case 'api':
                // 🔥 更新：使用后端真实字段
                this.setFormValue('rate-limit-per-minute', configData.rate_limit_per_minute);
                this.setFormValue('max-request-size', configData.max_request_size);
                this.setFormValue('timeout-seconds', configData.timeout_seconds);
                this.setFormValue('cors-enabled', configData.cors_enabled?.toString());
                break;

            case 'cache':
                // 🔥 更新：使用后端真实字段
                this.setFormValue('cache-name', configData.name);
                this.setFormValue('cache-description', configData.description);
                this.setFormValue('cache-enabled', configData.enabled?.toString());
                this.setFormValue('redis-enabled', configData.redis_enabled?.toString());
                this.setFormValue('default-ttl-seconds', configData.default_ttl_seconds);
                this.setFormValue('max-memory-mb', configData.max_memory_mb);
                this.setFormValue('max-entries', configData.max_entries);
                this.setFormValue('cleanup-interval-seconds', configData.cleanup_interval_seconds);
                this.setFormValue('memory-threshold-percentage', configData.memory_threshold_percentage);
                break;

            case 'monitoring':
                // 🔥 更新：使用后端真实字段
                this.setFormValue('monitoring-name', configData.name);
                this.setFormValue('monitoring-description', configData.description);
                this.setFormValue('monitoring-enabled', configData.enabled?.toString());
                this.setFormValue('metrics-enabled', configData.metrics_enabled?.toString());
                this.setFormValue('health-check-interval', configData.health_check_interval);
                this.setFormValue('data-collection-interval', configData.data_collection_interval);
                this.setFormValue('data-retention-days', configData.data_retention_days);
                break;
        }
    }

    /**
     * 从表单收集配置数据
     * @param {string} configType 配置类型
     * @returns {Object} 配置数据
     */
    collectFormData(configType) {
        switch (configType) {
            case 'system':
                // 🔥 更新：使用后端真实字段
                return {
                    maintenance_mode: this.getFormValue('maintenance-mode') === 'true',
                    max_concurrent_strategies: parseInt(this.getFormValue('max-concurrent-strategies')) || 50,
                    data_retention_days: parseInt(this.getFormValue('data-retention-days')) || 365,
                    backup_enabled: this.getFormValue('backup-enabled') === 'true',
                    log_level: this.getFormValue('log-level') || 'INFO'
                };

            case 'api':
                // 🔥 更新：使用后端真实字段
                return {
                    rate_limit_per_minute: parseInt(this.getFormValue('rate-limit-per-minute')) || 1000,
                    max_request_size: parseInt(this.getFormValue('max-request-size')) || 1048576,
                    timeout_seconds: parseInt(this.getFormValue('timeout-seconds')) || 30,
                    cors_enabled: this.getFormValue('cors-enabled') === 'true'
                };

            case 'cache':
                // 🔥 更新：使用后端真实字段
                return {
                    name: this.getFormValue('cache-name') || 'default',
                    description: this.getFormValue('cache-description') || null,
                    enabled: this.getFormValue('cache-enabled') === 'true',
                    redis_enabled: this.getFormValue('redis-enabled') === 'true',
                    default_ttl_seconds: parseInt(this.getFormValue('default-ttl-seconds')) || 3600,
                    max_memory_mb: parseInt(this.getFormValue('max-memory-mb')) || 512,
                    max_entries: parseInt(this.getFormValue('max-entries')) || 100000,
                    cleanup_interval_seconds: parseInt(this.getFormValue('cleanup-interval-seconds')) || 300,
                    memory_threshold_percentage: parseInt(this.getFormValue('memory-threshold-percentage')) || 80
                };

            case 'monitoring':
                // 🔥 更新：使用后端真实字段
                return {
                    name: this.getFormValue('monitoring-name') || 'default',
                    description: this.getFormValue('monitoring-description') || null,
                    enabled: this.getFormValue('monitoring-enabled') === 'true',
                    metrics_enabled: this.getFormValue('metrics-enabled') === 'true',
                    health_check_interval: parseInt(this.getFormValue('health-check-interval')) || 60,
                    data_collection_interval: parseInt(this.getFormValue('data-collection-interval')) || 30,
                    data_retention_days: parseInt(this.getFormValue('data-retention-days')) || 30
                };

            default:
                return {};
        }
    }

    /**
     * 保存指定配置
     * @param {string} configType 配置类型
     */
    async saveConfig(configType) {
        try {
            const configData = this.collectFormData(configType);
            
            await this.service.saveConfig(configType, configData);
            
            // 更新本地配置
            this.configs[configType] = configData;
            this.originalConfigs[configType] = JSON.parse(JSON.stringify(configData));
            
            this.showNotification(`${this.service.configTypes[configType].name}保存成功`, 'success');
            this.markClean();
            
        } catch (error) {
            console.error(`❌ 保存${configType}配置失败:`, error);
            this.showNotification(`保存失败: ${error.message}`, 'error');
        }
    }

    /**
     * 保存所有配置
     */
    async saveAllConfigs() {
        try {
            this.showLoading(true);
            
            // 收集所有表单数据
            const allConfigs = {};
            Object.keys(this.service.configTypes).forEach(configType => {
                allConfigs[configType] = this.collectFormData(configType);
            });
            
            const result = await this.service.saveAllConfigs(allConfigs);
            
            if (result.errors.length > 0) {
                this.showNotification(`部分配置保存失败: ${result.errors.join(', ')}`, 'warning');
            } else {
                this.showNotification('所有配置保存成功', 'success');
            }
            
            // 更新本地配置
            this.configs = allConfigs;
            this.originalConfigs = JSON.parse(JSON.stringify(allConfigs));
            this.markClean();
            
        } catch (error) {
            console.error('❌ 批量保存配置失败:', error);
            this.showNotification('批量保存失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 重置指定配置
     * @param {string} configType 配置类型
     */
    resetConfig(configType) {
        if (this.originalConfigs[configType]) {
            this.populateForm(configType, this.originalConfigs[configType]);
            this.showNotification(`${this.service.configTypes[configType].name}已重置`, 'info');
        }
    }

    /**
     * 重置所有配置
     */
    resetAllConfigs() {
        if (confirm('确定要重置所有配置吗？这将丢失所有未保存的更改。')) {
            this.populateAllForms();
            this.markClean();
            this.showNotification('所有配置已重置', 'info');
        }
    }

    /**
     * 切换标签
     * @param {string} tabType 标签类型
     */
    switchTab(tabType) {
        // 更新标签样式
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
            tab.classList.add('border-transparent', 'text-gray-500');
        });
        
        const activeTab = document.querySelector(`[data-tab="${tabType}"]`);
        if (activeTab) {
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }

        // 切换面板
        document.querySelectorAll('.config-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        const activePanel = document.getElementById(`${tabType}-config-panel`);
        if (activePanel) {
            activePanel.classList.remove('hidden');
        }

        this.currentTab = tabType;
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        // 默认显示系统配置标签
        this.switchTab('system');
    }

    /**
     * 标记为脏数据
     */
    markDirty() {
        this.isDirty = true;
    }

    /**
     * 标记为干净数据
     */
    markClean() {
        this.isDirty = false;
    }

    /**
     * 设置表单值
     * @param {string} elementId 元素ID
     * @param {any} value 值
     */
    setFormValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element && value !== undefined && value !== null) {
            element.value = value;
        }
    }

    /**
     * 获取表单值
     * @param {string} elementId 元素ID
     * @returns {string} 表单值
     */
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }

    /**
     * 显示加载状态
     * @param {boolean} show 是否显示
     */
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.toggle('hidden', !show);
        }
    }

    /**
     * 显示通知
     * @param {string} message 消息
     * @param {string} type 类型 (success, error, warning, info)
     */
    showNotification(message, type = 'info') {
        // 使用现有的通知系统
        if (window.Utils && window.Utils.showNotification) {
            window.Utils.showNotification(message, type);
        } else {
            // 简单的alert作为后备
            alert(message);
        }
    }
}

/**
 * 返回上一页
 */
function goBack() {
    if (window.configManager && window.configManager.isDirty) {
        if (confirm('您有未保存的更改，确定要离开吗？')) {
            window.history.back();
        }
    } else {
        window.history.back();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 确保依赖已加载
        if (!window.SystemConfigService) {
            throw new Error('SystemConfigService 未加载');
        }
        
        // 初始化API配置
        if (window.SigmaXAPIConfig) {
            window.SigmaXAPIConfig.init();
        }
        
        // 创建并初始化配置管理器
        window.configManager = new SystemConfigManager();
        await window.configManager.init();
        
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        alert('页面初始化失败: ' + error.message);
    }
});

console.log('✅ SystemConfigManager 已加载');
