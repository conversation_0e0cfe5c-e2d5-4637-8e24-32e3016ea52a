// SigmaX Trading System - 应用视图层
// 处理DOM操作、UI渲染、事件绑定

/**
 * 应用视图类
 * 负责处理所有与DOM操作和UI渲染相关的逻辑
 */
class AppView {
    constructor() {
        this.elements = {};
        this.eventHandlers = {};
        this.initElements();
    }

    /**
     * 初始化DOM元素引用
     */
    initElements() {
        this.elements = {
            sidebar: document.getElementById('sidebar'),
            mainContent: document.getElementById('main-content'),
            sidebarToggle: document.getElementById('sidebar-toggle'),
            pageTitle: document.getElementById('page-title'),
            pageSubtitle: document.getElementById('page-subtitle'),
            pageContent: document.getElementById('page-content'),
            systemStatus: document.getElementById('system-status'),
            menuItems: document.querySelectorAll('.menu-item')
        };

        console.log('应用视图DOM元素初始化完成');
    }

    /**
     * 绑定事件处理器
     * @param {Object} handlers 事件处理器对象
     */
    bindEvents(handlers) {
        this.eventHandlers = handlers;

        // 侧边栏切换事件
        if (this.elements.sidebarToggle) {
            this.elements.sidebarToggle.addEventListener('click', () => {
                if (handlers.onSidebarToggle) {
                    handlers.onSidebarToggle();
                }
            });
        }

        // 菜单项点击事件
        this.elements.menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.getAttribute('data-page');
                if (page && handlers.onPageNavigate) {
                    handlers.onPageNavigate(page);
                }
            });
        });

        // 响应式设计事件
        window.addEventListener('resize', () => {
            if (handlers.onWindowResize) {
                handlers.onWindowResize();
            }
        });

        // 路由变化事件
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.slice(1);
            if (hash && handlers.onHashChange) {
                handlers.onHashChange(hash);
            }
        });

        console.log('应用视图事件绑定完成');
    }

    /**
     * 更新页面标题
     * @param {Object} pageInfo 页面信息对象
     */
    updatePageTitle(pageInfo) {
        if (this.elements.pageTitle) {
            this.elements.pageTitle.textContent = pageInfo.title;
        }
        if (this.elements.pageSubtitle) {
            this.elements.pageSubtitle.textContent = pageInfo.subtitle;
        }
        document.title = `${pageInfo.title} - SigmaX Trading System`;
    }

    /**
     * 更新菜单状态
     * @param {string} activePage 当前激活的页面
     */
    updateMenuState(activePage) {
        this.elements.menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
            if (item.getAttribute('data-page') === activePage) {
                item.classList.add('active-menu-item');
            }
        });
    }

    /**
     * 显示页面加载状态
     */
    showPageLoading() {
        if (this.elements.pageContent) {
            this.elements.pageContent.innerHTML = `
                <div class="flex items-center justify-center h-64">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    <span class="ml-4 text-gray-600">加载中...</span>
                </div>
            `;
        }
    }

    /**
     * 显示页面内容
     * @param {string} content 页面内容HTML
     */
    showPageContent(content) {
        if (this.elements.pageContent) {
            this.elements.pageContent.innerHTML = content;
        }
    }

    /**
     * 显示页面加载错误
     * @param {string} error 错误信息
     */
    showPageError(error) {
        if (this.elements.pageContent) {
            this.elements.pageContent.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                        <div>
                            <h3 class="text-red-800 font-semibold">页面加载失败</h3>
                            <p class="text-red-600 mt-1">${error || '无法加载页面内容，请稍后重试。'}</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 切换侧边栏显示状态
     * @param {boolean} open 是否打开侧边栏
     */
    toggleSidebar(open) {
        if (!this.elements.sidebar || !this.elements.mainContent) return;

        if (open) {
            this.elements.sidebar.style.transform = 'translateX(0)';
            this.elements.mainContent.style.marginLeft = '16rem';
        } else {
            this.elements.sidebar.style.transform = 'translateX(-100%)';
            this.elements.mainContent.style.marginLeft = '0';
        }
    }

    /**
     * 处理响应式设计
     */
    handleResize() {
        const width = window.innerWidth;
        if (width < 1024) { // lg breakpoint
            // 移动端自动隐藏侧边栏
            if (this.eventHandlers.onMobileResize) {
                this.eventHandlers.onMobileResize();
            }
        }
    }

    /**
     * 更新系统状态指示器
     * @param {boolean} healthy 系统是否健康
     * @param {string} customText 自定义状态文本
     */
    updateSystemStatus(healthy, customText = null) {
        const statusIndicator = this.elements.systemStatus;
        const statusText = statusIndicator?.nextElementSibling;

        if (statusIndicator && statusText) {
            if (healthy) {
                statusIndicator.className = 'w-3 h-3 bg-green-400 rounded-full animate-pulse';
                statusText.textContent = customText || '系统正常';
            } else {
                statusIndicator.className = 'w-3 h-3 bg-red-400 rounded-full animate-pulse';
                statusText.textContent = customText || '系统异常';
            }
        }
    }

    /**
     * 显示系统初始化界面
     * @returns {HTMLElement} 初始化界面元素
     */
    showSystemInitializing() {
        const body = document.body;
        const overlay = document.createElement('div');
        overlay.id = 'system-initializing';
        overlay.className = 'fixed inset-0 bg-gray-900 bg-opacity-95 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="text-center text-white">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 mx-auto">
                    <i class="fas fa-chart-line text-3xl"></i>
                </div>
                <h2 class="text-2xl font-bold mb-4">SigmaX Trading System</h2>
                <div class="flex items-center justify-center space-x-3 mb-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    <span class="text-lg">正在初始化系统...</span>
                </div>
                <p class="text-gray-300 text-sm">正在检查系统健康状态，请稍候</p>
                <div class="mt-6">
                    <div class="w-64 bg-gray-700 rounded-full h-2 mx-auto">
                        <div id="init-progress" class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
        body.appendChild(overlay);

        // 模拟进度条
        this.startProgressAnimation(overlay);
        
        return overlay;
    }

    /**
     * 启动进度条动画
     * @param {HTMLElement} overlay 覆盖层元素
     */
    startProgressAnimation(overlay) {
        let progress = 0;
        const progressBar = overlay.querySelector('#init-progress');
        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
        }, 500);

        // 保存interval以便清理
        overlay.progressInterval = progressInterval;
    }

    /**
     * 隐藏系统初始化界面
     */
    hideSystemInitializing() {
        const overlay = document.getElementById('system-initializing');
        if (overlay) {
            // 清理进度条interval
            if (overlay.progressInterval) {
                clearInterval(overlay.progressInterval);
            }

            // 完成进度条
            const progressBar = overlay.querySelector('#init-progress');
            if (progressBar) {
                progressBar.style.width = '100%';
            }

            // 延迟移除overlay以显示完成效果
            setTimeout(() => {
                overlay.style.opacity = '0';
                overlay.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    overlay.remove();
                }, 500);
            }, 1000);
        }
    }

    /**
     * 显示系统错误界面
     */
    showSystemError() {
        const overlay = document.getElementById('system-initializing');
        if (overlay) {
            overlay.innerHTML = `
                <div class="text-center text-white max-w-md">
                    <div class="w-20 h-20 bg-red-500 rounded-xl flex items-center justify-center mb-6 mx-auto">
                        <i class="fas fa-exclamation-triangle text-3xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-4">系统连接失败</h2>
                    <p class="text-gray-300 mb-6">无法连接到SigmaX交易系统后端服务，请检查：</p>
                    <ul class="text-left text-gray-300 mb-6 space-y-2">
                        <li>• 后端服务是否正常运行</li>
                        <li>• 网络连接是否正常</li>
                        <li>• API服务地址是否正确</li>
                    </ul>
                    <div class="space-y-3">
                        <button id="retry-system-init" class="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-redo mr-2"></i>重试连接
                        </button>
                        <button id="enter-offline-mode" class="w-full px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-wifi-slash mr-2"></i>离线模式
                        </button>
                    </div>
                </div>
            `;

            // 绑定错误界面的事件
            this.bindErrorEvents();
        }
    }

    /**
     * 绑定错误界面的事件
     */
    bindErrorEvents() {
        const retryBtn = document.getElementById('retry-system-init');
        const offlineBtn = document.getElementById('enter-offline-mode');

        if (retryBtn && this.eventHandlers.onRetryInit) {
            retryBtn.addEventListener('click', this.eventHandlers.onRetryInit);
        }

        if (offlineBtn && this.eventHandlers.onEnterOffline) {
            offlineBtn.addEventListener('click', this.eventHandlers.onEnterOffline);
        }
    }

    /**
     * 显示开发信息对话框
     * @param {string} info 开发信息文本
     */
    showDevelopmentInfo(info) {
        alert(info);
    }

    /**
     * 获取DOM元素
     * @param {string} name 元素名称
     * @returns {HTMLElement|null} DOM元素
     */
    getElement(name) {
        return this.elements[name] || null;
    }

    /**
     * 销毁视图，清理事件监听器
     */
    destroy() {
        // 移除事件监听器
        if (this.elements.sidebarToggle) {
            this.elements.sidebarToggle.removeEventListener('click', this.eventHandlers.onSidebarToggle);
        }

        window.removeEventListener('resize', this.eventHandlers.onWindowResize);
        window.removeEventListener('hashchange', this.eventHandlers.onHashChange);

        this.elements = {};
        this.eventHandlers = {};
        console.log('应用视图已销毁');
    }
}

// 导出视图类
window.AppView = AppView;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppView;
}
