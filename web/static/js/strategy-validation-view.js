/**
 * 策略验证视图模块
 * 负责策略验证页面的UI渲染和交互
 */

class StrategyValidationView {
    constructor() {
        this.container = null;
        this.currentStrategy = null;
    }

    /**
     * 渲染策略验证页面
     * @param {HTMLElement} container - 容器元素
     */
    render(container) {
        this.container = container;

        container.innerHTML = `
            <div class="max-w-6xl mx-auto">
                <!-- 页面标题 -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">策略验证</h1>
                    <p class="text-gray-600">验证您的交易策略配置，确保参数设置合理有效</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 左侧：策略配置表单 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h2 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-cog text-blue-600 mr-2"></i>
                                策略配置
                            </h2>

                            <form id="strategy-validation-form" class="space-y-6">
                                <!-- 策略类型选择 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        策略类型 <span class="text-red-500">*</span>
                                    </label>
                                    <select id="strategy-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择策略类型</option>
                                    </select>
                                    <p class="mt-1 text-sm text-gray-500">选择您要验证的策略类型</p>
                                </div>

                                <!-- 交易对选择 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        交易对 <span class="text-red-500">*</span>
                                    </label>
                                    <select id="trading-pair" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择交易对</option>
                                    </select>
                                </div>

                                <!-- 初始资金 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        初始资金 (USDT) <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" id="initial-capital"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="请输入初始资金" min="100" max="1000000" step="0.01">
                                    <p class="mt-1 text-sm text-gray-500">建议范围：100 - 1,000,000 USDT</p>
                                </div>

                                <!-- 策略参数配置区域 -->
                                <div id="strategy-config-section" class="hidden">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">策略参数</h3>
                                    <div id="strategy-config-fields" class="space-y-4">
                                        <!-- 动态生成的策略参数字段 -->
                                    </div>
                                </div>

                                <!-- 验证按钮 -->
                                <div class="flex space-x-4">
                                    <button type="submit" id="validate-btn"
                                            class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        验证策略
                                    </button>
                                    <button type="button" id="reset-btn"
                                            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <i class="fas fa-undo mr-2"></i>
                                        重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 右侧：验证结果和信息 -->
                    <div class="space-y-6">
                        <!-- 策略信息卡片 -->
                        <div id="strategy-info-card" class="bg-white rounded-lg shadow-md p-6 hidden">
                            <h3 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                策略信息
                            </h3>
                            <div id="strategy-info-content">
                                <!-- 策略信息内容 -->
                            </div>
                        </div>

                        <!-- 验证结果卡片 -->
                        <div id="validation-result-card" class="bg-white rounded-lg shadow-md p-6 hidden">
                            <h3 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-clipboard-check text-green-600 mr-2"></i>
                                验证结果
                            </h3>
                            <div id="validation-result-content">
                                <!-- 验证结果内容 -->
                            </div>
                        </div>

                        <!-- 快速配置模板 -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-magic text-purple-600 mr-2"></i>
                                快速配置
                            </h3>
                            <div class="space-y-3">
                                <button class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
                                        data-template="conservative">
                                    <div class="font-medium text-gray-900">保守型配置</div>
                                    <div class="text-sm text-gray-500">低风险，稳定收益</div>
                                </button>
                                <button class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
                                        data-template="balanced">
                                    <div class="font-medium text-gray-900">平衡型配置</div>
                                    <div class="text-sm text-gray-500">中等风险，适中收益</div>
                                </button>
                                <button class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
                                        data-template="aggressive">
                                    <div class="font-medium text-gray-900">激进型配置</div>
                                    <div class="text-sm text-gray-500">高风险，高收益潜力</div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载遮罩 -->
                <div id="validation-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="text-gray-700">正在验证策略配置...</span>
                    </div>
                </div>
            </div>
        `;

        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 策略类型选择变化
        const strategyTypeSelect = this.container.querySelector('#strategy-type');
        strategyTypeSelect.addEventListener('change', (e) => {
            this.onStrategyTypeChange(e.target.value);
        });

        // 表单提交
        const form = this.container.querySelector('#strategy-validation-form');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.onValidateStrategy();
        });

        // 重置按钮
        const resetBtn = this.container.querySelector('#reset-btn');
        resetBtn.addEventListener('click', () => {
            this.resetForm();
        });

        // 快速配置模板
        const templateButtons = this.container.querySelectorAll('[data-template]');
        templateButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.currentTarget.dataset.template;
                this.applyTemplate(template);
            });
        });
    }

    /**
     * 策略类型变化处理
     * @param {string} strategyType - 策略类型
     */
    onStrategyTypeChange(strategyType) {
        if (this.onStrategyTypeChangeCallback) {
            this.onStrategyTypeChangeCallback(strategyType);
        }
    }

    /**
     * 验证策略处理
     */
    onValidateStrategy() {
        if (this.onValidateStrategyCallback) {
            this.onValidateStrategyCallback();
        }
    }

    /**
     * 应用配置模板
     * @param {string} template - 模板类型
     */
    applyTemplate(template) {
        if (this.onApplyTemplateCallback) {
            this.onApplyTemplateCallback(template);
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        const form = this.container.querySelector('#strategy-validation-form');
        form.reset();
        this.hideStrategyConfig();
        this.hideValidationResult();
        this.hideStrategyInfo();
    }

    /**
     * 设置回调函数
     */
    setCallbacks(callbacks) {
        this.onStrategyTypeChangeCallback = callbacks.onStrategyTypeChange;
        this.onValidateStrategyCallback = callbacks.onValidateStrategy;
        this.onApplyTemplateCallback = callbacks.onApplyTemplate;
    }

    /**
     * 填充策略类型选择框
     * @param {Array} strategies - 策略列表
     */
    populateStrategyTypes(strategies) {
        const select = this.container.querySelector('#strategy-type');
        select.innerHTML = '<option value="">请选择策略类型</option>';

        strategies.forEach(strategy => {
            const option = document.createElement('option');
            option.value = strategy.id;
            option.textContent = strategy.name;
            option.dataset.category = strategy.category;
            option.dataset.riskLevel = strategy.riskLevel;
            select.appendChild(option);
        });
    }

    /**
     * 填充交易对选择框
     * @param {Array} pairs - 交易对列表
     */
    populateTradingPairs(pairs) {
        const select = this.container.querySelector('#trading-pair');
        select.innerHTML = '<option value="">请选择交易对</option>';

        pairs.forEach(pair => {
            const option = document.createElement('option');
            option.value = pair;
            option.textContent = pair;
            select.appendChild(option);
        });
    }

    /**
     * 显示策略配置字段
     * @param {Object} strategy - 策略信息
     */
    showStrategyConfig(strategy) {
        this.currentStrategy = strategy;
        const section = this.container.querySelector('#strategy-config-section');
        const fieldsContainer = this.container.querySelector('#strategy-config-fields');

        // 生成配置字段
        fieldsContainer.innerHTML = this.generateConfigFields(strategy.defaultConfig);
        section.classList.remove('hidden');

        // 显示策略信息
        this.showStrategyInfo(strategy);
    }

    /**
     * 隐藏策略配置字段
     */
    hideStrategyConfig() {
        const section = this.container.querySelector('#strategy-config-section');
        section.classList.add('hidden');
    }

    /**
     * 生成配置字段HTML
     * @param {Object} config - 默认配置
     * @returns {string} HTML字符串
     */
    generateConfigFields(config) {
        let html = '';

        Object.entries(config).forEach(([key, value]) => {
            if (key === 'trading_pair') return; // 交易对已单独处理

            const fieldName = this.formatFieldName(key);
            const fieldType = this.getFieldType(key, value);
            const fieldId = `config-${key}`;

            html += `
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        ${fieldName}
                    </label>
                    <input type="${fieldType}" id="${fieldId}" name="${key}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="${value}" ${this.getFieldAttributes(key)}>
                    <p class="mt-1 text-sm text-gray-500">${this.getFieldDescription(key)}</p>
                </div>
            `;
        });

        return html;
    }

    /**
     * 格式化字段名称
     * @param {string} key - 字段键
     * @returns {string} 格式化后的名称
     */
    formatFieldName(key) {
        const nameMap = {
            grid_spacing: '网格间距',
            volatility_threshold: '波动率阈值',
            grid_multiplier: '网格倍数',
            max_position_size: '最大持仓',
            stop_loss_percentage: '止损百分比',
            investment_amount: '投资金额',
            investment_interval: '投资间隔',
            max_investment_count: '最大投资次数',
            momentum_period: '动量周期',
            entry_threshold: '入场阈值',
            exit_threshold: '出场阈值',
            lookback_period: '回看周期',
            deviation_threshold: '偏差阈值',
            position_size: '持仓大小'
        };

        return nameMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * 获取字段类型
     * @param {string} key - 字段键
     * @param {*} value - 字段值
     * @returns {string} 字段类型
     */
    getFieldType(key, value) {
        if (typeof value === 'number') {
            return 'number';
        }
        return 'text';
    }

    /**
     * 获取字段属性
     * @param {string} key - 字段键
     * @returns {string} 字段属性
     */
    getFieldAttributes(key) {
        const attributeMap = {
            grid_spacing: 'step="0.001" min="0.001" max="0.1"',
            volatility_threshold: 'step="0.001" min="0.001" max="0.1"',
            grid_multiplier: 'step="0.1" min="1.0" max="10.0"',
            max_position_size: 'step="1" min="1"',
            stop_loss_percentage: 'step="0.1" min="0.1" max="50.0"'
        };

        return attributeMap[key] || '';
    }

    /**
     * 获取字段描述
     * @param {string} key - 字段键
     * @returns {string} 字段描述
     */
    getFieldDescription(key) {
        const descriptionMap = {
            grid_spacing: '网格之间的价格间距，建议0.5%-2%',
            volatility_threshold: '触发动态调整的波动率阈值',
            grid_multiplier: '网格密度调整倍数',
            max_position_size: '单次交易的最大金额',
            stop_loss_percentage: '最大亏损百分比，超过则停止交易'
        };

        return descriptionMap[key] || '';
    }

    /**
     * 显示策略信息
     * @param {Object} strategy - 策略信息
     */
    showStrategyInfo(strategy) {
        const card = this.container.querySelector('#strategy-info-card');
        const content = this.container.querySelector('#strategy-info-content');

        const riskLevel = strategy.riskLevel;
        const riskColors = {
            low: 'text-green-600 bg-green-100',
            medium: 'text-yellow-600 bg-yellow-100',
            high: 'text-red-600 bg-red-100'
        };

        content.innerHTML = `
            <div class="space-y-3">
                <div>
                    <span class="text-sm font-medium text-gray-500">策略名称</span>
                    <p class="text-gray-900">${strategy.name}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">策略描述</span>
                    <p class="text-gray-700 text-sm">${strategy.description}</p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">风险等级</span>
                    <span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${riskColors[riskLevel]}">
                        ${strategy.riskLevel === 'low' ? '低风险' : strategy.riskLevel === 'medium' ? '中等风险' : '高风险'}
                    </span>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">策略类别</span>
                    <p class="text-gray-900">${strategy.category}</p>
                </div>
            </div>
        `;

        card.classList.remove('hidden');
    }

    /**
     * 隐藏策略信息
     */
    hideStrategyInfo() {
        const card = this.container.querySelector('#strategy-info-card');
        card.classList.add('hidden');
    }

    /**
     * 显示验证结果
     * @param {Object} result - 验证结果
     */
    showValidationResult(result) {
        const card = this.container.querySelector('#validation-result-card');
        const content = this.container.querySelector('#validation-result-content');

        const statusIcon = result.valid ?
            '<i class="fas fa-check-circle text-green-500"></i>' :
            '<i class="fas fa-times-circle text-red-500"></i>';

        const statusText = result.valid ? '验证通过' : '验证失败';
        const statusColor = result.valid ? 'text-green-600' : 'text-red-600';

        let html = `
            <div class="mb-4">
                <div class="flex items-center space-x-2 mb-2">
                    ${statusIcon}
                    <span class="font-medium ${statusColor}">${statusText}</span>
                </div>
            </div>
        `;

        // 显示错误信息
        if (result.errors && result.errors.length > 0) {
            html += `
                <div class="mb-4">
                    <h4 class="font-medium text-red-600 mb-2">错误信息</h4>
                    <ul class="space-y-1">
                        ${result.errors.map(error => `
                            <li class="text-sm text-red-600 flex items-start">
                                <i class="fas fa-exclamation-triangle mt-0.5 mr-2 flex-shrink-0"></i>
                                ${error}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        // 显示警告信息
        if (result.warnings && result.warnings.length > 0) {
            html += `
                <div class="mb-4">
                    <h4 class="font-medium text-yellow-600 mb-2">警告信息</h4>
                    <ul class="space-y-1">
                        ${result.warnings.map(warning => `
                            <li class="text-sm text-yellow-600 flex items-start">
                                <i class="fas fa-exclamation-circle mt-0.5 mr-2 flex-shrink-0"></i>
                                ${warning}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        // 显示建议信息
        if (result.suggestions && result.suggestions.length > 0) {
            html += `
                <div class="mb-4">
                    <h4 class="font-medium text-blue-600 mb-2">优化建议</h4>
                    <ul class="space-y-1">
                        ${result.suggestions.map(suggestion => `
                            <li class="text-sm text-blue-600 flex items-start">
                                <i class="fas fa-lightbulb mt-0.5 mr-2 flex-shrink-0"></i>
                                ${suggestion}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        content.innerHTML = html;
        card.classList.remove('hidden');
    }

    /**
     * 隐藏验证结果
     */
    hideValidationResult() {
        const card = this.container.querySelector('#validation-result-card');
        card.classList.add('hidden');
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loading = this.container.querySelector('#validation-loading');
        loading.classList.remove('hidden');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loading = this.container.querySelector('#validation-loading');
        loading.classList.add('hidden');
    }

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
        const form = this.container.querySelector('#strategy-validation-form');
        const formData = new FormData(form);

        const data = {
            strategyType: formData.get('strategy-type') || this.container.querySelector('#strategy-type').value,
            tradingPair: formData.get('trading-pair') || this.container.querySelector('#trading-pair').value,
            initialCapital: formData.get('initial-capital') || this.container.querySelector('#initial-capital').value,
            config: {}
        };

        // 收集策略配置参数
        const configFields = this.container.querySelectorAll('#strategy-config-fields input');
        configFields.forEach(field => {
            const key = field.name;
            let value = field.value;

            // 类型转换
            if (field.type === 'number') {
                value = parseFloat(value) || 0;
            }

            data.config[key] = value;
        });

        // 添加交易对到配置中
        data.config.trading_pair = data.tradingPair;

        return data;
    }

    /**
     * 设置表单数据
     * @param {Object} data - 表单数据
     */
    setFormData(data) {
        if (data.strategyType) {
            this.container.querySelector('#strategy-type').value = data.strategyType;
        }
        if (data.tradingPair) {
            this.container.querySelector('#trading-pair').value = data.tradingPair;
        }
        if (data.initialCapital) {
            this.container.querySelector('#initial-capital').value = data.initialCapital;
        }

        // 设置策略配置参数
        if (data.config) {
            Object.entries(data.config).forEach(([key, value]) => {
                const field = this.container.querySelector(`#config-${key}`);
                if (field) {
                    field.value = value;
                }
            });
        }
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-red-500 hover:text-red-700" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 3000);
    }
}

// 导出视图类
window.StrategyValidationView = StrategyValidationView;
