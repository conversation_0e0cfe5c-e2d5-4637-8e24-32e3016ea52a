<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX 系统配置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/custom.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">系统配置</h1>
                    <p class="text-gray-600 mt-1">配置SigmaX交易系统的连接参数</p>
                </div>
                <button onclick="goBack()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 配置表单 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold mb-4">系统连接配置</h2>
                
                <form id="config-form" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">服务器地址</label>
                        <input type="text" id="system-host" class="form-input" placeholder="127.0.0.1">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">端口号</label>
                        <input type="number" id="system-port" class="form-input" placeholder="8080" min="1" max="65535">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">协议</label>
                        <select id="system-protocol" class="form-input">
                            <option value="http">HTTP</option>
                            <option value="https">HTTPS</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API基础路径</label>
                        <input type="text" id="api-base-path" class="form-input" placeholder="/api/v1">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">请求超时 (毫秒)</label>
                        <input type="number" id="api-timeout" class="form-input" placeholder="30000" min="1000">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">重试次数</label>
                        <input type="number" id="retry-count" class="form-input" placeholder="3" min="0" max="10">
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="debug-mode" class="mr-2">
                        <label for="debug-mode" class="text-sm text-gray-700">启用调试模式</label>
                    </div>
                </form>
                
                <div class="mt-6 flex space-x-4">
                    <button onclick="saveConfig()" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>保存配置
                    </button>
                    <button onclick="resetConfig()" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-undo mr-2"></i>重置默认
                    </button>
                </div>
            </div>

            <!-- 配置预览和测试 -->
            <div class="space-y-6">
                <!-- 当前配置预览 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold mb-4">当前配置预览</h2>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">系统URL:</span>
                            <code id="preview-system-url" class="bg-gray-100 px-2 py-1 rounded">-</code>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">API基础URL:</span>
                            <code id="preview-api-url" class="bg-gray-100 px-2 py-1 rounded">-</code>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">健康检查URL:</span>
                            <code id="preview-health-url" class="bg-gray-100 px-2 py-1 rounded">-</code>
                        </div>
                    </div>
                </div>

                <!-- 连接测试 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold mb-4">连接测试</h2>
                    <div class="space-y-3">
                        <button onclick="testConnection()" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-plug mr-2"></i>测试连接
                        </button>
                        <div id="test-result" class="hidden p-3 rounded-lg">
                            <div class="flex items-center">
                                <i id="test-icon" class="mr-2"></i>
                                <span id="test-message">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速配置 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold mb-4">快速配置</h2>
                    <div class="space-y-2">
                        <button onclick="applyPreset('local')" class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-left">
                            <i class="fas fa-desktop mr-2"></i>本地开发 (127.0.0.1:8080)
                        </button>
                        <button onclick="applyPreset('production')" class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-left">
                            <i class="fas fa-server mr-2"></i>生产环境 (配置后填写)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadCurrentConfig();
            updatePreview();
            
            // 监听表单变化
            document.getElementById('config-form').addEventListener('input', updatePreview);
        });

        function loadCurrentConfig() {
            const config = window.SigmaXConfig;
            
            document.getElementById('system-host').value = config.system.host;
            document.getElementById('system-port').value = config.system.port;
            document.getElementById('system-protocol').value = config.system.protocol;
            document.getElementById('api-base-path').value = config.api.basePath;
            document.getElementById('api-timeout').value = config.api.timeout;
            document.getElementById('retry-count').value = config.api.retryCount;
            document.getElementById('debug-mode').checked = config.development.debug;
        }

        function updatePreview() {
            const host = document.getElementById('system-host').value || '127.0.0.1';
            const port = document.getElementById('system-port').value || '8080';
            const protocol = document.getElementById('system-protocol').value || 'http';
            const basePath = document.getElementById('api-base-path').value || '/api/v1';
            
            const systemUrl = `${protocol}://${host}:${port}`;
            const apiUrl = `${systemUrl}${basePath}`;
            const healthUrl = `${apiUrl}/health`;
            
            document.getElementById('preview-system-url').textContent = systemUrl;
            document.getElementById('preview-api-url').textContent = apiUrl;
            document.getElementById('preview-health-url').textContent = healthUrl;
        }

        function saveConfig() {
            const config = window.SigmaXConfig;
            
            // 更新配置
            config.system.host = document.getElementById('system-host').value;
            config.system.port = parseInt(document.getElementById('system-port').value);
            config.system.protocol = document.getElementById('system-protocol').value;
            config.api.basePath = document.getElementById('api-base-path').value;
            config.api.timeout = parseInt(document.getElementById('api-timeout').value);
            config.api.retryCount = parseInt(document.getElementById('retry-count').value);
            config.development.debug = document.getElementById('debug-mode').checked;
            
            // 验证配置
            const validation = config.validate();
            if (!validation.valid) {
                Utils.showNotification(`配置验证失败: ${validation.errors.join(', ')}`, 'error');
                return;
            }
            
            // 保存配置
            if (config.save()) {
                config.applyToUtils();
                Utils.showNotification('配置保存成功', 'success');
            } else {
                Utils.showNotification('配置保存失败', 'error');
            }
        }

        function resetConfig() {
            if (confirm('确定要重置为默认配置吗？')) {
                window.SigmaXConfig.reset();
                loadCurrentConfig();
                updatePreview();
                Utils.showNotification('配置已重置为默认值', 'info');
            }
        }

        async function testConnection() {
            const testResult = document.getElementById('test-result');
            const testIcon = document.getElementById('test-icon');
            const testMessage = document.getElementById('test-message');
            
            testResult.className = 'p-3 rounded-lg bg-yellow-50 border border-yellow-200';
            testIcon.className = 'fas fa-spinner fa-spin text-yellow-600 mr-2';
            testMessage.textContent = '正在测试连接...';
            testResult.classList.remove('hidden');
            
            try {
                // 临时应用当前表单配置进行测试
                const tempConfig = {
                    host: document.getElementById('system-host').value,
                    port: parseInt(document.getElementById('system-port').value),
                    protocol: document.getElementById('system-protocol').value,
                    basePath: document.getElementById('api-base-path').value
                };
                
                const testUrl = `${tempConfig.protocol}://${tempConfig.host}:${tempConfig.port}${tempConfig.basePath}/health`;
                const response = await fetch(testUrl);
                
                if (response.ok) {
                    testResult.className = 'p-3 rounded-lg bg-green-50 border border-green-200';
                    testIcon.className = 'fas fa-check-circle text-green-600 mr-2';
                    testMessage.textContent = '连接测试成功';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                testResult.className = 'p-3 rounded-lg bg-red-50 border border-red-200';
                testIcon.className = 'fas fa-times-circle text-red-600 mr-2';
                testMessage.textContent = `连接测试失败: ${error.message}`;
            }
        }

        function applyPreset(preset) {
            switch (preset) {
                case 'local':
                    document.getElementById('system-host').value = '127.0.0.1';
                    document.getElementById('system-port').value = '8080';
                    document.getElementById('system-protocol').value = 'http';
                    document.getElementById('api-base-path').value = '/api/v1';
                    break;
                case 'production':
                    // 用户可以根据需要修改生产环境配置
                    document.getElementById('system-host').value = 'your-server.com';
                    document.getElementById('system-port').value = '443';
                    document.getElementById('system-protocol').value = 'https';
                    document.getElementById('api-base-path').value = '/api/v1';
                    break;
            }
            updatePreview();
            Utils.showNotification(`已应用${preset === 'local' ? '本地开发' : '生产环境'}配置`, 'info');
        }

        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>
