//! 基准对比功能演示
//! 
//! 展示回测中完整的基准对比功能

use sigmax_core::{Amount, SigmaXResult, Candle, TradingPair};
use sigmax_engines::{BenchmarkManager, BenchmarkConfig, BenchmarkType};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use chrono::{DateTime, Utc, TimeZone};
use uuid::Uuid;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 SigmaX基准对比功能演示");
    println!("{}", "=".repeat(60));

    // 1. 基准管理器初始化演示
    demo_benchmark_manager_setup().await?;

    // 2. 基准数据加载演示
    demo_benchmark_data_loading().await?;

    // 3. 策略与基准对比演示
    demo_strategy_benchmark_comparison().await?;

    // 4. 多基准对比演示
    demo_multi_benchmark_comparison().await?;

    // 5. 基准对比报告演示
    demo_benchmark_comparison_report().await?;

    println!("\n🎉 基准对比功能演示完成！");
    println!("✅ 所有基准对比功能运行正常");

    Ok(())
}

/// 演示基准管理器设置
async fn demo_benchmark_manager_setup() -> SigmaXResult<()> {
    println!("\n📊 1. 基准管理器初始化演示");
    println!("{}", "-".repeat(40));

    let mut benchmark_manager = BenchmarkManager::new();

    // 创建不同类型的基准配置
    let benchmarks = vec![
        BenchmarkConfig {
            id: Uuid::new_v4(),
            name: "BTC基准".to_string(),
            benchmark_type: BenchmarkType::MarketIndex("BTC".to_string()),
            data_source: Some("binance".to_string()),
            rebalance_frequency: None,
        },
        BenchmarkConfig {
            id: Uuid::new_v4(),
            name: "ETH基准".to_string(),
            benchmark_type: BenchmarkType::MarketIndex("ETH".to_string()),
            data_source: Some("binance".to_string()),
            rebalance_frequency: None,
        },
        BenchmarkConfig {
            id: Uuid::new_v4(),
            name: "无风险利率".to_string(),
            benchmark_type: BenchmarkType::RiskFree(dec!(0.02)), // 2%年化
            data_source: None,
            rebalance_frequency: None,
        },
        BenchmarkConfig {
            id: Uuid::new_v4(),
            name: "等权重组合".to_string(),
            benchmark_type: BenchmarkType::EqualWeight(vec![
                TradingPair::new("BTC".to_string(), "USDT".to_string()),
                TradingPair::new("ETH".to_string(), "USDT".to_string()),
            ]),
            data_source: Some("binance".to_string()),
            rebalance_frequency: Some(sigmax_engines::RebalanceFrequency::Weekly),
        },
    ];

    println!("📈 添加 {} 个基准配置", benchmarks.len());

    for benchmark in benchmarks {
        println!("   添加基准: {} (类型: {:?})", benchmark.name, benchmark.benchmark_type);
        benchmark_manager.add_benchmark(benchmark)?;
    }

    let total_benchmarks = benchmark_manager.get_benchmarks().len();
    println!("✅ 成功添加 {} 个基准配置", total_benchmarks);

    Ok(())
}

/// 演示基准数据加载
async fn demo_benchmark_data_loading() -> SigmaXResult<()> {
    println!("\n📈 2. 基准数据加载演示");
    println!("{}", "-".repeat(40));

    let mut benchmark_manager = BenchmarkManager::new();

    // 创建基准配置
    let benchmark_id = Uuid::new_v4();
    let benchmark_config = BenchmarkConfig {
        id: benchmark_id,
        name: "BTC基准".to_string(),
        benchmark_type: BenchmarkType::MarketIndex("BTC".to_string()),
        data_source: Some("binance".to_string()),
        rebalance_frequency: None,
    };

    benchmark_manager.add_benchmark(benchmark_config)?;

    // 生成模拟的基准数据
    let mut benchmark_candles = Vec::new();
    let start_time = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut current_price = dec!(40000); // BTC起始价格

    for i in 0..100 {
        let timestamp = start_time + chrono::Duration::hours(i);
        
        // 模拟价格波动
        let price_change = if i % 7 == 0 {
            dec!(-0.02) // 偶尔下跌
        } else if i % 5 == 0 {
            dec!(0.03) // 偶尔大涨
        } else {
            dec!(0.005) // 小幅上涨
        };

        current_price = current_price * (dec!(1) + price_change);

        let candle = Candle {
            timestamp,
            open: current_price * dec!(0.999),
            high: current_price * dec!(1.002),
            low: current_price * dec!(0.998),
            close: current_price,
            volume: dec!(1000),
        };

        benchmark_candles.push(candle);
    }

    println!("📊 生成 {} 个基准数据点", benchmark_candles.len());

    // 加载基准数据
    benchmark_manager.load_benchmark_data(benchmark_id, benchmark_candles).await?;

    let benchmark_data = benchmark_manager.get_benchmark_data(&benchmark_id);
    if let Some(data) = benchmark_data {
        println!("✅ 成功加载基准数据，数据点数量: {}", data.len());
        
        // 显示前几个数据点
        for (i, point) in data.iter().take(3).enumerate() {
            println!("   数据点 {}: 价格 {}, 收益率 {:.4}%", 
                i + 1, point.value, point.return_rate * dec!(100));
        }
    }

    Ok(())
}

/// 演示策略与基准对比
async fn demo_strategy_benchmark_comparison() -> SigmaXResult<()> {
    println!("\n🎯 3. 策略与基准对比演示");
    println!("{}", "-".repeat(40));

    let mut benchmark_manager = BenchmarkManager::new();

    // 设置基准
    let benchmark_id = Uuid::new_v4();
    let benchmark_config = BenchmarkConfig {
        id: benchmark_id,
        name: "市场基准".to_string(),
        benchmark_type: BenchmarkType::MarketIndex("BTC".to_string()),
        data_source: Some("binance".to_string()),
        rebalance_frequency: None,
    };

    benchmark_manager.add_benchmark(benchmark_config)?;

    // 生成策略和基准的模拟数据
    let start_time = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut strategy_value = dec!(100000); // 策略初始资金
    let mut benchmark_price = dec!(40000); // 基准初始价格

    let mut benchmark_candles = Vec::new();

    println!("📈 模拟策略和基准表现数据");

    for i in 0..50 {
        let timestamp = start_time + chrono::Duration::days(i);

        // 模拟基准收益率
        let benchmark_return = if i % 10 == 0 {
            dec!(-0.03) // 偶尔大跌
        } else if i % 7 == 0 {
            dec!(0.04) // 偶尔大涨
        } else {
            dec!(0.01) // 正常上涨
        };

        // 模拟策略收益率（通常比基准稍好）
        let strategy_return = benchmark_return + dec!(0.005); // 策略超额收益

        // 更新价值
        benchmark_price = benchmark_price * (dec!(1) + benchmark_return);
        strategy_value = strategy_value * (dec!(1) + strategy_return);

        // 创建基准K线数据
        let candle = Candle {
            timestamp,
            open: benchmark_price * dec!(0.999),
            high: benchmark_price * dec!(1.001),
            low: benchmark_price * dec!(0.999),
            close: benchmark_price,
            volume: dec!(1000),
        };

        benchmark_candles.push(candle);

        // 更新策略数据
        benchmark_manager.update_strategy_data(timestamp, strategy_value)?;

        // 每10天显示一次进度
        if i % 10 == 0 {
            println!("   第 {} 天: 策略价值 ${:.2}, 基准价格 ${:.2}", 
                i + 1, strategy_value, benchmark_price);
        }
    }

    // 加载基准数据
    benchmark_manager.load_benchmark_data(benchmark_id, benchmark_candles).await?;

    // 计算基准对比结果
    let comparison = benchmark_manager.calculate_benchmark_comparison(benchmark_id)?;

    println!("\n📊 基准对比结果:");
    println!("   策略总收益: {:.2}%", comparison.strategy_return * dec!(100));
    println!("   基准总收益: {:.2}%", comparison.benchmark_return * dec!(100));
    println!("   超额收益: {:.2}%", comparison.excess_return * dec!(100));
    println!("   跟踪误差: {:.2}%", comparison.tracking_error * dec!(100));
    println!("   信息比率: {:.3}", comparison.information_ratio);
    println!("   贝塔系数: {:.3}", comparison.beta);
    println!("   阿尔法: {:.2}%", comparison.alpha * dec!(100));
    println!("   相关性: {:.3}", comparison.correlation);
    println!("   相对夏普比率: {:.3}", comparison.relative_sharpe);
    println!("   相对最大回撤: {:.2}%", comparison.relative_max_drawdown * dec!(100));
    println!("   超越表现比率: {:.1}%", comparison.outperformance_ratio * dec!(100));

    println!("\n💡 对比分析:");
    if comparison.excess_return > dec!(0.05) {
        println!("   ✅ 策略显著跑赢基准 (超额收益 > 5%)");
    } else if comparison.excess_return > Decimal::ZERO {
        println!("   📊 策略跑赢基准");
    } else {
        println!("   ❌ 策略跑输基准");
    }

    if comparison.information_ratio > dec!(1.0) {
        println!("   ✅ 信息比率优秀 (> 1.0)");
    } else if comparison.information_ratio > dec!(0.5) {
        println!("   📊 信息比率良好 (> 0.5)");
    } else {
        println!("   ⚠️ 信息比率一般 (< 0.5)");
    }

    println!("\n✅ 策略与基准对比分析完成");
    Ok(())
}

/// 演示多基准对比
async fn demo_multi_benchmark_comparison() -> SigmaXResult<()> {
    println!("\n🔄 4. 多基准对比演示");
    println!("{}", "-".repeat(40));

    let mut benchmark_manager = BenchmarkManager::new();

    // 创建多个基准
    let benchmark_configs = vec![
        ("保守基准", BenchmarkType::RiskFree(dec!(0.02))),
        ("市场基准", BenchmarkType::MarketIndex("BTC".to_string())),
        ("激进基准", BenchmarkType::MarketIndex("ETH".to_string())),
    ];

    let mut benchmark_ids = Vec::new();

    for (name, benchmark_type) in benchmark_configs {
        let id = Uuid::new_v4();
        let config = BenchmarkConfig {
            id,
            name: name.to_string(),
            benchmark_type,
            data_source: Some("binance".to_string()),
            rebalance_frequency: None,
        };

        benchmark_manager.add_benchmark(config)?;
        benchmark_ids.push((id, name));
    }

    // 生成模拟数据
    let start_time = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut strategy_value = dec!(100000);

    // 为每个基准生成数据
    for (benchmark_id, benchmark_name) in &benchmark_ids {
        let mut candles = Vec::new();
        let mut price = dec!(1000); // 标准化起始价格

        for i in 0..30 {
            let timestamp = start_time + chrono::Duration::days(i);

            // 不同基准的不同表现模式
            let return_rate = match benchmark_name {
                &"保守基准" => dec!(0.0001), // 稳定低收益
                &"市场基准" => if i % 5 == 0 { dec!(-0.02) } else { dec!(0.015) }, // 中等波动
                &"激进基准" => if i % 3 == 0 { dec!(-0.05) } else { dec!(0.03) }, // 高波动
                _ => dec!(0.01),
            };

            price = price * (dec!(1) + return_rate);

            let candle = Candle {
                timestamp,
                open: price,
                high: price * dec!(1.001),
                low: price * dec!(0.999),
                close: price,
                volume: dec!(1000),
            };

            candles.push(candle);
        }

        benchmark_manager.load_benchmark_data(*benchmark_id, candles).await?;
    }

    // 生成策略数据
    for i in 0..30 {
        let timestamp = start_time + chrono::Duration::days(i);
        let strategy_return = dec!(0.02); // 策略稳定收益
        strategy_value = strategy_value * (dec!(1) + strategy_return);
        benchmark_manager.update_strategy_data(timestamp, strategy_value)?;
    }

    println!("📊 多基准对比结果:");

    // 计算与每个基准的对比
    for (benchmark_id, benchmark_name) in &benchmark_ids {
        if let Ok(comparison) = benchmark_manager.calculate_benchmark_comparison(*benchmark_id) {
            println!("\n   vs {}:", benchmark_name);
            println!("     超额收益: {:.2}%", comparison.excess_return * dec!(100));
            println!("     信息比率: {:.3}", comparison.information_ratio);
            println!("     相关性: {:.3}", comparison.correlation);
            println!("     超越比率: {:.1}%", comparison.outperformance_ratio * dec!(100));
        }
    }

    println!("\n✅ 多基准对比分析完成");
    Ok(())
}

/// 演示基准对比报告
async fn demo_benchmark_comparison_report() -> SigmaXResult<()> {
    println!("\n📋 5. 基准对比报告演示");
    println!("{}", "-".repeat(40));

    let mut benchmark_manager = BenchmarkManager::new();

    // 设置基准
    let benchmark_id = Uuid::new_v4();
    let benchmark_config = BenchmarkConfig {
        id: benchmark_id,
        name: "综合基准".to_string(),
        benchmark_type: BenchmarkType::MarketIndex("BTC".to_string()),
        data_source: Some("binance".to_string()),
        rebalance_frequency: None,
    };

    benchmark_manager.add_benchmark(benchmark_config)?;

    // 生成详细的测试数据
    let start_time = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut strategy_value = dec!(1000000); // 100万初始资金
    let mut benchmark_price = dec!(50000);

    let mut benchmark_candles = Vec::new();

    // 模拟一年的数据
    for i in 0..365 {
        let timestamp = start_time + chrono::Duration::days(i);

        // 复杂的市场模拟
        let market_cycle = (i as f64 * 0.1).sin() * 0.01; // 市场周期
        let random_factor = if i % 13 == 0 { -0.03 } else if i % 7 == 0 { 0.025 } else { 0.005 };
        let benchmark_return = Decimal::from_f64_retain(market_cycle + random_factor).unwrap_or(dec!(0.005));

        // 策略表现（有alpha）
        let strategy_alpha = dec!(0.002); // 日alpha
        let strategy_return = benchmark_return * dec!(0.8) + strategy_alpha; // 低贝塔 + alpha

        benchmark_price = benchmark_price * (dec!(1) + benchmark_return);
        strategy_value = strategy_value * (dec!(1) + strategy_return);

        let candle = Candle {
            timestamp,
            open: benchmark_price,
            high: benchmark_price * dec!(1.005),
            low: benchmark_price * dec!(0.995),
            close: benchmark_price,
            volume: dec!(10000),
        };

        benchmark_candles.push(candle);
        benchmark_manager.update_strategy_data(timestamp, strategy_value)?;
    }

    benchmark_manager.load_benchmark_data(benchmark_id, benchmark_candles).await?;

    // 生成综合报告
    let comparison = benchmark_manager.calculate_benchmark_comparison(benchmark_id)?;

    println!("📊 年度基准对比报告");
    println!("{}", "=".repeat(50));
    
    println!("\n📈 收益表现:");
    println!("   策略年化收益率: {:.2}%", comparison.strategy_return * dec!(100));
    println!("   基准年化收益率: {:.2}%", comparison.benchmark_return * dec!(100));
    println!("   年化超额收益: {:.2}%", comparison.excess_return * dec!(100));
    
    println!("\n📊 风险指标:");
    println!("   年化跟踪误差: {:.2}%", comparison.tracking_error * dec!(100));
    println!("   信息比率: {:.3}", comparison.information_ratio);
    println!("   相对最大回撤: {:.2}%", comparison.relative_max_drawdown * dec!(100));
    
    println!("\n🔗 市场关系:");
    println!("   贝塔系数: {:.3}", comparison.beta);
    println!("   年化阿尔法: {:.2}%", comparison.alpha * dec!(100));
    println!("   相关系数: {:.3}", comparison.correlation);
    
    println!("\n🎯 表现统计:");
    println!("   超越基准天数比例: {:.1}%", comparison.outperformance_ratio * dec!(100));
    println!("   相对夏普比率: {:.3}", comparison.relative_sharpe);

    println!("\n💡 综合评价:");
    let score = calculate_strategy_score(&comparison);
    println!("   策略综合评分: {:.1}/100", score);
    
    if score >= 80.0 {
        println!("   🏆 优秀策略 - 显著跑赢基准");
    } else if score >= 60.0 {
        println!("   ✅ 良好策略 - 稳定跑赢基准");
    } else if score >= 40.0 {
        println!("   📊 一般策略 - 基本跟上基准");
    } else {
        println!("   ❌ 较差策略 - 明显跑输基准");
    }

    println!("\n✅ 基准对比报告生成完成");
    Ok(())
}

/// 计算策略综合评分
fn calculate_strategy_score(comparison: &sigmax_engines::BenchmarkComparison) -> f64 {
    let mut score = 50.0; // 基础分

    // 超额收益贡献 (30分)
    let excess_return_f64 = comparison.excess_return.to_string().parse::<f64>().unwrap_or(0.0);
    score += (excess_return_f64 * 100.0).min(30.0).max(-30.0);

    // 信息比率贡献 (25分)
    let info_ratio_f64 = comparison.information_ratio.to_string().parse::<f64>().unwrap_or(0.0);
    score += (info_ratio_f64 * 15.0).min(25.0).max(-25.0);

    // 相关性贡献 (15分)
    let correlation_f64 = comparison.correlation.to_string().parse::<f64>().unwrap_or(0.0);
    score += (correlation_f64.abs() * 15.0).min(15.0);

    // 超越比率贡献 (15分)
    let outperform_f64 = comparison.outperformance_ratio.to_string().parse::<f64>().unwrap_or(0.0);
    score += ((outperform_f64 - 0.5) * 30.0).min(15.0).max(-15.0);

    // 阿尔法贡献 (15分)
    let alpha_f64 = comparison.alpha.to_string().parse::<f64>().unwrap_or(0.0);
    score += (alpha_f64 * 100.0).min(15.0).max(-15.0);

    score.max(0.0).min(100.0)
}
