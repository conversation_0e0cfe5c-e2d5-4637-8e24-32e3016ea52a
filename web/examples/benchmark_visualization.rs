//! 基准对比可视化演示
//! 
//! 生成基准对比的图表和报告

use sigmax_core::{SigmaXResult, Candle};
use sigmax_engines::{BenchmarkManager, BenchmarkConfig, BenchmarkType};
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use chrono::{DateTime, Utc, TimeZone};
use uuid::Uuid;
use std::fs;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("📊 SigmaX基准对比可视化演示");
    println!("{}", "=".repeat(60));

    // 生成基准对比数据
    let comparison_data = generate_benchmark_comparison_data().await?;
    
    // 生成HTML报告
    generate_html_report(&comparison_data)?;
    
    // 生成Mermaid图表
    generate_mermaid_charts()?;
    
    println!("\n🎉 基准对比可视化生成完成！");
    println!("📄 查看 benchmark_report.html 获取详细报告");
    println!("📊 查看 benchmark_charts.md 获取图表");

    Ok(())
}

/// 基准对比数据结构
#[derive(Debug)]
struct BenchmarkComparisonData {
    strategy_name: String,
    benchmark_name: String,
    strategy_return: Decimal,
    benchmark_return: Decimal,
    excess_return: Decimal,
    tracking_error: Decimal,
    information_ratio: Decimal,
    beta: Decimal,
    alpha: Decimal,
    correlation: Decimal,
    sharpe_ratio: Decimal,
    max_drawdown: Decimal,
    win_rate: Decimal,
    daily_returns: Vec<(DateTime<Utc>, Decimal, Decimal)>, // (date, strategy, benchmark)
}

/// 生成基准对比数据
async fn generate_benchmark_comparison_data() -> SigmaXResult<BenchmarkComparisonData> {
    let mut benchmark_manager = BenchmarkManager::new();

    // 设置基准
    let benchmark_id = Uuid::new_v4();
    let benchmark_config = BenchmarkConfig {
        id: benchmark_id,
        name: "BTC市场基准".to_string(),
        benchmark_type: BenchmarkType::MarketIndex("BTC".to_string()),
        data_source: Some("binance".to_string()),
        rebalance_frequency: None,
    };

    benchmark_manager.add_benchmark(benchmark_config)?;

    // 生成一年的模拟数据
    let start_time = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut strategy_value = dec!(1000000); // 100万初始资金
    let mut benchmark_price = dec!(42000); // BTC初始价格

    let mut benchmark_candles = Vec::new();
    let mut daily_returns = Vec::new();

    println!("📈 生成一年的策略和基准数据...");

    for i in 0..365 {
        let timestamp = start_time + chrono::Duration::days(i);

        // 复杂的市场模拟
        let market_trend = (i as f64 * 0.02).sin() * 0.015; // 长期趋势
        let volatility = (i as f64 * 0.1).cos() * 0.02; // 波动性
        let random_shock = if i % 30 == 0 { -0.08 } else if i % 20 == 0 { 0.06 } else { 0.0 };
        
        let benchmark_return = Decimal::from_f64_retain(
            market_trend + volatility + random_shock + 0.001
        ).unwrap_or(dec!(0.001));

        // 策略表现（有alpha和不同的beta）
        let strategy_alpha = dec!(0.0015); // 日alpha
        let beta = dec!(0.75); // 低贝塔策略
        let strategy_specific = if i % 15 == 0 { dec!(0.01) } else { dec!(0.0) }; // 策略特有收益
        
        let strategy_return = benchmark_return * beta + strategy_alpha + strategy_specific;

        // 更新价值
        benchmark_price = benchmark_price * (dec!(1) + benchmark_return);
        strategy_value = strategy_value * (dec!(1) + strategy_return);

        // 记录日收益率
        daily_returns.push((timestamp, strategy_return, benchmark_return));

        // 创建基准K线数据
        let candle = Candle {
            timestamp,
            open: benchmark_price * dec!(0.999),
            high: benchmark_price * dec!(1.002),
            low: benchmark_price * dec!(0.998),
            close: benchmark_price,
            volume: dec!(50000),
        };

        benchmark_candles.push(candle);
        benchmark_manager.update_strategy_data(timestamp, strategy_value)?;

        if i % 50 == 0 {
            println!("   第 {} 天: 策略价值 ${:.0}, 基准价格 ${:.0}", 
                i + 1, strategy_value, benchmark_price);
        }
    }

    // 加载基准数据并计算对比结果
    benchmark_manager.load_benchmark_data(benchmark_id, benchmark_candles).await?;
    let comparison = benchmark_manager.calculate_benchmark_comparison(benchmark_id)?;

    // 计算额外指标
    let strategy_returns: Vec<Decimal> = daily_returns.iter().map(|(_, s, _)| *s).collect();
    let benchmark_returns: Vec<Decimal> = daily_returns.iter().map(|(_, _, b)| *b).collect();
    
    let strategy_sharpe = calculate_sharpe_ratio(&strategy_returns);
    let strategy_max_dd = calculate_max_drawdown(&strategy_returns);
    let win_rate = calculate_win_rate(&strategy_returns, &benchmark_returns);

    Ok(BenchmarkComparisonData {
        strategy_name: "量化网格策略".to_string(),
        benchmark_name: "BTC市场基准".to_string(),
        strategy_return: comparison.strategy_return,
        benchmark_return: comparison.benchmark_return,
        excess_return: comparison.excess_return,
        tracking_error: comparison.tracking_error,
        information_ratio: comparison.information_ratio,
        beta: comparison.beta,
        alpha: comparison.alpha,
        correlation: comparison.correlation,
        sharpe_ratio: strategy_sharpe,
        max_drawdown: strategy_max_dd,
        win_rate,
        daily_returns,
    })
}

/// 生成HTML报告
fn generate_html_report(data: &BenchmarkComparisonData) -> SigmaXResult<()> {
    let html_content = format!(r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX基准对比报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
        .metric-label {{ color: #7f8c8d; font-size: 14px; margin-top: 5px; }}
        .positive {{ color: #27ae60; }}
        .negative {{ color: #e74c3c; }}
        .chart-container {{ margin: 30px 0; height: 400px; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .performance-badge {{ display: inline-block; padding: 8px 16px; border-radius: 20px; color: white; font-weight: bold; margin: 5px; }}
        .excellent {{ background: #27ae60; }}
        .good {{ background: #f39c12; }}
        .average {{ background: #95a5a6; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 SigmaX基准对比分析报告</h1>
        
        <div class="summary">
            <h2>📈 执行摘要</h2>
            <p><strong>策略名称:</strong> {strategy_name}</p>
            <p><strong>基准名称:</strong> {benchmark_name}</p>
            <p><strong>分析期间:</strong> 2024年1月1日 - 2024年12月31日 (365天)</p>
            <p><strong>综合评价:</strong> 
                <span class="performance-badge excellent">优秀策略</span>
                <span class="performance-badge good">显著跑赢基准</span>
            </p>
        </div>

        <h2>🎯 核心表现指标</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value positive">{strategy_return:.2}%</div>
                <div class="metric-label">策略年化收益率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{benchmark_return:.2}%</div>
                <div class="metric-label">基准年化收益率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value positive">{excess_return:.2}%</div>
                <div class="metric-label">年化超额收益</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{sharpe_ratio:.3}</div>
                <div class="metric-label">夏普比率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{information_ratio:.3}</div>
                <div class="metric-label">信息比率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{max_drawdown:.2}%</div>
                <div class="metric-label">最大回撤</div>
            </div>
        </div>

        <h2>🔗 市场关系分析</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{beta:.3}</div>
                <div class="metric-label">贝塔系数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value positive">{alpha:.2}%</div>
                <div class="metric-label">年化阿尔法</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{correlation:.3}</div>
                <div class="metric-label">相关系数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{tracking_error:.2}%</div>
                <div class="metric-label">跟踪误差</div>
            </div>
            <div class="metric-card">
                <div class="metric-value positive">{win_rate:.1}%</div>
                <div class="metric-label">胜率</div>
            </div>
        </div>

        <h2>📊 收益率对比图表</h2>
        <div class="chart-container">
            <canvas id="returnsChart"></canvas>
        </div>

        <h2>📋 详细分析</h2>
        <div class="summary">
            <h3>🎯 策略优势</h3>
            <ul>
                <li><strong>显著超额收益:</strong> 策略实现了 {excess_return:.2}% 的年化超额收益，表现优异</li>
                <li><strong>风险控制良好:</strong> 贝塔系数 {beta:.3} 显示策略具有较好的风险控制能力</li>
                <li><strong>稳定的Alpha:</strong> 年化Alpha {alpha:.2}% 证明策略具有持续的超额收益能力</li>
                <li><strong>高胜率:</strong> {win_rate:.1}% 的胜率显示策略的稳定性</li>
            </ul>
            
            <h3>⚠️ 风险提示</h3>
            <ul>
                <li><strong>跟踪误差:</strong> {tracking_error:.2}% 的跟踪误差需要持续监控</li>
                <li><strong>市场相关性:</strong> {correlation:.3} 的相关性表明策略仍受市场影响</li>
                <li><strong>回撤控制:</strong> 最大回撤 {max_drawdown:.2}% 需要进一步优化</li>
            </ul>
        </div>
    </div>

    <script>
        // 生成收益率对比图表
        const ctx = document.getElementById('returnsChart').getContext('2d');
        
        // 模拟数据 - 实际应用中应该从服务器获取
        const dates = [];
        const strategyReturns = [];
        const benchmarkReturns = [];
        
        // 生成365天的累计收益率数据
        let strategyCumReturn = 1.0;
        let benchmarkCumReturn = 1.0;
        
        for (let i = 0; i < 365; i++) {{
            const date = new Date(2024, 0, i + 1);
            dates.push(date.toISOString().split('T')[0]);
            
            // 模拟日收益率
            const strategyDaily = (Math.random() - 0.45) * 0.03 + 0.002;
            const benchmarkDaily = (Math.random() - 0.48) * 0.025 + 0.0015;
            
            strategyCumReturn *= (1 + strategyDaily);
            benchmarkCumReturn *= (1 + benchmarkDaily);
            
            strategyReturns.push(((strategyCumReturn - 1) * 100).toFixed(2));
            benchmarkReturns.push(((benchmarkCumReturn - 1) * 100).toFixed(2));
        }}
        
        new Chart(ctx, {{
            type: 'line',
            data: {{
                labels: dates.filter((_, i) => i % 30 === 0), // 每月显示一个点
                datasets: [{{
                    label: '策略累计收益率',
                    data: strategyReturns.filter((_, i) => i % 30 === 0),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: false
                }}, {{
                    label: '基准累计收益率',
                    data: benchmarkReturns.filter((_, i) => i % 30 === 0),
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: false
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    title: {{
                        display: true,
                        text: '策略 vs 基准累计收益率对比'
                    }},
                    legend: {{
                        position: 'top'
                    }}
                }},
                scales: {{
                    y: {{
                        title: {{
                            display: true,
                            text: '累计收益率 (%)'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: '日期'
                        }}
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"#,
        strategy_name = data.strategy_name,
        benchmark_name = data.benchmark_name,
        strategy_return = data.strategy_return * dec!(100),
        benchmark_return = data.benchmark_return * dec!(100),
        excess_return = data.excess_return * dec!(100),
        sharpe_ratio = data.sharpe_ratio,
        information_ratio = data.information_ratio,
        max_drawdown = data.max_drawdown * dec!(100),
        beta = data.beta,
        alpha = data.alpha * dec!(100),
        correlation = data.correlation,
        tracking_error = data.tracking_error * dec!(100),
        win_rate = data.win_rate * dec!(100),
    );

    fs::write("benchmark_report.html", html_content)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("写入HTML文件失败: {}", e)))?;

    println!("✅ HTML报告已生成: benchmark_report.html");
    Ok(())
}

/// 生成Mermaid图表
fn generate_mermaid_charts() -> SigmaXResult<()> {
    let mermaid_content = r#"
# SigmaX基准对比流程图

## 基准对比系统架构

```mermaid
graph TB
    A[策略执行] --> B[数据收集]
    C[基准数据] --> B
    B --> D[BenchmarkManager]
    D --> E[时间序列对齐]
    E --> F[指标计算]
    F --> G[对比分析]
    G --> H[报告生成]
    
    subgraph "基准类型"
        I[市场指数]
        J[无风险利率]
        K[自定义基准]
        L[等权重组合]
    end
    
    subgraph "核心指标"
        M[超额收益]
        N[跟踪误差]
        O[信息比率]
        P[贝塔/阿尔法]
        Q[相关性]
    end
    
    C --> I
    C --> J
    C --> K
    C --> L
    
    F --> M
    F --> N
    F --> O
    F --> P
    F --> Q
```

## 基准对比计算流程

```mermaid
sequenceDiagram
    participant S as 策略
    participant BM as BenchmarkManager
    participant BD as 基准数据
    participant CA as 计算引擎
    participant R as 报告

    S->>BM: 更新策略数据
    BD->>BM: 加载基准数据
    BM->>CA: 触发计算
    CA->>CA: 时间序列对齐
    CA->>CA: 计算收益率
    CA->>CA: 计算风险指标
    CA->>CA: 计算市场关系
    CA->>R: 生成对比结果
    R->>S: 返回分析报告
```

## 指标计算关系图

```mermaid
mindmap
  root((基准对比指标))
    收益指标
      策略收益率
      基准收益率
      超额收益率
    风险指标
      跟踪误差
      信息比率
      相对夏普比率
      相对最大回撤
    市场关系
      贝塔系数
      阿尔法系数
      相关系数
    表现统计
      胜率
      超越比率
      综合评分
```
"#;

    fs::write("benchmark_charts.md", mermaid_content)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("写入Mermaid文件失败: {}", e)))?;

    println!("✅ Mermaid图表已生成: benchmark_charts.md");
    Ok(())
}

/// 计算夏普比率
fn calculate_sharpe_ratio(returns: &[Decimal]) -> Decimal {
    if returns.len() < 2 {
        return Decimal::ZERO;
    }

    let mean_return = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
    let variance = returns
        .iter()
        .map(|r| {
            let diff = *r - mean_return;
            diff * diff
        })
        .sum::<Decimal>() / Decimal::from(returns.len() - 1);

    let variance_f64: f64 = variance.to_string().parse().unwrap_or(0.0);
    let std_dev = Decimal::from_f64_retain(variance_f64.sqrt()).unwrap_or(Decimal::ZERO);

    if std_dev == Decimal::ZERO {
        return Decimal::ZERO;
    }

    // 年化夏普比率
    let annualized_return = mean_return * Decimal::from(252);
    let annualized_vol = std_dev * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16));
    
    annualized_return / annualized_vol
}

/// 计算最大回撤
fn calculate_max_drawdown(returns: &[Decimal]) -> Decimal {
    if returns.is_empty() {
        return Decimal::ZERO;
    }

    let mut equity_curve = vec![Decimal::ONE];
    for &return_rate in returns {
        let new_value = equity_curve.last().unwrap() * (Decimal::ONE + return_rate);
        equity_curve.push(new_value);
    }

    let mut max_drawdown = Decimal::ZERO;
    let mut peak = equity_curve[0];

    for &value in equity_curve.iter().skip(1) {
        if value > peak {
            peak = value;
        } else if peak > Decimal::ZERO {
            let drawdown = (peak - value) / peak;
            if drawdown > max_drawdown {
                max_drawdown = drawdown;
            }
        }
    }

    max_drawdown
}

/// 计算胜率
fn calculate_win_rate(strategy_returns: &[Decimal], benchmark_returns: &[Decimal]) -> Decimal {
    if strategy_returns.len() != benchmark_returns.len() || strategy_returns.is_empty() {
        return Decimal::ZERO;
    }

    let win_count = strategy_returns
        .iter()
        .zip(benchmark_returns.iter())
        .filter(|(s, b)| s > b)
        .count();

    Decimal::from(win_count) / Decimal::from(strategy_returns.len())
}
