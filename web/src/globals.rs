//! 全局服务访问
//! 
//! 提供全局配置缓存和仓储访问，支持系统其他模块直接使用配置缓存

use std::sync::Arc;
use once_cell::sync::OnceCell;
use sigmax_core::{ConfigCache, SigmaXResult};
use sigmax_database::repositories::traits::SystemConfigRepository;
use sigmax_core::{
    TradingConfig, RiskManagementConfig, SystemGeneralConfig, NotificationConfig,
    ApiConfig, DatabaseSystemConfig, CacheConfig, MonitoringConfig, StrategySystemConfig,
    StrategyTemplate, ExchangeSystemConfig
};

/// 全局配置缓存实例
static GLOBAL_CONFIG_CACHE: OnceCell<Arc<ConfigCache>> = OnceCell::new();

/// 全局配置仓储实例（用于缓存未命中时加载）
static GLOBAL_CONFIG_REPOSITORY: OnceCell<Arc<dyn SystemConfigRepository>> = OnceCell::new();

/// 初始化全局配置缓存和仓储
/// 
/// 这个方法应该在系统启动时调用一次
pub fn init_global_config_cache(
    cache: Arc<ConfigCache>, 
    repository: Arc<dyn SystemConfigRepository>
) -> Result<(), String> {
    GLOBAL_CONFIG_CACHE.set(cache)
        .map_err(|_| "Global config cache already initialized")?;
    GLOBAL_CONFIG_REPOSITORY.set(repository)
        .map_err(|_| "Global config repository already initialized")?;
    Ok(())
}

/// 获取全局配置缓存
/// 
/// # Panics
/// 如果全局配置缓存未初始化则会 panic
pub fn get_global_config_cache() -> &'static Arc<ConfigCache> {
    GLOBAL_CONFIG_CACHE.get().expect("Global config cache not initialized. Call init_global_config_cache() first.")
}

/// 获取全局配置仓储
/// 
/// # Panics
/// 如果全局配置仓储未初始化则会 panic
pub fn get_global_config_repository() -> &'static Arc<dyn SystemConfigRepository> {
    GLOBAL_CONFIG_REPOSITORY.get().expect("Global config repository not initialized. Call init_global_config_cache() first.")
}

/// 检查全局配置是否已初始化
pub fn is_global_config_initialized() -> bool {
    GLOBAL_CONFIG_CACHE.get().is_some() && GLOBAL_CONFIG_REPOSITORY.get().is_some()
}

// ============================================================================
// 便捷的配置访问函数
// ============================================================================

/// 获取交易配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_trading_config() -> SigmaXResult<TradingConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_trading_config(|| {
        repository.get_trading_config()
    }).await
}

/// 获取风险管理配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_risk_config() -> SigmaXResult<RiskManagementConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_risk_config(|| {
        repository.get_risk_config()
    }).await
}

/// 获取系统配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_system_config() -> SigmaXResult<SystemGeneralConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_system_config(|| {
        repository.get_system_config()
    }).await
}

/// 获取通知配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_notification_config() -> SigmaXResult<NotificationConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_notification_config(|| {
        repository.get_notification_config()
    }).await
}

/// 获取API配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_api_config() -> SigmaXResult<ApiConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_api_config(|| {
        repository.get_api_config()
    }).await
}

/// 获取数据库配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_database_config() -> SigmaXResult<DatabaseSystemConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_database_config(|| {
        repository.get_database_config()
    }).await
}

/// 获取缓存配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_cache_config() -> SigmaXResult<CacheConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_cache_config(|| {
        repository.get_cache_config()
    }).await
}

/// 获取监控配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_monitoring_config() -> SigmaXResult<MonitoringConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_monitoring_config(|| {
        repository.get_monitoring_config()
    }).await
}

/// 获取策略配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_strategy_config() -> SigmaXResult<StrategySystemConfig> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_strategy_config(|| {
        repository.get_strategy_config()
    }).await
}

/// 获取策略模板
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_strategy_template(template_name: &str) -> SigmaXResult<Option<StrategyTemplate>> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_strategy_template(template_name, || {
        repository.get_strategy_template(template_name)
    }).await
}

/// 获取交易所配置
/// 
/// 直接从全局缓存获取，缓存未命中时自动从数据库加载
pub async fn get_exchange_config(exchange_name: &str) -> SigmaXResult<Option<ExchangeSystemConfig>> {
    let cache = get_global_config_cache();
    let repository = get_global_config_repository();
    
    cache.get_or_load_exchange_config(exchange_name, || {
        repository.get_exchange_config(exchange_name)
    }).await
}

// ============================================================================
// 缓存管理函数
// ============================================================================

/// 清除所有配置缓存
/// 
/// 强制下次访问时重新从数据库加载
pub async fn clear_all_config_cache() {
    if let Some(cache) = GLOBAL_CONFIG_CACHE.get() {
        cache.clear_cache().await;
    }
}

/// 清除指定类型的配置缓存
/// 
/// 强制下次访问时重新从数据库加载
pub async fn clear_config_cache(config_type: &str) {
    if let Some(cache) = GLOBAL_CONFIG_CACHE.get() {
        cache.clear_config_cache(config_type).await;
    }
}
