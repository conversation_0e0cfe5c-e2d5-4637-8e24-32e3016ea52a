//! 策略管理服务

use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::Utc;
use serde_json::Value as JsonValue;
use tracing::{info, error};
use std::sync::Arc;

use crate::error::{ApiError, ApiResult};
use sigmax_core::SigmaXError;
use crate::handlers::strategy_handlers::{
    CreateStrategyRequest, UpdateStrategyRequest, StrategyResponse,
    StrategyRiskConfigResponse
    // 🗑️ 已移除: StrategyStatusResponse
};
use crate::common_types::{StrategyPerformanceResponse};
use crate::execution_engine::{StrategyExecutionEngine, StrategyExecutionStatus};

/// 策略管理服务
#[derive(Clone)]
pub struct StrategyService {
    db: PgPool,
    execution_engine: Option<Arc<StrategyExecutionEngine>>,
}

impl StrategyService {
    /// 创建新的策略服务
    pub fn new(db: PgPool) -> Self {
        Self {
            db,
            execution_engine: None,
        }
    }

    /// 设置执行引擎
    pub fn set_execution_engine(&mut self, engine: Arc<StrategyExecutionEngine>) {
        self.execution_engine = Some(engine);
    }

    /// 创建策略
    pub async fn create_strategy(&self, request: CreateStrategyRequest) -> ApiResult<StrategyResponse> {
        let strategy_id = Uuid::new_v4();
        let now = Utc::now();

        // 准备风控配置JSON
        let risk_config_json = if let Some(risk_config) = &request.risk_config {
            serde_json::json!({
                "max_position_size": risk_config.max_position_size.as_ref().unwrap_or(&"1000.00".to_string()),
                "max_daily_loss": risk_config.max_daily_loss.as_ref().unwrap_or(&"100.00".to_string()),
                "max_drawdown": risk_config.max_drawdown.as_ref().unwrap_or(&"500.00".to_string()),
                "stop_loss_percentage": risk_config.stop_loss_percentage.as_ref().unwrap_or(&"5.0".to_string()),
                "take_profit_percentage": risk_config.take_profit_percentage.as_ref().unwrap_or(&"10.0".to_string()),
                "max_leverage": risk_config.max_leverage.as_ref().unwrap_or(&"1.0".to_string())
            })
        } else {
            serde_json::json!({
                "max_position_size": "1000.00",
                "max_daily_loss": "100.00",
                "max_drawdown": "500.00",
                "stop_loss_percentage": "5.0",
                "take_profit_percentage": "10.0",
                "max_leverage": "1.0"
            })
        };

        // 准备配置JSON，包含初始资本和交易对信息
        let config_json = serde_json::json!({
            "initial_capital": request.initial_capital,
            "trading_pairs": request.trading_pairs,
            "enabled": request.enabled.unwrap_or(true),
            "parameters": request.parameters
        });

        // 插入策略记录
        let result = sqlx::query(
            r#"
            INSERT INTO strategies (
                id, name, strategy_type, description, config, risk_config, status, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, 'Created'::strategy_status, $7, $8)
            "#
        )
        .bind(strategy_id)
        .bind(&request.name)
        .bind(&request.strategy_type)
        .bind(&request.description)
        .bind(&config_json)
        .bind(&risk_config_json)
        .bind(now)
        .bind(now)
        .execute(&self.db)
        .await
        .map_err(|e| {
            error!("Failed to create strategy: {}", e);
            ApiError::from(SigmaXError::Database(e.to_string()))
        })?;

        if result.rows_affected() == 0 {
            return Err(ApiError::Internal("Failed to insert strategy".to_string()));
        }

        // 创建初始性能记录
        self.create_initial_performance_record(strategy_id).await?;

        info!("Strategy created successfully: {}", strategy_id);

        // 返回创建的策略
        self.get_strategy_by_id(strategy_id).await
    }

    /// 获取策略列表
    pub async fn get_strategies(&self, page: usize, per_page: usize) -> ApiResult<(Vec<StrategyResponse>, usize)> {
        let offset = (page - 1) * per_page;

        // 获取策略总数
        let total_count_row = sqlx::query("SELECT COUNT(*) as count FROM strategies")
            .fetch_one(&self.db)
            .await
            .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?;
        let total_count: i64 = total_count_row.get("count");

        // 获取策略列表
        let strategies = sqlx::query(
            r#"
            SELECT s.id, s.name, s.strategy_type, s.description, s.config, s.risk_config,
                   s.status::text as status, s.created_at, s.updated_at,
                   sp.total_trades, sp.winning_trades, sp.losing_trades,
                   sp.total_pnl, sp.win_rate, sp.sharpe_ratio, sp.max_drawdown,
                   sp.current_positions, sp.last_signal_time, sp.updated_at as perf_updated_at
            FROM strategies s
            LEFT JOIN strategy_performance sp ON s.id = sp.strategy_id
            ORDER BY s.created_at DESC
            LIMIT $1 OFFSET $2
            "#
        )
        .bind(per_page as i64)
        .bind(offset as i64)
        .fetch_all(&self.db)
        .await
        .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?;

        let strategy_responses = strategies
            .into_iter()
            .map(|row| {
                let config: JsonValue = row.try_get("config").unwrap_or_default();
                let risk_config: JsonValue = row.try_get("risk_config").unwrap_or_default();

                StrategyResponse {
                    id: row.get::<uuid::Uuid, _>("id").to_string(),
                    strategy_type: row.get("strategy_type"),
                    name: row.get("name"),
                    description: row.try_get("description").ok(),
                    trading_pairs: config["trading_pairs"].as_array()
                        .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect())
                        .unwrap_or_default(),
                    initial_capital: config["initial_capital"].as_str().unwrap_or("0.00").to_string(),
                    parameters: config["parameters"].clone(),
                    risk_config: StrategyRiskConfigResponse {
                        max_position_size: risk_config["max_position_size"].as_str().unwrap_or("1000.00").to_string(),
                        max_daily_loss: risk_config["max_daily_loss"].as_str().unwrap_or("100.00").to_string(),
                        max_drawdown: risk_config["max_drawdown"].as_str().unwrap_or("500.00").to_string(),
                        stop_loss_percentage: risk_config["stop_loss_percentage"].as_str().unwrap_or("5.0").to_string(),
                        take_profit_percentage: risk_config["take_profit_percentage"].as_str().unwrap_or("10.0").to_string(),
                        max_leverage: risk_config["max_leverage"].as_str().unwrap_or("1.0").to_string(),
                    },
                    status: row.get("status"),
                    enabled: config["enabled"].as_bool().unwrap_or(true),
                    performance: StrategyPerformanceResponse {
                        strategy_id: row.get::<uuid::Uuid, _>("id").to_string(),
                        total_trades: row.try_get::<i32, _>("total_trades").unwrap_or(0) as u64,
                        winning_trades: row.try_get::<i32, _>("winning_trades").unwrap_or(0) as u64,
                        losing_trades: row.try_get::<i32, _>("losing_trades").unwrap_or(0) as u64,
                        total_pnl: row.try_get::<rust_decimal::Decimal, _>("total_pnl").unwrap_or_default().to_string(),
                        win_rate: row.try_get::<rust_decimal::Decimal, _>("win_rate").unwrap_or_default().to_string(),
                        sharpe_ratio: row.try_get::<rust_decimal::Decimal, _>("sharpe_ratio").ok().map(|sr| sr.to_string()),
                        max_drawdown: row.try_get::<rust_decimal::Decimal, _>("max_drawdown").unwrap_or_default().to_string(),
                        current_positions: row.try_get::<i32, _>("current_positions").unwrap_or(0) as u32,
                        last_updated: row.try_get::<chrono::DateTime<chrono::Utc>, _>("perf_updated_at")
                            .or_else(|_| row.try_get::<chrono::DateTime<chrono::Utc>, _>("updated_at"))
                            .unwrap_or_else(|_| chrono::Utc::now())
                            .to_rfc3339(),
                    },
                    created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
                    updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at").to_rfc3339(),
                }
            })
            .collect();

        Ok((strategy_responses, total_count as usize))
    }

    /// 根据ID获取策略
    pub async fn get_strategy_by_id(&self, strategy_id: Uuid) -> ApiResult<StrategyResponse> {
        let row = sqlx::query(
            r#"
            SELECT s.id, s.name, s.strategy_type, s.description, s.config, s.risk_config,
                   s.status::text as status, s.created_at, s.updated_at,
                   sp.total_trades, sp.winning_trades, sp.losing_trades,
                   sp.total_pnl, sp.win_rate, sp.sharpe_ratio, sp.max_drawdown,
                   sp.current_positions, sp.last_signal_time, sp.updated_at as perf_updated_at
            FROM strategies s
            LEFT JOIN strategy_performance sp ON s.id = sp.strategy_id
            WHERE s.id = $1
            "#
        )
        .bind(strategy_id)
        .fetch_optional(&self.db)
        .await
        .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?
        .ok_or_else(|| ApiError::NotFound("Strategy not found".to_string()))?;

        let config: JsonValue = row.try_get("config").unwrap_or_default();
        let risk_config: JsonValue = row.try_get("risk_config").unwrap_or_default();

        Ok(StrategyResponse {
            id: row.get::<uuid::Uuid, _>("id").to_string(),
            strategy_type: row.get("strategy_type"),
            name: row.get("name"),
            description: row.try_get("description").ok(),
            trading_pairs: config["trading_pairs"].as_array()
                .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect())
                .unwrap_or_default(),
            initial_capital: config["initial_capital"].as_str().unwrap_or("0.00").to_string(),
            parameters: config["parameters"].clone(),
            risk_config: StrategyRiskConfigResponse {
                max_position_size: risk_config["max_position_size"].as_str().unwrap_or("1000.00").to_string(),
                max_daily_loss: risk_config["max_daily_loss"].as_str().unwrap_or("100.00").to_string(),
                max_drawdown: risk_config["max_drawdown"].as_str().unwrap_or("500.00").to_string(),
                stop_loss_percentage: risk_config["stop_loss_percentage"].as_str().unwrap_or("5.0").to_string(),
                take_profit_percentage: risk_config["take_profit_percentage"].as_str().unwrap_or("10.0").to_string(),
                max_leverage: risk_config["max_leverage"].as_str().unwrap_or("1.0").to_string(),
            },
            status: row.get("status"),
            enabled: config["enabled"].as_bool().unwrap_or(true),
            performance: StrategyPerformanceResponse {
                strategy_id: row.get::<uuid::Uuid, _>("id").to_string(),
                total_trades: row.try_get::<i32, _>("total_trades").unwrap_or(0) as u64,
                winning_trades: row.try_get::<i32, _>("winning_trades").unwrap_or(0) as u64,
                losing_trades: row.try_get::<i32, _>("losing_trades").unwrap_or(0) as u64,
                total_pnl: row.try_get::<rust_decimal::Decimal, _>("total_pnl").unwrap_or_default().to_string(),
                win_rate: row.try_get::<rust_decimal::Decimal, _>("win_rate").unwrap_or_default().to_string(),
                sharpe_ratio: row.try_get::<rust_decimal::Decimal, _>("sharpe_ratio").ok().map(|sr| sr.to_string()),
                max_drawdown: row.try_get::<rust_decimal::Decimal, _>("max_drawdown").unwrap_or_default().to_string(),
                current_positions: row.try_get::<i32, _>("current_positions").unwrap_or(0) as u32,
                last_updated: row.try_get::<chrono::DateTime<chrono::Utc>, _>("perf_updated_at")
                    .or_else(|_| row.try_get::<chrono::DateTime<chrono::Utc>, _>("updated_at"))
                    .unwrap_or_else(|_| chrono::Utc::now())
                    .to_rfc3339(),
            },
            created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
            updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at").to_rfc3339(),
        })
    }

    /// 更新策略
    pub async fn update_strategy(&self, strategy_id: Uuid, request: UpdateStrategyRequest) -> ApiResult<StrategyResponse> {
        // 先获取现有策略
        let existing = self.get_strategy_by_id(strategy_id).await?;

        // 构建更新的配置
        let mut config = serde_json::json!({
            "initial_capital": existing.initial_capital,
            "trading_pairs": existing.trading_pairs,
            "enabled": existing.enabled,
            "parameters": existing.parameters
        });

        // 更新配置字段
        if let Some(parameters) = request.parameters {
            config["parameters"] = parameters;
        }
        if let Some(enabled) = request.enabled {
            config["enabled"] = serde_json::Value::Bool(enabled);
        }

        // 执行更新
        let result = sqlx::query(
            r#"
            UPDATE strategies
            SET name = COALESCE($2, name),
                description = COALESCE($3, description),
                config = COALESCE($4, config),
                updated_at = NOW()
            WHERE id = $1
            "#
        )
        .bind(strategy_id)
        .bind(&request.name)
        .bind(&request.description)
        .bind(&config)
        .execute(&self.db)
        .await
        .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?;

        if result.rows_affected() == 0 {
            return Err(ApiError::NotFound("Strategy not found".to_string()));
        }

        info!("Strategy updated successfully: {}", strategy_id);

        // 返回更新后的策略
        self.get_strategy_by_id(strategy_id).await
    }

    /// 删除策略
    pub async fn delete_strategy(&self, strategy_id: Uuid) -> ApiResult<()> {
        // 不可以删除策略，只能禁用！！！
        let result = sqlx::query("DELETE FROM strategies WHERE id = $1")
            .bind(strategy_id)
            .execute(&self.db)
            .await
            .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?;

        if result.rows_affected() == 0 {
            return Err(ApiError::NotFound("Strategy not found".to_string()));
        }

        info!("Strategy deleted successfully: {}", strategy_id);
        Ok(())
    }

    // 🗑️ 已移除 start_strategy 方法
    // 原因：在单策略架构下，策略启动应该由引擎控制，而非独立的API调用

    /// 启动策略执行（由执行引擎调用）
    pub async fn start_strategy_execution(&self, strategy_id: Uuid) -> ApiResult<()> {
        info!("启动策略执行: {}", strategy_id);

        // 如果有执行引擎，使用执行引擎启动策略
        if let Some(engine) = &self.execution_engine {
            engine.start_strategy(strategy_id).await
                .map_err(|e| ApiError::from(e))?;
        }

        Ok(())
    }

    // 🗑️ 已移除 stop_strategy 方法
    // 原因：在单策略架构下，策略停止应该由引擎控制，而非独立的API调用

    /// 停止策略执行（由执行引擎调用）
    pub async fn stop_strategy_execution(&self, strategy_id: Uuid) -> ApiResult<()> {
        info!("停止策略执行: {}", strategy_id);

        // 如果有执行引擎，使用执行引擎停止策略
        if let Some(engine) = &self.execution_engine {
            engine.stop_strategy(strategy_id).await
                .map_err(|e| ApiError::from(e))?;
        }

        Ok(())
    }

    // 🗑️ 已移除 get_strategy_status 方法
    // 原因：在单策略架构下，策略状态应该通过引擎状态查询获得，而非独立的API调用

    /// 创建初始性能记录
    async fn create_initial_performance_record(&self, strategy_id: Uuid) -> ApiResult<()> {
        sqlx::query(
            r#"
            INSERT INTO strategy_performance (strategy_id, updated_at)
            VALUES ($1, NOW())
            "#
        )
        .bind(strategy_id)
        .execute(&self.db)
        .await
        .map_err(|e| ApiError::from(SigmaXError::Database(e.to_string())))?;

        Ok(())
    }

    /// 获取策略执行状态
    pub async fn get_execution_status(&self, strategy_id: Uuid) -> ApiResult<Option<StrategyExecutionStatus>> {
        if let Some(engine) = &self.execution_engine {
            Ok(engine.get_execution_status(strategy_id).await)
        } else {
            Ok(None)
        }
    }

    /// 获取所有运行中的策略
    pub async fn get_running_strategies(&self) -> ApiResult<Vec<Uuid>> {
        if let Some(engine) = &self.execution_engine {
            Ok(engine.get_running_strategies().await)
        } else {
            Ok(Vec::new())
        }
    }

    /// 获取策略实时性能
    pub async fn get_strategy_real_time_performance(&self, strategy_id: Uuid) -> ApiResult<Option<crate::execution_engine::StrategyPerformance>> {
        if let Some(engine) = &self.execution_engine {
            Ok(engine.get_strategy_performance(strategy_id).await)
        } else {
            Ok(None)
        }
    }

    /// 获取所有策略的执行状态
    pub async fn get_all_execution_status(&self) -> ApiResult<std::collections::HashMap<Uuid, StrategyExecutionStatus>> {
        if let Some(engine) = &self.execution_engine {
            Ok(engine.get_all_execution_status().await)
        } else {
            Ok(std::collections::HashMap::new())
        }
    }
}
