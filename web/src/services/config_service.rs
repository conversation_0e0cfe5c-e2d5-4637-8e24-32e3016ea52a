//! 配置管理服务
//!
//! 提供高级的系统配置管理功能，封装 SqlSystemConfigRepository

use std::sync::Arc;
use std::collections::HashMap;
use sqlx::PgPool;
use tracing::{info, error, warn};
use serde_json::Value as JsonValue;
use chrono::Utc;

use crate::error::{ApiError, ApiResult};
use sigmax_core::{
    SigmaXResult, SigmaXError, SystemConfigRecord, SystemConfigFilter, SystemConfigBatchResult,
    TradingConfig, RiskManagementConfig, SystemGeneralConfig, NotificationConfig,
    ApiConfig, DatabaseSystemConfig, CacheConfig, MonitoringConfig, StrategySystemConfig,
    StrategyTemplate, ExchangeSystemConfig, UserRoleConfig, ConfigCache
};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlSystemConfigRepository;
use sigmax_database::repositories::traits::{SystemConfigRepository, ConfigStatistics};

/// 配置管理服务
///
/// 提供高级的配置管理功能，包括：
/// - 基础配置 CRUD 操作
/// - 强类型配置管理
/// - 配置验证和模板管理
/// - 批量操作和命名空间管理
#[derive(Clone)]
pub struct ConfigService {
    repository: Arc<SqlSystemConfigRepository>,
    cache: Arc<ConfigCache>,
}

impl ConfigService {
    /// 创建新的配置服务
    pub fn new(database: Arc<DatabaseManager>) -> Self {
        let repository = Arc::new(SqlSystemConfigRepository::new(database));
        let cache = Arc::new(ConfigCache::with_defaults());
        Self { repository, cache }
    }

    /// 创建带自定义缓存的配置服务
    pub fn with_cache(database: Arc<DatabaseManager>, cache: Arc<ConfigCache>) -> Self {
        let repository = Arc::new(SqlSystemConfigRepository::new(database));
        Self { repository, cache }
    }

    /// 系统启动时初始化所有配置到缓存
    ///
    /// 这个方法应该在系统启动时调用，一次性加载所有系统配置到内存
    ///
    /// 🔧 TODO: 后续改造 - 只保留系统相关配置，其他配置由专门模块负责
    pub async fn initialize_system_configs(&self) -> ApiResult<()> {
        info!("🚀 Initializing system configurations to cache...");

        // 🔧 TODO: 后续改造 - 这里需要拆分配置类型
        // 预加载所有强类型配置
        let configs_to_load = vec![
            // 🏷️ 系统核心配置 - 保留
            ("system", "系统配置"),

            // 🏷️ 业务配置 - 后续移到专门模块
            ("trading", "交易配置"),        // → 移到交易模块
            ("risk", "风险管理配置"),       // → 移到风险管理模块
            ("strategy", "策略配置"),       // → 移到策略模块

            // 🏷️ 基础设施配置 - 后续移到专门模块
            ("notifications", "通知配置"),  // → 移到通知模块
            ("api", "API配置"),            // → 移到API模块
            ("database", "数据库配置"),     // → 移到数据库模块
            ("cache", "缓存配置"),         // → 移到缓存模块
            ("monitoring", "监控配置"),     // → 移到监控模块
        ];

        let mut loaded_count = 0;
        let mut failed_configs = Vec::new();

        for (config_type, description) in configs_to_load {
            match self.preload_config_type(config_type).await {
                Ok(_) => {
                    info!("✅ {} 加载成功", description);
                    loaded_count += 1;
                }
                Err(e) => {
                    warn!("⚠️  {} 加载失败: {}", description, e);
                    failed_configs.push((config_type, description));
                }
            }
        }

        // 🔧 TODO: 后续改造 - 复杂配置应该由各自模块负责预加载
        // 预加载复杂配置（策略模板、交易所配置等）
        if let Err(e) = self.preload_complex_configs().await {
            warn!("⚠️  复杂配置预加载失败: {}", e);
        }

        info!("🎉 系统配置初始化完成: {}/{} 个配置成功加载",
              loaded_count, loaded_count + failed_configs.len());

        if !failed_configs.is_empty() {
            warn!("❌ 以下配置加载失败: {:?}", failed_configs);
        }

        Ok(())
    }

    // ============================================================================
    // 基础配置管理
    // ============================================================================

    /// 获取单个配置
    pub async fn get_config(&self, key: &str) -> ApiResult<Option<SystemConfigRecord>> {
        info!("Getting config: {}", key);
        
        self.repository
            .get_config(key)
            .await
            .map_err(|e| {
                error!("Failed to get config {}: {}", key, e);
                ApiError::from(e)
            })
    }

    /// 获取多个配置
    pub async fn get_configs(&self, keys: &[String]) -> ApiResult<Vec<SystemConfigRecord>> {
        info!("Getting {} configs", keys.len());
        
        self.repository
            .get_configs(keys)
            .await
            .map_err(|e| {
                error!("Failed to get configs: {}", e);
                ApiError::from(e)
            })
    }

    /// 获取所有配置
    pub async fn get_all_configs(&self) -> ApiResult<Vec<SystemConfigRecord>> {
        info!("Getting all configs");
        
        self.repository
            .get_all_configs()
            .await
            .map_err(|e| {
                error!("Failed to get all configs: {}", e);
                ApiError::from(e)
            })
    }

    /// 按过滤器查询配置
    pub async fn find_configs(&self, filter: &SystemConfigFilter) -> ApiResult<Vec<SystemConfigRecord>> {
        info!("Finding configs with filter: {:?}", filter);
        
        self.repository
            .find_configs(filter)
            .await
            .map_err(|e| {
                error!("Failed to find configs: {}", e);
                ApiError::from(e)
            })
    }

    /// 保存配置
    pub async fn save_config(&self, config: &SystemConfigRecord) -> ApiResult<()> {
        info!("Saving config: {}", config.key);
        
        // 验证配置
        self.validate_config_record(config)?;
        
        self.repository
            .save_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save config {}: {}", config.key, e);
                ApiError::from(e)
            })?;
            
        info!("Config saved successfully: {}", config.key);
        Ok(())
    }

    /// 删除配置
    pub async fn delete_config(&self, key: &str) -> ApiResult<()> {
        info!("Deleting config: {}", key);
        
        self.repository
            .delete_config(key)
            .await
            .map_err(|e| {
                error!("Failed to delete config {}: {}", key, e);
                ApiError::from(e)
            })?;
            
        info!("Config deleted successfully: {}", key);
        Ok(())
    }

    /// 批量保存配置
    pub async fn batch_save_configs(&self, configs: &[SystemConfigRecord]) -> ApiResult<SystemConfigBatchResult> {
        info!("Batch saving {} configs", configs.len());
        
        // 验证所有配置
        for config in configs {
            self.validate_config_record(config)?;
        }
        
        self.repository
            .batch_save_configs(configs)
            .await
            .map_err(|e| {
                error!("Failed to batch save configs: {}", e);
                ApiError::from(e)
            })
    }

    // ============================================================================
    // 强类型配置管理
    // ============================================================================

    /// 获取交易配置
    pub async fn get_trading_config(&self) -> ApiResult<TradingConfig> {
        info!("Getting trading config");
        
        self.repository
            .get_trading_config()
            .await
            .map_err(|e| {
                error!("Failed to get trading config: {}", e);
                ApiError::from(e)
            })
    }

    /// 保存交易配置
    pub async fn save_trading_config(&self, config: &TradingConfig) -> ApiResult<()> {
        info!("Saving trading config");
        
        // 验证交易配置
        self.validate_trading_config(config)?;
        
        self.repository
            .save_trading_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save trading config: {}", e);
                ApiError::from(e)
            })?;
            
        info!("Trading config saved successfully");
        Ok(())
    }

    /// 获取风险管理配置
    pub async fn get_risk_config(&self) -> ApiResult<RiskManagementConfig> {
        info!("Getting risk management config");
        
        self.repository
            .get_risk_config()
            .await
            .map_err(|e| {
                error!("Failed to get risk config: {}", e);
                ApiError::from(e)
            })
    }

    /// 保存风险管理配置
    pub async fn save_risk_config(&self, config: &RiskManagementConfig) -> ApiResult<()> {
        info!("Saving risk management config");
        
        // 验证风险配置
        self.validate_risk_config(config)?;
        
        self.repository
            .save_risk_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save risk config: {}", e);
                ApiError::from(e)
            })?;
            
        info!("Risk config saved successfully");
        Ok(())
    }

    /// 获取系统配置（优先从缓存读取）
    pub async fn get_system_config(&self) -> ApiResult<SystemGeneralConfig> {
        info!("Getting system config from cache");

        self.cache.get_or_load_system_config(|| {
            self.repository.get_system_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get system config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存系统配置
    pub async fn save_system_config(&self, config: &SystemGeneralConfig) -> ApiResult<()> {
        info!("Saving system config");
        
        // 验证系统配置
        self.validate_system_config(config)?;
        
        self.repository
            .save_system_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save system config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("system").await;
        info!("System config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取通知配置（优先从缓存读取）
    pub async fn get_notification_config(&self) -> ApiResult<NotificationConfig> {
        info!("Getting notification config from cache");

        self.cache.get_or_load_notification_config(|| {
            self.repository.get_notification_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get notification config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存通知配置
    pub async fn save_notification_config(&self, config: &NotificationConfig) -> ApiResult<()> {
        info!("Saving notification config");
        
        // 验证通知配置
        self.validate_notification_config(config)?;
        
        self.repository
            .save_notification_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save notification config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("notifications").await;
        info!("Notification config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取API配置（优先从缓存读取）
    pub async fn get_api_config(&self) -> ApiResult<ApiConfig> {
        info!("Getting API config from cache");

        self.cache.get_or_load_api_config(|| {
            self.repository.get_api_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get API config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存API配置
    pub async fn save_api_config(&self, config: &ApiConfig) -> ApiResult<()> {
        info!("Saving API config");

        self.repository
            .save_api_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save API config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("api").await;
        info!("API config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取数据库配置（优先从缓存读取）
    pub async fn get_database_config(&self) -> ApiResult<DatabaseSystemConfig> {
        info!("Getting database config from cache");

        self.cache.get_or_load_database_config(|| {
            self.repository.get_database_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get database config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存数据库配置
    pub async fn save_database_config(&self, config: &DatabaseSystemConfig) -> ApiResult<()> {
        info!("Saving database config");

        self.repository
            .save_database_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save database config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("database").await;
        info!("Database config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取缓存配置（优先从缓存读取）
    pub async fn get_cache_config(&self) -> ApiResult<CacheConfig> {
        info!("Getting cache config from cache");

        self.cache.get_or_load_cache_config(|| {
            self.repository.get_cache_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get cache config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存缓存配置
    pub async fn save_cache_config(&self, config: &CacheConfig) -> ApiResult<()> {
        info!("Saving cache config");

        self.repository
            .save_cache_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save cache config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("cache").await;
        info!("Cache config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取监控配置（优先从缓存读取）
    pub async fn get_monitoring_config(&self) -> ApiResult<MonitoringConfig> {
        info!("Getting monitoring config from cache");

        self.cache.get_or_load_monitoring_config(|| {
            self.repository.get_monitoring_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get monitoring config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存监控配置
    pub async fn save_monitoring_config(&self, config: &MonitoringConfig) -> ApiResult<()> {
        info!("Saving monitoring config");

        self.repository
            .save_monitoring_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save monitoring config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("monitoring").await;
        info!("Monitoring config saved successfully and cache cleared");
        Ok(())
    }

    /// 获取策略配置（优先从缓存读取）
    pub async fn get_strategy_config(&self) -> ApiResult<StrategySystemConfig> {
        info!("Getting strategy config from cache");

        self.cache.get_or_load_strategy_config(|| {
            self.repository.get_strategy_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get strategy config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存策略配置
    pub async fn save_strategy_config(&self, config: &StrategySystemConfig) -> ApiResult<()> {
        info!("Saving strategy config");

        self.repository
            .save_strategy_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save strategy config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("strategy").await;
        info!("Strategy config saved successfully and cache cleared");
        Ok(())
    }

    // ============================================================================
    // 配置预加载辅助方法
    // ============================================================================

    /// 预加载指定类型的配置
    ///
    /// 🔧 TODO: 后续改造 - 这个方法需要重构，只处理系统相关配置
    async fn preload_config_type(&self, config_type: &str) -> ApiResult<()> {
        match config_type {
            // 🏷️ 系统核心配置 - 保留
            "system" => {
                self.cache.get_or_load_system_config(|| {
                    self.repository.get_system_config()
                }).await.map_err(ApiError::from)?;
            }

            // 🏷️ 业务配置 - 后续移到专门模块
            "trading" => {
                self.cache.get_or_load_trading_config(|| {
                    self.repository.get_trading_config()
                }).await.map_err(ApiError::from)?;
            }
            "risk" => {
                self.cache.get_or_load_risk_config(|| {
                    self.repository.get_risk_config()
                }).await.map_err(ApiError::from)?;
            }

            // 🏷️ 基础设施配置 - 后续移到专门模块
            "notifications" => {
                self.cache.get_or_load_notification_config(|| {
                    self.repository.get_notification_config()
                }).await.map_err(ApiError::from)?;
            }
            "api" => {
                self.cache.get_or_load_api_config(|| {
                    self.repository.get_api_config()
                }).await.map_err(ApiError::from)?;
            }
            "database" => {
                self.cache.get_or_load_database_config(|| {
                    self.repository.get_database_config()
                }).await.map_err(ApiError::from)?;
            }
            "cache" => {
                self.cache.get_or_load_cache_config(|| {
                    self.repository.get_cache_config()
                }).await.map_err(ApiError::from)?;
            }
            "monitoring" => {
                self.cache.get_or_load_monitoring_config(|| {
                    self.repository.get_monitoring_config()
                }).await.map_err(ApiError::from)?;
            }
            "strategy" => {
                self.cache.get_or_load_strategy_config(|| {
                    self.repository.get_strategy_config()
                }).await.map_err(ApiError::from)?;
            }
            _ => return Err(ApiError::BadRequest(format!("Unknown config type: {}", config_type))),
        }
        Ok(())
    }

    /// 预加载复杂配置（策略模板、交易所配置等）
    ///
    /// 🔧 TODO: 后续改造 - 这些复杂配置应该由各自模块负责
    /// - 策略模板 → 策略模块负责
    /// - 交易所配置 → 交易所模块负责
    async fn preload_complex_configs(&self) -> ApiResult<()> {
        // 🏷️ 策略相关配置 - 后续移到策略模块
        // 预加载所有策略模板
        match self.repository.get_all_strategy_templates().await {
            Ok(templates) => {
                for (template_name, template) in templates {
                    let _ = self.cache.get_or_load_strategy_template(&template_name, || async {
                        Ok(Some(template.clone()))
                    }).await;
                }
                info!("✅ 策略模板预加载完成");
            }
            Err(e) => warn!("⚠️  策略模板预加载失败: {}", e),
        }

        // 🏷️ 交易所相关配置 - 后续移到交易所模块
        // 预加载所有交易所配置
        match self.repository.get_all_exchange_configs().await {
            Ok(configs) => {
                for (exchange_name, config) in configs {
                    let _ = self.cache.get_or_load_exchange_config(&exchange_name, || async {
                        Ok(Some(config.clone()))
                    }).await;
                }
                info!("✅ 交易所配置预加载完成");
            }
            Err(e) => warn!("⚠️  交易所配置预加载失败: {}", e),
        }

        Ok(())
    }

    // ============================================================================
    // 组件访问方法
    // ============================================================================

    /// 获取配置缓存实例
    pub fn get_cache(&self) -> Arc<ConfigCache> {
        self.cache.clone()
    }

    /// 获取配置仓储实例
    pub fn get_repository(&self) -> Arc<SqlSystemConfigRepository> {
        self.repository.clone()
    }

    // ============================================================================
    // 配置验证
    // ============================================================================

    /// 验证配置记录
    fn validate_config_record(&self, config: &SystemConfigRecord) -> ApiResult<()> {
        if config.key.is_empty() {
            return Err(ApiError::BadRequest("配置键不能为空".to_string()));
        }

        if config.key.len() > 100 {
            return Err(ApiError::BadRequest("配置键长度不能超过100个字符".to_string()));
        }

        // 验证JSON值格式
        if config.value.is_null() {
            return Err(ApiError::BadRequest("配置值不能为null".to_string()));
        }

        Ok(())
    }

    /// 验证交易配置
    fn validate_trading_config(&self, config: &TradingConfig) -> ApiResult<()> {
        if config.max_orders_per_strategy == 0 {
            return Err(ApiError::BadRequest("每个策略的最大订单数必须大于0".to_string()));
        }

        if config.default_order_timeout == 0 {
            return Err(ApiError::BadRequest("订单超时时间必须大于0".to_string()));
        }

        Ok(())
    }

    /// 验证风险配置
    fn validate_risk_config(&self, config: &RiskManagementConfig) -> ApiResult<()> {
        // 使用新的统一验证系统
        config.validate_complete().map_err(|e| {
            error!("Risk config validation failed: {}", e);
            ApiError::BadRequest(format!("风险配置验证失败: {}", e))
        })?;

        Ok(())
    }

    /// 验证系统配置
    fn validate_system_config(&self, config: &SystemGeneralConfig) -> ApiResult<()> {
        if config.max_concurrent_strategies == 0 {
            return Err(ApiError::BadRequest("最大并发策略数必须大于0".to_string()));
        }

        if config.data_retention_days == 0 {
            return Err(ApiError::BadRequest("数据保留天数必须大于0".to_string()));
        }

        Ok(())
    }

    /// 验证通知配置
    fn validate_notification_config(&self, config: &NotificationConfig) -> ApiResult<()> {
        if config.email_enabled {
            if let Some(ref webhook_url) = config.webhook_url {
                if !webhook_url.starts_with("http") {
                    return Err(ApiError::BadRequest("Webhook URL格式无效".to_string()));
                }
            }
        }

        Ok(())
    }

    // ============================================================================
    // 高级配置管理功能
    // ============================================================================

    /// 获取配置命名空间列表
    pub async fn get_namespaces(&self) -> ApiResult<Vec<String>> {
        info!("Getting all namespaces");

        self.repository
            .get_all_namespaces()
            .await
            .map_err(|e| {
                error!("Failed to get namespaces: {}", e);
                ApiError::from(e)
            })
    }

    /// 按命名空间获取配置
    pub async fn get_configs_by_namespace(&self, namespace: &str) -> ApiResult<Vec<SystemConfigRecord>> {
        info!("Getting configs for namespace: {}", namespace);

        let filter = SystemConfigFilter {
            namespace: Some(namespace.to_string()),
            ..Default::default()
        };

        self.find_configs(&filter).await
    }

    /// 删除命名空间下的所有配置
    pub async fn delete_namespace(&self, namespace: &str) -> ApiResult<SystemConfigBatchResult> {
        info!("Deleting namespace: {}", namespace);

        self.repository
            .delete_configs_by_namespace(namespace)
            .await
            .map_err(|e| {
                error!("Failed to delete namespace {}: {}", namespace, e);
                ApiError::from(e)
            })
    }

    /// 获取配置统计信息
    pub async fn get_config_statistics(&self) -> ApiResult<ConfigStatistics> {
        info!("Getting config statistics");

        self.repository
            .get_config_statistics()
            .await
            .map_err(|e| {
                error!("Failed to get config statistics: {}", e);
                ApiError::from(e)
            })
    }

    /// 导出配置
    pub async fn export_configs(&self, namespace: Option<&str>) -> ApiResult<JsonValue> {
        info!("Exporting configs for namespace: {:?}", namespace);

        let configs = match namespace {
            Some(ns) => self.get_configs_by_namespace(ns).await?,
            None => self.get_all_configs().await?,
        };

        let mut export_data = serde_json::Map::new();
        export_data.insert("exported_at".to_string(), JsonValue::String(Utc::now().to_rfc3339()));
        export_data.insert("namespace".to_string(), JsonValue::String(namespace.unwrap_or("all").to_string()));
        export_data.insert("total_configs".to_string(), JsonValue::Number(configs.len().into()));

        let mut config_map = serde_json::Map::new();
        for config in configs {
            config_map.insert(config.key, config.value);
        }
        export_data.insert("configs".to_string(), JsonValue::Object(config_map));

        Ok(JsonValue::Object(export_data))
    }

    /// 导入配置
    pub async fn import_configs(&self, data: &JsonValue, overwrite: bool) -> ApiResult<SystemConfigBatchResult> {
        info!("Importing configs, overwrite: {}", overwrite);

        let configs_obj = data.get("configs")
            .and_then(|v| v.as_object())
            .ok_or_else(|| ApiError::BadRequest("导入数据格式无效：缺少configs字段".to_string()))?;

        let mut configs = Vec::new();
        let now = Utc::now();

        for (key, value) in configs_obj {
            // 如果不覆盖，检查配置是否已存在
            if !overwrite {
                if let Ok(Some(_)) = self.get_config(key).await {
                    warn!("Config {} already exists, skipping", key);
                    continue;
                }
            }

            let config = SystemConfigRecord {
                id: 0, // 将由数据库生成
                key: key.clone(),
                value: value.clone(),
                description: Some(format!("Imported at {}", now.format("%Y-%m-%d %H:%M:%S"))),
                is_encrypted: false,
                created_at: now,
                updated_at: now,
            };

            configs.push(config);
        }

        if configs.is_empty() {
            return Ok(SystemConfigBatchResult::new());
        }

        self.batch_save_configs(&configs).await
    }

    /// 获取配置模板
    pub async fn get_config_templates(&self) -> ApiResult<HashMap<String, JsonValue>> {
        info!("Getting config templates");

        let mut templates = HashMap::new();

        // 交易配置模板
        templates.insert("trading".to_string(), serde_json::json!({
            "max_orders_per_strategy": 10,
            "order_timeout_seconds": 300,
            "default_slippage_tolerance": 0.001,
            "enable_partial_fills": true,
            "retry_failed_orders": true,
            "max_retry_attempts": 3
        }));

        // 风险管理配置模板
        templates.insert("risk".to_string(), serde_json::json!({
            "max_position_size": 10000.0,
            "max_daily_loss": 1000.0,
            "max_drawdown": 5000.0,
            "position_size_limit": 0.1,
            "stop_loss_enabled": true,
            "take_profit_enabled": true
        }));

        // 系统配置模板
        templates.insert("system".to_string(), serde_json::json!({
            "maintenance_mode": false,
            "max_concurrent_strategies": 5,
            "data_retention_days": 90,
            "backup_enabled": true,
            "log_level": "INFO"
        }));

        // 通知配置模板
        templates.insert("notifications".to_string(), serde_json::json!({
            "email_enabled": false,
            "webhook_url": null,
            "slack_webhook": null,
            "telegram_bot_token": null,
            "telegram_chat_id": null
        }));

        Ok(templates)
    }

    /// 重置配置到默认值
    pub async fn reset_config_to_default(&self, namespace: &str) -> ApiResult<()> {
        info!("Resetting config to default for namespace: {}", namespace);

        let templates = self.get_config_templates().await?;

        if let Some(template) = templates.get(namespace) {
            match namespace {
                "trading" => {
                    let config: TradingConfig = serde_json::from_value(template.clone())
                        .map_err(|e| ApiError::BadRequest(format!("Invalid trading config template: {}", e)))?;
                    self.save_trading_config(&config).await?;
                },
                "risk" => {
                    let config: RiskManagementConfig = serde_json::from_value(template.clone())
                        .map_err(|e| ApiError::BadRequest(format!("Invalid risk config template: {}", e)))?;
                    self.save_risk_config(&config).await?;
                },
                "system" => {
                    let config: SystemGeneralConfig = serde_json::from_value(template.clone())
                        .map_err(|e| ApiError::BadRequest(format!("Invalid system config template: {}", e)))?;
                    self.save_system_config(&config).await?;
                },
                "notifications" => {
                    let config: NotificationConfig = serde_json::from_value(template.clone())
                        .map_err(|e| ApiError::BadRequest(format!("Invalid notification config template: {}", e)))?;
                    self.save_notification_config(&config).await?;
                },
                _ => {
                    return Err(ApiError::BadRequest(format!("Unknown namespace: {}", namespace)));
                }
            }

            info!("Config reset to default for namespace: {}", namespace);
            Ok(())
        } else {
            Err(ApiError::BadRequest(format!("No template found for namespace: {}", namespace)))
        }
    }
}
