//! 交易配置服务
//!
//! 专门负责交易相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{TradingConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlTradingRepository;
use sigmax_database::repositories::traits::TradingRepository;

/// 交易配置服务
///
/// 负责管理交易相关配置，包括：
/// - 订单限制和超时设置
/// - 持仓大小限制
/// - 滑点控制
/// - 交易间隔设置
#[derive(Clone)]
pub struct TradingConfigService {
    /// 交易配置仓储
    repository: Arc<SqlTradingRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl TradingConfigService {
    /// 创建新的交易配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlTradingRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取交易配置
    pub async fn get_config(&self) -> ApiResult<TradingConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("TradingConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_trading_config(|| {
            self.repository.get_trading_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get trading config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存交易配置
    pub async fn save_config(&self, config: &TradingConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("TradingConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_trading_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save trading config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("trading").await;
        info!("Trading config saved successfully and cache cleared");
        
        Ok(())
    }

    /// 重置交易配置为默认值
    pub async fn reset_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("TradingConfigService not initialized".to_string()));
        }

        // 重置到默认配置
        self.repository
            .reset_trading_config()
            .await
            .map_err(|e| {
                error!("Failed to reset trading config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("trading").await;
        info!("Trading config reset to default and cache cleared");

        Ok(())
    }

    /// 验证交易配置
    fn validate_config(&self, config: &TradingConfig) -> ApiResult<()> {
        // 验证最大订单数
        if config.max_orders_per_strategy == 0 {
            return Err(ApiError::BadRequest("Max orders per strategy must be greater than 0".to_string()));
        }

        if config.max_orders_per_strategy > 1000 {
            return Err(ApiError::BadRequest("Max orders per strategy cannot exceed 1000".to_string()));
        }

        // 验证最大持仓金额
        if config.max_position_size <= rust_decimal::Decimal::ZERO {
            return Err(ApiError::BadRequest("Max position size must be greater than 0".to_string()));
        }

        // 验证订单超时时间
        if config.default_order_timeout == 0 {
            return Err(ApiError::BadRequest("Default order timeout must be greater than 0".to_string()));
        }

        if config.default_order_timeout > 86400 { // 24小时
            return Err(ApiError::BadRequest("Default order timeout cannot exceed 24 hours".to_string()));
        }

        // 验证最小订单间隔
        if config.min_order_interval > 3600 { // 1小时
            return Err(ApiError::BadRequest("Min order interval cannot exceed 1 hour".to_string()));
        }

        // 验证最大滑点
        if config.max_slippage_percent < 0.0 || config.max_slippage_percent > 10.0 {
            return Err(ApiError::BadRequest("Max slippage percent must be between 0 and 10".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for TradingConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing TradingConfigService...");

        // 预加载交易配置到缓存
        match self.cache.get_or_load_trading_config(|| {
            self.repository.get_trading_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ TradingConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ TradingConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading TradingConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("trading").await;
        
        // 重新加载配置
        self.cache.get_or_load_trading_config(|| {
            self.repository.get_trading_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload trading config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ TradingConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "TradingConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("trading").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
