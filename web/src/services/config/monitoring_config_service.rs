//! 监控配置服务
//!
//! 专门负责监控相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{MonitoringConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlMonitoringRepository;
use sigmax_database::repositories::traits::MonitoringRepository;

/// 监控配置服务
///
/// 负责管理监控相关配置，包括：
/// - 系统性能监控配置
/// - 告警阈值和规则配置
/// - 通知渠道配置
/// - 健康检查配置
/// - 性能基准配置
#[derive(Clone)]
pub struct MonitoringConfigService {
    /// 监控配置仓储 - 使用专门的监控仓储
    repository: Arc<SqlMonitoringRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl MonitoringConfigService {
    /// 创建新的监控配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlMonitoringRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取监控配置
    pub async fn get_config(&self) -> ApiResult<MonitoringConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_monitoring_config(|| {
            self.repository.get_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get monitoring config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存监控配置
    pub async fn save_config(&self, config: &MonitoringConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        self.validate_config(config)?;

        self.repository
            .save_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save monitoring config: {}", e);
                ApiError::from(e)
            })?;

        self.cache.clear_config_cache("monitoring").await;
        info!("Monitoring config saved successfully and cache cleared");

        Ok(())
    }

    /// 更新监控配置
    pub async fn update_config(&self, id: uuid::Uuid, config: &MonitoringConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 更新到数据库
        self.repository
            .update_config(id, config)
            .await
            .map_err(|e| {
                error!("Failed to update monitoring config {}: {}", id, e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("monitoring").await;
        info!("Monitoring config {} updated successfully and cache cleared", id);

        Ok(())
    }

    /// 删除监控配置
    pub async fn delete_config(&self, id: uuid::Uuid) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        self.repository
            .delete_config(id)
            .await
            .map_err(|e| {
                error!("Failed to delete monitoring config {}: {}", id, e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("monitoring").await;
        info!("Monitoring config {} deleted successfully and cache cleared", id);

        Ok(())
    }

    /// 重置为默认配置
    pub async fn reset_to_default(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        self.repository
            .reset_to_default()
            .await
            .map_err(|e| {
                error!("Failed to reset monitoring config to default: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("monitoring").await;
        info!("Monitoring config reset to default successfully and cache cleared");

        Ok(())
    }

    /// 获取配置统计信息
    pub async fn get_statistics(&self) -> ApiResult<serde_json::Value> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("MonitoringConfigService not initialized".to_string()));
        }

        let stats = self.repository
            .get_config_statistics()
            .await
            .map_err(|e| {
                error!("Failed to get monitoring config statistics: {}", e);
                ApiError::from(e)
            })?;

        Ok(serde_json::to_value(stats).unwrap_or_default())
    }

    /// 验证监控配置
    fn validate_config(&self, config: &MonitoringConfig) -> ApiResult<()> {
        // 使用新的统一验证系统
        config.validate_complete().map_err(|e| {
            error!("Monitoring config validation failed: {}", e);
            ApiError::BadRequest(format!("监控配置验证失败: {}", e))
        })?;

        // 额外的业务逻辑验证
        if config.name.trim().is_empty() {
            return Err(ApiError::BadRequest("配置名称不能为空".to_string()));
        }

        if config.name.len() > 100 {
            return Err(ApiError::BadRequest("配置名称长度不能超过100个字符".to_string()));
        }

        // 验证告警规则数量限制
        if config.alert_rules.len() > 100 {
            return Err(ApiError::BadRequest("告警规则数量不能超过100个".to_string()));
        }

        // 验证通知渠道数量限制
        if config.notification_channels.len() > 50 {
            return Err(ApiError::BadRequest("通知渠道数量不能超过50个".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for MonitoringConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing MonitoringConfigService...");

        match self.cache.get_or_load_monitoring_config(|| {
            self.repository.get_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ MonitoringConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ MonitoringConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading MonitoringConfigService...");
        
        self.cache.clear_config_cache("monitoring").await;
        
        self.cache.get_or_load_monitoring_config(|| {
            self.repository.get_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload monitoring config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ MonitoringConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "MonitoringConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("monitoring").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0,
            last_updated: None,
            is_initialized: self.is_initialized(),
        })
    }
}
