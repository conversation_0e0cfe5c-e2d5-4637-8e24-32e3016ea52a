//! API配置服务
//!
//! 专门负责API相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{ApiConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlApiRepository;
use sigmax_database::repositories::traits::ApiRepository;

/// API配置服务
///
/// 负责管理API相关配置，包括：
/// - 限流设置
/// - 认证配置
/// - 路由设置
/// - 超时和重试配置
#[derive(Clone)]
pub struct ApiConfigService {
    /// API仓储 - 使用专门的API仓储
    repository: Arc<SqlApiRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl ApiConfigService {
    /// 创建新的API配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlApiRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取API配置
    pub async fn get_config(&self) -> ApiResult<ApiConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("ApiConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_api_config(|| {
            self.repository.get_api_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get API config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存API配置
    pub async fn save_config(&self, config: &ApiConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("ApiConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_api_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save API config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("api").await;
        info!("API config saved successfully and cache cleared");
        
        Ok(())
    }

    /// 重置API配置为默认值
    pub async fn reset_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("ApiConfigService not initialized".to_string()));
        }

        // 重置到默认配置
        self.repository
            .reset_api_config()
            .await
            .map_err(|e| {
                error!("Failed to reset API config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("api").await;
        info!("API config reset to default and cache cleared");

        Ok(())
    }

    /// 验证API配置
    fn validate_config(&self, config: &ApiConfig) -> ApiResult<()> {
        // 验证限流设置
        if config.rate_limit_per_minute == 0 {
            return Err(ApiError::BadRequest("Rate limit per minute must be greater than 0".to_string()));
        }

        if config.rate_limit_per_minute > 100000 {
            return Err(ApiError::BadRequest("Rate limit per minute cannot exceed 100000".to_string()));
        }

        // 验证最大请求大小
        if config.max_request_size == 0 {
            return Err(ApiError::BadRequest("Max request size must be greater than 0".to_string()));
        }

        if config.max_request_size > 100 * 1024 * 1024 { // 100MB
            return Err(ApiError::BadRequest("Max request size cannot exceed 100MB".to_string()));
        }

        // 验证超时设置
        if config.timeout_seconds == 0 {
            return Err(ApiError::BadRequest("Timeout seconds must be greater than 0".to_string()));
        }

        if config.timeout_seconds > 300 { // 5分钟
            return Err(ApiError::BadRequest("Timeout seconds cannot exceed 5 minutes".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for ApiConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing ApiConfigService...");

        // 预加载API配置到缓存
        match self.cache.get_or_load_api_config(|| {
            self.repository.get_api_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ ApiConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ ApiConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading ApiConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("api").await;
        
        // 重新加载配置
        self.cache.get_or_load_api_config(|| {
            self.repository.get_api_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload API config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ ApiConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "ApiConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("api").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
