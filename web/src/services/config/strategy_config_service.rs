//! 策略配置服务
//!
//! 专门负责策略相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{StrategySystemConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlStrategyRepository;
use sigmax_database::repositories::traits::StrategyRepository;

/// 策略配置服务
///
/// 负责管理策略相关配置，包括：
/// - 策略执行参数
/// - 策略优化设置
/// - 策略风险控制
/// - 策略性能监控
#[derive(Clone)]
pub struct StrategyConfigService {
    /// 策略仓储 - 使用专门的策略仓储
    repository: Arc<SqlStrategyRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl StrategyConfigService {
    /// 创建新的策略配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlStrategyRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取策略配置
    pub async fn get_config(&self) -> ApiResult<StrategySystemConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("StrategyConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_strategy_config(|| {
            self.repository.get_strategy_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get strategy config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存策略配置
    pub async fn save_config(&self, config: &StrategySystemConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("StrategyConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_strategy_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save strategy config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("strategy").await;
        info!("Strategy config saved successfully and cache cleared");
        
        Ok(())
    }

    /// 重置策略配置为默认值
    pub async fn reset_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("StrategyConfigService not initialized".to_string()));
        }

        // 重置到默认配置
        self.repository
            .reset_strategy_config()
            .await
            .map_err(|e| {
                error!("Failed to reset strategy config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("strategy").await;
        info!("Strategy config reset to default and cache cleared");

        Ok(())
    }

    /// 验证策略配置
    fn validate_config(&self, config: &StrategySystemConfig) -> ApiResult<()> {
        // 验证默认网格层数
        if config.default_grid_levels == 0 {
            return Err(ApiError::BadRequest("Default grid levels must be greater than 0".to_string()));
        }

        if config.default_grid_levels > 100 {
            return Err(ApiError::BadRequest("Default grid levels cannot exceed 100".to_string()));
        }

        // 验证默认网格间距
        if config.default_grid_spacing <= 0.0 {
            return Err(ApiError::BadRequest("Default grid spacing must be greater than 0".to_string()));
        }

        if config.default_grid_spacing > 50.0 { // 50%
            return Err(ApiError::BadRequest("Default grid spacing cannot exceed 50%".to_string()));
        }

        // 验证最大活跃订单数
        if config.max_active_orders == 0 {
            return Err(ApiError::BadRequest("Max active orders must be greater than 0".to_string()));
        }

        if config.max_active_orders > 1000 {
            return Err(ApiError::BadRequest("Max active orders cannot exceed 1000".to_string()));
        }

        // 验证重平衡间隔
        if config.rebalance_interval == 0 {
            return Err(ApiError::BadRequest("Rebalance interval must be greater than 0".to_string()));
        }

        if config.rebalance_interval > 86400 { // 24小时
            return Err(ApiError::BadRequest("Rebalance interval cannot exceed 24 hours".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for StrategyConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing StrategyConfigService...");

        // 预加载策略配置到缓存
        match self.cache.get_or_load_strategy_config(|| {
            self.repository.get_strategy_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ StrategyConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ StrategyConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading StrategyConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("strategy").await;
        
        // 重新加载配置
        self.cache.get_or_load_strategy_config(|| {
            self.repository.get_strategy_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload strategy config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ StrategyConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "StrategyConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("strategy").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
