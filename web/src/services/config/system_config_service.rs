//! 系统配置服务
//!
//! 专门负责系统核心配置的管理，包括系统名称、版本、环境等核心信息

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{SystemGeneralConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlSystemConfigRepository;
use sigmax_database::repositories::traits::SystemConfigRepository;

/// 系统配置服务
///
/// 负责管理系统核心配置，包括：
/// - 系统名称和版本信息
/// - 环境配置（开发/测试/生产）
/// - 系统级别的开关和参数
#[derive(Clone)]
pub struct SystemConfigService {
    /// 配置仓储
    repository: Arc<SqlSystemConfigRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl SystemConfigService {
    /// 创建新的系统配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlSystemConfigRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取系统配置
    ///
    /// 优先从缓存获取，缓存未命中时从数据库加载
    pub async fn get_config(&self) -> ApiResult<SystemGeneralConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("SystemConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_system_config(|| {
            self.repository.get_system_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get system config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存系统配置
    pub async fn save_config(&self, config: &SystemGeneralConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("SystemConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_system_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save system config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存，强制下次重新加载
        self.cache.clear_config_cache("system").await;
        info!("System config saved successfully and cache cleared");
        
        Ok(())
    }

    /// 验证系统配置
    fn validate_config(&self, config: &SystemGeneralConfig) -> ApiResult<()> {
        // 验证最大并发策略数
        if config.max_concurrent_strategies == 0 {
            return Err(ApiError::BadRequest("Max concurrent strategies must be greater than 0".to_string()));
        }

        if config.max_concurrent_strategies > 1000 {
            return Err(ApiError::BadRequest("Max concurrent strategies cannot exceed 1000".to_string()));
        }

        // 验证数据保留天数
        if config.data_retention_days == 0 {
            return Err(ApiError::BadRequest("Data retention days must be greater than 0".to_string()));
        }

        if config.data_retention_days > 3650 { // 10年
            return Err(ApiError::BadRequest("Data retention days cannot exceed 10 years".to_string()));
        }

        // 验证日志级别
        let valid_log_levels = ["TRACE", "DEBUG", "INFO", "WARN", "ERROR"];
        if !valid_log_levels.contains(&config.log_level.as_str()) {
            return Err(ApiError::BadRequest(
                format!("Invalid log level: {}. Must be one of: {:?}",
                       config.log_level, valid_log_levels)
            ));
        }

        Ok(())
    }

    /// 获取配置缓存实例
    pub fn get_cache(&self) -> Arc<ConfigCache> {
        self.cache.clone()
    }

    /// 获取配置仓储实例
    pub fn get_repository(&self) -> Arc<SqlSystemConfigRepository> {
        self.repository.clone()
    }
}

#[async_trait]
impl ConfigService for SystemConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing SystemConfigService...");

        // 预加载系统配置到缓存
        match self.cache.get_or_load_system_config(|| {
            self.repository.get_system_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ SystemConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ SystemConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading SystemConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("system").await;
        
        // 重新加载配置
        self.cache.get_or_load_system_config(|| {
            self.repository.get_system_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload system config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ SystemConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "SystemConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("system").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1, // 系统配置只有一个配置对象
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
