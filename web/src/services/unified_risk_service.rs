//! 统一风控服务
//!
//! 提供统一风控管理的业务逻辑层，封装Repository操作

use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tracing::{info, debug, warn};

use crate::error::{ApiError, ApiResult};
use sigmax_core::SigmaXResult;
use sigmax_risk::{SqlUnifiedRiskRepository, UnifiedRiskRepository, unified_engine::{UnifiedRiskEngine, RiskCheckResult, RuleExecutionContext}};

/// 统一风控服务
#[derive(Clone)]
pub struct UnifiedRiskService {
    repository: Arc<SqlUnifiedRiskRepository>,
    engine: Arc<UnifiedRiskEngine>,
}

impl UnifiedRiskService {
    /// 创建新的统一风控服务
    pub fn new(repository: Arc<SqlUnifiedRiskRepository>, engine: Arc<UnifiedRiskEngine>) -> Self {
        Self { repository, engine }
    }

    pub async fn check_risk(&self, context: &RuleExecutionContext) -> ApiResult<RiskCheckResult> {
        info!("执行风控检查");

        let result = if let Some(order) = &context.order {
            // 如果上下文中包含订单信息，执行订单风险检查
            self.engine.check_order_risk(order, context.strategy_type.as_deref()).await
                .map_err(|e| ApiError::Internal(format!("订单风控检查失败: {}", e)))?
        } else if let Some(balances) = &context.balances {
            // 如果上下文中包含持仓信息，执行持仓风险检查
            self.engine.check_position_risk(balances, context.strategy_type.as_deref()).await
                .map_err(|e| ApiError::Internal(format!("持仓风控检查失败: {}", e)))?
        } else {
            // 如果上下文中没有订单或持仓信息，返回默认通过结果
            warn!("风控检查上下文不包含订单或持仓信息，默认通过");
            RiskCheckResult::passed()
        };
        Ok(result)
    }

    /// 获取规则列表
    pub async fn get_rules(&self, query: RuleListQuery) -> ApiResult<RuleListResponse> {
        info!("获取风控规则列表: {:?}", query);

        let page = query.page.unwrap_or(1);
        let per_page = query.per_page.unwrap_or(20);

        // 构建过滤器
        let mut filter = sigmax_risk::unified_engine::RuleFilter::default();
        if let Some(category) = &query.category {
            filter.category = Some(category.clone());
        }
        if let Some(rule_type) = &query.rule_type {
            filter.rule_type = Some(rule_type.clone());
        }
        if let Some(enabled) = query.enabled {
            filter.enabled = Some(enabled);
        }
        if let Some(strategy_type) = &query.strategy_type {
            filter.strategy_type = Some(strategy_type.clone());
        }

        // 查询规则
        let rules = self.repository.get_rules_with_filter(&filter, page, per_page).await
            .map_err(|e| ApiError::Internal(format!("查询规则失败: {}", e)))?;

        let total_count = self.repository.count_rules_with_filter(&filter).await
            .map_err(|e| ApiError::Internal(format!("统计规则数量失败: {}", e)))?;

        let total_pages = (total_count + per_page - 1) / per_page;

        // 转换为响应格式
        let rule_responses: Vec<RuleResponse> = rules.into_iter()
            .map(|rule| self.convert_rule_to_response(rule))
            .collect();

        Ok(RuleListResponse {
            rules: rule_responses,
            total_count,
            page,
            per_page,
            total_pages,
        })
    }

    /// 获取单个规则详情
    pub async fn get_rule(&self, rule_id: Uuid) -> ApiResult<RuleResponse> {
        info!("获取风控规则详情: {}", rule_id);

        let rule = self.repository.get_rule(rule_id).await
            .map_err(|e| ApiError::Internal(format!("查询规则失败: {}", e)))?
            .ok_or_else(|| ApiError::NotFound("风控规则不存在".to_string()))?;

        Ok(self.convert_rule_to_response(rule))
    }

    /// 更新规则
    pub async fn update_rule(&self, rule_id: Uuid, request: UpdateRuleRequest) -> ApiResult<RuleResponse> {
        info!("更新风控规则: {}", rule_id);

        // 获取现有规则
        let mut rule = self.repository.get_rule(rule_id).await
            .map_err(|e| ApiError::Internal(format!("查询规则失败: {}", e)))?
            .ok_or_else(|| ApiError::NotFound("风控规则不存在".to_string()))?;

        // 更新字段
        if let Some(name) = request.name {
            rule.name = name;
        }
        if let Some(description) = request.description {
            rule.description = Some(description);
        }
        if let Some(category) = request.category {
            rule.category = category;
        }
        if let Some(rule_type) = request.rule_type {
            rule.rule_type = rule_type;
        }
        if let Some(parameters) = request.parameters {
            rule.parameters = parameters;
        }
        if let Some(conditions) = request.conditions {
            rule.conditions = Some(conditions);
        }
        if let Some(enabled) = request.enabled {
            rule.enabled = enabled;
        }
        if let Some(priority) = request.priority {
            rule.priority = priority;
        }
        if let Some(strategy_type) = request.strategy_type {
            rule.strategy_type = Some(strategy_type);
        }
        if let Some(trading_pairs) = request.trading_pairs {
            rule.trading_pairs = trading_pairs;
        }

        rule.updated_at = Utc::now();

        // 保存更新
        self.repository.update_rule(&rule).await
            .map_err(|e| ApiError::Internal(format!("更新规则失败: {}", e)))?;

        info!("风控规则更新成功: {}", rule_id);
        Ok(self.convert_rule_to_response(rule))
    }

    /// 更新规则参数
    pub async fn update_rule_parameters(&self, rule_id: Uuid, parameters: serde_json::Value) -> ApiResult<RuleParametersResponse> {
        info!("更新风控规则参数: {}", rule_id);

        // 获取现有规则
        let mut rule = self.repository.get_rule(rule_id).await
            .map_err(|e| ApiError::Internal(format!("查询规则失败: {}", e)))?
            .ok_or_else(|| ApiError::NotFound("风控规则不存在".to_string()))?;

        // 更新参数
        rule.parameters = parameters.clone();
        rule.updated_at = Utc::now();

        // 保存更新
        self.repository.update_rule(&rule).await
            .map_err(|e| ApiError::Internal(format!("更新规则参数失败: {}", e)))?;

        info!("风控规则参数更新成功: {}", rule_id);
        Ok(RuleParametersResponse {
            rule_id,
            parameters,
            updated_at: rule.updated_at,
        })
    }

    /// 切换规则启用状态
    pub async fn toggle_rule(&self, rule_id: Uuid, enabled: bool) -> ApiResult<RuleResponse> {
        info!("切换风控规则状态: {} -> {}", rule_id, enabled);

        // 获取现有规则
        let mut rule = self.repository.get_rule(rule_id).await
            .map_err(|e| ApiError::Internal(format!("查询规则失败: {}", e)))?
            .ok_or_else(|| ApiError::NotFound("风控规则不存在".to_string()))?;

        // 更新状态
        rule.enabled = enabled;
        rule.updated_at = Utc::now();

        // 保存更新
        self.repository.update_rule(&rule).await
            .map_err(|e| ApiError::Internal(format!("更新规则状态失败: {}", e)))?;

        let action = if enabled { "启用" } else { "禁用" };
        info!("风控规则{}成功: {}", action, rule_id);
        
        Ok(self.convert_rule_to_response(rule))
    }

    /// 获取规则执行历史
    pub async fn get_rule_execution_history(&self, rule_id: Uuid, query: ExecutionHistoryQuery) -> ApiResult<ExecutionHistoryResponse> {
        info!("获取规则执行历史: {}", rule_id);

        let page = query.page.unwrap_or(1);
        let per_page = query.per_page.unwrap_or(10);

        let (records, total_count) = self.repository.get_execution_history(
            rule_id,
            page as usize,
            per_page as usize,
            None, // result_filter
            query.start_time,
            query.end_time
        ).await
            .map_err(|e| ApiError::Internal(format!("查询执行历史失败: {}", e)))?;

        let total_pages = (total_count as u64 + per_page - 1) / per_page;

        Ok(ExecutionHistoryResponse {
            page,
            per_page,
            records,
            total_count: total_count as u64,
            total_pages,
        })
    }

    /// 转换规则为响应格式
    fn convert_rule_to_response(&self, rule: sigmax_risk::unified_engine::UnifiedRiskRule) -> RuleResponse {
        RuleResponse {
            id: rule.id,
            name: rule.name,
            description: rule.description,
            category: rule.category,
            rule_type: rule.rule_type,
            parameters: rule.parameters,
            conditions: rule.conditions,
            enabled: rule.enabled,
            priority: rule.priority,
            strategy_type: rule.strategy_type,
            trading_pairs: rule.trading_pairs,
            execution_stats: RuleExecutionStats {
                execution_count: rule.execution_count,
                success_count: rule.success_count,
                failure_count: rule.failure_count,
                success_rate: if rule.execution_count > 0 {
                    (rule.success_count as f64 / rule.execution_count as f64) * 100.0
                } else {
                    0.0
                },
                last_executed_at: rule.last_executed_at,
            },
            created_at: rule.created_at,
            updated_at: rule.updated_at,
        }
    }
}

// DTO定义
#[derive(Debug, Deserialize)]
pub struct RuleListQuery {
    pub page: Option<u64>,
    pub per_page: Option<u64>,
    pub category: Option<String>,
    pub rule_type: Option<String>,
    pub enabled: Option<bool>,
    pub strategy_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateRuleRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub category: Option<String>,
    pub rule_type: Option<String>,
    pub parameters: Option<serde_json::Value>,
    pub conditions: Option<serde_json::Value>,
    pub enabled: Option<bool>,
    pub priority: Option<i32>,
    pub strategy_type: Option<String>,
    pub trading_pairs: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct ExecutionHistoryQuery {
    pub page: Option<u64>,
    pub per_page: Option<u64>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct RuleListResponse {
    pub rules: Vec<RuleResponse>,
    pub total_count: u64,
    pub page: u64,
    pub per_page: u64,
    pub total_pages: u64,
}

#[derive(Debug, Serialize)]
pub struct RuleResponse {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub rule_type: String,
    pub parameters: serde_json::Value,
    pub conditions: Option<serde_json::Value>,
    pub enabled: bool,
    pub priority: i32,
    pub strategy_type: Option<String>,
    pub trading_pairs: Vec<String>,
    pub execution_stats: RuleExecutionStats,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct RuleExecutionStats {
    pub execution_count: u64,
    pub success_count: u64,
    pub failure_count: u64,
    pub success_rate: f64,
    pub last_executed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct RuleParametersResponse {
    pub rule_id: Uuid,
    pub parameters: serde_json::Value,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ExecutionHistoryResponse {
    pub page: u64,
    pub per_page: u64,
    pub records: Vec<sigmax_risk::unified_engine::ExecutionHistoryRecord>,
    pub total_count: u64,
    pub total_pages: u64,
}
