//! API错误处理

use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use sigmax_core::SigmaXError;
use std::fmt;

/// API错误类型
#[derive(Debug)]
pub enum ApiError {
    Internal(String),
    BadRequest(String),
    NotFound(String),
    Unauthorized(String),
    Forbidden(String),
    Conflict(String),
    ValidationError(Vec<String>),
    InvalidOperation(String),
}

impl fmt::Display for ApiError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ApiError::Internal(msg) => write!(f, "Internal error: {}", msg),
            ApiError::BadRequest(msg) => write!(f, "Bad request: {}", msg),
            ApiError::NotFound(msg) => write!(f, "Not found: {}", msg),
            ApiError::Unauthorized(msg) => write!(f, "Unauthorized: {}", msg),
            ApiError::Forbidden(msg) => write!(f, "Forbidden: {}", msg),
            ApiError::Conflict(msg) => write!(f, "Conflict: {}", msg),
            ApiError::ValidationError(errors) => write!(f, "Validation error: {:?}", errors),
            ApiError::InvalidOperation(msg) => write!(f, "Invalid operation: {}", msg),
        }
    }
}

impl From<SigmaXError> for ApiError {
    fn from(err: SigmaXError) -> Self {
        match err {
            SigmaXError::NotFound(msg) => ApiError::NotFound(msg),
            SigmaXError::InvalidParameter(msg) => ApiError::BadRequest(msg),
            SigmaXError::Database(msg) => ApiError::Internal(format!("Database error: {}", msg)),
            SigmaXError::Network(msg) => ApiError::Internal(format!("Network error: {}", msg)),
            SigmaXError::Internal(msg) => ApiError::Internal(msg),
            SigmaXError::Config(msg) => ApiError::BadRequest(format!("Configuration error: {}", msg)),
            SigmaXError::Unauthorized => ApiError::Unauthorized("Unauthorized".to_string()),
            _ => ApiError::Internal("Unknown error".to_string()),
        }
    }
}

impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            ApiError::Internal(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            ApiError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            ApiError::Unauthorized(msg) => (StatusCode::UNAUTHORIZED, msg),
            ApiError::Forbidden(msg) => (StatusCode::FORBIDDEN, msg),
            ApiError::Conflict(msg) => (StatusCode::CONFLICT, msg),
            ApiError::ValidationError(errors) => {
                let error_response = json!({
                    "error": "Validation failed",
                    "details": errors
                });
                return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
            }
            ApiError::InvalidOperation(msg) => (StatusCode::BAD_REQUEST, msg),
        };

        let error_response = json!({
            "error": error_message
        });

        (status, Json(error_response)).into_response()
    }
}

/// API结果类型
pub type ApiResult<T> = Result<T, ApiError>;

/// Web错误类型别名（为了向后兼容）
pub type WebError = ApiError;

/// 统一API响应格式
#[derive(Debug, serde::Serialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub message: String,
    pub data: Option<T>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T, message: impl Into<String>) -> Self {
        Self {
            code: 200,
            message: message.into(),
            data: Some(data),
        }
    }

    pub fn success_with_message(message: impl Into<String>) -> ApiResponse<()> {
        ApiResponse {
            code: 200,
            message: message.into(),
            data: None,
        }
    }

    pub fn error(code: i32, message: impl Into<String>) -> ApiResponse<()> {
        ApiResponse {
            code,
            message: message.into(),
            data: None,
        }
    }
}


/// 分页响应
#[derive(Debug, serde::Serialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub total: usize,
    pub page: usize,
    pub per_page: usize,
    pub total_pages: usize,
}

impl<T> PaginatedResponse<T> {
    pub fn new(data: Vec<T>, total: usize, page: usize, per_page: usize) -> Self {
        let total_pages = (total + per_page - 1) / per_page;
        Self {
            data,
            total,
            page,
            per_page,
            total_pages,
        }
    }
}

/// 分页查询参数
#[derive(Debug, serde::Deserialize)]
pub struct PaginationQuery {
    #[serde(default = "default_page")]
    pub page: usize,
    #[serde(default = "default_per_page")]
    pub per_page: usize,
}

fn default_page() -> usize {
    1
}

fn default_per_page() -> usize {
    20
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: default_page(),
            per_page: default_per_page(),
        }
    }
}
