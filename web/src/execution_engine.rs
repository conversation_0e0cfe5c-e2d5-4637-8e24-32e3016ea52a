//! 策略执行引擎
//!
//! 负责管理策略的实际执行，包括：
//! - 策略生命周期管理
//! - 市场数据处理
//! - 交易信号执行
//! - 风控检查集成
//! - 订单管理
//! - 性能监控

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use tokio::task::JoinHandle;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::{Decimal, prelude::FromPrimitive};
use serde::{Serialize, Deserialize};
use tracing::{info, warn, error, debug};

use sigmax_core::{SigmaXResult, SigmaXError, Order, Balance, OrderStatus, OrderSide, OrderType, ExchangeId, TradingPair, Quantity, Price};
use crate::services::{StrategyService, UnifiedRiskService};
use crate::handlers::strategy_handlers::StrategyResponse;
use sigmax_risk::unified_engine::RuleExecutionContext;

/// 策略执行状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum StrategyExecutionStatus {
    /// 已创建但未启动
    Created,
    /// 正在启动
    Starting,
    /// 运行中
    Running,
    /// 正在停止
    Stopping,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

/// 市场数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub symbol: String,
    pub price: Decimal,
    pub volume: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 交易信号
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingSignal {
    pub id: Uuid,
    pub strategy_id: Uuid,
    pub symbol: String,
    pub signal_type: SignalType,
    pub price: Decimal,
    pub quantity: Decimal,
    pub timestamp: DateTime<Utc>,
    pub reason: String,
}

/// 信号类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SignalType {
    Buy,
    Sell,
    Hold,
    StopLoss,
    TakeProfit,
}

/// 策略执行实例
pub struct StrategyExecution {
    pub strategy_id: Uuid,
    pub status: StrategyExecutionStatus,
    pub started_at: Option<DateTime<Utc>>,
    pub stopped_at: Option<DateTime<Utc>>,
    pub task_handle: Option<JoinHandle<()>>,
    pub signal_sender: mpsc::UnboundedSender<TradingSignal>,
    pub market_data_sender: mpsc::UnboundedSender<MarketData>,
    pub performance: StrategyPerformance,
}

/// 策略性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyPerformance {
    pub total_trades: u64,
    pub winning_trades: u64,
    pub losing_trades: u64,
    pub total_pnl: Decimal,
    pub win_rate: Decimal,
    pub max_drawdown: Decimal,
    pub current_positions: u32,
    pub last_updated: DateTime<Utc>,
}

impl Default for StrategyPerformance {
    fn default() -> Self {
        Self {
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            total_pnl: Decimal::ZERO,
            win_rate: Decimal::ZERO,
            max_drawdown: Decimal::ZERO,
            current_positions: 0,
            last_updated: Utc::now(),
        }
    }
}

/// 策略执行引擎
pub struct StrategyExecutionEngine {
    /// 运行中的策略执行实例
    executions: Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
    /// 策略服务
    strategy_service: Arc<StrategyService>,
    /// 风控服务
    risk_service: Arc<UnifiedRiskService>,
    /// 全局市场数据广播器
    market_data_broadcaster: Arc<RwLock<Vec<mpsc::UnboundedSender<MarketData>>>>,
}

impl StrategyExecutionEngine {
    /// 创建新的策略执行引擎
    pub fn new(
        strategy_service: Arc<StrategyService>,
        risk_service: Arc<UnifiedRiskService>,
    ) -> Self {
        Self {
            executions: Arc::new(RwLock::new(HashMap::new())),
            strategy_service,
            risk_service,
            market_data_broadcaster: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 启动策略执行
    pub async fn start_strategy(&self, strategy_id: Uuid) -> SigmaXResult<()> {
        info!("启动策略执行: {}", strategy_id);

        // 检查策略是否已经在运行
        {
            let executions = self.executions.read().await;
            if let Some(execution) = executions.get(&strategy_id) {
                if execution.status == StrategyExecutionStatus::Running {
                    return Err(SigmaXError::InvalidOperation(
                        "Strategy is already running".to_string()
                    ));
                }
            }
        }

        // 获取策略信息
        let strategy = self.strategy_service.get_strategy_by_id(strategy_id).await
            .map_err(|e| SigmaXError::NotFound(format!("Strategy not found: {:?}", e)))?;

        // 创建信号和市场数据通道
        let (signal_sender, signal_receiver) = mpsc::unbounded_channel();
        let (market_data_sender, market_data_receiver) = mpsc::unbounded_channel();

        // 注册市场数据接收器
        {
            let mut broadcasters = self.market_data_broadcaster.write().await;
            broadcasters.push(market_data_sender.clone());
        }

        // 创建策略执行实例
        let mut execution = StrategyExecution {
            strategy_id,
            status: StrategyExecutionStatus::Starting,
            started_at: Some(Utc::now()),
            stopped_at: None,
            task_handle: None,
            signal_sender,
            market_data_sender,
            performance: StrategyPerformance::default(),
        };

        // 启动策略执行任务
        let task_handle = self.spawn_strategy_task(
            strategy_id,
            strategy,
            signal_receiver,
            market_data_receiver,
        ).await?;

        execution.task_handle = Some(task_handle);
        execution.status = StrategyExecutionStatus::Running;

        // 存储执行实例
        {
            let mut executions = self.executions.write().await;
            executions.insert(strategy_id, execution);
        }

        // TODO: 直接更新数据库状态，避免循环调用
        // 这里应该直接调用数据库操作，而不是通过 StrategyService

        info!("策略执行启动成功: {}", strategy_id);
        Ok(())
    }

    /// 停止策略执行
    pub async fn stop_strategy(&self, strategy_id: Uuid) -> SigmaXResult<()> {
        info!("停止策略执行: {}", strategy_id);

        let mut execution = {
            let mut executions = self.executions.write().await;
            executions.remove(&strategy_id)
                .ok_or_else(|| SigmaXError::NotFound("Strategy execution not found".to_string()))?
        };

        // 更新状态
        execution.status = StrategyExecutionStatus::Stopping;
        execution.stopped_at = Some(Utc::now());

        // 停止执行任务
        if let Some(handle) = execution.task_handle.take() {
            handle.abort();
        }

        // TODO: 直接更新数据库状态，避免循环调用
        // 这里应该直接调用数据库操作，而不是通过 StrategyService

        info!("策略执行停止成功: {}", strategy_id);
        Ok(())
    }

    /// 获取策略执行状态
    pub async fn get_execution_status(&self, strategy_id: Uuid) -> Option<StrategyExecutionStatus> {
        let executions = self.executions.read().await;
        executions.get(&strategy_id).map(|e| e.status.clone())
    }

    /// 获取所有运行中的策略
    pub async fn get_running_strategies(&self) -> Vec<Uuid> {
        let executions = self.executions.read().await;
        executions.iter()
            .filter(|(_, execution)| execution.status == StrategyExecutionStatus::Running)
            .map(|(id, _)| *id)
            .collect()
    }

    /// 广播市场数据到所有策略
    pub async fn broadcast_market_data(&self, market_data: MarketData) -> SigmaXResult<()> {
        let broadcasters = self.market_data_broadcaster.read().await;
        let mut failed_senders = Vec::new();

        for (index, sender) in broadcasters.iter().enumerate() {
            if sender.send(market_data.clone()).is_err() {
                failed_senders.push(index);
            }
        }

        // 清理失败的发送器
        if !failed_senders.is_empty() {
            drop(broadcasters);
            let mut broadcasters = self.market_data_broadcaster.write().await;
            for &index in failed_senders.iter().rev() {
                broadcasters.remove(index);
            }
        }

        Ok(())
    }

    /// 生成策略执行任务
    async fn spawn_strategy_task(
        &self,
        strategy_id: Uuid,
        strategy: StrategyResponse,
        mut signal_receiver: mpsc::UnboundedReceiver<TradingSignal>,
        mut market_data_receiver: mpsc::UnboundedReceiver<MarketData>,
    ) -> SigmaXResult<JoinHandle<()>> {
        let executions = Arc::clone(&self.executions);
        let risk_service = Arc::clone(&self.risk_service);
        let strategy_service = Arc::clone(&self.strategy_service);

        let handle = tokio::spawn(async move {
            info!("策略执行任务启动: {} - {}", strategy_id, strategy.name);

            loop {
                tokio::select! {
                    // 处理交易信号
                    signal = signal_receiver.recv() => {
                        if let Some(signal) = signal {
                            if let Err(e) = Self::process_trading_signal_static(
                                &risk_service,
                                &strategy_service,
                                &executions,
                                signal
                            ).await {
                                error!("处理交易信号失败: {}", e);
                            }
                        } else {
                            debug!("信号通道关闭，退出策略任务: {}", strategy_id);
                            break;
                        }
                    }

                    // 处理市场数据
                    market_data = market_data_receiver.recv() => {
                        if let Some(data) = market_data {
                            if let Err(e) = Self::process_market_data_static(
                                &executions,
                                strategy_id,
                                &strategy,
                                data
                            ).await {
                                error!("处理市场数据失败: {}", e);
                            }
                        } else {
                            debug!("市场数据通道关闭，退出策略任务: {}", strategy_id);
                            break;
                        }
                    }

                    // 定期性能更新
                    _ = tokio::time::sleep(tokio::time::Duration::from_secs(30)) => {
                        if let Err(e) = Self::update_strategy_performance_static(
                            &strategy_service,
                            &executions,
                            strategy_id
                        ).await {
                            error!("更新策略性能失败: {}", e);
                        }
                    }
                }
            }

            info!("策略执行任务结束: {}", strategy_id);
        });

        Ok(handle)
    }

    /// 静态方法：处理交易信号
    async fn process_trading_signal_static(
        risk_service: &Arc<UnifiedRiskService>,
        _strategy_service: &Arc<StrategyService>,
        executions: &Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
        signal: TradingSignal,
    ) -> SigmaXResult<()> {
        debug!("处理交易信号: {:?}", signal);

        // 风控检查
        let risk_check = RuleExecutionContext {
            order: Some(Order {
                id: Uuid::new_v4(), // Generate a new UUID for the order
                strategy_id: Some(signal.strategy_id),
                exchange_id: ExchangeId::Binance, // Assuming Binance as default
                trading_pair: TradingPair { base: signal.symbol.clone(), quote: "USDT".to_string() }, // Assuming USDT as quote
                side: match signal.signal_type {
                    SignalType::Buy => OrderSide::Buy,
                    SignalType::Sell => OrderSide::Sell,
                    _ => return Err(SigmaXError::InvalidOperation("Unsupported signal type for order risk check".to_string())),
                },
                order_type: OrderType::Market, // Assuming market order
                quantity: signal.quantity,
                price: Some(signal.price),
                stop_price: None,
                status: OrderStatus::Pending,
                filled_quantity: Quantity::new(0, 0), // Initialize with zero
                average_price: None,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            }),
            balances: None,
            strategy_type: None,
            trading_pair: Some(signal.symbol.clone()),
            extra_data: HashMap::new(),
        };

        let risk_result = risk_service.check_risk(&risk_check).await
            .map_err(|e| SigmaXError::InvalidOperation(format!("Risk check failed: {:?}", e)))?;

        if !risk_result.passed {
            warn!("交易信号未通过风控检查: {:?}, 违规: {:?}", signal, risk_result.failed_rules);
            return Ok(());
        }

        // 执行交易信号
        match signal.signal_type {
            SignalType::Buy | SignalType::Sell => {
                Self::execute_trade_signal_static(executions, signal).await?;
            }
            SignalType::StopLoss | SignalType::TakeProfit => {
                Self::execute_risk_signal_static(executions, signal).await?;
            }
            SignalType::Hold => {
                debug!("持有信号，无需执行: {}", signal.symbol);
            }
        }

        Ok(())
    }

    /// 静态方法：执行交易信号
    async fn execute_trade_signal_static(
        executions: &Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
        signal: TradingSignal,
    ) -> SigmaXResult<()> {
        info!("执行交易信号: {:?} {} @ {} x {}",
              signal.signal_type, signal.symbol, signal.price, signal.quantity);

        // 模拟订单执行（在实际实现中，这里会调用交易所API）
        let order_status_mock = sigmax_core::OrderStatus::Filled; // Use the actual enum

        // Update strategy performance
        {
            let mut executions_guard = executions.write().await;
            if let Some(execution) = executions_guard.get_mut(&signal.strategy_id) {
                execution.performance.total_trades += 1;
                execution.performance.last_updated = Utc::now();

                // Simulate PnL calculation (simplified)
                let pnl = if matches!(signal.signal_type, SignalType::Sell) {
                    signal.quantity * signal.price * Decimal::new(1, 2) // Assume 1% profit
                } else {
                    Decimal::ZERO
                };

                execution.performance.total_pnl += pnl;

                if pnl > Decimal::ZERO {
                    execution.performance.winning_trades += 1;
                } else if pnl < Decimal::ZERO {
                    execution.performance.losing_trades += 1;
                }

                // Update win rate
                if execution.performance.total_trades > 0 {
                    execution.performance.win_rate =
                        Decimal::from(execution.performance.winning_trades) /
                        Decimal::from(execution.performance.total_trades);
                }
            }
        }

        info!("交易信号执行完成: 订单ID {}", signal.id);
        Ok(())
    }

    /// 静态方法：执行风控信号
    async fn execute_risk_signal_static(
        _executions: &Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
        signal: TradingSignal,
    ) -> SigmaXResult<()> {
        info!("执行风控信号: {:?} {} @ {}",
              signal.signal_type, signal.symbol, signal.price);

        // In a real implementation, this would handle stop-loss/take-profit logic
        // For now, just log
        debug!("风控信号处理完成: {}", signal.id);
        Ok(())
    }

    /// 静态方法：处理市场数据
    async fn process_market_data_static(
        executions: &Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
        strategy_id: Uuid,
        strategy: &StrategyResponse,
        market_data: MarketData,
    ) -> SigmaXResult<()> {
        debug!("策略 {} 处理市场数据: {} @ {}",
               strategy_id, market_data.symbol, market_data.price);

        // Check if the strategy is interested in this trading pair
        if !strategy.trading_pairs.contains(&market_data.symbol) {
            return Ok(());
        }

        // Generate trading signals based on strategy type
        let signals = Self::generate_trading_signals_static(strategy, &market_data).await?;

        // Send signals to the strategy executor
        {
            let executions_guard = executions.read().await;
            if let Some(execution) = executions_guard.get(&strategy_id) {
                for signal in signals {
                    if let Err(e) = execution.signal_sender.send(signal) {
                        warn!("发送交易信号失败: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 静态方法：生成交易信号
    async fn generate_trading_signals_static(
        strategy: &StrategyResponse,
        market_data: &MarketData,
    ) -> SigmaXResult<Vec<TradingSignal>> {
        let mut signals = Vec::new();

        // Generate signals based on strategy type
        match strategy.strategy_type.as_str() {
            "grid" => {
                // Grid strategy signal generation logic
                signals.extend(Self::generate_grid_signals_static(strategy, market_data).await?);
            }
            "dca" => {
                // DCA strategy signal generation logic
                signals.extend(Self::generate_dca_signals_static(strategy, market_data).await?);
            }
            _ => {
                debug!("Unknown strategy type: {}", strategy.strategy_type);
            }
        }

        Ok(signals)
    }

    /// 静态方法：生成网格策略信号
    async fn generate_grid_signals_static(
        strategy: &StrategyResponse,
        market_data: &MarketData,
    ) -> SigmaXResult<Vec<TradingSignal>> {
        let mut signals = Vec::new();

        // Simplified grid strategy logic
        let grid_count = strategy.parameters.get("grid_count")
            .and_then(|v| v.as_u64())
            .unwrap_or(10);

        let price_range = strategy.parameters.get("price_range")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.1);

        // Calculate grid prices
        let base_price = market_data.price;
        let _grid_spacing = base_price * Decimal::from_f64(price_range / grid_count as f64)
            .unwrap_or(Decimal::new(1, 2));

        // Simple buy signal generation (when price drops)
        if market_data.price < base_price * Decimal::new(99, 2) { // Price drops by 1%
            let signal = TradingSignal {
                id: Uuid::new_v4(),
                strategy_id: Uuid::parse_str(&strategy.id)
                    .map_err(|e| SigmaXError::InvalidOperation(format!("Invalid strategy ID: {}", e)))?,
                symbol: market_data.symbol.clone(),
                signal_type: SignalType::Buy,
                price: market_data.price,
                quantity: Decimal::new(100, 0), // Fixed quantity, should be based on strategy config
                timestamp: Utc::now(),
                reason: "Grid strategy buy signal".to_string(),
            };
            signals.push(signal);
        }

        Ok(signals)
    }

    /// 静态方法：生成DCA策略信号
    async fn generate_dca_signals_static(
        strategy: &StrategyResponse,
        market_data: &MarketData,
    ) -> SigmaXResult<Vec<TradingSignal>> {
        let mut signals = Vec::new();

        // Simplified DCA strategy logic
        let _interval_hours = strategy.parameters.get("interval_hours")
            .and_then(|v| v.as_u64())
            .unwrap_or(24);

        // Simple periodic buy signal (simplified to generate signal every time)
        let signal = TradingSignal {
            id: Uuid::new_v4(),
            strategy_id: Uuid::parse_str(&strategy.id)
                .map_err(|e| SigmaXError::InvalidOperation(format!("Invalid strategy ID: {}", e)))?,
            symbol: market_data.symbol.clone(),
            signal_type: SignalType::Buy,
            price: market_data.price,
            quantity: Decimal::new(50, 0), // Fixed quantity
            timestamp: Utc::now(),
            reason: "DCA strategy periodic buy".to_string(),
        };
        signals.push(signal);

        Ok(signals)
    }

    /// 静态方法：更新策略性能
    async fn update_strategy_performance_static(
        _strategy_service: &Arc<StrategyService>,
        executions: &Arc<RwLock<HashMap<Uuid, StrategyExecution>>>,
        strategy_id: Uuid,
    ) -> SigmaXResult<()> {
        debug!("更新策略性能: {}", strategy_id);

        let performance = {
            let executions_guard = executions.read().await;
            executions_guard.get(&strategy_id)
                .map(|e| e.performance.clone())
        };

        if let Some(perf) = performance {
            // In a real implementation, this would save performance data to the database
            // For now, just log
            info!("策略 {} 性能更新: 总交易 {}, 胜率 {:.2}%, 总PnL {}",
                  strategy_id,
                  perf.total_trades,
                  perf.win_rate * Decimal::new(100, 0),
                  perf.total_pnl);
        }

        Ok(())
    }

    /// 获取策略性能
    pub async fn get_strategy_performance(&self, strategy_id: Uuid) -> Option<StrategyPerformance> {
        let executions = self.executions.read().await;
        executions.get(&strategy_id).map(|e| e.performance.clone())
    }

    /// 获取所有策略的执行状态
    pub async fn get_all_execution_status(&self) -> HashMap<Uuid, StrategyExecutionStatus> {
        let executions = self.executions.read().await;
        executions.iter()
            .map(|(id, execution)| (*id, execution.status.clone()))
            .collect()
    }
}