//! 策略配置验证API处理器

use axum::{
    extract::{Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn};
use rust_decimal::Decimal;

use crate::{
    state::AppState,
    error::{ApiError, ApiResult, ApiResponse},
};

// 导入strategies模块的验证功能
use sigmax_strategies::{
    // StrategyValidator,
    core::StrategyConfig,
    utils::{
        validation::{ValidationResult, StrategyValidationError, ValidationWarning, StrategyValidationErrorType},
    },
    asymmetric_grid::AsymmetricGridConfig,
};

// ============================================================================
// 请求和响应数据结构
// ============================================================================

/// 策略配置验证请求
#[derive(Debug, Deserialize)]
pub struct ValidateStrategyConfigRequest {
    /// 策略类型
    pub strategy_type: String,
    /// 策略配置
    pub config: serde_json::Value,
    /// 初始资金
    pub initial_capital: Decimal,
    /// 交易对（可选）
    pub trading_pair: Option<String>,
    /// 是否使用历史数据验证（可选）
    pub use_historical_data: Option<bool>,
    /// 历史数据文件名（可选）
    pub data_file: Option<String>,
}

/// 策略配置验证响应
#[derive(Debug, Serialize)]
pub struct ValidateStrategyConfigResponse {
    /// 验证是否通过
    pub is_valid: bool,
    /// 验证结果摘要
    pub summary: ValidationSummary,
    /// 错误列表
    pub errors: Vec<ValidationErrorResponse>,
    /// 警告列表
    pub warnings: Vec<ValidationWarningResponse>,
    /// 建议列表
    pub suggestions: Vec<ValidationSuggestionResponse>,
    /// 性能预估
    pub performance_estimate: Option<PerformanceEstimateResponse>,
    /// 验证时间戳
    pub validated_at: String,
}

/// 验证结果摘要
#[derive(Debug, Serialize)]
pub struct ValidationSummary {
    /// 总错误数
    pub total_errors: usize,
    /// 关键错误数
    pub critical_errors: usize,
    /// 高风险错误数
    pub high_risk_errors: usize,
    /// 警告数
    pub total_warnings: usize,
    /// 建议数
    pub total_suggestions: usize,
    /// 验证状态
    pub status: String,
    /// 状态描述
    pub status_message: String,
}

/// 验证错误响应
#[derive(Debug, Serialize)]
pub struct ValidationErrorResponse {
    /// 字段名
    pub field: String,
    /// 错误消息
    pub message: String,
    /// 错误类型
    pub error_type: String,
    /// 严重程度
    pub severity: String,
    /// 错误代码（用于国际化）
    pub error_code: Option<String>,
}

/// 验证警告响应
#[derive(Debug, Serialize)]
pub struct ValidationWarningResponse {
    /// 字段名
    pub field: String,
    /// 警告消息
    pub message: String,
    /// 建议操作
    pub suggestion: Option<String>,
    /// 警告类型
    pub warning_type: String,
}

/// 验证建议响应
#[derive(Debug, Serialize)]
pub struct ValidationSuggestionResponse {
    /// 字段名
    pub field: String,
    /// 当前值
    pub current_value: String,
    /// 建议值
    pub suggested_value: String,
    /// 建议原因
    pub reason: String,
    /// 建议类型
    pub suggestion_type: String,
}

/// 性能预估响应
#[derive(Debug, Serialize)]
pub struct PerformanceEstimateResponse {
    /// 预估日订单数
    pub estimated_orders_per_day: u32,
    /// 预估日手续费
    pub estimated_fee_cost_per_day: String,
    /// 内存使用（MB）
    pub memory_usage_mb: f64,
    /// CPU使用率（%）
    pub cpu_usage_percentage: f64,
    /// 最大资金需求
    pub max_capital_requirement: String,
    /// 风险等级
    pub risk_level: String,
    /// 复杂度等级
    pub complexity_level: String,
}

/// 支持的策略类型查询参数
#[derive(Debug, Deserialize)]
pub struct SupportedStrategiesQuery {
    /// 是否包含详细信息
    pub include_details: Option<bool>,
}

/// 支持的策略类型响应
#[derive(Debug, Serialize)]
pub struct SupportedStrategiesResponse {
    /// 支持的策略类型列表
    pub strategies: Vec<StrategyTypeInfo>,
    /// 总数
    pub total: usize,
}

/// 策略类型信息
#[derive(Debug, Serialize)]
pub struct StrategyTypeInfo {
    /// 策略类型
    pub strategy_type: String,
    /// 策略名称
    pub name: String,
    /// 策略描述
    pub description: String,
    /// 是否可用
    pub available: bool,
    /// 配置模板（可选）
    pub config_template: Option<serde_json::Value>,
    /// 必需参数
    pub required_parameters: Vec<ParameterInfo>,
    /// 可选参数
    pub optional_parameters: Vec<ParameterInfo>,
}

/// 参数信息
#[derive(Debug, Serialize)]
pub struct ParameterInfo {
    /// 参数名
    pub name: String,
    /// 参数类型
    pub parameter_type: String,
    /// 参数描述
    pub description: String,
    /// 默认值
    pub default_value: Option<serde_json::Value>,
    /// 最小值
    pub min_value: Option<serde_json::Value>,
    /// 最大值
    pub max_value: Option<serde_json::Value>,
    /// 示例值
    pub example_value: Option<serde_json::Value>,
}

// ============================================================================
// API处理器实现
// ============================================================================

/// 验证策略配置
pub async fn validate_strategy_config(
    State(state): State<AppState>,
    Json(request): Json<ValidateStrategyConfigRequest>,
) -> ApiResult<Json<ApiResponse<ValidateStrategyConfigResponse>>> {
    info!("开始验证策略配置: 类型={}, 初始资金={}",
          request.strategy_type, request.initial_capital);

    // 准备历史数据（如果需要）
    let _market_data = if request.use_historical_data.unwrap_or(false) {
        if let Some(data_file) = &request.data_file {
            // 加载历史数据
            match state.backtest_data_manager.load_candles(data_file).await {
                Ok(candles) => {
                    info!("成功加载 {} 条历史数据用于验证", candles.len());
                    Some(candles)
                }
                Err(e) => {
                    warn!("加载历史数据失败: {}, 将不使用历史数据验证", e);
                    None
                }
            }
        } else {
            None
        }
    } else {
        None
    };

    // 执行策略验证
    let validation_result = match request.strategy_type.as_str() {
        "asymmetric_volatility_grid_strategy" => {
            // 解析配置为AsymmetricGridConfig
            let config: AsymmetricGridConfig = serde_json::from_value(request.config)
                .map_err(|e| ApiError::BadRequest(format!("配置解析失败: {}", e)))?;

            // 执行验证 - 直接调用配置的validate方法
            match config.validate() {
                Ok(()) => ValidationResult::success(),
                Err(e) => {
                    let mut result = ValidationResult::success();
                    result.add_error(StrategyValidationError::logic("config", &e.to_string()));
                    result
                }
            }
        }
        "dca_strategy" => {
            // DCA策略验证
            validate_dca_strategy_config(&request.config)?
        }
        "momentum_strategy" => {
            // 动量策略验证
            validate_momentum_strategy_config(&request.config)?
        }
        "mean_reversion_strategy" => {
            // 均值回归策略验证
            validate_mean_reversion_strategy_config(&request.config)?
        }
        _ => {
            return Err(ApiError::BadRequest(format!("不支持的策略类型: {}", request.strategy_type)));
        }
    };

    // 转换验证结果为API响应格式
    let response = convert_validation_result(validation_result, &request.strategy_type);

    info!("策略配置验证完成: 有效={}, 错误数={}, 警告数={}",
          response.is_valid, response.summary.total_errors, response.summary.total_warnings);

    let message = if response.is_valid {
        "策略配置验证通过"
    } else {
        "策略配置验证完成，存在问题"
    };

    let api_response = ApiResponse::success(response, message);
    Ok(Json(api_response))
}

/// 获取支持的策略类型
pub async fn get_supported_strategies(
    Query(query): Query<SupportedStrategiesQuery>,
) -> ApiResult<Json<ApiResponse<SupportedStrategiesResponse>>> {
    info!("获取支持的策略类型列表");

    let include_details = query.include_details.unwrap_or(false);

    let strategies = vec![
        StrategyTypeInfo {
            strategy_type: "grid".to_string(),
            name: "网格策略".to_string(),
            description: "在价格区间内设置多个买卖网格，通过价格波动获利".to_string(),
            available: true,
            config_template: if include_details {
                Some(serde_json::json!({
                    "grid_levels": 10,
                    "upper_price": 300.0,
                    "lower_price": 200.0,
                    "base_order_size": 0.1,
                    "take_profit": 0.02
                }))
            } else {
                None
            },
            required_parameters: if include_details {
                vec![
                    ParameterInfo {
                        name: "grid_levels".to_string(),
                        parameter_type: "integer".to_string(),
                        description: "网格数量".to_string(),
                        default_value: Some(serde_json::json!(10)),
                        min_value: Some(serde_json::json!(1)),
                        max_value: Some(serde_json::json!(100)),
                        example_value: Some(serde_json::json!(10)),
                    },
                    ParameterInfo {
                        name: "upper_price".to_string(),
                        parameter_type: "number".to_string(),
                        description: "网格上限价格".to_string(),
                        default_value: None,
                        min_value: Some(serde_json::json!(0.0)),
                        max_value: None,
                        example_value: Some(serde_json::json!(300.0)),
                    },
                    ParameterInfo {
                        name: "lower_price".to_string(),
                        parameter_type: "number".to_string(),
                        description: "网格下限价格".to_string(),
                        default_value: None,
                        min_value: Some(serde_json::json!(0.0)),
                        max_value: None,
                        example_value: Some(serde_json::json!(200.0)),
                    },
                    ParameterInfo {
                        name: "base_order_size".to_string(),
                        parameter_type: "number".to_string(),
                        description: "基础订单大小".to_string(),
                        default_value: Some(serde_json::json!(0.1)),
                        min_value: Some(serde_json::json!(0.001)),
                        max_value: None,
                        example_value: Some(serde_json::json!(0.1)),
                    },
                ]
            } else {
                vec![]
            },
            optional_parameters: if include_details {
                vec![
                    ParameterInfo {
                        name: "take_profit".to_string(),
                        parameter_type: "number".to_string(),
                        description: "止盈比例".to_string(),
                        default_value: Some(serde_json::json!(0.02)),
                        min_value: Some(serde_json::json!(0.001)),
                        max_value: Some(serde_json::json!(0.5)),
                        example_value: Some(serde_json::json!(0.02)),
                    },
                ]
            } else {
                vec![]
            },
        },
        StrategyTypeInfo {
            strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
            name: "非对称波动率网格策略".to_string(),
            description: "基于波动率的非对称网格策略，下跌时密集吸筹，上涨时稀疏止盈".to_string(),
            available: true,
            config_template: if include_details {
                Some(serde_json::json!({
                    "strategy_preset": "Balanced",
                    "down_range_start": -0.02,
                    "down_range_end": -0.05,
                    "down_grid_count": 10,
                    "up_range_start": 0.02,
                    "up_range_end": 0.08,
                    "up_grid_count": 6,
                    "volatility_multiplier": 1.2,
                    "max_loss_percentage": 8.0
                }))
            } else {
                None
            },
            required_parameters: if include_details {
                vec![
                    ParameterInfo {
                        name: "down_range_start".to_string(),
                        parameter_type: "number".to_string(),
                        description: "下跌区间起始百分比".to_string(),
                        default_value: Some(serde_json::json!(-0.02)),
                        min_value: Some(serde_json::json!(-0.5)),
                        max_value: Some(serde_json::json!(0.0)),
                        example_value: Some(serde_json::json!(-0.02)),
                    },
                    ParameterInfo {
                        name: "down_range_end".to_string(),
                        parameter_type: "number".to_string(),
                        description: "下跌区间结束百分比".to_string(),
                        default_value: Some(serde_json::json!(-0.05)),
                        min_value: Some(serde_json::json!(-0.5)),
                        max_value: Some(serde_json::json!(0.0)),
                        example_value: Some(serde_json::json!(-0.05)),
                    },
                    ParameterInfo {
                        name: "down_grid_count".to_string(),
                        parameter_type: "integer".to_string(),
                        description: "下跌区间网格数量".to_string(),
                        default_value: Some(serde_json::json!(10)),
                        min_value: Some(serde_json::json!(1)),
                        max_value: Some(serde_json::json!(50)),
                        example_value: Some(serde_json::json!(10)),
                    },
                    ParameterInfo {
                        name: "up_range_start".to_string(),
                        parameter_type: "number".to_string(),
                        description: "上涨区间起始百分比".to_string(),
                        default_value: Some(serde_json::json!(0.02)),
                        min_value: Some(serde_json::json!(0.0)),
                        max_value: Some(serde_json::json!(0.5)),
                        example_value: Some(serde_json::json!(0.02)),
                    },
                    ParameterInfo {
                        name: "up_range_end".to_string(),
                        parameter_type: "number".to_string(),
                        description: "上涨区间结束百分比".to_string(),
                        default_value: Some(serde_json::json!(0.08)),
                        min_value: Some(serde_json::json!(0.0)),
                        max_value: Some(serde_json::json!(0.5)),
                        example_value: Some(serde_json::json!(0.08)),
                    },
                    ParameterInfo {
                        name: "up_grid_count".to_string(),
                        parameter_type: "integer".to_string(),
                        description: "上涨区间网格数量".to_string(),
                        default_value: Some(serde_json::json!(6)),
                        min_value: Some(serde_json::json!(1)),
                        max_value: Some(serde_json::json!(50)),
                        example_value: Some(serde_json::json!(6)),
                    },
                ]
            } else {
                vec![]
            },
            optional_parameters: if include_details {
                vec![
                    ParameterInfo {
                        name: "strategy_preset".to_string(),
                        parameter_type: "string".to_string(),
                        description: "预设策略类型".to_string(),
                        default_value: Some(serde_json::json!("Balanced")),
                        min_value: None,
                        max_value: None,
                        example_value: Some(serde_json::json!("Balanced")),
                    },
                    ParameterInfo {
                        name: "volatility_multiplier".to_string(),
                        parameter_type: "number".to_string(),
                        description: "波动率乘数".to_string(),
                        default_value: Some(serde_json::json!(1.2)),
                        min_value: Some(serde_json::json!(0.1)),
                        max_value: Some(serde_json::json!(5.0)),
                        example_value: Some(serde_json::json!(1.2)),
                    },
                    ParameterInfo {
                        name: "max_loss_percentage".to_string(),
                        parameter_type: "number".to_string(),
                        description: "最大亏损百分比".to_string(),
                        default_value: Some(serde_json::json!(8.0)),
                        min_value: Some(serde_json::json!(1.0)),
                        max_value: Some(serde_json::json!(50.0)),
                        example_value: Some(serde_json::json!(8.0)),
                    },
                ]
            } else {
                vec![]
            },
        },
    ];

    let response_data = SupportedStrategiesResponse {
        total: strategies.len(),
        strategies,
    };

    let api_response = ApiResponse::success(response_data, "获取支持的策略类型列表成功");
    Ok(Json(api_response))
}

/// 获取策略配置模板
pub async fn get_strategy_config_template(
    axum::extract::Path(strategy_type): axum::extract::Path<String>,
) -> ApiResult<Json<ApiResponse<serde_json::Value>>> {
    info!("获取策略配置模板: {}", strategy_type);

    let template = match strategy_type.as_str() {
        "grid" => serde_json::json!({
            "type": "grid",
            "config": {
                "grid_levels": 10,
                "upper_price": 300.0,
                "lower_price": 200.0,
                "base_order_size": 0.1,
                "take_profit": 0.02
            },
            "description": "网格策略配置模板",
            "parameters": {
                "grid_levels": {
                    "type": "integer",
                    "description": "网格数量",
                    "min": 1,
                    "max": 100,
                    "default": 10
                },
                "upper_price": {
                    "type": "number",
                    "description": "网格上限价格",
                    "min": 0.0,
                    "required": true
                },
                "lower_price": {
                    "type": "number",
                    "description": "网格下限价格",
                    "min": 0.0,
                    "required": true
                },
                "base_order_size": {
                    "type": "number",
                    "description": "基础订单大小",
                    "min": 0.001,
                    "default": 0.1
                },
                "take_profit": {
                    "type": "number",
                    "description": "止盈比例",
                    "min": 0.001,
                    "max": 0.5,
                    "default": 0.02
                }
            }
        }),
        "asymmetric_volatility_grid_strategy" => serde_json::json!({
            "type": "asymmetric_volatility_grid_strategy",
            "config": {
                "strategy_preset": "Balanced",
                "base_price": 0.0,
                "down_range_start": -0.02,
                "down_range_end": -0.05,
                "down_grid_count": 10,
                "down_base_quantity": 0.02,
                "up_range_start": 0.02,
                "up_range_end": 0.08,
                "up_grid_count": 6,
                "up_base_quantity": 0.015,
                "volatility_multiplier": 1.2,
                "max_loss_percentage": 8.0,
                "simulation_mode": true
            },
            "description": "非对称波动率网格策略配置模板",
            "parameters": {
                "strategy_preset": {
                    "type": "string",
                    "description": "预设策略类型",
                    "enum": ["Conservative", "Balanced", "Aggressive", "VeryAggressive", "Custom"],
                    "default": "Balanced"
                },
                "down_range_start": {
                    "type": "number",
                    "description": "下跌区间起始百分比",
                    "min": -0.5,
                    "max": 0.0,
                    "required": true
                },
                "down_range_end": {
                    "type": "number",
                    "description": "下跌区间结束百分比",
                    "min": -0.5,
                    "max": 0.0,
                    "required": true
                },
                "down_grid_count": {
                    "type": "integer",
                    "description": "下跌区间网格数量",
                    "min": 1,
                    "max": 50,
                    "required": true
                },
                "up_range_start": {
                    "type": "number",
                    "description": "上涨区间起始百分比",
                    "min": 0.0,
                    "max": 0.5,
                    "required": true
                },
                "up_range_end": {
                    "type": "number",
                    "description": "上涨区间结束百分比",
                    "min": 0.0,
                    "max": 0.5,
                    "required": true
                },
                "up_grid_count": {
                    "type": "integer",
                    "description": "上涨区间网格数量",
                    "min": 1,
                    "max": 50,
                    "required": true
                },
                "volatility_multiplier": {
                    "type": "number",
                    "description": "波动率乘数",
                    "min": 0.1,
                    "max": 5.0,
                    "default": 1.2
                },
                "max_loss_percentage": {
                    "type": "number",
                    "description": "最大亏损百分比",
                    "min": 1.0,
                    "max": 50.0,
                    "default": 8.0
                }
            }
        }),
        _ => {
            return Err(ApiError::BadRequest(format!("不支持的策略类型: {}", strategy_type)));
        }
    };

    let api_response = ApiResponse::success(template, &format!("获取{}策略配置模板成功", strategy_type));
    Ok(Json(api_response))
}

/// 预览策略配置效果
pub async fn preview_strategy_config(
    State(_state): State<AppState>,
    Json(request): Json<ValidateStrategyConfigRequest>,
) -> ApiResult<Json<ApiResponse<StrategyPreviewResponse>>> {
    info!("预览策略配置效果: 类型={}", request.strategy_type);

    // 首先验证配置
    // 临时注释掉旧的策略验证管理器，因为已经重构
    // let validation_manager = sigmax_strategies::StrategyValidationManager::new();
    // 临时跳过验证，因为已经重构
    // let validation_result = validation_manager.validate_strategy_config(...).await?;
    // 假设验证通过

    // 生成预览数据
    let preview = generate_strategy_preview(&request.strategy_type, &request.config, request.initial_capital)?;

    let api_response = ApiResponse::success(preview, "策略配置预览生成成功");
    Ok(Json(api_response))
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 转换验证结果为API响应格式
fn convert_validation_result(
    result: ValidationResult,
    _strategy_type: &str,
) -> ValidateStrategyConfigResponse {
    // 简化错误分类 - 由于ValidationError没有severity字段，我们基于error_type来分类
    let critical_errors = result.errors.iter()
        .filter(|e| matches!(e.error_type, StrategyValidationErrorType::Required | StrategyValidationErrorType::Logic))
        .count();

    let high_risk_errors = result.errors.iter()
        .filter(|e| matches!(e.error_type, StrategyValidationErrorType::Range | StrategyValidationErrorType::Conflict))
        .count();

    let status = if result.is_valid {
        "valid"
    } else if critical_errors > 0 {
        "critical_errors"
    } else if high_risk_errors > 0 {
        "high_risk"
    } else {
        "warnings"
    };

    let status_message = match status {
        "valid" => "配置验证通过".to_string(),
        "critical_errors" => format!("配置存在 {} 个关键错误", critical_errors),
        "high_risk" => format!("配置存在 {} 个高风险问题", high_risk_errors),
        "warnings" => format!("配置存在 {} 个警告", result.warnings.len()),
        _ => "未知状态".to_string(),
    };

    ValidateStrategyConfigResponse {
        is_valid: result.is_valid,
        validated_at: chrono::Utc::now().to_rfc3339(),
        summary: ValidationSummary {
            total_errors: result.errors.len(),
            critical_errors,
            high_risk_errors,
            total_warnings: result.warnings.len(),
            total_suggestions: result.suggestions.len(),
            status: status.to_string(),
            status_message,
        },
        errors: result.errors.into_iter().map(|e| ValidationErrorResponse {
            field: e.field,
            message: e.message,
            error_type: format!("{:?}", e.error_type),
            severity: match e.error_type {
                StrategyValidationErrorType::Required |
                StrategyValidationErrorType::Logic => "critical".to_string(),
                StrategyValidationErrorType::Range |
                StrategyValidationErrorType::Conflict => "high".to_string(),
                StrategyValidationErrorType::Format => "medium".to_string(),
            },
            error_code: None,
        }).collect(),
        warnings: result.warnings.into_iter().map(|w| ValidationWarningResponse {
            field: w.field,
            message: w.message,
            suggestion: w.suggestion,
            warning_type: "general".to_string(),
        }).collect(),
        suggestions: result.suggestions.into_iter().map(|s| ValidationSuggestionResponse {
            field: "general".to_string(),
            current_value: "".to_string(),
            suggested_value: s,
            reason: "系统建议".to_string(),
            suggestion_type: "optimization".to_string(),
        }).collect(),
        performance_estimate: None, // 暂时不提供性能预估
    }
}



/// 策略预览响应
#[derive(Debug, Serialize)]
pub struct StrategyPreviewResponse {
    /// 策略类型
    pub strategy_type: String,
    /// 预览数据
    pub preview_data: StrategyPreviewData,
    /// 生成时间
    pub generated_at: String,
}

/// 策略预览数据
#[derive(Debug, Serialize)]
pub struct StrategyPreviewData {
    /// 网格信息（针对网格策略）
    pub grid_info: Option<GridPreviewInfo>,
    /// 资金分配
    pub capital_allocation: CapitalAllocation,
    /// 预期表现
    pub expected_performance: ExpectedPerformance,
}

/// 网格预览信息
#[derive(Debug, Serialize)]
pub struct GridPreviewInfo {
    /// 网格数量
    pub grid_count: u32,
    /// 价格范围
    pub price_range: PriceRange,
    /// 网格间距
    pub grid_spacing: String,
    /// 网格价格列表
    pub grid_prices: Vec<String>,
}

/// 价格范围
#[derive(Debug, Serialize)]
pub struct PriceRange {
    /// 上限价格
    pub upper_price: String,
    /// 下限价格
    pub lower_price: String,
    /// 中间价格
    pub middle_price: String,
    /// 价格范围百分比
    pub range_percentage: String,
}

/// 资金分配
#[derive(Debug, Serialize)]
pub struct CapitalAllocation {
    /// 总资金
    pub total_capital: String,
    /// 买单资金
    pub buy_orders_capital: String,
    /// 卖单资金
    pub sell_orders_capital: String,
    /// 预留资金
    pub reserved_capital: String,
    /// 资金利用率
    pub capital_utilization: String,
}

/// 预期表现
#[derive(Debug, Serialize)]
pub struct ExpectedPerformance {
    /// 预期日收益率
    pub expected_daily_return: String,
    /// 预期月收益率
    pub expected_monthly_return: String,
    /// 最大回撤预估
    pub estimated_max_drawdown: String,
    /// 胜率预估
    pub estimated_win_rate: String,
}

/// 生成策略预览
fn generate_strategy_preview(
    strategy_type: &str,
    config: &serde_json::Value,
    initial_capital: Decimal,
) -> Result<StrategyPreviewResponse, ApiError> {
    match strategy_type {
        "grid" => generate_grid_strategy_preview(config, initial_capital),
        "asymmetric_volatility_grid_strategy" => generate_asymmetric_grid_strategy_preview(config, initial_capital),
        _ => Err(ApiError::BadRequest(format!("不支持的策略类型: {}", strategy_type))),
    }
}

/// 生成网格策略预览
fn generate_grid_strategy_preview(
    config: &serde_json::Value,
    initial_capital: Decimal,
) -> Result<StrategyPreviewResponse, ApiError> {
    let config_obj = config.get("config")
        .ok_or_else(|| ApiError::BadRequest("缺少config字段".to_string()))?;

    let grid_count = config_obj.get("grid_levels")
        .and_then(|v| v.as_u64())
        .unwrap_or(10) as u32;

    let upper_price = config_obj.get("upper_price")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(300, 0)))
        .unwrap_or(Decimal::new(300, 0));

    let lower_price = config_obj.get("lower_price")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(200, 0)))
        .unwrap_or(Decimal::new(200, 0));

    let base_order_size = config_obj.get("base_order_size")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(1, 1)))
        .unwrap_or(Decimal::new(1, 1));

    // 计算网格信息
    let price_range_total = upper_price - lower_price;
    let grid_spacing = price_range_total / Decimal::from(grid_count);
    let middle_price = (upper_price + lower_price) / Decimal::TWO;
    let range_percentage = (price_range_total / middle_price) * Decimal::from(100);

    // 生成网格价格列表
    let mut grid_prices = Vec::new();
    for i in 0..=grid_count {
        let price = lower_price + grid_spacing * Decimal::from(i);
        grid_prices.push(price.to_string());
    }

    // 计算资金分配
    let buy_orders_count = (grid_count + 1) / 2;
    let buy_orders_capital = Decimal::from(buy_orders_count) * base_order_size * middle_price;
    let reserved_capital = initial_capital * Decimal::new(1, 1); // 10%预留
    let capital_utilization = (buy_orders_capital / initial_capital) * Decimal::from(100);

    let preview_data = StrategyPreviewData {
        grid_info: Some(GridPreviewInfo {
            grid_count,
            price_range: PriceRange {
                upper_price: upper_price.to_string(),
                lower_price: lower_price.to_string(),
                middle_price: middle_price.to_string(),
                range_percentage: format!("{:.2}%", range_percentage),
            },
            grid_spacing: grid_spacing.to_string(),
            grid_prices,
        }),
        capital_allocation: CapitalAllocation {
            total_capital: initial_capital.to_string(),
            buy_orders_capital: buy_orders_capital.to_string(),
            sell_orders_capital: "0".to_string(), // 网格策略初始时没有卖单
            reserved_capital: reserved_capital.to_string(),
            capital_utilization: format!("{:.2}%", capital_utilization),
        },
        expected_performance: ExpectedPerformance {
            expected_daily_return: "0.5%".to_string(), // 示例值
            expected_monthly_return: "15%".to_string(), // 示例值
            estimated_max_drawdown: "5%".to_string(), // 示例值
            estimated_win_rate: "65%".to_string(), // 示例值
        },
    };

    Ok(StrategyPreviewResponse {
        strategy_type: "grid".to_string(),
        preview_data,
        generated_at: chrono::Utc::now().to_rfc3339(),
    })
}

/// 生成非对称网格策略预览
fn generate_asymmetric_grid_strategy_preview(
    config: &serde_json::Value,
    initial_capital: Decimal,
) -> Result<StrategyPreviewResponse, ApiError> {
    let config_obj = config.get("config")
        .ok_or_else(|| ApiError::BadRequest("缺少config字段".to_string()))?;

    let down_grid_count = config_obj.get("down_grid_count")
        .and_then(|v| v.as_u64())
        .unwrap_or(10) as u32;

    let up_grid_count = config_obj.get("up_grid_count")
        .and_then(|v| v.as_u64())
        .unwrap_or(6) as u32;

    let down_range_start = config_obj.get("down_range_start")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(-2, 2)))
        .unwrap_or(Decimal::new(-2, 2));

    let down_range_end = config_obj.get("down_range_end")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(-5, 2)))
        .unwrap_or(Decimal::new(-5, 2));

    let up_range_start = config_obj.get("up_range_start")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(2, 2)))
        .unwrap_or(Decimal::new(2, 2));

    let up_range_end = config_obj.get("up_range_end")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(8, 2)))
        .unwrap_or(Decimal::new(8, 2));

    let down_base_quantity = config_obj.get("down_base_quantity")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(2, 2)))
        .unwrap_or(Decimal::new(2, 2));

    let _volatility_multiplier = config_obj.get("volatility_multiplier")
        .and_then(|v| v.as_f64())
        .map(|f| Decimal::from_f64_retain(f).unwrap_or(Decimal::new(12, 1)))
        .unwrap_or(Decimal::new(12, 1));

    // 计算网格信息
    let total_grid_count = down_grid_count + up_grid_count;
    let down_range_total = down_range_end - down_range_start;
    let up_range_total = up_range_end - up_range_start;
    let total_range = up_range_end - down_range_start;

    // 假设基准价格为50000 USDT（BTC示例）
    let base_price = Decimal::new(50000, 0);
    let down_spacing = down_range_total / Decimal::from(down_grid_count);
    let up_spacing = up_range_total / Decimal::from(up_grid_count);

    // 生成网格价格列表
    let mut grid_prices = Vec::new();

    // 下跌网格
    for i in 0..down_grid_count {
        let range_pct = down_range_start + down_spacing * Decimal::from(i);
        let price = base_price * (Decimal::ONE + range_pct);
        grid_prices.push(format!("{:.2}", price));
    }

    // 上涨网格
    for i in 0..up_grid_count {
        let range_pct = up_range_start + up_spacing * Decimal::from(i);
        let price = base_price * (Decimal::ONE + range_pct);
        grid_prices.push(format!("{:.2}", price));
    }

    // 计算资金分配
    let down_orders_capital = Decimal::from(down_grid_count) * down_base_quantity * base_price;
    let reserved_capital = initial_capital * Decimal::new(1, 1); // 10%预留
    let capital_utilization = (down_orders_capital / initial_capital) * Decimal::from(100);

    let preview_data = StrategyPreviewData {
        grid_info: Some(GridPreviewInfo {
            grid_count: total_grid_count,
            price_range: PriceRange {
                upper_price: format!("{:.2}", base_price * (Decimal::ONE + up_range_end)),
                lower_price: format!("{:.2}", base_price * (Decimal::ONE + down_range_start)),
                middle_price: format!("{:.2}", base_price),
                range_percentage: format!("{:.2}%", total_range * Decimal::from(100)),
            },
            grid_spacing: format!("非对称间距: 下跌{:.2}%, 上涨{:.2}%",
                                down_spacing * Decimal::from(100),
                                up_spacing * Decimal::from(100)),
            grid_prices,
        }),
        capital_allocation: CapitalAllocation {
            total_capital: initial_capital.to_string(),
            buy_orders_capital: down_orders_capital.to_string(),
            sell_orders_capital: "0".to_string(), // 初始时没有卖单
            reserved_capital: reserved_capital.to_string(),
            capital_utilization: format!("{:.2}%", capital_utilization),
        },
        expected_performance: ExpectedPerformance {
            expected_daily_return: "0.8%".to_string(), // 非对称策略预期更高收益
            expected_monthly_return: "24%".to_string(),
            estimated_max_drawdown: "8%".to_string(),
            estimated_win_rate: "70%".to_string(), // 非对称策略胜率更高
        },
    };

    Ok(StrategyPreviewResponse {
        strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
        preview_data,
        generated_at: chrono::Utc::now().to_rfc3339(),
    })
}

// ============================================================================
// 新增策略类型验证函数
// ============================================================================

/// DCA策略配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DcaStrategyConfig {
    pub trading_pair: String,
    pub investment_amount: f64,
    pub investment_interval: String, // "1h", "1d", "1w", etc.
    pub max_investment_count: Option<u32>,
    pub price_threshold: Option<f64>, // 价格阈值，低于此价格才投资
    pub stop_loss_percentage: Option<f64>,
    pub take_profit_percentage: Option<f64>,
}

/// 验证DCA策略配置
fn validate_dca_strategy_config(config: &serde_json::Value) -> ApiResult<ValidationResult> {
    let config: DcaStrategyConfig = serde_json::from_value(config.clone())
        .map_err(|e| ApiError::BadRequest(format!("DCA策略配置解析失败: {}", e)))?;

    let mut result = ValidationResult::success();

    // 验证交易对
    if config.trading_pair.is_empty() {
        result.add_error(StrategyValidationError::required("trading_pair", "交易对不能为空"));
    }

    // 验证投资金额
    if config.investment_amount <= 0.0 {
        result.add_error(StrategyValidationError::range("investment_amount", "投资金额必须大于0"));
    } else if config.investment_amount < 10.0 {
        result.add_warning(ValidationWarning {
            field: "investment_amount".to_string(),
            message: "投资金额较小，可能影响策略效果".to_string(),
            suggestion: Some("建议投资金额至少为10 USDT".to_string()),
        });
    }

    // 验证投资间隔
    let valid_intervals = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"];
    if !valid_intervals.contains(&config.investment_interval.as_str()) {
        result.add_error(StrategyValidationError::format("investment_interval",
            &format!("无效的投资间隔: {}，支持的间隔: {:?}", config.investment_interval, valid_intervals)));
    }

    // 验证最大投资次数
    if let Some(max_count) = config.max_investment_count {
        if max_count == 0 {
            result.add_error(StrategyValidationError::range("max_investment_count", "最大投资次数必须大于0"));
        } else if max_count > 1000 {
            result.add_warning(ValidationWarning {
                field: "max_investment_count".to_string(),
                message: "最大投资次数过大，可能导致资金风险".to_string(),
                suggestion: Some("建议最大投资次数不超过100次".to_string()),
            });
        }
    }

    // 验证止损百分比
    if let Some(stop_loss) = config.stop_loss_percentage {
        if stop_loss <= 0.0 || stop_loss >= 100.0 {
            result.add_error(StrategyValidationError::range("stop_loss_percentage", "止损百分比必须在0-100之间"));
        }
    }

    // 验证止盈百分比
    if let Some(take_profit) = config.take_profit_percentage {
        if take_profit <= 0.0 || take_profit >= 1000.0 {
            result.add_error(StrategyValidationError::range("take_profit_percentage", "止盈百分比必须在0-1000之间"));
        }
    }

    Ok(result)
}

/// 动量策略配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MomentumStrategyConfig {
    pub trading_pair: String,
    pub momentum_period: u32,
    pub entry_threshold: f64,
    pub exit_threshold: f64,
    pub position_size: f64,
    pub max_position_size: Option<f64>,
    pub stop_loss_percentage: Option<f64>,
    pub take_profit_percentage: Option<f64>,
    pub lookback_period: Option<u32>,
}

/// 验证动量策略配置
fn validate_momentum_strategy_config(config: &serde_json::Value) -> ApiResult<ValidationResult> {
    let config: MomentumStrategyConfig = serde_json::from_value(config.clone())
        .map_err(|e| ApiError::BadRequest(format!("动量策略配置解析失败: {}", e)))?;

    let mut result = ValidationResult::success();

    // 验证交易对
    if config.trading_pair.is_empty() {
        result.add_error(StrategyValidationError::required("trading_pair", "交易对不能为空"));
    }

    // 验证动量周期
    if config.momentum_period == 0 {
        result.add_error(StrategyValidationError::range("momentum_period", "动量周期必须大于0"));
    } else if config.momentum_period < 5 {
        result.add_warning(ValidationWarning {
            field: "momentum_period".to_string(),
            message: "动量周期过短，可能产生过多噪音信号".to_string(),
            suggestion: Some("建议动量周期至少为5".to_string()),
        });
    } else if config.momentum_period > 200 {
        result.add_warning(ValidationWarning {
            field: "momentum_period".to_string(),
            message: "动量周期过长，可能错过交易机会".to_string(),
            suggestion: Some("建议动量周期不超过200".to_string()),
        });
    }

    // 验证入场阈值
    if config.entry_threshold <= 0.0 {
        result.add_error(StrategyValidationError::range("entry_threshold", "入场阈值必须大于0"));
    } else if config.entry_threshold > 0.5 {
        result.add_warning(ValidationWarning {
            field: "entry_threshold".to_string(),
            message: "入场阈值过高，可能错过交易机会".to_string(),
            suggestion: Some("建议入场阈值不超过0.1 (10%)".to_string()),
        });
    }

    // 验证出场阈值
    if config.exit_threshold <= 0.0 {
        result.add_error(StrategyValidationError::range("exit_threshold", "出场阈值必须大于0"));
    }

    // 验证阈值关系
    if config.exit_threshold >= config.entry_threshold {
        result.add_error(StrategyValidationError::logic("exit_threshold", "出场阈值应该小于入场阈值"));
    }

    // 验证持仓大小
    if config.position_size <= 0.0 {
        result.add_error(StrategyValidationError::range("position_size", "持仓大小必须大于0"));
    } else if config.position_size > 1.0 {
        result.add_warning(ValidationWarning {
            field: "position_size".to_string(),
            message: "持仓大小超过100%，风险较高".to_string(),
            suggestion: Some("建议持仓大小不超过0.5 (50%)".to_string()),
        });
    }

    Ok(result)
}

/// 均值回归策略配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeanReversionStrategyConfig {
    pub trading_pair: String,
    pub lookback_period: u32,
    pub deviation_threshold: f64,
    pub position_size: f64,
    pub max_positions: Option<u32>,
    pub rebalance_threshold: Option<f64>,
    pub stop_loss_percentage: Option<f64>,
    pub take_profit_percentage: Option<f64>,
}

/// 验证均值回归策略配置
fn validate_mean_reversion_strategy_config(config: &serde_json::Value) -> ApiResult<ValidationResult> {
    let config: MeanReversionStrategyConfig = serde_json::from_value(config.clone())
        .map_err(|e| ApiError::BadRequest(format!("均值回归策略配置解析失败: {}", e)))?;

    let mut result = ValidationResult::success();

    // 验证交易对
    if config.trading_pair.is_empty() {
        result.add_error(StrategyValidationError::required("trading_pair", "交易对不能为空"));
    }

    // 验证回看周期
    if config.lookback_period == 0 {
        result.add_error(StrategyValidationError::range("lookback_period", "回看周期必须大于0"));
    } else if config.lookback_period < 10 {
        result.add_warning(ValidationWarning {
            field: "lookback_period".to_string(),
            message: "回看周期过短，均值计算可能不稳定".to_string(),
            suggestion: Some("建议回看周期至少为10".to_string()),
        });
    } else if config.lookback_period > 500 {
        result.add_warning(ValidationWarning {
            field: "lookback_period".to_string(),
            message: "回看周期过长，可能对市场变化反应迟钝".to_string(),
            suggestion: Some("建议回看周期不超过200".to_string()),
        });
    }

    // 验证偏差阈值
    if config.deviation_threshold <= 0.0 {
        result.add_error(StrategyValidationError::range("deviation_threshold", "偏差阈值必须大于0"));
    } else if config.deviation_threshold < 1.0 {
        result.add_warning(ValidationWarning {
            field: "deviation_threshold".to_string(),
            message: "偏差阈值过小，可能产生过多交易信号".to_string(),
            suggestion: Some("建议偏差阈值至少为1.0".to_string()),
        });
    } else if config.deviation_threshold > 5.0 {
        result.add_warning(ValidationWarning {
            field: "deviation_threshold".to_string(),
            message: "偏差阈值过大，可能错过交易机会".to_string(),
            suggestion: Some("建议偏差阈值不超过3.0".to_string()),
        });
    }

    // 验证持仓大小
    if config.position_size <= 0.0 {
        result.add_error(StrategyValidationError::range("position_size", "持仓大小必须大于0"));
    } else if config.position_size > 0.5 {
        result.add_warning(ValidationWarning {
            field: "position_size".to_string(),
            message: "单次持仓大小较大，风险较高".to_string(),
            suggestion: Some("建议单次持仓大小不超过0.2 (20%)".to_string()),
        });
    }

    // 验证最大持仓数
    if let Some(max_positions) = config.max_positions {
        if max_positions == 0 {
            result.add_error(StrategyValidationError::range("max_positions", "最大持仓数必须大于0"));
        } else if max_positions > 20 {
            result.add_warning(ValidationWarning {
                field: "max_positions".to_string(),
                message: "最大持仓数过多，可能难以管理".to_string(),
                suggestion: Some("建议最大持仓数不超过10".to_string()),
            });
        }
    }

    Ok(result)
}