//! WebSocket管理API处理器
//!
//! 提供WebSocket连接管理、统计信息和配置的API端点

use axum::{
    extract::{State, Path, Query},
    response::Json,
};
use serde::{Deserialize, Serialize};
use crate::{
    state::AppState,
    error::ApiError,
    websocket::ConnectionId,
};

/// WebSocket连接查询参数
#[derive(Debug, Deserialize)]
pub struct WebSocketConnectionQuery {
    /// 连接类型过滤
    pub connection_type: Option<String>,
    /// 用户ID过滤
    pub user_id: Option<String>,
    /// 是否包含详细信息
    pub detailed: Option<bool>,
}

/// 频道管理请求
#[derive(Debug, Deserialize)]
pub struct ChannelManagementRequest {
    /// 频道名称
    pub name: String,
    /// 是否需要认证
    pub requires_auth: Option<bool>,
    /// 最大订阅者数量
    pub max_subscribers: Option<usize>,
    /// 消息缓存大小
    pub buffer_size: Option<usize>,
    /// 消息过期时间（秒）
    pub message_ttl_seconds: Option<u64>,
}

/// 消息广播请求
#[derive(Debug, Deserialize)]
pub struct BroadcastMessageRequest {
    /// 目标频道
    pub channel: String,
    /// 事件类型
    pub event: String,
    /// 消息数据
    pub data: serde_json::Value,
    /// 目标连接类型
    pub target_connection_types: Option<Vec<String>>,
    /// 目标用户ID列表
    pub target_users: Option<Vec<String>>,
}

/// 连接管理请求
#[derive(Debug, Deserialize)]
pub struct ConnectionManagementRequest {
    /// 操作类型
    pub action: String, // "disconnect", "update_permissions", "force_subscribe"
    /// 连接ID列表
    pub connection_ids: Option<Vec<ConnectionId>>,
    /// 新的权限设置
    pub permissions: Option<serde_json::Value>,
    /// 强制订阅的频道
    pub channels: Option<Vec<String>>,
}

/// WebSocket配置响应
#[derive(Debug, Serialize)]
pub struct WebSocketConfigResponse {
    /// 服务器配置
    pub server_config: ServerConfig,
    /// 频道配置列表
    pub channels: Vec<ChannelConfigResponse>,
    /// 连接类型配置
    pub connection_types: Vec<ConnectionTypeConfig>,
}

/// 服务器配置
#[derive(Debug, Serialize)]
pub struct ServerConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 是否启用压缩
    pub compression_enabled: bool,
    /// 消息队列大小
    pub message_queue_size: usize,
}

/// 频道配置响应
#[derive(Debug, Serialize)]
pub struct ChannelConfigResponse {
    /// 频道名称
    pub name: String,
    /// 是否需要认证
    pub requires_auth: bool,
    /// 最大订阅者数量
    pub max_subscribers: Option<usize>,
    /// 当前订阅者数量
    pub current_subscribers: usize,
    /// 消息缓存大小
    pub buffer_size: usize,
    /// 消息过期时间（秒）
    pub message_ttl_seconds: u64,
    /// 是否活跃
    pub active: bool,
}

/// 连接类型配置
#[derive(Debug, Serialize)]
pub struct ConnectionTypeConfig {
    /// 连接类型
    pub connection_type: String,
    /// 允许的频道
    pub allowed_channels: Vec<String>,
    /// 最大订阅数
    pub max_subscriptions: usize,
    /// 消息速率限制
    pub rate_limit: u32,
    /// 当前连接数
    pub current_connections: usize,
}

/// 获取WebSocket服务器统计信息
pub async fn get_websocket_stats(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let stats = state.websocket_server.get_server_stats().await;

    Ok(Json(serde_json::json!({
        "success": true,
        "data": stats
    })))
}

/// 获取WebSocket连接列表
pub async fn get_websocket_connections(
    State(state): State<AppState>,
    Query(query): Query<WebSocketConnectionQuery>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let connections = state.websocket_server.get_detailed_connections().await;

    // 根据查询参数过滤连接（简化版过滤）
    let filtered_connections = if query.connection_type.is_some() || query.user_id.is_some() {
        connections.into_iter()
            .filter(|conn| {
                if let Some(ref conn_type) = query.connection_type {
                    conn.get("connection_type").and_then(|v| v.as_str()).unwrap_or("User").to_lowercase() == conn_type.to_lowercase()
                } else {
                    true
                }
            })
            .filter(|conn| {
                if let Some(ref user_id) = query.user_id {
                    conn.get("user_id").and_then(|v| v.as_str()) == Some(user_id)
                } else {
                    true
                }
            })
            .collect::<Vec<_>>()
    } else {
        connections
    };

    Ok(Json(serde_json::json!({
        "success": true,
        "data": {
            "connections": filtered_connections,
            "total_count": filtered_connections.len(),
            "filtered": query.connection_type.is_some() || query.user_id.is_some()
        }
    })))
}

/// 获取特定WebSocket连接信息
pub async fn get_websocket_connection(
    State(state): State<AppState>,
    Path(connection_id): Path<ConnectionId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let connections = state.websocket_server.get_detailed_connections().await;

    if let Some(connection) = connections.into_iter().find(|c| {
        c.get("connection_id").and_then(|v| v.as_str()).and_then(|s| s.parse::<ConnectionId>().ok()) == Some(connection_id)
    }) {
        Ok(Json(serde_json::json!({
            "success": true,
            "data": connection
        })))
    } else {
        Err(ApiError::NotFound("连接不存在".to_string()))
    }
}

/// 断开WebSocket连接
pub async fn disconnect_websocket_connection(
    State(_state): State<AppState>,
    Path(connection_id): Path<ConnectionId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    // 这里需要实现连接断开逻辑
    // 由于当前架构限制，我们返回一个模拟响应

    Ok(Json(serde_json::json!({
        "success": true,
        "data": {
            "connection_id": connection_id,
            "message": "连接断开请求已发送"
        }
    })))
}

/// 广播消息到WebSocket连接
pub async fn broadcast_websocket_message(
    State(state): State<AppState>,
    Json(request): Json<BroadcastMessageRequest>,
) -> Result<Json<serde_json::Value>, ApiError> {
    // 广播消息
    if let Err(e) = state.websocket_server.broadcast_message_to_channel(
        &request.channel,
        &request.event,
        request.data,
    ).await {
        return Err(ApiError::Internal(format!("广播消息失败: {}", e)));
    }

    Ok(Json(serde_json::json!({
        "success": true,
        "data": {
            "message": "消息广播成功",
            "channel": request.channel,
            "event": request.event
        }
    })))
}

/// 获取WebSocket配置
pub async fn get_websocket_config(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let server_config = ServerConfig {
        max_connections: 10000,
        heartbeat_interval: 30,
        connection_timeout: 300,
        compression_enabled: true,
        message_queue_size: 1000,
    };

    // 获取实际的WebSocket配置
    let ws_config = state.websocket_server.get_config().await;

    let channels = ws_config.channels.iter().map(|ch| {
        ChannelConfigResponse {
            name: ch.name.clone(),
            requires_auth: ch.requires_auth,
            max_subscribers: ch.max_subscribers,
            current_subscribers: ch.current_subscribers,
            buffer_size: ch.buffer_size,
            message_ttl_seconds: ch.message_ttl,
            active: true,
        }
    }).collect::<Vec<_>>();

    let connection_types = vec![
        ConnectionTypeConfig {
            connection_type: "User".to_string(),
            allowed_channels: vec!["market_data".to_string(), "system".to_string()],
            max_subscriptions: 5,
            rate_limit: 50,
            current_connections: 0,
        },
        ConnectionTypeConfig {
            connection_type: "Admin".to_string(),
            allowed_channels: vec![
                "engines".to_string(),
                "orders".to_string(),
                "trades".to_string(),
                "market_data".to_string(),
                "system".to_string(),
                "admin".to_string()
            ],
            max_subscriptions: 50,
            rate_limit: 1000,
            current_connections: 0,
        },
    ];

    let config = WebSocketConfigResponse {
        server_config,
        channels,
        connection_types,
    };

    Ok(Json(serde_json::json!({
        "success": true,
        "data": config
    })))
}

/// 清理非活跃WebSocket连接
pub async fn cleanup_websocket_connections(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let removed_count = state.websocket_server.cleanup_inactive_connections().await;

    Ok(Json(serde_json::json!({
        "success": true,
        "data": {
            "removed_connections": removed_count,
            "timeout_seconds": 300,
            "message": format!("已清理 {} 个非活跃连接", removed_count)
        }
    })))
}
