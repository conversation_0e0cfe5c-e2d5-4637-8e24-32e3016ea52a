//! 策略执行相关的API处理器

use axum::{
    extract::{Path, State},
    response::Json,
};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use tracing::{info, error};

use crate::state::AppState;
use crate::error::{ApiError, ApiResult, ApiResponse};
use crate::execution_engine::{StrategyExecutionStatus, MarketData};

/// 策略执行状态响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionStatusResponse {
    pub strategy_id: String,
    pub status: StrategyExecutionStatus,
    pub started_at: Option<String>,
    pub stopped_at: Option<String>,
    pub uptime: Option<String>,
}
use crate::common_types::{StrategyPerformanceResponse};

/// 运行中策略列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct RunningStrategiesResponse {
    pub strategies: Vec<String>,
    pub total_count: usize,
}

/// 所有策略执行状态响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AllExecutionStatusResponse {
    pub strategies: Vec<ExecutionStatusResponse>,
    pub total_count: usize,
}

/// 市场数据推送请求
#[derive(Debug, Serialize, Deserialize)]
pub struct MarketDataPushRequest {
    pub symbol: String,
    pub price: String,
    pub volume: String,
}

/// 获取策略执行状态
pub async fn get_strategy_execution_status(
    State(state): State<AppState>,
    Path(strategy_id): Path<String>,
) -> ApiResult<Json<ApiResponse<ExecutionStatusResponse>>> {
    let strategy_id = Uuid::parse_str(&strategy_id)
        .map_err(|_| ApiError::BadRequest("Invalid strategy ID format".to_string()))?;

    info!("获取策略执行状态，ID: {}", strategy_id);

    let status = state.strategy_service.get_execution_status(strategy_id).await?;

    let response_data = ExecutionStatusResponse {
        strategy_id: strategy_id.to_string(),
        status: status.unwrap_or(StrategyExecutionStatus::Created),
        started_at: None, // TODO: 从执行引擎获取实际时间
        stopped_at: None,
        uptime: None,
    };

    let response = ApiResponse::success(response_data, "获取策略执行状态成功");
    Ok(Json(response))
}

/// 获取策略实时性能
pub async fn get_strategy_real_time_performance(
    State(state): State<AppState>,
    Path(strategy_id): Path<String>,
) -> ApiResult<Json<ApiResponse<StrategyPerformanceResponse>>> {
    let strategy_id = Uuid::parse_str(&strategy_id)
        .map_err(|_| ApiError::BadRequest("Invalid strategy ID format".to_string()))?;

    info!("获取策略实时性能，ID: {}", strategy_id);

    let performance = state.strategy_service.get_strategy_real_time_performance(strategy_id).await?;

    let response = if let Some(perf) = performance {
        StrategyPerformanceResponse {
            strategy_id: strategy_id.to_string(),
            total_trades: perf.total_trades,
            winning_trades: perf.winning_trades,
            losing_trades: perf.losing_trades,
            total_pnl: perf.total_pnl.to_string(),
            win_rate: (perf.win_rate * rust_decimal::Decimal::new(100, 0)).to_string(),
            sharpe_ratio: Some("0".to_string()), //TODO
            max_drawdown: perf.max_drawdown.to_string(),
            current_positions: perf.current_positions,
            last_updated: perf.last_updated.to_rfc3339(),
        }
    } else {
        StrategyPerformanceResponse {
            strategy_id: strategy_id.to_string(),
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            total_pnl: "0".to_string(),
            win_rate: "0".to_string(),
            sharpe_ratio: Some("0".to_string()), //TODO
            max_drawdown: "0".to_string(),
            current_positions: 0,
            last_updated: chrono::Utc::now().to_rfc3339(),
        }
    };

    let api_response = ApiResponse::success(response, "获取策略实时性能成功");
    Ok(Json(api_response))
}

/// 获取所有运行中的策略
pub async fn get_running_strategies(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<RunningStrategiesResponse>>> {
    info!("获取所有运行中的策略");

    let strategies = state.strategy_service.get_running_strategies().await?;

    let response_data = RunningStrategiesResponse {
        total_count: strategies.len(),
        strategies: strategies.into_iter().map(|id| id.to_string()).collect(),
    };

    let response = ApiResponse::success(response_data, "获取运行中策略列表成功");
    Ok(Json(response))
}

/// 获取所有策略的执行状态
pub async fn get_all_execution_status(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<AllExecutionStatusResponse>>> {
    info!("获取所有策略的执行状态");

    let status_map = state.strategy_service.get_all_execution_status().await?;

    let strategies = status_map.into_iter()
        .map(|(id, status)| ExecutionStatusResponse {
            strategy_id: id.to_string(),
            status,
            started_at: None, // TODO: 从执行引擎获取实际时间
            stopped_at: None,
            uptime: None,
        })
        .collect::<Vec<_>>();

    let response_data = AllExecutionStatusResponse {
        total_count: strategies.len(),
        strategies,
    };

    let response = ApiResponse::success(response_data, "获取所有策略执行状态成功");
    Ok(Json(response))
}

/// 推送市场数据到策略执行引擎
pub async fn push_market_data(
    State(_state): State<AppState>,
    Json(request): Json<MarketDataPushRequest>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("推送市场数据: {} @ {}", request.symbol, request.price);

    let price = request.price.parse::<rust_decimal::Decimal>()
        .map_err(|_| ApiError::BadRequest("Invalid price format".to_string()))?;

    let volume = request.volume.parse::<rust_decimal::Decimal>()
        .map_err(|_| ApiError::BadRequest("Invalid volume format".to_string()))?;

    let _market_data = MarketData {
        symbol: request.symbol,
        price,
        volume,
        timestamp: chrono::Utc::now(),
    };

    // TODO: 将市场数据推送到执行引擎
    // 这里需要访问执行引擎实例
    // state.execution_engine.broadcast_market_data(market_data).await?;

    let response = ApiResponse::success((), "市场数据推送成功");
    Ok(Json(response))
}

/// 强制停止所有策略
pub async fn emergency_stop_all_strategies(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("紧急停止所有策略");

    let running_strategies = state.strategy_service.get_running_strategies().await?;

    for strategy_id in running_strategies {
        if let Err(e) = state.strategy_service.stop_strategy_execution(strategy_id).await {
            error!("停止策略失败 {}: {:?}", strategy_id, e);
        }
    }

    let response = ApiResponse::success((), "所有策略已紧急停止");
    Ok(Json(response))
}
