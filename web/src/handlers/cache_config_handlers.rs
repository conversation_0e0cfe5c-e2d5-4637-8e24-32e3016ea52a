//! 缓存配置API处理器
//!
//! 整合缓存配置管理和缓存操作功能

use axum::{
    extract::{Query, State},
    response::Json,
};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use tracing::{info, error};

use crate::{
    state::AppState,
    error::{ApiResult, ApiResponse},
    services::config::traits::ConfigService,
};
use sigmax_core::CacheConfig;

// ============================================================================
// 请求和响应数据结构
// ============================================================================

/// 缓存配置请求
#[derive(Debug, Deserialize)]
pub struct CacheConfigRequest {
    pub config: CacheConfig,
}

/// 缓存配置响应
#[derive(Debug, Serialize)]
pub struct CacheConfigResponse {
    pub config: CacheConfig,
    pub last_updated: Option<DateTime<Utc>>,
    pub is_active: bool,
}

/// 缓存清理请求
#[derive(Debug, Deserialize)]
pub struct CacheClearRequest {
    pub cache_types: Option<Vec<String>>,
    pub key_patterns: Option<Vec<String>>,
    pub expired_only: Option<bool>,
    pub force: Option<bool>,
}

/// 缓存清理响应
#[derive(Debug, Serialize)]
pub struct CacheClearResponse {
    pub cleared_keys: u64,
    pub freed_memory_mb: f64,
    pub cache_types: Vec<String>,
    pub expired_only: bool,
    pub processing_time_ms: u64,
}

/// 缓存预热请求
#[derive(Debug, Deserialize)]
pub struct CacheWarmRequest {
    pub cache_types: Vec<String>,
    pub priority_keys: Option<Vec<String>>,
    pub batch_size: Option<u32>,
    pub max_concurrent: Option<u32>,
}

/// 缓存预热响应
#[derive(Debug, Serialize)]
pub struct CacheWarmResponse {
    pub success: bool,
    pub message: String,
    pub cache_types: Vec<String>,
    pub estimated_keys: u32,
    pub batch_size: u32,
    pub max_concurrent: u32,
    pub estimated_time_minutes: u32,
    pub status: String,
}

/// 缓存键查询参数
#[derive(Debug, Deserialize)]
pub struct CacheKeyQueryParams {
    pub cache_type: Option<String>,
    pub pattern: Option<String>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

/// 缓存键信息
#[derive(Debug, Serialize)]
pub struct CacheKeyInfo {
    pub key: String,
    pub cache_type: String,
    pub size_bytes: u64,
    pub ttl_seconds: Option<u64>,
    pub access_count: u64,
    pub last_accessed: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

// ============================================================================
// 缓存配置管理API
// ============================================================================

/// 获取缓存配置
/// GET /api/v1/config/cache
pub async fn get_cache_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<CacheConfigResponse>>> {
    info!("📋 Getting cache configuration");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.get_config().await {
        Ok(config) => {
            let response = CacheConfigResponse {
                config,
                last_updated: Some(Utc::now()),
                is_active: true,
            };
            
            info!("✅ Cache configuration retrieved successfully");
            Ok(Json(ApiResponse::success(response, "Cache configuration retrieved successfully")))
        }
        Err(e) => {
            error!("❌ Failed to get cache configuration: {}", e);
            Err(e)
        }
    }
}

/// 保存缓存配置
/// POST /api/v1/config/cache
pub async fn save_cache_config(
    State(state): State<AppState>,
    Json(request): Json<CacheConfigRequest>,
) -> ApiResult<Json<ApiResponse<CacheConfigResponse>>> {
    info!("💾 Saving cache configuration");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.save_config(&request.config).await {
        Ok(_) => {
            let response = CacheConfigResponse {
                config: request.config,
                last_updated: Some(Utc::now()),
                is_active: true,
            };
            
            info!("✅ Cache configuration saved successfully");
            Ok(Json(ApiResponse::success(response, "Cache configuration saved successfully")))
        }
        Err(e) => {
            error!("❌ Failed to save cache configuration: {}", e);
            Err(e)
        }
    }
}

/// 更新缓存配置
/// PUT /api/v1/config/cache
pub async fn update_cache_config(
    State(state): State<AppState>,
    Json(request): Json<CacheConfigRequest>,
) -> ApiResult<Json<ApiResponse<CacheConfigResponse>>> {
    info!("🔄 Updating cache configuration");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.update_config(&request.config).await {
        Ok(_) => {
            let response = CacheConfigResponse {
                config: request.config,
                last_updated: Some(Utc::now()),
                is_active: true,
            };
            
            info!("✅ Cache configuration updated successfully");
            Ok(Json(ApiResponse::success(response, "Cache configuration updated successfully")))
        }
        Err(e) => {
            error!("❌ Failed to update cache configuration: {}", e);
            Err(e)
        }
    }
}

/// 删除缓存配置
/// DELETE /api/v1/config/cache
pub async fn delete_cache_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<serde_json::Value>>> {
    info!("🗑️ Deleting cache configuration");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.delete_config().await {
        Ok(_) => {
            info!("✅ Cache configuration deleted successfully");
            Ok(Json(ApiResponse::success(serde_json::json!({
                "message": "Cache configuration deleted successfully",
                "deleted_at": Utc::now()
            }), "Cache configuration deleted successfully")))
        }
        Err(e) => {
            error!("❌ Failed to delete cache configuration: {}", e);
            Err(e)
        }
    }
}

// ============================================================================
// 缓存操作API
// ============================================================================

/// 清理缓存数据
/// POST /api/v1/cache/clear
pub async fn clear_cache_data(
    State(state): State<AppState>,
    Json(request): Json<CacheClearRequest>,
) -> ApiResult<Json<ApiResponse<CacheClearResponse>>> {
    info!("🧹 Clearing cache data");

    let cache_service = state.config_manager.cache_config.clone();
    let start_time = std::time::Instant::now();

    match cache_service.clear_cache_data(request.cache_types.clone()).await {
        Ok(cleared_keys) => {
            let processing_time = start_time.elapsed().as_millis() as u64;
            let freed_memory_mb = (cleared_keys as f64 / 1000.0) * 0.5; // 估算释放的内存
            
            let response = CacheClearResponse {
                cleared_keys,
                freed_memory_mb,
                cache_types: request.cache_types.unwrap_or_else(|| vec!["all".to_string()]),
                expired_only: request.expired_only.unwrap_or(false),
                processing_time_ms: processing_time,
            };
            
            info!("✅ Cache data cleared successfully: {} keys", cleared_keys);
            Ok(Json(ApiResponse::success(response, "Cache data cleared successfully")))
        }
        Err(e) => {
            error!("❌ Failed to clear cache data: {}", e);
            Err(e)
        }
    }
}

/// 预热缓存
/// POST /api/v1/cache/warm
pub async fn warm_cache(
    State(state): State<AppState>,
    Json(request): Json<CacheWarmRequest>,
) -> ApiResult<Json<ApiResponse<CacheWarmResponse>>> {
    info!("🔥 Warming cache");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.warm_cache(
        request.cache_types.clone(),
        request.priority_keys.clone(),
    ).await {
        Ok(_) => {
            let estimated_keys = request.cache_types.len() as u32 * 5000;
            let batch_size = request.batch_size.unwrap_or(100);
            let max_concurrent = request.max_concurrent.unwrap_or(10);
            
            let response = CacheWarmResponse {
                success: true,
                message: "缓存预热已启动".to_string(),
                cache_types: request.cache_types,
                estimated_keys,
                batch_size,
                max_concurrent,
                estimated_time_minutes: (estimated_keys / (batch_size * max_concurrent)) / 60,
                status: "warming".to_string(),
            };
            
            info!("✅ Cache warming started successfully");
            Ok(Json(ApiResponse::success(response, "Cache warming started successfully")))
        }
        Err(e) => {
            error!("❌ Failed to start cache warming: {}", e);
            Err(e)
        }
    }
}

/// 获取缓存键信息
/// GET /api/v1/cache/keys
pub async fn get_cache_keys(
    State(state): State<AppState>,
    Query(params): Query<CacheKeyQueryParams>,
) -> ApiResult<Json<ApiResponse<Vec<serde_json::Value>>>> {
    info!("🔍 Getting cache keys");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.get_cache_keys(
        params.cache_type.as_deref(),
        params.pattern.as_deref(),
        params.limit,
        params.offset,
    ).await {
        Ok(keys) => {
            info!("✅ Cache keys retrieved successfully: {} keys", keys.len());
            Ok(Json(ApiResponse::success(keys, "Cache keys retrieved successfully")))
        }
        Err(e) => {
            error!("❌ Failed to get cache keys: {}", e);
            Err(e)
        }
    }
}

/// 重新加载缓存配置
/// POST /api/v1/config/cache/reload
pub async fn reload_cache_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<serde_json::Value>>> {
    info!("🔄 Reloading cache configuration");

    let cache_service = state.config_manager.cache_config.clone();

    match cache_service.reload().await {
        Ok(_) => {
            info!("✅ Cache configuration reloaded successfully");
            Ok(Json(ApiResponse::success(serde_json::json!({
                "message": "Cache configuration reloaded successfully",
                "reloaded_at": Utc::now()
            }), "Cache configuration reloaded successfully")))
        }
        Err(e) => {
            error!("❌ Failed to reload cache configuration: {}", e);
            Err(e)
        }
    }
}
