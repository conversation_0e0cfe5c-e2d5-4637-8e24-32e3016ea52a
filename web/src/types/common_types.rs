
use serde::{Deserialize,Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

/// 告警严重程度
#[derive(Debug, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,     // 信息
    Warning,  // 警告
    Critical, // 严重
    Emergency, // 紧急
}

/// 健康状态
#[derive(Debug, Serialize)]
pub enum HealthStatus {
    Healthy,    // 健康
    Warning,    // 警告
    Critical,   // 严重
    Down,       // 宕机
}

/// 时间周期类型
#[derive(Debug, Serialize, Deserialize)]
pub enum PeriodType {
    Daily,     // 日报
    Weekly,    // 周报
    Monthly,   // 月报
    Quarterly, // 季报
    Yearly,    // 年报
    Custom,    // 自定义
}

/// 问题严重程度
#[derive(Debug, Serialize, Deserialize)]
pub enum IssueSeverity {
    Low,      // 低
    Medium,   // 中
    High,     // 高
    Critical, // 严重
}

/// 订单类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderType {
    Market,      // 市价单
    Limit,       // 限价单
    StopLoss,    // 止损单
    TakeProfit,  // 止盈单
    StopLimit,   // 止损限价单
    TrailingStop, // 追踪止损单
}


/// 时间范围
#[derive(Debug, Deserialize,Serialize)]
pub struct TimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
    pub duration_minutes: u64,
    pub period_type: PeriodType,
}

/// 资产余额
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssetBalance {
    pub asset: String,
    pub total: Decimal,
    pub available: Decimal,
    pub locked: Decimal,
    pub value_usd: Decimal,
    pub percentage: f64, // 占投资组合的百分比
}

/// 策略性能响应
#[derive(Debug, Serialize, Deserialize)]
pub struct StrategyPerformanceResponse {
    pub strategy_id: String,
    pub total_trades: u64,
    pub winning_trades: u64,
    pub losing_trades: u64,
    pub total_pnl: String,
    pub win_rate: String,
    pub sharpe_ratio: Option<String>,
    pub max_drawdown: String,
    pub current_positions: u32,
    pub last_updated: String,
}