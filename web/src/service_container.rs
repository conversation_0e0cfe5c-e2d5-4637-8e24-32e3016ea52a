//! 服务容器实现
//!
//! 为Web应用提供统一的服务访问接口

use std::sync::Arc;
use async_trait::async_trait;
use sigmax_core::{
    ServiceContainer, DataProvider, Exchange, RiskManager, PortfolioManager,
    StrategyManager, SigmaXResult, Amount, TradingPair, Price, Candle,
    TimeFrame, Trade, OrderBook, Balance, ExchangeId, Order, OrderId,
    OrderSide, OrderType, Quantity, Strategy, StrategyId, ConfigCache
};
use sigmax_data::providers::BacktestDataProvider;
use sigmax_database::DatabaseManager;
use sigmax_risk::unified_engine::UnifiedRiskEngine;
use sigmax_risk::repositories::SqlUnifiedRiskRepository;

/// Web应用的服务容器实现
pub struct WebServiceContainer {
    data_provider: Arc<dyn DataProvider>,
    exchange_manager: Arc<dyn Exchange>,
    risk_manager: Arc<dyn RiskManager>,
}

impl WebServiceContainer {
    /// 创建新的服务容器（使用回测数据提供者）
    pub fn new() -> Self {
        // 创建回测模式的数据提供者配置
        let mut config = sigmax_core::DataProviderConfig::default();
        config.provider_type = sigmax_core::DataProviderType::Backtest;
        config.name = "backtest_provider".to_string();

        Self {
            data_provider: Arc::new(BacktestDataProvider::new(config)),
            exchange_manager: Arc::new(WebMockExchange::new()),
            risk_manager: Arc::new(WebMockRiskManager::new()),
        }
    }

    /// 创建带有统一风控引擎的服务容器
    pub async fn with_unified_risk_engine(
        database: Arc<DatabaseManager>,
    ) -> SigmaXResult<Self> {
        // 1. 创建统一风控仓储
        let unified_risk_repo = Arc::new(SqlUnifiedRiskRepository::new(database));

        // 2. 创建统一风控引擎（直接作为风控管理器使用）
        let risk_manager = Arc::new(UnifiedRiskEngine::new(unified_risk_repo).await?);

        // 创建回测模式的数据提供者配置
        let mut config = sigmax_core::DataProviderConfig::default();
        config.provider_type = sigmax_core::DataProviderType::Backtest;
        config.name = "backtest_provider".to_string();

        Ok(Self {
            data_provider: Arc::new(BacktestDataProvider::new(config)),
            exchange_manager: Arc::new(WebMockExchange::new()),
            risk_manager: risk_manager,
        })
    }

    /// 创建带有自定义风控管理器的服务容器
    pub fn with_risk_manager(risk_manager: Arc<dyn RiskManager>) -> Self {
        // 创建回测模式的数据提供者配置
        let mut config = sigmax_core::DataProviderConfig::default();
        config.provider_type = sigmax_core::DataProviderType::Backtest;
        config.name = "backtest_provider".to_string();

        Self {
            data_provider: Arc::new(BacktestDataProvider::new(config)),
            exchange_manager: Arc::new(WebMockExchange::new()),
            risk_manager,
        }
    }
}

#[async_trait]
impl ServiceContainer for WebServiceContainer {
    fn get_data_provider(&self) -> Arc<dyn DataProvider> {
        Arc::clone(&self.data_provider)
    }

    fn get_exchange_manager(&self) -> Arc<dyn Exchange> {
        Arc::clone(&self.exchange_manager)
    }

    fn get_risk_manager(&self) -> Arc<dyn RiskManager> {
        Arc::clone(&self.risk_manager)
    }

    async fn create_portfolio_manager(&self, initial_capital: Amount) -> SigmaXResult<Arc<dyn PortfolioManager>> {
        // 🔥 修复：使用真正的DefaultPortfolioManager而不是Mock版本
        Ok(Arc::new(sigmax_portfolio::DefaultPortfolioManager::with_initial_value(initial_capital)))
    }

    async fn create_strategy_manager(&self) -> SigmaXResult<Arc<dyn StrategyManager>> {
        // 临时返回一个Mock策略管理器，因为已经重构
        Ok(Arc::new(WebMockStrategyManager::new()))
    }
}

impl Default for WebServiceContainer {
    fn default() -> Self {
        Self::new()
    }
}

// Mock组件实现

/// Mock交易所（如果sigmax_exchange中没有的话）
pub struct WebMockExchange;

impl WebMockExchange {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Exchange for WebMockExchange {
    fn id(&self) -> ExchangeId {
        ExchangeId::Simulator
    }

    async fn get_balances(&self) -> SigmaXResult<Vec<Balance>> {
        Ok(vec![Balance::new(
            ExchangeId::Simulator,
            "USDT".to_string(),
            Amount::from(10000),
            Amount::ZERO,
        )])
    }

    async fn place_order(&self, _order: &Order) -> SigmaXResult<OrderId> {
        Ok(uuid::Uuid::new_v4())
    }

    async fn cancel_order(&self, _order_id: OrderId) -> SigmaXResult<()> {
        Ok(())
    }

    async fn get_order(&self, _order_id: OrderId) -> SigmaXResult<Order> {
        // 返回一个模拟订单
        Ok(Order::new(
            ExchangeId::Simulator,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Market,
            Quantity::from(1),
            None,
        ))
    }

    async fn get_order_book(&self, _trading_pair: &TradingPair) -> SigmaXResult<OrderBook> {
        Ok(OrderBook {
            timestamp: chrono::Utc::now(),
            bids: vec![(Price::from(49900), Quantity::from(1))],
            asks: vec![(Price::from(50100), Quantity::from(1))],
        })
    }

    async fn get_candles(
        &self,
        _trading_pair: &TradingPair,
        _timeframe: TimeFrame,
        _limit: Option<u32>,
    ) -> SigmaXResult<Vec<Candle>> {
        Ok(vec![])
    }

    async fn get_trades(&self, _trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        Ok(vec![])
    }
}

/// Mock策略管理器
pub struct WebMockStrategyManager;

impl WebMockStrategyManager {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl StrategyManager for WebMockStrategyManager {
    async fn add_strategy(&self, _strategy: Arc<dyn Strategy>) -> SigmaXResult<()> {
        Ok(())
    }

    async fn remove_strategy(&self, _strategy_id: StrategyId) -> SigmaXResult<()> {
        Ok(())
    }

    async fn get_active_strategies(&self) -> SigmaXResult<Vec<Arc<dyn Strategy>>> {
        Ok(vec![])
    }

    async fn start_all(&self) -> SigmaXResult<()> {
        Ok(())
    }

    async fn stop_all(&self) -> SigmaXResult<()> {
        Ok(())
    }
}

/// Mock投资组合管理器（如果sigmax_portfolio中没有的话）
pub struct WebMockPortfolioManager {
    initial_capital: Amount,
}

impl WebMockPortfolioManager {
    pub fn new(initial_capital: Amount) -> Self {
        Self { initial_capital }
    }
}

#[async_trait]
impl PortfolioManager for WebMockPortfolioManager {
    async fn get_balances(&self) -> SigmaXResult<std::collections::HashMap<String, Balance>> {
        let mut balances = std::collections::HashMap::new();
        balances.insert("USDT".to_string(), Balance::new(
            ExchangeId::Simulator,
            "USDT".to_string(),
            self.initial_capital,
            Amount::ZERO,
        ));
        Ok(balances)
    }

    async fn update_balance(&mut self, _balance: Balance) -> SigmaXResult<()> {
        Ok(())
    }

    async fn calculate_total_value(&self) -> SigmaXResult<Amount> {
        Ok(self.initial_capital)
    }

    async fn get_pnl(&self) -> SigmaXResult<Amount> {
        Ok(Amount::ZERO)
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

/// Mock风险管理器（如果sigmax_risk中没有的话）
pub struct WebMockRiskManager;

impl WebMockRiskManager {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl RiskManager for WebMockRiskManager {
    async fn check_order_risk(&self, _order: &Order) -> SigmaXResult<bool> {
        Ok(true) // 总是允许订单
    }

    async fn check_position_risk(&self, _balances: &[Balance]) -> SigmaXResult<bool> {
        Ok(true) // 总是允许持仓
    }

    async fn get_max_order_size(&self, _trading_pair: &TradingPair) -> SigmaXResult<Quantity> {
        Ok(Quantity::from(1000)) // 最大订单大小
    }
}