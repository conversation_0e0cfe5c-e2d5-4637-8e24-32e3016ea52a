[package]
name = "sigmax-web"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[[bin]]
name = "server"
path = "src/bin/server.rs"

[dependencies]
sigmax-core.workspace = true
sigmax-engines.workspace = true
sigmax-strategies.workspace = true
sigmax-reporting.workspace = true
sigmax-database.workspace = true
sigmax-data.workspace = true
sigmax-risk.workspace = true
sigmax-portfolio.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
axum.workspace = true
sqlx.workspace = true
tower = { workspace = true, features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "trace", "fs"] }
anyhow.workspace = true
tracing.workspace = true
uuid.workspace = true
chrono.workspace = true
rust_decimal.workspace = true
async-trait.workspace = true
tracing-subscriber = { workspace = true, features = ["fmt", "registry"] }
tracing-appender = "0.2"
futures = "0.3"
rust_decimal_macros = "1.37"
flate2 = "1.0"
dotenv = "0.15"
once_cell = "1.19"

[dev-dependencies]
axum-test = "15.0"

[env]
SQLX_OFFLINE = "true"
