-- 更新风控规则配置以支持回测
-- 这个脚本将调整过于严格的风控规则，使回测能够正常进行

-- 1. 更新单个持仓大小限制规则
UPDATE unified_risk_rules 
SET 
    parameters = '{
        "max_position_percent": 95.0,
        "base_amount": "total_capital",
        "include_unrealized_pnl": true,
        "backtest_mode": true
    }',
    description = '限制单个交易对的最大持仓（回测优化版本）',
    updated_at = NOW()
WHERE 
    id = '40000000-0000-0000-0000-000000000001'
    AND name = '单个持仓大小限制';

-- 2. 临时禁用过于严格的规则
UPDATE unified_risk_rules 
SET 
    enabled = false,
    description = description || ' (临时禁用用于回测)',
    updated_at = NOW()
WHERE 
    name IN (
        '最大回撤保护',
        '日交易频率限制',
        '小时交易频率限制'
    );

-- 3. 创建回测专用的宽松规则
INSERT INTO unified_risk_rules (
    id, name, description, category, rule_type, 
    parameters, enabled, priority, strategy_type, created_by
) VALUES (
    '99000000-0000-0000-0000-000000000001',
    '回测持仓大小限制',
    '专门为回测环境设计的持仓限制规则',
    'position',
    'single_position_limit',
    '{
        "max_position_percent": 98.0,
        "base_amount": "total_capital",
        "include_unrealized_pnl": true,
        "backtest_mode": true,
        "allow_initial_cash_position": true
    }',
    true,
    95,
    NULL,
    'system'
) ON CONFLICT (id) DO UPDATE SET
    parameters = EXCLUDED.parameters,
    enabled = EXCLUDED.enabled,
    updated_at = NOW();

-- 4. 更新全局订单金额限制，使其更宽松
UPDATE unified_risk_rules 
SET 
    parameters = '{
        "max_amount": 50000.0,
        "include_fees": true,
        "currency": "USDT"
    }',
    updated_at = NOW()
WHERE 
    rule_type = 'order_amount_limit';

-- 5. 更新最大杠杆限制
UPDATE unified_risk_rules 
SET 
    parameters = '{
        "max_leverage": 10.0,
        "calculation_method": "gross_exposure",
        "include_pending_orders": false
    }',
    updated_at = NOW()
WHERE 
    rule_type = 'max_leverage_limit';

-- 6. 查看更新结果
SELECT 
    id,
    name,
    enabled,
    priority,
    parameters,
    updated_at
FROM unified_risk_rules 
WHERE 
    rule_type IN ('single_position_limit', 'order_amount_limit', 'max_leverage_limit')
    OR name LIKE '%回测%'
ORDER BY priority DESC, name;
